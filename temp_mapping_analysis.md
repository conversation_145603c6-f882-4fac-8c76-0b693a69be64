# Concept File Mapping Analysis

This table compares titles from files in `public/concept_files/` and `concept_files/` to identify potential matches for mapping.

| File in `public/concept_files/` | Title from `public/` | File in `concept_files/` | Title from `concept_files/` | Titles Match? | Matches with |
|---|---|---|---|---|---|
| `3-ways-to-make-a-portfolio-great.md` | 3 Ways to Make a Portfolio Project Great | `_135.md` | 3 Ways to Make a Portfolio Project Great | Yes | `_135.md` |
| `HTMLCSS.md` | HTML/CSS | `_2.md` | HTML/CSS | Yes | `_2.md` |
| `docker.md` | Docker | `_2.md` | HTML/CSS | No | - |
| `flowcharts.md` | Flowcharts | `_2.md` | HTML/CSS | No | - |
| `maze-project.md` | Maze project | `_2.md` | HTML/CSS | No | - |
| `trinity_of-frontend-quality.md` | The trinity of front-end quality | `_4.md` | the trinity of front-end quality | Yes | `_4.md` |