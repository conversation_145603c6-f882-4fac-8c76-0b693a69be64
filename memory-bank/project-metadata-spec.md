# Project Metadata Specification

## Standard Format

### 1. Project Header
```markdown
# [Project Title]
![Category](https://img.shields.io/badge/Category-Type-color)
```

### 2. Metadata Block
```markdown
👤 By [Author Name]
📝 [Level]
⚖️ Weight: [number]
📅 Start: [date] [time]
📅 End: [date] [time]
⏱️ Duration: [period]
```

### 3. Project Summary
```markdown
### In a nutshell...
Auto QA review: [score]/[total] mandatory
Altogether: [percentage]%
* Mandatory: [percentage]%
* Optional: [status or percentage]
```

## Standardization Details

### 1. Level Indicators
Use 📝 with one of:
- Beginner
- Novice
- Intermediate
- Advanced
- Expert

Guidelines for level assignment:
- Beginner: First introduction to concepts
- Novice: Basic understanding required
- Intermediate: Building on previous knowledge
- Advanced: Complex concepts/implementations
- Expert: System design/architecture level

### 2. Metadata Components

#### Author Format
- Always use emoji: 👤
- Format: `👤 By [Name]`
- Example: `👤 By ALX Staff`

#### Level Format
- Always use emoji: 📝
- Format: `📝 [Level]`
- Example: `📝 Intermediate`

#### Weight Format
- Always use emoji: ⚖️
- Format: `⚖️ Weight: [number]`
- Example: `⚖️ Weight: 1`

#### Dates and Duration
- Start/End: `📅 [YYYY-MM-DD HH:mm AM/PM]`
- Duration: `⏱️ Duration: [period]`
- Get dates from months/[month].js files

### 3. Summary Section
- Always use H3: `### In a nutshell...`
- Include all completion metrics
- Consistent formatting for percentages

## Required Emojis
- 👤 Author (U+1F464)
- 📝 Level (U+1F4DD)
- ⚖️ Weight (U+2696)
- 📅 Dates (U+1F4C5)
- ⏱️ Duration (U+23F1)

## Implementation Guidelines

### 1. Project Types and Levels
- Onboarding: Usually Beginner/Novice
- Tools: Novice to Intermediate
- Core Projects: Intermediate to Advanced
- System Design: Advanced/Expert

### 2. Level Assessment Criteria
1. **Beginner**
   - Introduction to concepts
   - Guided exercises
   - Basic tool usage

2. **Novice**
   - Basic problem-solving
   - Tool familiarity
   - Simple implementations

3. **Intermediate**
   - Independent problem-solving
   - Multiple concept integration
   - System component understanding

4. **Advanced**
   - Complex implementations
   - Performance optimization
   - System integration

5. **Expert**
   - System architecture
   - Design patterns
   - Best practices implementation

### 3. Timeline Integration
- Use dates from months/*.js for consistency
- Example timeline format:
```javascript
{
  "day": [
    { id: "project-id", title: "Project Title" }
  ]
}
```

## Migration Notes
1. Convert all plain text metadata to emoji format
2. Assess and update project levels based on content
3. Standardize dates from timeline data
4. Maintain consistent formatting and spacing
5. Preserve existing content while updating metadata