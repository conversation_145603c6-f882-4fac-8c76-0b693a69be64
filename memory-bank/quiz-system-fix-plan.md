# Quiz System Fix Plan

## Issue Identified
The quizzes are not being rendered in the UI because the quiz files from the `system-engineering-devops` directory aren't being properly imported and exported in the main quiz index file.

## Current Structure
- Quiz files exist in `src/data/project-quizzes/system-engineering-devops/` (shell-basics-quiz.js, shell-permissions-quiz.js, etc.)
- The main index file (`src/data/project-quizzes/index.js`) imports and exports quizzes from other directories but not from `system-engineering-devops`
- In `ProjectDetails.js`, the quiz component tries to access quizzes through the `projectQuizzes` object using the project ID

## Fix Implementation Plan

### 1. Update the project-quizzes/index.js file

The main issue is that the `system-engineering-devops` quizzes are not being imported and mapped in the index.js file. The fix requires:

```javascript
// Add to the top of the file with other imports
// System Engineering & DevOps quizzes
import { shellBasicsQuiz } from './system-engineering-devops/shell-basics-quiz.js';
import { shellPermissionsQuiz } from './system-engineering-devops/shell-permissions-quiz.js';
import { shellIoRedirectionsQuiz } from './system-engineering-devops/shell-io-redirections-quiz.js';
import { shellVariablesQuiz } from './system-engineering-devops/shell-variables-quiz.js';
import { shellLoopsQuiz } from './system-engineering-devops/shell-loops-quiz.js';
import { shellProcessesQuiz } from './system-engineering-devops/shell-processes-quiz.js';
import { shellRegexQuiz } from './system-engineering-devops/shell-regex-quiz.js';
import { networkingBasics0Quiz } from './system-engineering-devops/networking-basics-0-quiz.js';

// Add to the projectQuizzes object
export const projectQuizzes = {
  // Existing entries...

  // System Engineering & DevOps quizzes
  'shell-basics': shellBasicsQuiz,
  'shell-permissions': shellPermissionsQuiz,
  'shell-io-redirections': shellIoRedirectionsQuiz,
  'shell-variables': shellVariablesQuiz,
  'shell-loops': shellLoopsQuiz,
  'shell-processes': shellProcessesQuiz,
  'shell-regex': shellRegexQuiz,
  'networking-basics-0': networkingBasics0Quiz,
};
```

### 2. Verify Project IDs Match Quiz References

Ensure that the project IDs used in `ProjectDetails.js` match the keys in the `projectQuizzes` object. From the logs in the code:

```javascript
// In ProjectDetails.js, line 469
const projectId = id.includes('/') ? id.split('/').pop() : id;
const quiz = projectQuizzes[projectId]; 
console.log('Loading quiz for:', projectId);
```

This suggests that the project ID is extracted from the URL path. We need to ensure these IDs match the keys in `projectQuizzes`.

### 3. Debug Logging for Verification

Add debugging logs to verify the correct quiz mapping:

```javascript
// Add in ProjectDetails.js after line 470
console.log('Available quiz keys:', Object.keys(projectQuizzes));
console.log('Project ID from URL:', projectId);
console.log('Retrieved quiz:', quiz);
```

## Testing Plan

1. After implementing the changes, navigate to a project page that should display a quiz
2. Open browser developer console to view the logs
3. Verify that:
   - The project ID is correctly extracted
   - The quiz is found in the projectQuizzes object
   - The quiz component receives and renders the questions

## Alternative Approaches

If the above doesn't work, investigate:

1. Check if there's another import/export mechanism where these quizzes should be registered
2. Verify if the project IDs might be transformed or prefixed in some way
3. Check if the `system-engineering-devops` directory structure follows a different pattern than the other quiz directories

## Implementation Note

This fix requires editing JavaScript files (specifically `src/data/project-quizzes/index.js`), which can't be done in Architect mode. A mode switch to Code mode is necessary for implementation.