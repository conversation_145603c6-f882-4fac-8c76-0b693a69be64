# Code Changes Required

## 1. Problem Identified
The ProjectDetails component expects content to be a string but our new standardized format provides content as an object:
```javascript
{
  title: "Tool Title",
  status: "onboarding-tools",
  content: `...markdown content...`
}
```

## 2. Required Changes

### ProjectDetails.js
1. Update content access at line 720:
```javascript
  const projectData = projectContent[projectId];
  const content = projectData?.content;
```

2. Add title and status handling:
```javascript
  if (!projectData || !projectData.content) {
    // ... not found UI ...
  }

  // Add status tag if present
  const statusTag = projectData.status ? (
    <div className="inline-block px-2 py-1 text-sm rounded bg-blue-100 text-blue-800">
      {projectData.status}
    </div>
  ) : null;
```

3. Update the main render section to show status:
```javascript
  return (
    <div className="min-h-[calc(100vh-40px)] flex flex-col bg-background">
      <nav>...</nav>
      <main className="flex-grow w-full">
        <div className="max-w-6xl mx-auto px-6 py-8 md:px-10 lg:px-16">
          {statusTag && (
            <div className="mb-4">
              {statusTag}
            </div>
          )}
          <div className="space-y-8">
            {parseContent(projectData.content, id)}
          </div>
        </div>
      </main>
      <footer>...</footer>
    </div>
  );
```

## 3. Next Steps
1. Switch to code mode to implement these changes
2. Test each project type:
   - Shell navigation
   - Vi
   - Emacs
   - Git
3. Verify status tags appear correctly
4. Ensure quiz functionality still works
