# ALX Course Tracker Component Reference

## Core Components

### ProjectCard
**Purpose**: Display a concise project summary in a list or grid view
**Location**: `src/components/Project/ProjectCard.jsx`
**Props**:
- `project`: Project data object with title, description, status, etc.
- `onClick`: Function to handle click navigation
- `compact`: Boolean to toggle between full and compact display mode

**Usage Example**:
```jsx
<ProjectCard 
  project={projectData}
  onClick={() => navigateToProject(projectData.id)}
  compact={false}
/>
```

**Styling Pattern**:
- Card container: `rounded-md shadow-sm p-4 hover:shadow-md transition-shadow`
- Status tag positioning: `absolute top-2 right-2`
- Consistent spacing and typography

### ProjectDetails
**Purpose**: Display comprehensive project information from markdown
**Location**: `src/components/Project/ProjectDetails.jsx`
**Props**:
- `projectId`: String identifier for the project
- `markdownContent`: String containing markdown content
- `resources`: Array of resource links
- `status`: Project status (onboarding, mandatory, etc.)

**Usage Example**:
```jsx
<ProjectDetails
  projectId="shell-basics"
  markdownContent={markdownContent}
  resources={projectResources}
  status="mandatory"
/>
```

**Key Behaviors**:
- Renders markdown content with proper formatting
- Displays status tag with appropriate styling
- Organizes resources into accessible links
- Handles navigation between project sections

### ProjectQuiz
**Purpose**: Present interactive quizzes related to project content
**Location**: `src/components/Quiz/ProjectQuiz.jsx`
**Props**:
- `quizData`: Object containing structured quiz questions and answers
- `projectId`: String identifier for the related project
- `onComplete`: Function called when quiz is finished

**Usage Example**:
```jsx
<ProjectQuiz
  quizData={shellBasicsQuiz}
  projectId="shell-basics"
  onComplete={handleQuizCompletion}
/>
```

**State Management**:
- Tracks current question index
- Manages user answers and scoring
- Handles validation and feedback
- Controls quiz completion state

### MonthContainer
**Purpose**: Organize projects by month of curriculum
**Location**: `src/components/Month/Month1.jsx` (through `Month10.jsx`)
**Props**:
- `projects`: Array of project data for the specific month
- `filter`: Optional filter criteria

**Usage Example**:
```jsx
<Month1 
  projects={month1Projects}
  filter={activeFilter}
/>
```

**Internal Structure**:
- Month header with relevant information
- ProjectList component grouping projects by category
- Navigation links to adjacent months

### StatusTag
**Purpose**: Visual indicator of project status/category
**Location**: `src/components/UI/StatusTag.jsx`
**Props**:
- `status`: String representing status ("onboarding", "mandatory", "optional", "onboarding-tools")
- `size`: Optional size variant ("sm", "md", "lg")

**Usage Example**:
```jsx
<StatusTag status="mandatory" size="md" />
```

**Styling Implementation**:
- onboarding: `bg-green-100 text-green-800`
- onboarding-tools: `bg-green-100 text-green-800`
- mandatory: `bg-blue-100 text-blue-800`
- optional: `bg-gray-100 text-gray-800`

### MarkdownRenderer
**Purpose**: Process and display markdown content with proper formatting
**Location**: `src/components/Content/MarkdownRenderer.jsx`
**Props**:
- `content`: String containing markdown content
- `toc`: Boolean to toggle table of contents display

**Usage Example**:
```jsx
<MarkdownRenderer 
  content={projectMarkdown}
  toc={true} 
/>
```

**Features**:
- Syntax highlighting for code blocks
- Proper heading hierarchy
- Link handling
- Table formatting
- List rendering

## Page Components

### HomePage
**Purpose**: Entry point with navigation to curriculum months
**Location**: `src/pages/HomePage.jsx`
**Key Sections**:
- Welcome information
- Month navigation grid
- Quick links to key resources
- Status explanation

### ProjectPage
**Purpose**: Container for project listing within a category
**Location**: `src/pages/ProjectPage.jsx`
**Props**:
- `category`: String identifier for project category

**Usage Example**:
```jsx
<ProjectPage category="system-engineering-devops" />
```

**Behavior**:
- Loads projects for specified category
- Handles filtering and sorting
- Renders projects in appropriate layout
- Maintains navigation state

### ProjectDetailsPage
**Purpose**: Full page dedicated to a single project
**Location**: `src/pages/ProjectDetailsPage.jsx`
**Props**:
- `projectId`: String identifier for the project

**Usage Example**:
```jsx
<ProjectDetailsPage projectId="shell-basics" />
```

**Structure**:
- Project header with title and status
- Markdown content section
- Related resources panel
- Quiz integration

## Helper Components

### CodeBlock
**Purpose**: Display formatted code with syntax highlighting
**Location**: `src/components/Content/CodeBlock.jsx`
**Props**:
- `code`: String containing code
- `language`: Programming language for syntax highlighting
- `fileName`: Optional file name to display

### ResourceLink
**Purpose**: Formatted link to external resources
**Location**: `src/components/UI/ResourceLink.jsx`
**Props**:
- `url`: URL to the resource
- `title`: Display text for the link
- `type`: Resource type (documentation, github, article, etc.)

### QuizQuestion
**Purpose**: Individual quiz question with options
**Location**: `src/components/Quiz/QuizQuestion.jsx`
**Props**:
- `question`: Question text
- `options`: Array of possible answers
- `correctAnswer`: Index or value of correct answer
- `onAnswer`: Function called when answered

## Component Relationships

### Hierarchy Tree
```
App
├── Navigation
│   └── NavBar
├── Pages
│   ├── HomePage
│   │   └── MonthGrid
│   ├── ProjectPage
│   │   └── ProjectList
│   │       └── ProjectCard
│   └── ProjectDetailsPage
│       ├── ProjectDetails
│       │   └── MarkdownRenderer
│       │       ├── CodeBlock
│       │       └── ResourceLink
│       └── ProjectQuiz
│           └── QuizQuestion
└── Shared
    └── StatusTag
```

### Data Flow
1. App loads project structure and organization
2. User navigates to Month or Category view
3. ProjectCards display available projects
4. User selects specific project
5. ProjectDetails loads and displays markdown content
6. Related ProjectQuiz is made available for knowledge testing

## State Management Patterns

### Local Component State
- Used for UI interactions, form inputs, and component-specific state
- Implemented with React's `useState` hook
- Examples: quiz answer selection, expanded/collapsed sections

### Context-Based State
- Used for shared state across component trees
- Implemented with React's Context API
- Examples: active filters, theme preferences, global navigation state

### Props Drilling
- Used for passing data down component hierarchy
- Limited to 2-3 levels to avoid excessive passing
- Examples: project data, status information, click handlers

## Implementation Guidelines

### Component Creation Pattern
```jsx
// Functional component template
import React from 'react';

const ComponentName = ({ prop1, prop2 }) => {
  // State and hooks
  const [state, setState] = React.useState(initialValue);

  // Effects as needed
  React.useEffect(() => {
    // Effect logic
  }, [dependencies]);

  // Helper functions
  const handleSomething = () => {
    // Logic
  };

  // Render
  return (
    <div className="component-wrapper">
      {/* Component JSX */}
    </div>
  );
};

export default ComponentName;
```

### Styling Approach
- Tailwind CSS utility classes as primary styling method
- Consistent spacing and typography scale
- Status colors maintained per project guidelines
- Responsive design patterns with mobile-first approach