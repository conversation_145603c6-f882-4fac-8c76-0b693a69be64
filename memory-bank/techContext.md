# ALX Course Tracker Technical Context

## Technology Stack

### Frontend Framework
- React (with TypeScript)
- Components organized for modularity
- Functional components with hooks
- Month-based content organization

### Styling
- Tailwind CSS for utility-first styling
- Custom components.json for configuration
- Responsive design implementation
- Consistent status tag styling

### Build Tools
- Vite/TypeScript configuration
- PostCSS for CSS processing
- TSConfig for TypeScript settings
- Markdown processing capabilities

### Package Management
- Multiple package management files present:
  - package.json
  - package-lock.json
  - bun.lock
  - yarn.lock

## Development Setup

### Core Dependencies
```json
{
  "typescript": "^4.x",
  "react": "^18.x",
  "tailwindcss": "^3.x",
  "markdown-it": "latest"
}
```

### Configuration Files
- tsconfig.json - TypeScript configuration
- tsconfig.node.json - Node-specific TS config
- postcss.config.js - PostCSS setup
- tailwind.config.js - Tailwind customization
- .clinerules - Project-specific rules

## Technical Constraints

### Content Organization
- Structured markdown files
- Organized quiz content
- Month-based component system
- Category-based project structure

### Browser Support
- Modern browsers with ES6+ support
- Responsive design for all screen sizes
- Mobile-first approach
- Content accessibility

### Performance Requirements
- Fast page loads
- Efficient data management
- Optimized resource loading
- Quick content navigation

### Code Quality Standards
- TypeScript for type safety
- Consistent code formatting
- Component reusability
- Proper error handling
- Clear documentation

## Development Patterns

### File Structure
```
/
├── src/
│   ├── components/
│   │   ├── Month1-10/
│   │   ├── Project/
│   │   └── Quiz/
│   └── data/
│       ├── projects/
│       │   ├── low-level/
│       │   ├── onboarding/
│       │   └── zero-day/
│       └── project-quizzes/
│           ├── low-level/
│           ├── onboarding/
│           └── tools/
├── markdown_files/
└── quiz_files/
```

### Data Management
- JSON files for structured data
- Markdown for content storage
- Efficient data loading patterns
- Clear data flow through components
- Category-based organization

### Styling Guidelines
- Tailwind utility classes
- Consistent color schemes
- Responsive design patterns
- Component-specific styles
- Status tag styling system

## Environment Setup

### Required Tools
- Node.js
- TypeScript
- Package manager (npm/yarn/bun)
- Git for version control
- Markdown processor

### Development Workflow
- Local development server
- Hot module replacement
- TypeScript compilation
- CSS processing
- Content validation

## Technical Debt Considerations

### Areas to Monitor
- Package manager consistency
- Build optimization
- Content organization
- Performance optimization
- Navigation efficiency

### Maintenance Requirements
- Regular dependency updates
- Code quality monitoring
- Performance benchmarking
- Browser compatibility checks
- Content structure validation
- Documentation updates

### Content Management
- Regular markdown validation
- Quiz system maintenance
- Resource link verification
- Content organization review
- Navigation optimization
