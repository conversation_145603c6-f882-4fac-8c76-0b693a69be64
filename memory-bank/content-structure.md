# ALX Course Tracker Content Structure

## Content Types & Relationships

### Primary Content Types
```
┌───────────────────┐     ┌─────────────────────┐     ┌────────────────┐
│  Markdown Files   │ ←→  │    Project Data     │ ←→  │   Quiz Files   │
│  (Content)        │     │  (Metadata/Config)  │     │  (Assessment)  │
└───────────────────┘     └─────────────────────┘     └────────────────┘
```

### Content Type Details

#### 1. Markdown Files
- **Location**: `/markdown_files/`
- **Format**: Markdown with YAML frontmatter
- **Naming Convention**: `0x{number}__{Project_Name}.md` with double underscores for spaces
- **Purpose**: Comprehensive content for each course module/project
- **Integration Point**: Referenced by project data files

#### 2. Project Data Files
- **Location**: `/src/data/projects/{category}/`
- **Format**: JavaScript objects exported as modules
- **Naming Convention**: kebab-case (e.g., `shell-basics.js`)
- **Purpose**: Project metadata, status, requirements, and resources
- **Integration Point**: Links markdown content with UI components and quiz data

#### 3. Quiz Files
- **Location**: 
  - Source: `/src/data/project-quizzes/{category}/`
  - Markdown: `/quiz_files/`
- **Format**: JavaScript objects with structured quiz questions
- **Naming Convention**: kebab-case with `-quiz` suffix (e.g., `shell-basics-quiz.js`)
- **Purpose**: Knowledge assessment related to project content
- **Integration Point**: Connected to projects via matching naming patterns

## Directory Structure Relationships

```
markdown_files/                 src/data/projects/             src/data/project-quizzes/
└── 0x00__Shell__basics.md  →  └── shell-basics.js       ←→   └── shell-basics-quiz.js
```

## Content Organization Hierarchy

### Primary Categorization
1. **Programming Level**:
   - Low-level (C programming)
   - Higher-level (Python, JavaScript)
   - System Engineering & DevOps

2. **Learning Progression**:
   - Month-based organization (Month1-Month10)
   - Project difficulty progression
   - Concept building sequence

3. **Project Type**:
   - Onboarding projects
   - Development tools
   - Core programming projects
   - Special projects (AirBnB clone, portfolio)

## Content Relationships

### Project ⟷ Quiz Connection
Projects and quizzes are connected through:
- Matching file names (e.g., `shell-basics.js` ↔ `shell-basics-quiz.js`)
- Project IDs referenced in quiz data
- Common categorization structure
- Sequential learning progression

### Markdown ⟷ Project Data Connection
Markdown files are connected to project data through:
- File path references in project data
- Matching naming conventions
- Content extraction and transformation processes
- Shared metadata elements

## Content Management Flow

```
Content Authoring → Content Organization → Content Integration → Content Presentation
    (Markdown)     →   (Project Data)    →    (Quiz Data)     →   (Components)
```

## File Naming Patterns

### Pattern Examples

| Content Type | Example Path | Naming Convention |
|-------------|-------------|------------------|
| Markdown | `/markdown_files/0x00__Shell__basics.md` | `0x{number}__{Name__With__Double__Underscores}.md` |
| Project Data | `/src/data/projects/system-engineering-devops/shell-basics.js` | `{kebab-case}.js` |
| Quiz Data | `/src/data/project-quizzes/system-engineering-devops/shell-basics-quiz.js` | `{project-name}-quiz.js` |

## Content Status Tagging

### Status Types
- **onboarding**: General onboarding content
- **onboarding-tools**: Development tool tutorials
- **mandatory**: Required core projects
- **optional**: Optional learning materials

### Implementation
Status tags are defined in project data files and visually represented with consistent styling:
```javascript
{
  title: "Shell Basics",
  status: "mandatory",
  // other project data
}
```

## Content Cross-Referencing

### External References
- Links to ALX Learning Platform
- GitHub repository references
- Documentation links
- Additional learning resources

### Internal References
- Related projects connections
- Prerequisite relationships
- Learning path progression
- Topic groupings

## Content Update Process

1. **Markdown Update**:
   - Edit or create markdown content in `/markdown_files/`
   - Follow established formatting patterns
   - Ensure proper sectioning and hierarchy

2. **Project Data Update**:
   - Update project metadata in corresponding data file
   - Maintain relationships with markdown and quiz files
   - Verify status tag accuracy

3. **Quiz Data Update**:
   - Modify quiz questions and answers in quiz data file
   - Ensure alignment with markdown content
   - Validate question formats and answer correctness

4. **Integration Verification**:
   - Confirm connections between all content types
   - Test rendering and interactions
   - Verify navigation paths and links