# Project Content Synchronization Plan

## Identified Issues
- Incomplete task details in project files
- Missing code examples and repository information
- Inconsistent structure between markdown and JS files

## Content Sources and Mapping

### Source Files Location
1. Markdown Files: `markdown_files/` directory
   - Contains complete project documentation
   - File naming format: `0x**__Project_Name.md`
   - Example: `0x0A__C_-_argc__argv.md` maps to `argv.js`

2. Project Files: `src/data/projects/` directory
   - Organized by category (low-level, higher-level, etc.)
   - Contains JS files that need to be updated with content from markdown files

## Task Content Organization

### Information to Extract per Task
1. Task Status
   - Mandatory vs Advanced
   - Completion status and score
   - Task number and title

2. Task Requirements
   - Description and objectives
   - Input/output specifications
   - Constraints and limitations
   - Examples with expected outputs

3. Repository Details
   - GitHub repository name
   - Directory path
   - File name
   - All enclosed in escaped backticks

4. Code Examples
   - Terminal commands and outputs
   - Source code snippets
   - All properly escaped with backticks
## Files to Update

### Low Level Programming
1. argv.js (Current Focus)
2. binary-trees.js
3. bit-manipulation.js
4. debugging.js
5. doubly-linked-lists.js
6. even-more-pointers.js
7. file-io.js
8. function-pointers.js
9. functions.js
10. hash-tables.js
11. linked-lists.js
12. makefiles.js
13. malloc.js
14. more-functions.js
15. more-linked-lists.js
16. more-malloc.js
17. more-pointers.js
18. pointers.js
19. preprocessor.js
20. printf.js
21. recursion.js
22. search.js
23. simple-shell.js
24. sorting.js
25. stacks-queues.js
26. static-libraries.js
27. structures.js
28. variables.js
29. variadic.js

### Higher Level Programming

#### Python Track
1. hello-world.js
2. if-else-loops-functions.js
3. import-modules.js
4. data-structures.js
5. more-data-structures.js
6. exceptions.js
7. classes-objects.js
8. test-driven-development.js
9. more-classes.js
10. everything-is-object.js
11. inheritance.js
12. input-output.js
13. almost-circle.js
14. orm.js
15. network-0.js
16. network-1.js

#### JavaScript Track
1. warmup.js
2. objects.js
3. web-scraping.js
4. jquery.js

#### SQL Track
1. introduction.js
2. more-queries.js

#### AirBnB Clone
1. console.js
2. web-static.js
3. mysql.js
4. deploy-static.js
5. web-framework.js
6. rest-api.js
7. web-dynamic.js

### System Engineering DevOps
1. shell-basics.js
2. shell-permissions.js
3. shell-io-redirections.js
4. shell-variables.js
5. shell-loops.js
6. shell-processes.js
7. shell-regex.js
8. networking-basics-0.js
9. networking-basics-1.js
10. web-infrastructure.js
11. load-balancer.js
12. https-ssl.js
13. mysql.js
14. api.js
15. api-advanced.js
16. web-server.js
17. web-stack-debugging-0.js
18. web-stack-debugging-1.js

### More Projects
1. attack-is-best-defense.js
2. command-line-win.js
3. fix-my-code-0.js
4. fix-my-code-1.js
5. rsa-factoring.js

### Portfolio Projects
1. research-1.js
2. research-2.js
3. research-3.js
4. week-1.js
5. week-2.js
6. landing-page.js
7. presentation.js
8. cleanup.js
9. blog-post.js

### Onboarding & Tools

#### Onboarding
1. welcome-onboard.js
2. mindset.js
3. grit-assignment.js
4. mental-health.js
5. map-your-mind.js
6. owning-learning.js
7. learning-community.js
8. networking.js
9. slack-intro.js

#### Tools
1. emacs.js
2. vi.js
3. git.js
4. professional-tech.js

## Content Synchronization Process

### For Each Project:
1. Identify markdown source file
   - Match by project number (0x**) and topic
   - Example: For `argv.js`, use `markdown_files/0x0A__C_-_argc__argv.md`

2. Extract from markdown file:
   - Project title and metadata
   - Task descriptions and requirements
   - Code examples and expected outputs
   - Repository information

### Reference Structure (from hello-world.js)
1. Import statement for quiz
2. Export object with:
   - title
   - status
   - Complete content including:
     - Score information
     - Task descriptions with examples
     - Repository information
     - Status tags
   - Note: Ensure backticks are properly escaped in code examples

## Update Process
1. For each file:
   - Read corresponding markdown file
   - Compare with hello-world.js structure
   - Update JS file to include:
     - Complete task descriptions
     - Code examples (properly escaped)
     - Repository information
     - Status tags
   - Validate content synchronization
   - Cross-reference with markdown source for accuracy

## Quality Checks
1. Verify backticks are properly escaped
2. Ensure filenames and paths use backticks
3. Confirm code examples are complete
4. Validate status tags
5. Check repository information accuracy

## Validation Steps

### Content Validation
1. Compare task counts between markdown and JS files
2. Verify all examples are included
3. Check formatting of code blocks
4. Validate repository information

### Structure Validation
1. Confirm proper export object structure
2. Verify quiz import statement
3. Check status field value
4. Validate markdown formatting

### Format Consistency
1. Verify proper escaping:
   - Backticks in code blocks
   - Special characters in content
   - File paths and names

2. Check content structure:
   - Proper indentation
   - Consistent newlines
   - Markdown syntax
## Status Tracking
- [ ] Review files by category:
    - [-] Low-level programming
        - [x] hello-world.js
        - [x] bit-manipulation.js
        - [x] argv.js
        - [x] debugging.js
        - [x] functions.js
        - [x] variables.js
        - [x] binary-trees.js
        - [ ] Other low-level files...
    - [ ] Higher-level programming
    - [ ] System engineering
    - [ ] More projects
    - [ ] Portfolio projects
    - [ ] Onboarding & Tools
    - [ ] Other projects
- [ ] Document discrepancies
- [ ] Update files one by one
- [ ] Validate updates
- [ ] Test functionality
## Notes
- Maintain existing functionality while updating
- Keep consistent structure across all files
- Ensure appropriate categorization in status field
- Match content between markdown and JS files