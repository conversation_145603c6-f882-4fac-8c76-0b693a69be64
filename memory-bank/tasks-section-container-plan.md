# Tasks Section Container Implementation Plan

## Problem Statement
Current task content has formatting issues:
- Excessive spacing between sentences
- Improper indentation of numbered lists
- Inconsistent text formatting
- Code block spacing issues

## Component Purpose
Create a container specifically for task content (not headers) that:
- Controls inter-sentence spacing
- <PERSON>perly indents numbered lists
- Maintains consistent text formatting
- <PERSON>les code blocks and special content types

## Implementation Details

### 1. Component Structure
```jsx
const TaskContentContainer = ({ children }) => {
  return (
    <div className={cn(
      "task-content",
      "prose prose-stone", // Using Tailwind Typography for base text styling
      "max-w-none", // Allow container to fill width
      
      // Paragraph spacing
      "[&>p]:my-2", // Moderate spacing between paragraphs
      "[&>p+p]:mt-2", // Consistent spacing between consecutive paragraphs
      
      // List formatting
      "[&>ol]:pl-6", // List indentation
      "[&>ol>li]:my-0.5", // List item spacing
      "[&>ul]:pl-6", // Unordered list indentation
      "[&>ul>li]:my-0.5", // Unordered list item spacing
      
      // Code blocks
      "[&>pre]:my-3", // Code block vertical spacing
      "[&>pre]:p-3", // Code block padding
      "[&>pre]:bg-muted", // Code block background
      "[&>code]:px-1", // Inline code padding
      "[&>code]:bg-muted", // Inline code background
      
      // General text
      "[&_p]:leading-normal", // Line height
      "text-base" // Base font size
    )}>
      {children}
    </div>
  );
};
```

### 2. Key Styling Features
- Moderate paragraph spacing (0.5rem) to distinguish paragraphs without excessive gaps
- Consistent list indentation (1.5rem)
- Code block formatting with:
  - Distinct background color
  - Proper padding
  - Appropriate vertical spacing
- Inline code styling with background highlight

### 3. Usage Example
```jsx
<TaskContainer isTaskHeading title="Task Name">
  <TaskContentContainer>
    <p>First task instruction with normal spacing.</p>
    <p>Second paragraph with moderate spacing.</p>
    
    <ol>
      <li>Properly indented list item one</li>
      <li>Properly indented list item two</li>
    </ol>
    
    <pre><code>
    # Example code block
    def example():
        return "Properly formatted"
    </code></pre>
    
    <p>Text with <code>inline code</code> properly spaced.</p>
  </TaskContentContainer>
</TaskContainer>
```

## Technical Considerations

### Typography
- Use Tailwind Typography for consistent base styles
- Override specific spacing and indentation rules
- Maintain heading hierarchy from existing components

### Spacing Rules
- Moderate spacing between paragraphs (0.5rem)
- Consistent list indentation (1.5rem)
- Appropriate code block padding and margins
- Control inter-sentence spacing with line-height

### Special Content
- Code blocks with distinct styling
- Inline code formatting
- List indentation and spacing
- Block quote formatting if needed

## Next Steps
1. Create component file
2. Implement base styling
3. Test with various content types:
   - Regular paragraphs
   - Numbered and unordered lists
   - Code blocks
   - Inline code
4. Verify spacing and formatting
5. Document usage patterns