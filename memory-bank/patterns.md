# Content Synchronization Patterns

## Project Files Consistency

### Task Documentation Pattern
1. Core Task Information
   - Title with status indicator (mandatory/advanced)
   - Task description
   - Requirements and constraints
   - Expected behavior

2. Extended Task Information (Markdown Files)
   - Completion status and scores
   - Code examples with expected outputs
   - Repository information
   - Detailed implementation notes

3. Simplified Task Information (JS Files)
   - Basic task requirements
   - Status indicators using {{tags}}
   - Essential implementation details

### Content Synchronization Rules
- Maintain identical task descriptions between files
- Preserve task ordering and structure
- Keep consistent task status indicators
- Ensure task requirements match exactly

## Project Status Tracking

### Status Indicators
- Markdown Files: Include detailed completion metrics
- JS Files: Use status tags for quick reference
- Common Statuses:
  - mandatory: Required tasks
  - advanced: Optional/bonus tasks
  - published: Fully documented content

### Version Control
- Track content updates in both file types
- Maintain synchronization between related files
- Document any deliberate differences

## File Organization

### Directory Structure
- markdown_files/: Complete project documentation
- src/data/projects/: Project implementation files
- Maintain parallel structure between directories

### Naming Conventions
- Markdown Files: Detailed names with underscores
- JS Files: Simplified names focusing on key concepts
- Consistent naming patterns across project

## Update Procedures

1. Content Updates
   - Update both markdown and JS files
   - Maintain consistency in task descriptions
   - Preserve existing patterns

2. New Content Addition
   - Create both markdown and JS versions
   - Follow established patterns
   - Ensure cross-file consistency

3. Status Updates
   - Update completion status in markdown
   - Reflect status changes in JS files
   - Maintain synchronization

## Documentation Standards

### Markdown Files
- Include complete metadata
- Provide detailed examples
- Document scores and completion status
- Include repository information

### JS Files
- Focus on essential implementation details
- Use consistent status tags
- Maintain clean, readable format
- Include necessary imports