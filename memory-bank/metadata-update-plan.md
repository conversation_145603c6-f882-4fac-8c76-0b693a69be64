# Project Metadata Update Implementation Plan

## Phase 1: Project Assessment
1. Identify all project directories:
   - src/data/projects/0-day/
   - src/data/projects/alx-zero_day/
   - src/data/projects/higher-level/
   - src/data/projects/low-level/
   - src/data/projects/more/
   - src/data/projects/onboarding/
   - src/data/projects/portfolio/
   - src/data/projects/system-engineering-devops/
   - src/data/projects/tools/

2. Create project level map:
```
Level Categories:
- Beginner: First steps (welcome, intro)
- Novice: Basic tools (shell nav, git basics, onboarding)
- Intermediate: Development tools (emacs, vi), Low Level
- Advanced: System tools (debugging, optimization), Higher Level, System Engineering, DevOps
- Expert: Portfolio, More, Architecture/Design, ALX Zero Day
```

## Phase 2: Timeline Integration

1. Read project dates from ALL files in months/ directory (e.g., february.js, march.js, etc.)
2. Create a consolidated mapping of projects to dates, across all months
3. Update metadata dates based on consolidated schedule
   - Ensure Start and End times are always 6:00 AM

## Phase 3: File Updates

### Step 1: Backup
1. Create backup of each file before modification
2. Store in appropriate backup directory

### Step 2: Metadata Conversion
1. Parse existing metadata
2. Convert to emoji format
3. Add/update level indicator (using level map from Phase 1)
4. Verify dates from timeline (using consolidated timeline from Phase 2)
5. Maintain existing content

### Step 3: Update Process
For each project file:
1. Read file content
2. Extract metadata section
3. Convert to new format:
   ```markdown
   # [Title]
   ![Badge]

   👤 By [Author]
   📝 [Level]
   ⚖️ Weight: [number]
   📅 Start: [date] 6:00 AM
   📅 End: [date] 6:00 AM
   ⏱️ Duration: [period]
   ```
4. Preserve remaining content
5. Write updated file

## Phase 4: Validation

### Automated Checks
1. Verify all required metadata fields present
2. Check emoji format consistency
3. Validate dates against timeline
4. Verify heading levels
5. Check content preservation

### Manual Review
1. Level assignments appropriate
2. Content flow maintained
3. Formatting consistent

## Implementation Order

1. **Tools Projects**
   - Start with simpler tools projects
   - Progress to more complex ones
   - Examples: shell-navigation.js → emacs.js → vi.js

2. **Onboarding Projects**
   - Begin with welcome-onboard.js
   - Progress through orientation projects
   - Complete with professional-tech.js

3. **Low Level Projects**
4. **Higher Level Projects**
5. **System Engineering DevOps Projects**
6. **More Projects**
7. **Portfolio Projects**
8. **ALX Zero Day Projects**
9. **0-day Projects** (if any remain after cleanup)


## Rollback Plan

1. Keep backups for quick restoration
2. Document all changes
3. Test each update before proceeding
4. Maintain version control history

## Success Criteria

- All projects use emoji format
- Levels properly assigned
- Dates match timeline (from all months files)
- Start and End times are 6:00 AM consistently
- Content preserved
- Formatting consistent

## Timeline

1. Phase 1: 1 hour
2. Phase 2: 2 hours (to read and consolidate all months files)
3. Phase 3: 4-5 hours (more projects to update)
4. Phase 4: 1 hour

Total estimated time: 8-9 hours