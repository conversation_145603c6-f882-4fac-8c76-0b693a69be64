# Data Directory Cleanup Plan

## Directory Analysis

### Project Organization
- Each course exists in its proper section intentionally
- Files in onboarding-tools/ and tools/ serve different purposes
- Need to preserve both directories while removing duplicates

### Current Structure
1. Tools Directory (src/data/projects/tools/):
   - shell-navigation.js ✓ (Keep)
   - emacs.js ✓ (Keep)
   - vi.js ✓ (Keep)
   - git.js ✓ (Keep)
   - professional-tech.js ✓ (Keep)

2. Onboarding-Tools Directory:
   - emacs.js ✓ (Keep)
   - emacs-01.js (Move to backups)
   - professional-tech.js (Compare with tools version)

3. Zero-Day Directory:
   - git-1.js ✓ (Keep - This is 0x03.Git for zero-day)
   - Keep unique content for zero-day section

### New Backup Structure
```
src/data/backups/
├── onboarding-tools/
│   └── emacs-01.js
└── README.md  # Document backup contents and dates
```

## Implementation Plan

### 1. Backup Setup
1. Create backup directory:
   ```bash
   mkdir -p src/data/backups/onboarding-tools
   ```
2. Create README.md in backups directory:
   ```markdown
   # Backup Files
   This directory contains backup files from cleanup operations.
   
   ## Content
   1. onboarding-tools/emacs-01.js
      - Backup date: [DATE]
      - Original location: src/data/projects/onboarding-tools/
      - Reason: Duplicate content with emacs.js
   ```

### 2. Content Organization
1. Move duplicate files to backup:
   - Move emacs-01.js to backups/onboarding-tools/
   - Document the move in backups/README.md

2. Compare Professional Tech Content:
   - Check for unique content between versions
   - Keep both if serving different purposes
   - Document differences

### 3. Import Updates
- No changes needed to index.js
- Keep current import structure
- Document file locations

### 4. Directory Structure
1. Maintain Directories:
   ```
   src/data/projects/
   ├── tools/
   │   ├── shell-navigation.js
   │   ├── emacs.js
   │   ├── vi.js
   │   ├── git.js
   │   └── professional-tech.js
   ├── onboarding-tools/
   │   ├── emacs.js
   │   └── professional-tech.js
   └── alx-zero_day/
       └── git-1.js
   ```

### 5. Testing Steps
1. File Access:
   - [ ] All project content accessible
   - [ ] Backups properly stored
   - [ ] Original files functioning

2. Project Display:
   - [ ] All sections showing correct content
   - [ ] No broken links
   - [ ] All content preserved

3. Navigation:
   - [ ] All project links functional
   - [ ] Project details accessible
   - [ ] Backups documented

## Post-Implementation Verification

### 1. Content Integrity
- [ ] All project data preserved
- [ ] Backup files complete
- [ ] Documentation updated

### 2. Functionality
- [ ] Projects load correctly
- [ ] Navigation works
- [ ] Status tags display properly

### 3. Documentation
- [ ] Update technical documentation
- [ ] Document backup locations
- [ ] Note file relationships
- [ ] Record cleanup decisions

## Notes
- Keep both onboarding-tools/ and tools/ directories
- Maintain course separation
- Create comprehensive backup system
- Document all changes

## Implementation Order
1. Create backup directory and README
2. Move emacs-01.js to backup
3. Compare professional-tech.js versions
4. Update documentation
5. Test functionality
6. Verify backups