# ALX Course Tracker System Patterns

## Architecture Overview

### Component Structure
```mermaid
flowchart TD
    App --> ProjectPage
    App --> ProjectDetails
    ProjectPage --> ProjectList
    ProjectPage --> StatusFilters
    ProjectPage --> MonthComponents
    MonthComponents --> Month1
    MonthComponents --> Month2
    MonthComponents --> Month3
    MonthComponents --> Month4
    MonthComponents --> Month5
    MonthComponents --> Month6
    MonthComponents --> Month7
    MonthComponents --> Month8
    MonthComponents --> Month9
    MonthComponents --> Month10
    ProjectDetails --> ProjectQuiz
    ProjectDetails --> ResourceLinks
    ProjectDetails --> MarkdownContent
```

## Core Patterns

### 1. Data Management
- JSON files store project and quiz data
- Structured content organization:
  - `/src/data/projects/` - Project definitions by category
    - `/low-level/` - Low-level programming projects
    - `/onboarding/` - Onboarding projects
    - `/zero-day/` - Zero day projects
  - `/src/data/project-quizzes/` - Quiz content by category
    - `/low-level/` - Low-level programming quizzes
    - `/onboarding/` - Onboarding quizzes
    - `/tools/` - Development tools quizzes
  - `/markdown_files/` - Comprehensive course content
  - `/quiz_files/` - Structured quiz markdown files

### 2. Component Organization
- Reusable components for common UI elements
- Month-based content organization (Month1-Month10)
- Consistent component naming conventions
- Clear separation of concerns between components
- Status tag styling consistency across components

### 3. Status Management
Project status categories:
- Onboarding: bg-green-100 text-green-800
- Onboarding Tools: bg-green-100 text-green-800
- Mandatory: bg-blue-100 text-blue-800
- Optional: bg-gray-100 text-gray-800
- Low-level: bg-blue-100 text-blue-800

### 4. File Organization
```mermaid
flowchart TD
    src[src/] --> components[components/]
    components --> monthComponents[Month1-10/]
    components --> projectComponents[Project/]
    components --> quizComponents[Quiz/]
    src --> data[data/]
    data --> projects[projects/]
    projects --> lowLevel[low-level/]
    projects --> onboarding[onboarding/]
    projects --> zeroDay[zero-day/]
    data --> quizzes[project-quizzes/]
    quizzes --> quizLowLevel[low-level/]
    quizzes --> quizOnboarding[onboarding/]
    quizzes --> quizTools[tools/]
    Root --> markdown[markdown_files/]
    Root --> quiz[quiz_files/]
```

## Design Patterns

### 1. Component Patterns
- Functional components with hooks
- Prop-based configuration
- Consistent styling through Tailwind classes
- Reusable UI components
- Month-based content organization

### 2. Data Flow
- Top-down props passing
- JSON data imports
- Markdown content integration
- Quiz state management
- Resource linking system

### 3. UI Patterns
- Responsive design implementation
- Consistent color schemes
- Clear navigation structure
- Status indicator system
- Month-based content navigation

### 4. Code Organization
- Category-based project structure
- Modular component design
- Clear file naming conventions
- Consistent code formatting
- Separated content by type

## Technical Decisions

### 1. Technology Stack
- React for UI components
- Tailwind CSS for styling
- JSON for data storage
- Markdown for content
- Month-based organization

### 2. Project Structure
- Category-based directories
- Separated content types
- Modular component design
- Consistent naming conventions
- Clear content organization

### 3. Development Practices
- Responsive design first
- Component reusability
- Consistent code style
- Clear documentation
- Regular content updates

### 4. State Management
- Component-level state
- Props for data passing
- JSON for data storage
- Quiz state handling
- Content organization state
