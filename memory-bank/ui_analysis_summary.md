# Frontend UI CSS Analysis Summary

This document summarizes the purpose and key styling sections of the CSS files found in the project.

## CSS Files and Summaries:

### 1. [`src/App.css`](src/App.css:1)
**Summary:**
This file provides the core layout styles for the application.
- **Key Sections:**
    - `.App`: Defines the main container as a flex display, sets minimum height, background, text color, and handles overflow.
    - `.main-content`: Styles the primary content area, managing its growth, padding, transitions, and margin adjustments when a sidebar is open.
    - **Global Styles:** Includes a basic CSS reset (`*`), default `body` styling (font family, font smoothing, background, text color, line height, and transitions for color changes).
    - **Custom Scrollbar:** Styles for `::-webkit-scrollbar`, `::-webkit-scrollbar-track`, and `::-webkit-scrollbar-thumb` using CSS variables.
    - **Dark Mode:** Contains a `@media (prefers-color-scheme: dark)` block that adjusts `body`, `.App`, and scrollbar colors for dark mode.
    - **Responsive Adjustments:** Includes `@media` queries for `max-width: 1024px` and `max-width: 768px` to adjust padding and layout of `.main-content`, especially in relation to the sidebar.
- **Theme Support:** Uses CSS variables (e.g., `var(--background)`, `var(--text)`) for colors, indicating theme support. Explicit dark mode styles are present via `@media (prefers-color-scheme: dark)`.

### 2. [`src/index.css`](src/index.css:1)
**Summary:**
This file initializes Tailwind CSS and defines global custom styles, including comprehensive theming for light and dark modes using CSS variables.
- **Key Sections:**
    - **Tailwind CSS Import:** Imports Tailwind's base, components, and utilities.
    - **CSS Variables (Theming):**
        - Defines a `:root` layer with CSS variables for light mode (background, text, border, sidebar, card, hover states, muted text, task header, code blocks).
        - Defines a `:root.dark` layer with corresponding CSS variables for dark mode.
    - **Global Styles:** `body` styling, text visibility overrides, theme-specific backgrounds, smooth transitions.
    - **Component-Specific Styles (using theme variables):** Calendar, project cards, sidebar, borders, headings, section cards, task header.
    - **Code Block Styling:** `pre`, `pre code`, syntax highlighting tokens (`.token.*`), `.inline-code`. All styled using theme variables.
    - **Font Loading:** `@font-face` for 'Fira Code'.
    - **Theme Declarations:** `.dark { color-scheme: dark; }` and `.light { color-scheme: light; }`.
- **Theme Support:** Extensive use of CSS variables for light and dark themes, controlled by a `.dark` class on `:root`. This is the primary theming engine.

### 3. [`src/components/ComingSoon.css`](src/components/ComingSoon.css:1)
**Summary:**
This file styles a "Coming Soon" page or component.
- **Key Sections:**
    - `.coming-soon`: Main container, flexbox centering.
    - `.coming-soon .content`: Content box styling (background, padding, shadow).
    - Headings (`h1`), icon (`.construction-icon` with bounce animation), message (`.message`), and features list (`.features`, `h2`, `ul`, `li` with custom bullets).
    - **Animation:** `@keyframes bounce`.
    - **Dark Mode:** `@media (prefers-color-scheme: dark)` block adjusts background and text colors.
    - **Responsive Design:** `@media (max-width: 768px)` for smaller screen adjustments.
- **Theme Support:** Uses hardcoded colors primarily. Dark mode is supported via `@media (prefers-color-scheme: dark)` but does not use the global CSS variables from `index.css`.

### 4. [`src/components/ErrorBoundary.css`](src/components/ErrorBoundary.css:1)
**Summary:**
This file styles an error boundary component for displaying JavaScript errors.
- **Key Sections:**
    - `.error-boundary`: Main container, flexbox centering.
    - `.error-boundary .content`: Content box styling.
    - Error icon (`.error-icon` with shake animation), title (`h1`), message (`p`).
    - Error details section (`.error-details`, `.error-message` for error text, `.stack-trace` for expandable stack trace).
    - Action buttons (`.actions`, `.refresh-button`, `.home-link`).
    - **Animation:** `@keyframes shake`.
    - **Dark Mode:** `@media (prefers-color-scheme: dark)` block adjusts colors.
    - **Responsive Design:** `@media (max-width: 768px)` for smaller screen adjustments.
- **Theme Support:** Uses hardcoded colors primarily. Dark mode is supported via `@media (prefers-color-scheme: dark)` but does not use global CSS variables.

### 5. [`src/components/Month0.css`](src/components/Month0.css:1)
**Summary:**
Styles a calendar component with day, week, and month views, designed to work with the global theming system.
- **Key Sections:**
    - General Calendar (`.calendar`, `.calendar-header`).
    - Day View (`.day-view`, `.day-events`, `.no-events`).
    - Week View (`.week-view`, `.week-day`, `.week-date`, `.week-day-name`, `.week-day-number`, `.week-events`).
    - Month View (`.calendar-grid`, `.calendar-cell` with states: hover, selected, empty, today; `.cell-header`, `.day-name`, `.day-number`, `.cell-content`).
    - Events (`.event`, `.event.bg-blue`, `.event.selected`).
    - Buttons & Navigation (`.btn`, `.title`, `.nav-btn`).
    - **Responsive Styles:** `@media (max-width: 768px)` adjusts layouts for smaller screens.
- **Theme Support:** Heavily uses CSS variables from `index.css` (e.g., `var(--background)`, `var(--card-bg)`). Some specific states use hardcoded accent colors (e.g., `#3b82f6`). Relies on global variables for dark mode.

### 6. [`src/components/NotFound.css`](src/components/NotFound.css:1)
**Summary:**
Styles a "Not Found" (404 error) page.
- **Key Sections:**
    - `.not-found`: Main container, flexbox centering.
    - `.not-found .content`: Content box styling.
    - Large error code (`.error-code`), title (`h1`), message (`p`).
    - Action buttons (`.actions`, `.home-link`, `.back-button`).
    - **Dark Mode:** `@media (prefers-color-scheme: dark)` block adjusts colors.
    - **Responsive Design:** `@media (max-width: 768px)` for smaller screen adjustments.
- **Theme Support:** Uses hardcoded colors primarily. Dark mode is supported via `@media (prefers-color-scheme: dark)` but does not use global CSS variables.

### 7. [`src/components/ProjectDetails.css`](src/components/ProjectDetails.css:1)
**Summary:**
A complex file for styling the project details page, including layout, navigation, typography, forms, media, extensive code display, buttons, and footer.
- **Key Sections:**
    - Layout & Navigation (`.project-details`, `.project-nav`, `.back-link`).
    - Content Area (`.content`, `.section-container`, `.intro-section`).
    - Typography (headings `h1-h4`, paragraphs `p`, lists `ul, li`).
    - Forms (`.social-inputs`, `.input-group`).
    - Media Display (`.inline-image`, `iframe` styling).
    - **Code Display (Extensive):** `.code-block`, `.inline-code`, numerous overrides for `pre`, `code` to ensure proper wrapping and display, specific styling for JSON examples (`.format-json-example`) and syntax highlighter elements.
    - Action Buttons & Footer (`.action-buttons`, `.action-button`, `.project-footer`).
    - Project Header Info (styling first `h1` and subsequent `p` tags in `.content`).
    - Layout Refinements (increasing `max-width` of containers).
    - **Dark Mode:** Comprehensive `@media (prefers-color-scheme: dark)` block with many hardcoded dark theme colors, though some elements like `.inline-code` background use `var(--code-bg)`.
    - **Responsive Design:** `@media (max-width: 768px)` for smaller screen adjustments.
- **Theme Support:** Mixed approach. Uses some global CSS variables but also has extensive, explicit dark mode styles with hardcoded colors. High complexity due to numerous specific selectors and overrides for code display.

### 8. [`src/components/ProjectPage.css`](src/components/ProjectPage.css:1)
**Summary:**
Styles a "Projects" page listing projects, possibly with "Current" and "All Projects" categories and expandable sections.
- **Key Sections:**
    - Page Layout & Header (`.project-page`, `.page-header`).
    - "My Projects" / Placeholder (`.my-projects`).
    - "Current Projects" (`.current-projects`, `.project-grid`, `.project-card`).
    - "All Projects" (`.all-projects`, `.all-projects-header`, `.expand-collapse-buttons`).
    - Expandable Sections (`.sections`, `.project-group`, `.section`, `.section-header`, `.expand-icon`).
    - Project Cards (General) (`.project-card`, `.project-title` - hardcoded red, `.project-timeline`).
    - Metadata display (badges `.status-badge`, `.dates`, `.description`, `.tags`).
    - Page Footer (`.page-footer`).
    - **Dark Mode:** `@media (prefers-color-scheme: dark)` adjusts colors, using a mix of theme variables and specific dark theme colors for elements like badges.
    - **Responsive Design:** `@media (max-width: 1024px)` and `@media (max-width: 768px)` for layout adjustments.
- **Theme Support:** Good use of CSS variables from `index.css`. Dark mode styles complement this, with some hardcoded colors for specific elements like badges and the project title.

### 9. [`src/components/ProjectQuiz.css`](src/components/ProjectQuiz.css:1)
**Summary:**
Styles elements for a quiz, focusing on custom radio/checkbox inputs and card hover effects, using Tailwind CSS's `@apply`.
- **Key Sections:**
    - Custom Radio/Checkbox (`.quiz-input`): Styles for inputs, including `:checked` state. Uses Tailwind dark mode variants (`dark:`).
    - Card Hover Effect (`.quiz-card`): Transition and hover effect.
    - Answer Options Container (`.quiz-options`): Spacing, border, padding.
    - Quiz Option Text Color.
- **Theme Support:** Achieved via Tailwind CSS's dark mode variants (e.g., `dark:bg-gray-700`), not direct use of custom CSS variables from `index.css`.

### 10. [`src/components/Sidebar.css`](src/components/Sidebar.css:1)
**Summary:**
Styles a collapsible sidebar navigation, starting collapsed and expanding on interaction.
- **Key Sections:**
    - Sidebar Container (`.sidebar`, `.sidebar.open` for expanded state).
    - Toggle Button (`.sidebar-toggle`).
    - Header (`.sidebar-header`, `h2` text visibility toggles).
    - Navigation Area (`.sidebar-nav` with custom scrollbar).
    - Navigation Items (`.nav-item` with icons `.nav-icon` and text labels that toggle visibility). Active state uses a hardcoded blue.
    - **Responsive Adjustments:** `@media (max-width: 768px)` for smaller screens.
- **Theme Support:** Extensive use of CSS variables from `index.css`. Relies on these variables for dark mode adaptation. Active nav item uses a hardcoded blue.

### 11. [`src/components/concepts/ConceptDetail.css`](src/components/concepts/ConceptDetail.css:1)
**Summary:**
Styles a "Concept Detail" page with a main content area and a sticky table of contents. Uses a mix of Tailwind `@apply` and custom CSS with variables.
- **Key Sections:**
    - Concept Navigation (`.concept-nav`, `.concept-nav-button`) - sticky top bar.
    - Table of Contents (`.toc`, `.table-of-contents`, `.toc-item`) - sticky side element with custom scrollbar.
    - Syntax Highlighter Overrides (`.syntax-highlighter`).
    - Loading States (`.concept-skeleton`).
    - Custom Prose Modifications (`.prose` for links, images, code blocks, blockquotes).
    - Main Layout (`.concept-detail`, `.concept-header`, `.concept-navigation`, `.concept-content` - grid, `.main-content`).
    - **Responsive Adjustments:** `@media (max-width: 1024px)` and `@media (max-width: 768px)` (stacks TOC on mobile).
- **Theme Support:** Mixes Tailwind utilities (with `dark:` variants) and custom CSS using variables from `index.css`. Prose link overrides use hardcoded red but with dark mode alternatives.

## General Observations on Theme Support (Light/Dark Modes):

*   **Primary Theming Engine:** [`src/index.css`](src/index.css:1) establishes a robust theming system using CSS variables for light (`:root`) and dark (`:root.dark`) modes. This is the intended global mechanism.
*   **Good Adherence:** Files like [`src/components/Month0.css`](src/components/Month0.css:1), [`src/components/ProjectPage.css`](src/components/ProjectPage.css:1), and [`src/components/Sidebar.css`](src/components/Sidebar.css:1) make good use of these global CSS variables and thus adapt well to the theme changes.
*   **Mixed Approach / Hardcoded Values:**
    *   [`src/App.css`](src/App.css:1), [`src/components/ComingSoon.css`](src/components/ComingSoon.css:1), [`src/components/ErrorBoundary.css`](src/components/ErrorBoundary.css:1), and [`src/components/NotFound.css`](src/components/NotFound.css:1) use `@media (prefers-color-scheme: dark)` for dark mode but rely on hardcoded color values within these blocks rather than the global CSS variables. This could lead to inconsistencies if the main theme variables in `index.css` are updated.
    *   [`src/components/ProjectDetails.css`](src/components/ProjectDetails.css:1) is a mix; it uses some global variables but also has extensive hardcoded colors in its dark mode media query.
    *   Some accent colors (e.g., active sidebar item, project titles, specific event colors) are hardcoded even in files that otherwise use theme variables.
*   **Tailwind Dark Mode:** [`src/components/ProjectQuiz.css`](src/components/ProjectQuiz.css:1) and parts of [`src/components/concepts/ConceptDetail.css`](src/components/concepts/ConceptDetail.css:1) use Tailwind's `dark:` prefix variants for theming, which relies on a `dark` class on an ancestor element. This is a separate mechanism from the custom CSS variables but generally compatible.

Overall, the project has a strong foundation for theming via `index.css`, but several component-specific CSS files implement dark mode independently with hardcoded values.