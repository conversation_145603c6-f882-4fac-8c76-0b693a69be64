# Project Data Conformity Plan

## Overview
This plan outlines the process for checking and ensuring all project data files conform to established standards across the ALX Course Tracker system.

## Implementation Approach

### Phase 1: Automated Validation
1. **Validation Script Development**
   - Create a script to automatically check files against conformity requirements
   - <PERSON>ript will scan both markdown and JS files
   - Generate reports of non-conforming elements
   - Validation checks:
     ```javascript
     // Example validation checks
     {
       metadata: {
         hasTitle: boolean,
         hasBadge: boolean,
         hasEmojis: {
           author: boolean,    // 👤
           level: boolean,     // 📝
           weight: boolean,    // ⚖️
           dates: boolean,     // 📅
           duration: boolean   // ⏱️
         },
         hasProjectSummary: boolean
       },
       structure: {
         sectionsInOrder: boolean,
         hasRequiredSections: {
           resources: boolean,
           learningObjectives: boolean,
           requirements: boolean,
           quizQuestions: boolean,
           tasks: boolean
         }
       },
       technical: {
         codeBlocksFormatted: boolean,
         repoInfoPresent: boolean,
         linksWorking: boolean,
         hasActionButtons: boolean
       }
     }
     ```

2. **Batch Processing**
   - Process files by category/directory
   - Generate comprehensive reports
   - Identify common patterns of non-conformity
   - Example directory structure to process:
     ```
     src/data/projects/
     ├── low-level/         (~30 files)
     ├── higher-level/
     ├── system-engineering-devops/
     ├── tools/
     ├── alx-zero_day/
     ├── onboarding/
     ├── portfolio/
     └── more/
     ```

3. **Report Generation**
   - Summary of files checked
   - Conformity statistics
   - Detailed issues list
   - Prioritized fixes needed

### Phase 1 Completion Report
1. **Implementation Status**
   - Validation script successfully created and deployed in `/scripts/validate-project-files.js`
   - Batch processing functionality implemented with comprehensive directory scanning
   - Report generation system operational with detailed validation summaries
   - Directory structure processing complete across all project categories

2. **Initial Validation Results**
   - Total files processed: 88
   - Files with issues: 86
   - Overall success rate: 2.27%
   
   Category breakdown:
   - Low-level: 6.67% success (28/30 files with issues)
   - Higher-level: 0% success (29/29 files with issues)
   - System-engineering-devops: 0% success (24/24 files with issues)
   - More: 0% success (5/5 files with issues)

3. **Key Findings**
   - Critical conformity issues across all categories
   - Zero success rate in three out of four categories
   - Slightly better performance in Low-level programming projects
   - Common validation failures identified in:
     - Metadata formatting and completeness
     - Project summary standardization
     - Section structure consistency
     - Technical requirements compliance

### Phase 2: Manual Verification (Current Phase)
Current focus - systematic review and validation adjustment:
1. **Immediate Priority Actions**
   - Begin with Low-level category (highest current success rate)
   - Document successful files as templates
   - Create standardized fix templates for common issues
   - Implement automated fixes for repetitive problems

2. **Validation Rule Refinement**
   - Review and document all validation patterns in validate-project-files.js
   - Create test cases for each metadata format variation
   - Expand project summary pattern matching
   - Add flexibility for acceptable variations while maintaining standards

3. **Category-Specific Review Process**
   - Start with sample set from Low-level category
   - Document all variations of valid metadata blocks
   - Create category-specific templates
   - Develop automated fix scripts for common patterns

4. **Next Steps**
   - Manual review of passing files to confirm validation accuracy
   - Creation of category-specific fix templates
   - Development of automated update scripts
   - Implementation of batch fixes for common issues

### Phase 3: Systematic Updates
1. **Priority-Based Implementation**
   - Focus on zero-success categories first
   - Address common issues across multiple files
   - Target quick wins for success rate improvement
   - Schedule complex fixes strategically

2. **Template Development**
   - Create category-specific fix templates
   - Design automated update scripts
   - Build verification test suite
   - Document template usage guidelines

3. **Update Process**
   - Apply fixes in controlled batches
   - Review and validate each batch
   - Document all changes made
   - Track success rate improvements

4. **Quality Control**
   - Re-run validation after each batch
   - Compare against baseline metrics
   - Document conformity improvements
   - Adjust process based on results

## Standards Reference
### 1. Metadata Requirements
Based on project-metadata-spec.md:
- Project Header with title and category badge
- Complete metadata block with:
  - Author (👤)
  - Level (📝)
  - Weight (⚖️)
  - Start/End dates (📅)
  - Duration (⏱️)
- "in a nutshell..." sectionwith QA review scores

Example Metadata Format:
```markdown
# C - Hello, World
![C](https://img.shields.io/badge/C-Programming-blue)

👤 By ALX Staff
📝 Beginner
⚖️ Weight: 1
📅 Start: Aug 8, 2023, 6:00 AM
📅 End: Aug 9, 2023, 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell...
* **Auto QA review**: 65/70 mandatory
* **Altogether**: 92.86%
  * **Mandatory**: 92.86%
  * **Optional**: No optional tasks
```

### 2. Content Structure Requirements
Based on content-sync-plan.md:
- Complete task details
- Code examples and repository information
- Consistent structure between markdown and JS files
- Properly escaped code blocks and repository paths
- Required section order:
  1. Project header and metadata
  2. Resources section
  3. Learning Objectives
  4. Requirements
  5. "## Quiz Questions" heading (Both Qs capitalized)
  6. Tasks section

### Content Synchronization Between Files
- All content in JavaScript files (src/data/projects/**/*.js) must be sourced from their corresponding markdown files in the markdown_files directory
- File naming convention:
  - Markdown files: `markdown_files/project-name.md`
  - JavaScript files: `src/data/projects/category/project-name.js`
- Example mapping:
  ```javascript
  // src/data/projects/low-level/functions.js sources from:
  // markdown_files/0x02__C_-_Functions__nested_loops.md
  ```
- Content synchronization must maintain:
  - Exact text content
  - Formatting and structure
  - Code blocks and examples
  - All metadata and badges
  - Task descriptions and requirements

Example Content Structure:
```markdown
# Project Title
![Badge](link-to-badge)

[metadata block]

## Resources
// ...content...

## Learning Objectives
// ...content...

## Requirements
// ...content...

## Quiz Questions

## Tasks
### Task 0: First Task
// ...task content...
{{ACTION_BUTTONS}}
```

Example Task Structure:
```markdown
## Task 0: Preprocessor
Write a script that runs a C file through the preprocessor and save the result into another file.

* The C file name will be saved in the variable $CFILE
* The output should be saved in the file c

Example usage:
```bash
julien@ubuntu:~/c/0x00$ cat main.c 
#include <stdio.h>

/**
 * main - Entry point
 *
 * Return: Always 0 (Success)
 */
int main(void)
{
    return (0);
}
julien@ubuntu:~/c/0x00$ export CFILE=main.c
julien@ubuntu:~/c/0x00$ ./0-preprocessor
julien@ubuntu:~/c/0x00$ tail c
# 942 "/usr/include/stdio.h" 3 4

# 2 "main.c" 2

int main(void)
{
 return (0);
}
```

Repository Information:
```markdown
GitHub repository: alx-low_level_programming
Directory: 0x00-hello_world
File: 0-preprocessor
```

{{ACTION_BUTTONS}}

## Task 1: Compiler
Write a script that compiles a C file but does not link...
```

## Verification Process
### 1. Project File Checks
For each project file:
1. Check Metadata Conformity:
   - [ ] Project title format with properly linked badge
   - [ ] Complete metadata block with all emojis in correct order:
     ```markdown
     👤 By [Author]
     📝 [Level]
     ⚖️ Weight: [number]
     📅 Start/End dates with proper formatting
     ⏱️ Duration in consistent format
     ```
   - [ ] Project summary with standardized format:
     ```markdown
     ### In a nutshell...
     Auto QA review: [score]/[total] mandatory
     Altogether: [percentage]%
     * Mandatory: [percentage]%
     * Optional: [status or percentage]
     ```

2. Content Structure Validation:
   - [ ] Task format with consistent headers and numbering
   - [ ] Code blocks with proper language tags
   - [ ] Repository information in standard format:
     ```markdown
     **Repo:**
     * GitHub repository: [repo-name]
     * Directory: [directory-path]
     * File: [filename]
     ```
   - [ ] Action button placement after each task:
     ```markdown
     {{ACTION_BUTTONS}}
     ```

3. Technical Requirements:
   - [ ] Proper backtick escaping in code blocks
   - [ ] Valid repository paths
   - [ ] Correct file references
   - [ ] Working links and references

### 2. Directory Structure Validation
1. Project Location Check:
   - [ ] Correct category placement
   - [ ] Proper directory structure
   - [ ] No unintended duplicates

2. Content Synchronization:
   - [ ] Markdown to JS file mapping
   - [ ] Content consistency
   - [ ] Updated repository information

### 3. Data Integrity
1. Cross-Reference Validation:
   - [ ] Timeline data matches project dates
   - [ ] Repository information accuracy
   - [ ] Score and status consistency

2. Backup Verification:
   - [ ] Backup copy exists for modified files
   - [ ] Backup documentation updated
   - [ ] Changes tracked in version control

## Implementation Steps
1. Initial Setup:
   - [ ] Develop validation script
   - [ ] Test on sample files
   - [ ] Adjust validation rules
   - [ ] Create reporting format

2. Batch Processing:
   - [ ] Run validation on all files
   - [ ] Generate comprehensive report
   - [ ] Review results
   - [ ] Plan update strategy

3. Updates Implementation:
   - [ ] Create update templates
   - [ ] Apply batch fixes
   - [ ] Manual complex fixes
   - [ ] Verify changes

4. Quality Assurance:
   - [ ] Re-run validation
   - [ ] Compare results
   - [ ] Document improvements
   - [ ] Plan maintenance

## Project Categories to Check
1. Low Level Programming
2. Higher Level Programming
   - Python Track
   - JavaScript Track
   - SQL Track
3. System Engineering DevOps
4. AirBnB Clone
5. More Projects
6. Portfolio Projects
7. Onboarding & Tools

## Quiz Implementation Standards
### 1. File Structure
- Quiz files must be located in `src/data/project-quizzes/{category}/{project-name}.js`
- Project files must import their corresponding quiz from the correct relative path
- Export quiz as a named constant: `export const {projectName}Quiz`

### 2. Quiz Array Format
Each quiz must be an array of question objects with the following structure:
```javascript
{
  category: "Project Category Name",
  question: "The question text",
  options: [
    "option1",
    "option2",
    "option3"
  ],
  correctAnswer: "correct option" | ["correct1", "correct2"],
  isMulti: boolean  // Optional, include for multiple choice questions
}
```

### 3. Question Requirements
- Each question must have:
  - category matching the project category
  - clear, concise question text
  - minimum of 2 options
  - correctAnswer matching exactly one of the options
  - isMulti flag when multiple correct answers are possible

### 4. Example Quiz Implementation
```javascript
// In project-quizzes/{category}/{project}.js
export const projectQuiz = [
  {
    category: "Project Category",
    question: "Single choice question?",
    options: [
      "option1",
      "option2",
      "option3"
    ],
    correctAnswer: "option2"
  },
  {
    category: "Project Category",
    question: "Multiple choice question?",
    options: [
      "option1",
      "option2",
      "option3",
      "option4"
    ],
    correctAnswer: ["option1", "option3"],
    isMulti: true
  }
];

// In projects/{category}/{project}.js
import { projectQuiz } from "../../project-quizzes/{category}/{project}";

export const project = {
  title: "Project Title",
  status: "category",
  content: `
    // ...markdown content...
  `
};
```

## Reporting
Create summary reports for:
1. Conformity status per project
2. Required corrections list
3. Completed updates log
4. Outstanding issues

## Success Criteria
- All project files follow metadata specification
- Content structure meets requirements
- Directory organization is clean and logical
- Backups are complete and documented
- No broken references or invalid paths
- All content is properly synchronized

## Maintenance Plan
1. Regular Audits:
   - Monthly metadata checks
   - Content synchronization verification
   - Directory structure review

2. Update Process:
   - Document new changes
   - Maintain backup system
   - Keep change logs current

3. Quality Control:
   - Regular testing
   - Periodic full system review
   - User feedback incorporation