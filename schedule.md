205 Shell, basics Feb2625...Feb2725
207 Shell, permissions Mar0325...Mar0425
208 Shell, I/O Redirections and filters Mar0425...Mar0525
212 0x00. C - Hello, World Mar1025...Mar1125
100073 First Day of C Programming Mar1025...Mar1425
213 0x01. C - Variables, if, else, while Mar1325...Mar1425
214 0x02. C - Functions, nested loops Mar1725...Mar1825
539 0x03. C - Debugging Mar1825...Mar2125
215 0x04. C - More functions, more nested loops Mar2425...Mar2525
216 0x05. C - Pointers, arrays and strings Mar2425...Mar2525
217 0x06. C - More pointers, arrays and strings Mar2725...Mar2825
218 0x07. C - Even more pointers, arrays and strings Mar3125...Apr0125
219 0x08. C - Recursion Apr0225...Apr0325
220 0x09. C - Static libraries Apr0725...Apr1025
221 0x0A. C - argc, argv Apr0825...Apr1125
222 0x0B. C <PERSON> mall<PERSON>, free Apr1425...Apr1625
223 0x0C. C - More malloc, free Apr1425...Apr1525
224 0x0D. C - Preprocessor Apr1725...Apr1825
225 0x0E. C - Structures, typedef Apr2125...Apr2225
226 0x0F. C - Function pointers Apr2225...Apr2325
227 0x10. C - Variadic functions Apr2825...Apr2925
228 0x11. C - printf Apr3025...May0525
229 0x12. C - Singly linked lists May0125...May0225
230 0x13. C - More singly linked lists May0525...May0725
232 0x14. C - Bit manipulation May0725...May0825
234 0x15. C - File I/O May1225...May1325
235 0x16. C - Simple Shell May1325...May2825
240 0x17. C - Doubly linked lists May1925...May2025
242 0x18. C - Dynamic libraries May1925...May2025
249 0x19. C - Stacks, Queues - LIFO, FIFO May2325...May2625
253 0x1A. C - Hash tables May2625...May3025
248 0x1B. C - Sorting algorithms & Big O May2725...Jun0325
273 0x1C. C - Makefiles Jun0225...Jun0325
270 0x1D. C - Binary trees Jun0225...Jun0625
295 0x1E. C - Search Algorithms Jun0525...Jun0625
231 0x00. Python - Hello, World Jun0925...Jun1025
233 0x01. Python - if/else, loops, functions Jun1125...Jun1225
239 0x02. Python - import & modules Jun1625...Jun1725
241 0x03. Python - Data Structures: Lists, Tuples Jun1925...Jun2325
243 0x04. Python - More Data Structures: Set, Dictionary Jun2325...Jun2425
245 0x05. Python - Exceptions Jun2325...Jun2425
247 0x06. Python - Classes and Objects Jun2625...Jun2725
246 0x07. Python - Test-driven development Jul0125...Jul0725
250 0x08. Python - More Classes and Objects Jul0125...Jul0225
252 0x09. Python - Everything is object Jul0725...Jul0825
254 0x0A. Python - Inheritance Jul0725...Jul0825
260 0x0B. Python - Input/Output Jul1025...Jul1125
331 0x0C. Python - Almost a circle Jul1625...Jul2125
283 0x0F. Python - Object-relational mapping Jul1725...Jul2125
299 0x10. Python - Network #0 Jul2125...Jul2225
300 0x11. Python - Network #1 Jul2225...Jul2325
263 0x00. AirBnB clone - The console Jul2825...Aug0525
268 0x01. AirBnB clone - Web static Jul3025...Aug0425
289 0x02. AirBnB clone - MySQL Jul3125...Aug0725
288 0x03. AirBnB clone - Deploy static Aug0425...Aug0625
290 0x04. AirBnB clone - Web framework Aug0625...Aug1125
301 0x05. AirBnB clone - RESTful API Aug1125...Aug1225
309 0x06. AirBnB clone - Web dynamic Aug1325...Aug1825
209 0x03. Shell, init files, variables and expansions Aug1825...Aug2025
251 0x04. Loops, conditions and parsing Aug1825...Aug1925
255 0x05. Processes and signals Aug2025...Aug2125
78 0x06. Regular expression Aug2525...Aug2625
259 0x07. Networking basics #0 Aug2625...Aug2825
285 0x08. Networking basics #1 Sep0125...Sep0325
302 0x09. Web infrastructure design Sep0125...Sep0525
292 0x0A. Configuration management Sep0425...Sep0525
244 0x0B. SSH Sep0825...Sep1125
265 0x0D. Web stack debugging #0 Sep0925...Sep1125
266 0x0C. Web server Sep1525...Sep1725
275 0x0F. Load balancer Sep1525...Sep1625
271 0x0E. Web stack debugging #1 Sep1825...Sep2325
276 0x10. HTTPS SSL Sep2225...Sep2325
298 0x11. What happens when you type google.com in your browser and press Enter Sep2425...Oct0225
284 0x13. Firewall Sep2925...Sep3025
287 0x12. Web stack debugging #2 Sep3025...Oct0225
280 0x14. MySQL Oct0625...Oct0725
269 0x15. API Oct0625...Oct0725
294 0x19. Postmortem Oct1025...Oct1325
314 0x16. API advanced Oct1325...Oct1425
293 0x17. Web stack debugging #3 Oct1425...Oct1725
281 0x18. Webstack monitoring Oct1725...Oct2025
311 0x1A. Application server Oct2025...Oct2425
313 0x1B. Web stack debugging #4 Oct2325...Oct2725
262 RSA Factoring Challenge Oct2725...Nov1025
324 Command line for the win Oct2925...Nov1325
372 0x00. Fix my code Nov0325...Nov1725
264 Attack is the best defense Nov0425...Nov1925
375 0x01. Fix my code Nov0725...Nov2125
361 Research & Project approval (Part 1) Nov1025...Nov1125
571 Research & Project approval (Part 2) Nov1325...Nov2025
572 Research & Project approval (Part 3) Nov1725...Nov2425
312 Build your portfolio project (Week 1): Making Progress Nov1825...Dec1625
564 Build your portfolio project (Week 2): MVP Complete Nov2125...Dec1725
565 Build your portfolio project (Week 3): Project Landing Page Nov2425...Dec0125
573 Build your portfolio project (Week 3): Presentation Nov2725...Dec1725
567 Cleanup your Portfolio Project Dec0225...Dec1525
566 Portfolio Project Blog post Dec0325...Dec1625
