| File in `public/concept_files/` | Matches with `concept_files/` |
|---|---|
| `3-ways-to-make-a-portfolio-great.md` | `concept_135.md` |
| `ALX-SE-program_cheatsheet.md` | `concept_104541.md` |
| `ALX_SE-discord-migration.md` | `concept_100033.md` |
| `C-programming.md` | `concept_26.md` |
| `C-static_libraries.md` | `concept_61.md` |
| `CICD.md` | `concept_43.md` |
| `DNS.md` | `concept_12.md` |
| `Databases.md` | `concept_37.md` |
| `HTMLCSS.md` | `concept_2.md` |
| `Pointers-and_arrays.md` | `concept_60.md` |
| `Printf-function_brief.md` | `concept_100034.md` |
| `Python-programming.md` | `concept_550.md` |
| `README.md` | - |
| `SE-Face_off-cup.md` | `concept_106963.md` |
| `SE-Face_off-starter-pack.md` | `concept_106962.md` |
| `Shell.md` | `concept_9.md` |
| `airbnb_clone.md` | `concept_74.md` |
| `all-about-team-projects-FAQ-pairings.md` | `concept_100037.md` |
| `approaching-a-project.md` | `concept_350.md` |
| `authenticating-git.md` | `concept_100035.md` |
| `automatic_and-dynamic-allocation.md` | `concept_62.md` |
| `child-process.md` | `concept_110.md` |
| `chrome.md` | `concept_224.md` |
| `code-of-conduct-intro.md` | `concept_100010.md` |
| `code-of-conduct.md` | `concept_100011.md` |
| `code-of-conduct_enforcement.md` | `concept_100012.md` |
| `coding-your-own_shell.md` | `concept_64.md` |
| `data-structures.md` | `concept_120.md` |
| `dealing-with-data_statistically.md` | `concept_35.md` |
| `docker.md` | `concept_65.md` |
| `essential_cheat-sheet.md` | `concept_105416.md` |
| `flowcharts.md` | `concept_130.md` |
| `fresh-reset_and-install_mysql.md` | `concept_100002.md` |
| `git-github_cheatsheet.md` | `concept_57.md` |
| `group-projects.md` | `concept_111.md` |
| `javascript-in-the-browser.md` | `concept_3.md` |
| `load-balancer.md` | `concept_46.md` |
| `maze-project.md` | `concept_133.md` |
| `mentor_yellow-pages.md` | `concept_100031.md` |
| `monitoring.md` | `concept_13.md` |
| `network-basics.md` | `concept_33.md` |
| `never-forget-a-test.md` | `concept_47.md` |
| `on-call.md` | `concept_39.md` |
| `pair-programming.md` | `concept_121.md` |
| `peer-learning-day.md` | `concept_58.md` |
| `portfolio-project_deeper-overview.md` | `concept_102042.md` |
| `portfolio-project_overview.md` | `concept_137.md` |
| `python-packages.md` | `concept_66.md` |
| `regular-expression.md` | `concept_29.md` |
| `research-and_project-approval.md` | `concept_138.md` |
| `rest_api.md` | `concept_45.md` |
| `right-engineering_rightdocumenting.md` | `concept_6.md` |
| `server.md` | `concept_67.md` |
| `slack.md` | `concept_221.md` |
| `source-code-mgt.md` | `concept_22.md` |
| `struggling-with-sandbox.md` | `concept_100039.md` |
| `take-the_hot_seat.md` | `concept_106541.md` |
| `technical-writing.md` | `concept_225.md` |
| `technical_questioning.md` | `concept_100006.md` |
| `the_framework.md` | `concept_75.md` |
| `trinity_of-frontend-quality.md` | `concept_4.md` |
| `using_emacs-as-editor.md` | `concept_54.md` |
| `web-server.md` | `concept_17.md` |
| `webstack-debugging.md` | `concept_68.md` |
| `webstack-portfolio_criteria.md` | `concept_102912.md` |
| `white-boarding.md` | `concept_100000.md` |
| `your-learning_community.md` | `concept_100001.md` |
| `job-search-resources.md` | `concept_100.md` |
| `database-administration.md` | `concept_49.md` |
| `databases.md` | `concept_556.md` |
| `the-framework.md` | `concept_559.md` |
| `se-face-off-cup-round-of-11.md` | `concept_107041.md` |
| `the-alx-se-tutors-program-a-guide.md` | `concept_107371.md` |
| `se-face-off-cup-semi-finals.md` | `concept_107372.md` |
| `portfolio-project-review-introducing-automated-project-review-system.md` | `concept_107416.md` |
| `brandu-challenge-focus-draft-elevate.md` | `concept_108711.md` |
| `brandu-challenge-1st-challenge-draft-your-personal-mission-statement.md` | `concept_108713.md` |
| `brandu-challenge-2nd-challenge-revamp-your-cv-cover-letter.md` | `concept_108825.md` |
| `brandu-challenge-3rd-challenge-crafting-your-professional-story.md` | `concept_108835.md` |