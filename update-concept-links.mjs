import { readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { glob } from 'glob';

// Regex to find concept links with lookbehind/lookahead for markdown link structure
const CONCEPT_LINK_REGEX = /(?<=\]\()(https:\/\/intranet\.alxswe\.com\/concepts\/(\d+))(?=\))/g;
const TARGET_DIR = 'src/data/projects';

// Helper function to process a single file
async function processFile(filePath) {
  try {
    // Read the file content
    const content = await readFile(filePath, 'utf8');
    
    // Apply the replacement
    const modifiedContent = content.replace(
      CONCEPT_LINK_REGEX,
      (_, fullUrl, conceptId) => `/concepts/${conceptId}`
    );
    
    // Check if any changes were made
    if (content !== modifiedContent) {
      // Write the modified content back to the file
      await writeFile(filePath, modifiedContent, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  Skipped: ${filePath} (no changes needed)`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main function to process all files
async function main() {
  try {
    console.log('🔍 Starting concept link update process...');
    
    // Find all .js files in the target directory
    const files = await glob(`${TARGET_DIR}/**/*.js`);
    
    if (files.length === 0) {
      console.log('⚠️  No JavaScript files found in the target directory');
      return;
    }
    
    console.log(`📁 Found ${files.length} JavaScript files to process`);
    
    // Process all files and collect results
    const results = await Promise.all(files.map(processFile));
    
    // Calculate statistics
    const updatedCount = results.filter(Boolean).length;
    
    console.log('\n📊 Summary:');
    console.log(`Total files processed: ${files.length}`);
    console.log(`Files updated: ${updatedCount}`);
    console.log(`Files skipped: ${files.length - updatedCount}`);
    console.log('✨ Concept link update process completed');
    
  } catch (error) {
    console.error('❌ Fatal error:', error.message);
    process.exit(1);
  }
}

// Execute the script
main().catch(error => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});