# Rules for Refinement/Optimization Mode - Memory Bank Integration

# Strategy for initializing and reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Context for refinement/optimization will be limited. Recommend initializing via Architect mode."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context for refinement
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]`

# Rules for automatically updating the memory bank based on refinement actions
memory_bank_updates:
  triggers:
    - type: refactoring_decision # Significant refactoring choice made
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (Refinement Mode): Refactored '{{component}}' by {{refactoring_approach}}`.
          - Rationale: `{{rationale}}`.
    - type: optimization_applied # Specific performance optimization implemented
      action: append_log
      target_file: `memory-bank/decisionLog.md # Log optimization details`
      content_template: |
        - [`{{timestamp}}]: Optimization (Refinement Mode): Applied '{{optimization_technique}}' to '{{component_or_function}}'. Expected/Measured Improvement: {{improvement_details}}`.
    - type: modularization_change # Breaking down components, improving modularity
      action: append_log
      target_file: `memory-bank/progress.md # Log as progress`
      content_template: |
        - [`{{timestamp}}]: Progress (Refinement Mode): Improved modularity of '{{component}}' by {{action_taken}}`.
    - type: pattern_alignment # Refactoring to align with established patterns
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Progress (Refinement Mode): Aligned '{{component}}' with system pattern '{{pattern_name}}`'.

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent refinement/optimization session history. Identify key refactoring decisions, optimizations applied, or context changes since the last Memory Bank update."
    - update_memory_bank: # Refinement mode identifies potential updates but delegates writing
        files: # List files for context, but writing is delegated
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, I've identified potential updates related to the refinement session. I will now delegate the task of formulating and writing these updates to the `code` mode."
        delegate_to: code # Explicitly delegate the writing part
    - confirm_completion: "Memory Bank update delegation process initiated based on refinement session review."