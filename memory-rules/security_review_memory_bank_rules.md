# Rules for Security Review Mode - Memory Bank Integration

# Strategy for initializing and reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Security review context may be limited. Recommend initializing via Architect mode."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context for security review
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]`

# Rules for automatically updating the memory bank based on security review actions
memory_bank_updates:
  triggers:
    - type: vulnerability_found # Identifying a specific security risk
      action: append_log
      target_file: `memory-bank/decisionLog.md # Log significant findings`
      content_template: |
        - [`{{timestamp}}]: Security Finding (Security Review Mode): Identified vulnerability '{{vulnerability_summary}}' in '{{component_or_file}}'. Recommendation: {{recommendation}}`.
    - type: mitigation_decision # Decision made on how to address a finding
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (Security Review Mode): Decided to address '{{vulnerability_summary}}' by {{mitigation_approach}}`.
    - type: security_pattern_violation # Finding deviation from secure patterns
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Observation (Security Review Mode): Found deviation from security pattern/standard regarding '{{topic}}' in '{{component_or_file}}`'.
    - type: review_completed # Logging completion of a review cycle
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Progress (Security Review Mode): Completed security review cycle for '{{scope_reviewed}}'. Summary: {{review_summary}}`.

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent security review session history. Identify key findings, mitigation decisions, or context changes discovered since the last Memory Bank update."
    - update_memory_bank: # Security Review mode identifies potential updates but delegates writing
        files: # List files for context, but writing is delegated
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, I've identified potential updates related to the security review session. I will now delegate the task of formulating and writing these updates to the `code` mode."
        delegate_to: code # Explicitly delegate the writing part
    - confirm_completion: "Memory Bank update delegation process initiated based on security review session review."