# Rules for Integration Mode - Memory Bank Integration

# Strategy for initializing and reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Context for integration will be limited. Recommend initializing via Architect mode."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context for integration
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]`

# Rules for automatically updating the memory bank based on integration actions
memory_bank_updates:
  triggers:
    - type: integration_completed # After successfully integrating components/features
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Progress (Integration Mode): Successfully integrated '{{component_A}}' and '{{component_B}}`'.
    - type: integration_issue_found # If an issue is found during integration
      action: append_log
      target_file: `memory-bank/activeContext.md # Log as an active issue/blocker`
      content_template: |
        - [`{{timestamp}}]: Blocker (Integration Mode): Found issue integrating '{{component_A}}' and '{{component_B}}'. Details: {{issue_details}}`. Needs debugging/review.
    - type: integration_decision # If a decision is made to resolve an integration conflict
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (Integration Mode): Resolved integration conflict between '{{component_A}}' and '{{component_B}}' by {{resolution_approach}}`.

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent integration session history. Identify key integration steps completed, issues found, decisions made, or context changes since the last Memory Bank update."
    - update_memory_bank: # Integration mode identifies potential updates but delegates writing
        files: # List files for context, but writing is delegated
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, I've identified potential updates related to the integration session. I will now delegate the task of formulating and writing these updates to the `code` mode."
        delegate_to: code # Explicitly delegate the writing part
    - confirm_completion: "Memory Bank update delegation process initiated based on integration session review."