# Rules for MCP Integration Mode - Memory Bank Integration

# Strategy for reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Recommend switching to Architect mode to initialize. Proceeding without Memory Bank integration."
    set_status: `[MEMORY BANK: INACTIVE]`
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]`

# Rules for automatically updating the memory bank based on actions
memory_bank_updates:
  triggers:
    - type: decision_made # e.g., Selecting MCP server, choosing auth method, defining data mapping
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (MCP Mode): {{decision_summary}}`
          - Rationale: `{{rationale}}`
          - Service/Tool: `{{mcp_service_or_tool}}`
    - type: context_change # e.g., Change in external API requirements, new integration scope
      action: append_log # Or modify relevant section
      target_file: `memory-bank/productContext.md`
      content_template: |
        ## Update: `{{timestamp}}` (MCP Mode)
        Integration Context Change: `{{context_summary}}`
    - type: pattern_defined # e.g., Establishing a standard retry mechanism for a specific API, defining a data transformation pattern
      action: append_log
      target_file: `memory-bank/systemPatterns.md`
      content_template: |
        ## `{{pattern_name}} - Defined {{timestamp}}` (MCP Mode)
        Integration Pattern: `{{pattern_description}}`
        *   Rationale: `{{rationale}}`
        *   Applies to: `{{mcp_service_or_tool}}`
    - type: integration_milestone # e.g., Connection established, Authentication successful, Data sync complete/failed
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Milestone (MCP Mode): {{milestone_summary}}`
          - Status: `{{status}}`
          - Service/Tool: `{{mcp_service_or_tool}}`
    - type: active_context_update # e.g., Currently configuring service X, troubleshooting connection Y
      action: overwrite # Overwrite active context with current focus
      target_file: `memory-bank/activeContext.md`
      content_template: |
        # Active Context (`{{timestamp}}`, MCP Mode)
        *   Current Focus: `{{current_focus_summary}}`
        *   Service/Tool: `{{mcp_service_or_tool}}`
        *   Next Step: `{{next_step}}`

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history and identify key MCP integration decisions, context changes, patterns, or progress updates since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files reflecting MCP integration activities. Delegate the writing of these updates via `new_task` to the `code` mode." # Delegate writing
    - confirm_completion: "Memory Bank update process initiated based on session review (MCP Mode)."