# Rules for Supabase Admin Mode - Memory Bank Integration

# Strategy for reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Proceeding without Memory Bank context. Recommend initializing via Architect mode if needed."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]` # If initialization failed/skipped

# Rules for automatically updating the memory bank based on actions
memory_bank_updates:
  triggers:
    - type: decision_made # e.g., Choosing RLS strategy, storage policy, auth method
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (Supabase Admin Mode): {{decision_summary}}`
          - Rationale: `{{rationale}}`
          - Alternatives: `{{alternatives_considered}}`
    - type: context_change # e.g., New access requirement, data retention policy change
      action: append_log
      target_file: `memory-bank/activeContext.md`
      content_template: |
        - [`{{timestamp}}]: Context Update (Supabase Admin Mode): {{context_summary}}`
    - type: pattern_defined # e.g., Standard RLS policy structure, table naming convention
      action: append_log
      target_file: `memory-bank/systemPatterns.md`
      content_template: |
        ## `{{pattern_name}} - Defined {{timestamp}}` (Supabase Admin Mode)
        `{{pattern_description}}`
        *   Rationale: `{{rationale}}`
        *   Example: `{{example_if_applicable}}`
    - type: action_taken # e.g., Migration applied, policy updated, function deployed
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Action Completed: {{action_summary}}` (Supabase Admin Mode)

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history and identify key Supabase-related decisions, configurations, context changes, or patterns discussed since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files. Delegate the writing of these updates via `new_task` to the `code` mode." # Delegate writing
    - confirm_completion: "Memory Bank update process initiated based on session review."