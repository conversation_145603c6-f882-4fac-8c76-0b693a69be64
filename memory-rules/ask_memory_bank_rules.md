# Rules for Ask Mode - Memory Bank Integration

# Strategy for initializing and reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Context may be limited. Recommend initializing via Architect mode."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]`

# Rules for updating the memory bank (Ask mode suggests, doesn't write)
memory_bank_updates:
  triggers:
    - type: clarification_reveals_update_need # e.g., User clarifies something that contradicts or adds to logged context
      action: suggest_update
      suggestion_template: |
        Based on our discussion, it seems the Memory Bank might need an update regarding '`{{topic}}'. Would you like me to delegate a task to Architect or Code mode to add this information to '{{target_file}}`'?
  # Ask mode does not directly trigger file writes based on its actions.

# Handling the manual "Update Memory Bank" command (Ask mode can trigger review but delegates writing)
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true
  actions:
    - halt_current_task: true # Stop answering the current question
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history. Identify any significant clarifications, decisions, or context changes discussed that might warrant a Memory Bank update."
    - update_memory_bank: # Ask mode identifies potential updates but delegates writing
        files: # List files for context, but writing is delegated
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, I've identified potential updates. I will now delegate the task of formulating and writing these updates to the `code` mode."
        delegate_to: code # Explicitly delegate the writing part
    - confirm_completion: "Memory Bank update delegation process initiated based on session review."