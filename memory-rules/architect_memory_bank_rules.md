# Rules for Architect Mode - Memory Bank Integration

# Strategy for initializing and reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Initializing..."
    # Optional: Read project brief if it exists and incorporate into productContext
    # read_optional_file: projectBrief.md
    create_directory: memory-bank/
    create_files: # Create core files with initial content
      - path: memory-bank/productContext.md
        content: |
          # Product Context
          *(Awaiting initial population based on project understanding or projectBrief.md)*
      - path: memory-bank/decisionLog.md
        content: |
          # Decision Log
          *(Log key architectural and technical decisions here)*
      - path: memory-bank/progress.md
        content: |
          # Progress Log
          *(Track project phase completion and key milestones)*
      - path: memory-bank/systemPatterns.md
        content: |
          # System Patterns
          *(Document recurring design patterns, standards, and conventions)*
      - path: memory-bank/activeContext.md
        content: |
          # Active Context (Initialization)
          *   Memory Bank Initialized by Architect Mode.
          *   Next Step: Populate Product Context based on requirements.
    set_status: `[MEMORY BANK: ACTIVE]`
    inform_user_completion: "Memory Bank initialized successfully with core files."
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]` # Should only happen if initialization fails unexpectedly

# Rules for automatically updating the memory bank based on actions
memory_bank_updates:
  triggers:
    - type: decision_made # e.g., Choosing architecture pattern, defining API, selecting core tech
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (Architect Mode): {{decision_summary}}`
          - Rationale: `{{rationale}}`
          - Alternatives: `{{alternatives_considered}}`
    - type: context_change # e.g., Scope change, major requirement clarification
      action: append_log # Or modify relevant section
      target_file: `memory-bank/productContext.md # Often affects product context`
      content_template: |
        ## Update: `{{timestamp}}` (Architect Mode)
        `{{context_summary}}`
    - type: pattern_defined # e.g., Establishing a new coding standard or architectural pattern
      action: append_log
      target_file: `memory-bank/systemPatterns.md`
      content_template: |
        ## `{{pattern_name}} - Defined {{timestamp}}` (Architect Mode)
        `{{pattern_description}}`
        *   Rationale: `{{rationale}}`
        *   Example: `{{example_if_applicable}}`
    - type: phase_milestone # e.g., Architecture design complete
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Milestone: {{milestone_summary}}` (Architect Mode)

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history and identify key architectural decisions, context changes, or patterns discussed since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files. Delegate the writing of these updates via `new_task` to the `code` mode." # Delegate writing
    - confirm_completion: "Memory Bank update process initiated based on session review."