# Rules for Tutorial Mode - Memory Bank Integration

# Strategy for reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Tutorial mode primarily reads existing context. Proceeding without Memory Bank."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context for tutorial examples/reference
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]` # If initialization failed/skipped or no bank exists

# Rules for automatically updating the memory bank based on actions
# Tutorial mode primarily reads; updates are minimal and focus on learning context.
memory_bank_updates:
  triggers:
    - type: context_change # e.g., User completes a tutorial section, asks a clarifying question relevant to project context
      action: append_log
      target_file: `memory-bank/activeContext.md`
      content_template: |
        - [`{{timestamp}}]: Context Update (Tutorial Mode): {{context_summary}}` # Log learning progress or context shifts

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history (Tutorial Mode) and identify key learning points, conceptual clarifications, or context changes relevant to the project since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files reflecting learning progress or clarifications. Delegate the writing of these updates via `new_task` to the `code` mode." # Delegate writing
    - confirm_completion: "Memory Bank update process initiated based on session review (Tutorial Mode)."