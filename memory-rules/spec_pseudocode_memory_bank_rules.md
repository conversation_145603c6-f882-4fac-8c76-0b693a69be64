# Rules for Spec-Pseudocode Mode - Memory Bank Integration

# Strategy for reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Proceeding without Memory Bank context. Recommend initializing via Architect mode if needed."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]` # If initialization failed/skipped

# Rules for automatically updating the memory bank based on actions
memory_bank_updates:
  triggers:
    - type: requirement_clarified # e.g., Clarifying a functional requirement or acceptance criteria
      action: append_log # Or modify relevant section
      target_file: `memory-bank/productContext.md`
      content_template: |
        ## Update: `{{timestamp}}` (Spec-Pseudocode Mode) - Requirement Clarification
        **Requirement:** `{{requirement_summary}}`
        **Clarification:** `{{clarification_details}}`
    - type: constraint_identified # e.g., Identifying a technical or business constraint
      action: append_log
      target_file: `memory-bank/decisionLog.md` # Constraints often influence decisions
      content_template: |
        - [`{{timestamp}}]: Constraint Identified (Spec-Pseudocode Mode): `{{constraint_summary}}`
          - Details: `{{constraint_details}}`
          - Impact: `{{potential_impact}}`
    - type: domain_model_decision # e.g., Defining a core entity or relationship
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Domain Model Decision (Spec-Pseudocode Mode): `{{decision_summary}}`
          - Rationale: `{{rationale}}`
          - Alternatives: `{{alternatives_considered}}`
    - type: pseudocode_structure_decision # e.g., Deciding on module structure or core algorithm logic
      action: append_log
      target_file: `memory-bank/decisionLog.md` # Log significant structural choices
      content_template: |
        - [`{{timestamp}}]: Pseudocode Structure Decision (Spec-Pseudocode Mode): `{{decision_summary}}`
          - Rationale: `{{rationale}}`
    - type: tdd_anchor_pattern # e.g., Establishing a standard way to anchor tests for a specific scenario
      action: append_log
      target_file: `memory-bank/systemPatterns.md`
      content_template: |
        ## TDD Anchor Pattern: `{{pattern_name}} - Defined {{timestamp}}` (Spec-Pseudocode Mode)
        `{{pattern_description}}`
        *   Example Anchor: `// TEST: {{example_anchor}}`
        *   Rationale: `{{rationale}}`
    - type: phase_milestone # e.g., Requirements capture complete, Pseudocode design complete
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Milestone: {{milestone_summary}}` (Spec-Pseudocode Mode)

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history and identify key requirement clarifications, constraints, domain decisions, pseudocode structures, or TDD patterns discussed since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files. Delegate the writing of these updates via `new_task` to the `code` mode." # Delegate writing
    - confirm_completion: "Memory Bank update process initiated based on session review."