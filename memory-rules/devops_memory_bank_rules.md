# Rules for DevOps Mode - Memory Bank Integration

# Strategy for reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Proceeding without Memory Bank context. Recommend initializing via Architect mode if needed."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]` # If initialization failed/skipped

# Rules for automatically updating the memory bank based on actions
memory_bank_updates:
  triggers:
    - type: decision_made # e.g., Choosing IaC tool/module, pipeline structure, deployment strategy, monitoring tool
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (DevOps Mode): {{decision_summary}}`
          - Rationale: `{{rationale}}`
          - Alternatives: `{{alternatives_considered}}`
          - Impact: `{{impact_on_infra_pipeline}}`
    - type: context_change # e.g., New environment requirement, security policy update, target platform change
      action: append_log
      target_file: `memory-bank/activeContext.md`
      content_template: |
        - [`{{timestamp}}]: Context Update (DevOps Mode): {{context_summary}}`
    - type: pattern_defined # e.g., Standardized pipeline stage, reusable IaC module, common monitoring alert
      action: append_log
      target_file: `memory-bank/systemPatterns.md`
      content_template: |
        ## `{{pattern_name}} - Defined {{timestamp}}` (DevOps Mode)
        `{{pattern_description}}`
        *   Type: `{{IaC/Pipeline/Monitoring/Security}}`
        *   Rationale: `{{rationale}}`
        *   Usage: `{{usage_guidelines}}`
    - type: phase_milestone # e.g., Infrastructure provisioned, CI/CD pipeline live, Monitoring configured
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Milestone: {{milestone_summary}}` (DevOps Mode)
          - Details: `{{relevant_details_like_env_version}}`

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history and identify key DevOps decisions (infra, pipeline, deployment, monitoring), context changes, or patterns discussed since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md` # Less likely to be updated directly by DevOps, but included for completeness
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files, focusing on DevOps aspects. Delegate the writing of these updates via `new_task` to the `code` mode." # Delegate writing
    - confirm_completion: "Memory Bank update process initiated based on session review."