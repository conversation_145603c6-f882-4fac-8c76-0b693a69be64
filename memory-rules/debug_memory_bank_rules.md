# Rules for Debug Mode - Memory Bank Integration

# Strategy for initializing and reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Context for debugging may be limited. Recommend initializing via Architect mode."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context for debugging
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]`

# Rules for automatically updating the memory bank based on debugging actions
memory_bank_updates:
  triggers:
    - type: root_cause_identified # Finding the core reason for a bug
      action: append_log
      target_file: `memory-bank/decisionLog.md # Log the finding as a decision/analysis point`
      content_template: |
        - [`{{timestamp}}]: Analysis (Debug Mode): Identified root cause for '{{bug_summary}}'. Cause: {{root_cause_details}}`.
    - type: fix_implemented_or_planned # When a fix is applied or a clear plan is made
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Progress (Debug Mode): {{fix_status_summary}} for bug '{{bug_summary}}`'.
    - type: context_change # e.g., Understanding system state related to the bug, identifying related issues
      action: append_log
      target_file: `memory-bank/activeContext.md`
      content_template: |
        - [`{{timestamp}}]: Context Update (Debug Mode): {{context_summary_related_to_bug}}`.
    - type: pattern_violation_found # If debugging reveals a deviation from established patterns
      action: append_log
      target_file: `memory-bank/decisionLog.md # Log this as it might require architectural review`
      content_template: |
        - [`{{timestamp}}]: Observation (Debug Mode): Found deviation from pattern '{{pattern_name}}' while debugging '{{bug_summary}}'. Details: {{deviation_details}}`.

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent debugging session history. Identify key findings (root causes), fixes applied/planned, or context changes discovered since the last Memory Bank update."
    - update_memory_bank: # Debug mode identifies potential updates but delegates writing
        files: # List files for context, but writing is delegated
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, I've identified potential updates related to the debugging session. I will now delegate the task of formulating and writing these updates to the `code` mode."
        delegate_to: code # Explicitly delegate the writing part
    - confirm_completion: "Memory Bank update delegation process initiated based on debugging session review."