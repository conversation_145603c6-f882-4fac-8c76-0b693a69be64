# Rules for TDD Mode - Memory Bank Integration

# Strategy for initializing and reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Context for TDD may be limited. Recommend initializing via Architect mode."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context for TDD
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]`

# Rules for automatically updating the memory bank based on TDD actions
memory_bank_updates:
  triggers:
    - type: test_written # After writing a new failing test
      action: append_log
      target_file: `memory-bank/activeContext.md # Note the current focus`
      content_template: |
        - [`{{timestamp}}]: TDD Cycle (TDD Mode): Wrote failing test for '{{feature_or_unit}}`'.
    - type: test_passed # After implementing code to make the test pass
      action: append_log
      target_file: `memory-bank/activeContext.md`
      content_template: |
        - [`{{timestamp}}]: TDD Cycle (TDD Mode): Implemented code for '{{feature_or_unit}}`'; test passing.
    - type: refactoring_complete # After refactoring code while tests remain green
      action: append_log
      target_file: `memory-bank/progress.md # Log refactoring as progress`
      content_template: |
        - [`{{timestamp}}]: Progress (TDD Mode): Refactored code related to '{{feature_or_unit}}`'.
    - type: significant_refactoring_decision # If refactoring involves a notable choice
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (TDD Mode): Refactored '{{component}}' using '{{pattern_or_approach}}`'.
          - Rationale: `{{rationale}}`.
    - type: tdd_cycle_complete # Optionally log completion of a full Red-Green-Refactor cycle
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Progress (TDD Mode): Completed TDD cycle for '{{feature_or_unit}}`'.

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent TDD session history. Identify key tests written, implementation steps, refactoring decisions, or context changes discovered since the last Memory Bank update."
    - update_memory_bank: # TDD mode identifies potential updates but delegates writing
        files: # List files for context, but writing is delegated
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, I've identified potential updates related to the TDD session. I will now delegate the task of formulating and writing these updates to the `code` mode."
        delegate_to: code # Explicitly delegate the writing part
    - confirm_completion: "Memory Bank update delegation process initiated based on TDD session review."