# Rules for Code Mode - Memory Bank Integration

# Strategy for initializing and reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Recommend initializing via Architect mode."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank if user chooses
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]` # Only if initialization failed/skipped

# Rules for automatically updating the memory bank based on actions
memory_bank_updates:
  triggers:
    - type: decision_made # e.g., Choosing a specific library, implementation approach
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - `[{{timestamp}}]`: Decision (Code Mode): `{{decision_summary}}`
          - Rationale: `{{rationale}}`
          - Alternatives: `{{alternatives_considered}}`
    - type: task_progress # e.g., Feature implemented, bug fixed, refactoring step done
      action: append_log # Or potentially modify existing entries
      target_file: `memory-bank/progress.md`
      content_template: |
        - `[{{timestamp}}]`: Completed: `{{task_summary}}` (Code Mode)
    - type: context_change # e.g., New requirement clarified, blocker identified/resolved
      action: append_log
      target_file: `memory-bank/activeContext.md`
      content_template: |
        - `[{{timestamp}}]`: Context Update (Code Mode): `{{context_summary}}`
    - type: pattern_identified # e.g., New reusable pattern created or existing one applied
      action: append_log
      target_file: `memory-bank/systemPatterns.md`
      content_template: |
        - `[{{timestamp}}]`: Pattern Note (Code Mode): `{{pattern_summary}}`

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history and identify key decisions, progress updates, context changes, or patterns discussed since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md`
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files. Delegate the writing of these updates via `new_task` to the `code` mode."
    - confirm_completion: "Memory Bank update process initiated based on session review."