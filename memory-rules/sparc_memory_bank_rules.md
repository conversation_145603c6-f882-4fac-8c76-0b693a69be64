# Rules for SPARC Orchestrator Mode - Memory Bank Integration

# Strategy for reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Proceeding without Memory Bank context. Recommend initializing via Architect mode if needed."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]` # If initialization failed/skipped

# Rules for automatically updating the memory bank based on actions
memory_bank_updates:
  triggers:
    - type: task_delegated # e.g., Using new_task to assign work to another mode
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Task Delegated (SPARC Mode): Delegated `{{task_summary}}` to `{{target_mode}}` mode.
    - type: task_completion_confirmed # e.g., Receiving confirmation that a delegated task is done
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Task Completed (SPARC Mode): Received confirmation for `{{task_summary}}` from `{{source_mode}}` mode.
    - type: mode_switch_decision # e.g., Deciding to switch to a specific mode for the next step
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (SPARC Mode): Switching to `{{target_mode}}` mode.
          - Rationale: `{{rationale}}`
    - type: workflow_status_change # e.g., Moving from Specification to Pseudocode phase
      action: append_log
      target_file: `memory-bank/activeContext.md`
      content_template: |
        - [`{{timestamp}}]: Workflow Update (SPARC Mode): `{{status_summary}}`. Current Phase: `{{current_phase}}`. Next Step: `{{next_step}}`.

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history and identify key orchestration decisions, task delegations, completions, or workflow changes since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md` # Less likely to be updated directly by SPARC, but included for completeness
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files. Delegate the writing of these updates via `new_task` to the `code` mode." # Delegate writing
    - confirm_completion: "Memory Bank update process initiated based on session review."