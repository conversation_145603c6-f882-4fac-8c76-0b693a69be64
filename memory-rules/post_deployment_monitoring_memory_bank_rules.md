# Rules for Post-Deployment Monitoring Mode - Memory Bank Integration

# Strategy for reading the memory bank upon mode activation
memory_bank_strategy:
  check_exists: `memory-bank/` # Directory to check
  if_no_memory_bank:
    inform_user: "Memory Bank not found. Proceeding without Memory Bank context. Recommend initializing via Architect mode if needed."
    set_status: `[MEMORY BANK: INACTIVE]`
    proceed: true # Allow proceeding without memory bank
  if_memory_bank_exists:
    read_files: # Files to read into context
      - `memory-bank/productContext.md`
      - `memory-bank/decisionLog.md`
      - `memory-bank/progress.md`
      - `memory-bank/systemPatterns.md`
      - `memory-bank/activeContext.md`
    set_status: `[MEMORY BANK: ACTIVE]`

# General rules applied to all responses from this mode
general:
  response_prefix: # Ensure status is always clear
    - `[MEMORY BANK: ACTIVE]`
    - `[MEMORY BANK: INACTIVE]` # If initialization failed/skipped

# Rules for automatically updating the memory bank based on actions
memory_bank_updates:
  triggers:
    - type: monitoring_setup_decision # e.g., Choosing monitoring tool, setting alert threshold, defining SLI/SLO
      action: append_log
      target_file: `memory-bank/decisionLog.md`
      content_template: |
        - [`{{timestamp}}]: Decision (Monitor Mode): {{decision_summary}}`
          - Details: `{{details}}`
          - Rationale: `{{rationale}}`
    - type: performance_anomaly # e.g., Significant increase in latency, high error rate detected
      action: append_log
      target_file: `memory-bank/activeContext.md` # Log anomalies as context updates
      content_template: |
        - [`{{timestamp}}]: Performance Anomaly Detected (Monitor Mode): {{anomaly_summary}}`
          - Metric: `{{metric}}`
          - Value: `{{value}}`
          - Threshold: `{{threshold}}`
          - Impact: `{{impact}}`
    - type: log_event_significant # e.g., Critical error spike, unusual security log pattern
      action: append_log
      target_file: `memory-bank/activeContext.md`
      content_template: |
        - [`{{timestamp}}]: Significant Log Event (Monitor Mode): {{event_summary}}`
          - Log Source: `{{log_source}}`
          - Details: `{{details}}`
    - type: user_feedback_summary # e.g., Consolidated report of user issues related to performance/stability
      action: append_log
      target_file: `memory-bank/productContext.md` # User feedback informs product context
      content_template: |
        ## User Feedback Summary: `{{timestamp}}` (Monitor Mode)
        `{{feedback_summary}}`
        *   Source: `{{source}}`
    - type: monitoring_milestone # e.g., Monitoring setup complete, incident resolved
      action: append_log
      target_file: `memory-bank/progress.md`
      content_template: |
        - [`{{timestamp}}]: Milestone: {{milestone_summary}}` (Monitor Mode)

# Handling the manual "Update Memory Bank" command
umb:
  trigger: # User prompt must exactly match one of these
    - "UMB"
    - "update memory bank"
  override_permissions: true # Allows UMB to potentially bypass normal task flow
  actions:
    - halt_current_task: true
    - review_context: # Instructs mode to review session history
        prompt: "Review the recent conversation history and identify key monitoring decisions, observed anomalies, significant log events, or user feedback summaries since the last Memory Bank update."
    - update_memory_bank: # Instructs mode to update relevant files
        files:
          - `memory-bank/productContext.md`
          - `memory-bank/decisionLog.md`
          - `memory-bank/progress.md`
          - `memory-bank/systemPatterns.md` # Less likely to be updated here, but included for consistency
          - `memory-bank/activeContext.md`
        prompt: "Based on the review, formulate concise, timestamped updates for the relevant Memory Bank files. Delegate the writing of these updates via `new_task` to the `code` mode." # Delegate writing
    - confirm_completion: "Memory Bank update process initiated based on session review."