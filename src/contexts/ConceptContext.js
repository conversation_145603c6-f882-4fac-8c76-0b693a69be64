import React, { createContext, useContext, useState, useEffect } from 'react';
import { processConceptFiles } from '../utils/conceptProcessor';

const ConceptContext = createContext(null);

export const useConceptContext = () => {
  const context = useContext(ConceptContext);
  if (!context) {
    throw new Error('useConceptContext must be used within a ConceptProvider');
  }
  return context;
};

export const ConceptProvider = ({ children }) => {
  const [categories, setCategories] = useState([]);
  const [concepts, setConcepts] = useState({});
  const [currentConcept, setCurrentConcept] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadConcepts = async () => {
      try {
        setIsLoading(true);
        const { categories: processedCategories, concepts: processedConcepts } = 
          await processConceptFiles();
        
        setCategories(processedCategories);
        setConcepts(processedConcepts);
      } catch (err) {
        setError(err.message);
        console.error('Error loading concepts:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadConcepts();
  }, []);

  const getConcept = (conceptId) => {
    const concept = concepts[conceptId] || null;
    if (concept) {
      const category = categories.find(cat => cat.concepts.some(c => c.id === conceptId));
      concept.category = category ? category.name : 'Concepts';
    }
    return concept;
  };

  const getConceptsByCategory = (categoryId) => {
    return categories.find(cat => cat.id === categoryId)?.concepts || [];
  };

  const searchConcepts = (query) => {
    if (!query) return [];
    
    const lowercaseQuery = query.toLowerCase();
    return Object.values(concepts).filter(concept => 
      concept.title.toLowerCase().includes(lowercaseQuery) ||
      concept.content.toLowerCase().includes(lowercaseQuery)
    );
  };

  const value = {
    categories,
    concepts,
    currentConcept,
    isLoading,
    error,
    setCurrentConcept,
    getConcept,
    getConceptsByCategory,
    searchConcepts
  };

  return (
    <ConceptContext.Provider value={value}>
      {children}
    </ConceptContext.Provider>
  );
};

export default ConceptContext;