import React from 'react';
import './ComingSoon.css'; // Keep for bounce animation

function ComingSoon({ title }) {
  return (
    <div className="min-h-[calc(100vh-40px)] flex items-center justify-center p-5 bg-background text-text">
      <div className="max-w-xl w-full text-center bg-card p-6 md:p-10 rounded-xl shadow-md">
        <h1 className="text-[2.5rem] leading-tight md:text-3xl font-bold mb-6 text-text">{title}</h1>
        <div className="construction-icon text-[4rem] md:text-5xl my-6">🚧</div>
        <p className="text-[1.1rem] leading-[1.6] md:text-base text-muted-foreground mb-8">
          This section is currently under construction. We're working hard to bring you amazing content!
        </p>
        <div className="text-left bg-hover-bg p-4 md:p-6 rounded-lg mt-8">
          <h2 className="text-xl md:text-lg font-semibold mb-4 text-text">Coming Features</h2>
          <ul className="list-none p-0 m-0">
            <li className="text-muted-foreground py-2 pl-7 relative">
              <span className="absolute left-0 text-primary dark:text-primary-light">→</span>
              Detailed documentation and guides
            </li>
            <li className="text-muted-foreground py-2 pl-7 relative">
              <span className="absolute left-0 text-primary dark:text-primary-light">→</span>
              Interactive learning materials
            </li>
            <li className="text-muted-foreground py-2 pl-7 relative">
              <span className="absolute left-0 text-primary dark:text-primary-light">→</span>
              Progress tracking
            </li>
            <li className="text-muted-foreground py-2 pl-7 relative">
              <span className="absolute left-0 text-primary dark:text-primary-light">→</span>
              Resource library
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}

export default ComingSoon;
