import React from 'react';
import { Link } from 'react-router-dom';
// import './NotFound.css'; // Removed as styles are now inline with Tailwind

function NotFound() {
  return (
    <div className="flex min-h-[calc(100vh-40px)] items-center justify-center bg-background p-5 -m-5 md:-m-3">
      <div className="text-center max-w-lg p-10 md:p-6 rounded-xl bg-card shadow-md">
        <div 
          className="text-8xl md:text-[4rem] font-extrabold text-primary dark:text-primary-light leading-none mb-4 font-mono -tracking-[4px] [text-shadow:2px_2px_0_#bfdbfe] dark:[text-shadow:2px_2px_0_#1e40af]"
        >
          404
        </div>
        <h1 className="text-[2rem] md:text-[1.75rem] font-bold text-text dark:text-text mb-3">
          Page Not Found
        </h1>
        <p className="text-[1.1rem] md:text-base text-muted-foreground dark:text-muted-foreground mb-8 leading-relaxed">
          The page you're looking for doesn't exist or has been moved.
        </p>
        <div className="flex flex-col md:flex-row gap-3 md:gap-4 justify-center">
          <Link 
            to="/" 
            className="w-full md:w-auto py-3 px-6 rounded-md text-base font-medium no-underline transition-all duration-200 ease-in-out cursor-pointer bg-primary text-white hover:bg-primary-dark hover:-translate-y-px dark:bg-primary dark:text-white dark:hover:bg-primary-darker"
          >
            Go to Home
          </Link>
          <button 
            className="w-full md:w-auto py-3 px-6 rounded-md text-base font-medium no-underline transition-all duration-200 ease-in-out cursor-pointer bg-secondary text-secondary-foreground border border-border hover:bg-secondary-hover hover:-translate-y-px dark:bg-secondary dark:text-secondary-foreground dark:border-border dark:hover:bg-secondary-hover"
            onClick={() => window.history.back()}
          >
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
}

export default NotFound;
