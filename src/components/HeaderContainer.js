import React from 'react';
import { cn } from '../lib/utils';

const HeaderContainer = ({
  title,
  badge,
  metadata
}) => {
  return (
    <div className="border border-border dark:border-dark-border rounded-lg mb-3 bg-card dark:bg-dark-card">
      <h1 className="bg-card dark:bg-dark-card text-text dark:text-dark-text text-2xl font-semibold px-4 py-2 border-b border-border dark:border-dark-border">
        {title}
      </h1>
      {badge && (
        <div className="px-4 py-2 border-b border-border dark:border-dark-border flex items-center justify-start text-muted-foreground dark:text-dark-muted-foreground">
          <span className="px-3 py-2 rounded text-sm font-medium">
            {badge}
          </span>
        </div>
      )}
      <div className="text-sm">
        {metadata.map((item, index) => (
          <div
            key={index}
            className={cn(
              "bg-card dark:bg-dark-card px-4 py-2 text-muted-foreground dark:text-dark-muted-foreground",
              index !== metadata.length - 1 && "border-b border-border dark:border-dark-border"
            )}
          >
            {item}
          </div>
        ))}
      </div>
    </div>
  );
};

export default HeaderContainer;
