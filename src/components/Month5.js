import React, { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { julyProjects } from "../data/months/july";
import { useNavigate } from "react-router-dom";
import { Button } from "./ui/button";

function Month5({
  onPrevMonth,
  onNextMonth,
  onResetMonth,
  isFirstMonth,
  isLastMonth,
}) {
  const navigate = useNavigate();
  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];
  
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [currentView, setCurrentView] = useState('month');
  const [selectedDate, setSelectedDate] = useState(new Date(2025, 6, 1));

  const handleProjectClick = (projectId) => {
    navigate(`/projects/${projectId}`);
  };

  const renderDayView = () => {
    const dayProjects = julyProjects[selectedDate.getDate().toString()] || [];
    return (
      <div className="p-5 bg-card-bg rounded-lg">
        <h3 className="mb-5 text-text">{selectedDate.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</h3>
        <div className="flex flex-col gap-2.5">
          {dayProjects.map((project, index) => (
            <div
              key={`${selectedDate.getDate()}-${index}`}
              className={`px-2 py-1 rounded text-sm cursor-pointer transition-all ${
                selectedEvent === `${selectedDate.getDate()}-${project.id}`
                  ? "bg-blue-500 text-white"
                  : "bg-blue-500/15 text-blue-400"
              }`}
              onClick={() => handleProjectClick(project.id)}
            >
              {project.title}
            </div>
          ))}
          {dayProjects.length === 0 && (
            <div className="text-muted-text text-center p-5">No projects scheduled for this day</div>
          )}
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    const currentDate = new Date(selectedDate);
    const firstDayOfWeek = new Date(currentDate.setDate(currentDate.getDate() - currentDate.getDay()));
    const week = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(firstDayOfWeek);
      date.setDate(firstDayOfWeek.getDate() + i);
      const dayProjects = julyProjects[date.getDate().toString()] || [];
      
      week.push(
        <div key={i} className="min-h-[150px] border border-border rounded p-2.5 bg-card-bg">
          <div className="mb-2.5 text-center">
            <div className="text-sm text-muted-text">{days[i]}</div>
            <div className="text-xl font-semibold text-text">{date.getDate()}</div>
          </div>
          <div className="flex flex-col gap-1">
            {dayProjects.map((project, index) => (
              <div
                key={`${date.getDate()}-${index}`}
                className={`event bg-blue ${
                  selectedEvent === `${date.getDate()}-${project.id}` ? "selected" : ""
                }`}
                onClick={() => handleProjectClick(project.id)}
              >
                {project.title}
              </div>
            ))}
          </div>
        </div>
      );
    }
    return <div className="grid grid-cols-1 md:grid-cols-7 gap-2.5 p-2.5">{week}</div>;
  };

  const renderCalendarDays = () => {
    const firstDayOfMonth = new Date(2025, 6, 1); // July 1, 2025
    const startDayOffset = firstDayOfMonth.getDay();
    const cells = [];

    // Add empty cells for days before July 1st
    for (let i = 0; i < startDayOffset; i++) {
      cells.push(
        <div key={`empty-${i}`} className="min-h-[100px] border border-border rounded p-2.5 bg-card-bg opacity-50">
          <div className="flex justify-between mb-2.5">
            <div className="text-sm text-muted-text">{days[i]}</div>
          </div>
        </div>
      );
    }

    // Add cells for each day of July
    for (let day = 1; day <= 31; day++) {
      const date = new Date(2025, 6, day);
      const dayOfWeek = date.getDay();
      const dayProjects = julyProjects[day.toString()] || [];
      const isToday = day === new Date().getDate() && new Date().getMonth() === 6;
      const isSelected = day === selectedDate.getDate();

      cells.push(
        <div
          key={day}
          className={`
            min-h-[100px] border border-border rounded p-2.5 cursor-pointer transition-all bg-card-bg hover:bg-hover-bg
            ${isToday ? "border-blue-500 border-2" : ""}
            ${isSelected ? "bg-hover-bg border-blue-500" : ""}
          `}
          onClick={() => setSelectedDate(new Date(2025, 6, day))}
        >
          <div className="flex justify-between mb-2.5">
            <div className="text-sm text-muted-text">{days[dayOfWeek]}</div>
            <div className="text-base font-semibold text-text">{day}</div>
          </div>
          <div className="flex flex-col gap-1">
            {dayProjects.map((project, index) => (
              <div
                key={`${day}-${index}`}
                className={`event bg-blue ${
                  selectedEvent === `${day}-${project.id}` ? "selected" : ""
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleProjectClick(project.id);
                }}
              >
                {project.title}
              </div>
            ))}
          </div>
        </div>
      );
    }

    return cells;
  };

  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  const handleTodayClick = () => {
    const today = new Date();
    setSelectedDate(new Date(2025, 6, today.getDate()));
    onResetMonth();
  };

  const renderView = () => {
    switch (currentView) {
      case 'day':
        return renderDayView();
      case 'week':
        return renderWeekView();
      default:
        return <div className="grid grid-cols-7 gap-2.5 p-2.5 bg-background rounded-lg">{renderCalendarDays()}</div>;
    }
  };

  return (
    <div className="p-5 bg-background rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-5 md:flex-row flex-col gap-2.5">
        <div className="flex gap-2">
          <Button
            variant={currentView === 'day' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleViewChange('day')}
          >
            Day
          </Button>
          <Button
            variant={currentView === 'week' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleViewChange('week')}
          >
            Week
          </Button>
          <Button
            variant={currentView === 'month' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleViewChange('month')}
          >
            Month
          </Button>
        </div>
        <div className="text-xl font-semibold text-text">July 2025</div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTodayClick}
          >
            Today
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={onPrevMonth}
            disabled={isFirstMonth}
          >
            <ChevronLeft size={20} />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={onNextMonth}
            disabled={isLastMonth}
          >
            <ChevronRight size={20} />
          </Button>
        </div>
      </div>
      {renderView()}
    </div>
  );
}

export default Month5; 