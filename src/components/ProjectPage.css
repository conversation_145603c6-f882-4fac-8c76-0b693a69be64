.project-page {
  width: 100%;
  margin: 0;
  padding: 2rem 3rem;
  background-color: var(--background);
  min-height: 100vh;
}

.my-projects {
  border: 2px dashed var(--border);
  border-radius: 0.5rem;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
  background-color: var(--card-bg);
  width: 100%;
}

.my-projects-icon {
  color: #94a3b8;
  font-size: 2rem;
  margin-bottom: 1rem;
}

.no-projects-text {
  color: var(--text);
  font-size: 1rem;
}

.current-projects {
  text-align: left;
}

.current-projects h3 {
  color: var(--text);
  font-size: 1.2rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.current-projects .project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.current-projects .project-card {
  padding: 1rem;
  border: 1px solid var(--border);
  border-radius: 0.375rem;
  background-color: var(--background);
  text-decoration: none;
  transition: all 0.2s;
}

.current-projects .project-card:hover {
  background-color: var(--hover-bg);
  border-color: var(--muted-text);
}

.all-projects {
  margin-top: 2rem;
}

.all-projects-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.all-projects-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text);
  margin: 0;
}

.expand-collapse-buttons {
  display: flex;
  gap: 0.5rem;
}

.expand-collapse-buttons button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border);
  border-radius: 0.375rem;
  background-color: var(--card-bg);
  color: var(--text);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.expand-collapse-buttons button:hover {
  background-color: var(--hover-bg);
}

.sections {
  display: flex;
  flex-direction: column;
  gap: 1.0rem;
}

.project-group {
  display: flex;
  flex-direction: column;
  gap: 0.05rem;
}

/* Remove the previous spacing rule */
/* Add more spacing between main sections */
.section[data-main-section] {
  margin-top: 2rem;
}

/* Keep related subsections closer together */
.section:not([data-main-section]) {
  margin-top: 0.5rem;
}

.section {
  border: 1px solid var(--border);
  border-radius: 0.25rem;
  overflow: hidden;
  background-color: var(--card-bg);
}

.section-header {
  padding: 0.75rem 1rem;
  background-color: var(--card-bg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.section-header:hover {
  background-color: var(--hover-bg);
}

.section-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: var(--text);
}

.expand-icon {
  color: var(--text);
  opacity: 0.5;
  font-size: 0.875rem;
}

.project-grid {
  display: none;
  padding: 1rem;
  gap: 1rem;
  background-color: var(--background);
}

.section.expanded .project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.project-card {
  padding: 1rem;
  border: 1px solid var(--border);
  border-radius: 0.375rem;
  background-color: var(--card-bg);
  text-decoration: none;
  transition: all 0.2s;
}

.project-card:hover {
  background-color: var(--hover-bg);
  border-color: var(--muted-text);
}

.project-title {
  color: #ac2020 !important;
  text-decoration: none;
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

.project-timeline {
  color: #64748b;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.page-header {
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  font-size: 2rem;
  color: #1e293b;
  margin-bottom: 8px;
  font-weight: 700;
}

.header-description {
  color: #64748b;
  font-size: 1.1rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.text-green-800 {
  color: #166534;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

.text-blue-800 {
  color: #1e40af;
}

.bg-gray-100 {
  background-color: #f1f5f9;
}

.text-gray-800 {
  color: #1e293b;
}

.dates {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.description {
  font-size: 0.875rem;
  color: #4b5563;
  margin-bottom: 1rem;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background-color: #f3f4f6;
  border-radius: 9999px;
  font-size: 0.75rem;
  color: #374151;
}

.page-footer {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e5e7eb;
  color: #6b7280;
  font-size: 0.875rem;
  text-align: center;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  .page-header h1 {
    color: #f8fafc;
  }

  .header-description {
    color: #94a3b8;
  }

  .project-card {
    background-color: var(--card-bg);
    border-color: var(--border);
  }

  .project-card:hover {
    background-color: var(--hover-bg);
    border-color: var(--muted-text);
  }

  .card-header h3 {
    color: #f8fafc;
  }

  .dates {
    color: #94a3b8;
  }

  .description {
    color: #cbd5e1;
  }

  .tag {
    background: #334155;
    color: #94a3b8;
  }

  .page-footer {
    color: #94a3b8;
    border-top-color: #334155;
  }

  .bg-green-100 {
    background-color: #064e3b;
  }

  .text-green-800 {
    color: #6ee7b7;
  }

  .bg-blue-100 {
    background-color: #1e3a8a;
  }

  .text-blue-800 {
    color: #60a5fa;
  }

  .bg-gray-100 {
    background-color: #334155;
  }

  .text-gray-800 {
    color: #cbd5e1;
  }

  .section {
    background-color: var(--card-bg);
    border-color: var(--border);
  }

  .section-header {
    background-color: var(--card-bg);
  }

  .section-header:hover {
    background-color: var(--hover-bg);
  }
}

/* Responsive */
@media (max-width: 1024px) {
  .project-page {
    padding: 1.5rem 2rem;
  }

  .project-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .project-page {
    padding: 1rem;
  }

  .project-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .project-card {
    padding: 24px;
  }

  .page-header h1 {
    font-size: 1.75rem;
  }

  .header-description {
    font-size: 1rem;
  }
}
