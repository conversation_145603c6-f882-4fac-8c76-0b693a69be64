import React from 'react';
import { NavLink } from 'react-router-dom';
import { ChevronLeft, ChevronRight } from 'lucide-react';
// import './Sidebar.css'; // Styles moved to Tailwind utility classes

function Sidebar({ isOpen, onToggle }) {
  const navItemBaseClasses = "flex items-center py-3 px-5 no-underline transition-all duration-200 ease-in-out border-l-[3px] text-sm whitespace-nowrap";
  const navItemTextClasses = (open) => `transition-opacity duration-150 ease-in-out ${open ? 'opacity-100 visible' : 'opacity-0 invisible delay-150'}`;
  const headerTextClasses = (open) => `m-0 text-text dark:text-text text-2xl font-semibold transition-opacity duration-150 ease-in-out ${open ? 'opacity-100 visible' : 'opacity-0 invisible delay-150'}`;

  const getNavItemClasses = ({ isActive }) =>
    `${navItemBaseClasses} ${isActive
      ? 'bg-hover-bg dark:bg-hover-bg text-blue-500 dark:text-blue-400 border-l-blue-500 dark:border-l-blue-400 font-medium'
      : 'text-muted-text dark:text-muted-text border-transparent hover:bg-hover-bg dark:hover:bg-hover-bg hover:text-text dark:hover:text-text'
    }`;

  return (
    <>
      {/* Button moved inside the main sidebar div */}
      <div
        className={`
          fixed top-0 left-0 bottom-0
          bg-card-bg dark:bg-card-bg
          shadow-md z-[1000] flex flex-col overflow-hidden
          border-r border-border dark:border-border
          transition-all duration-300 ease-in-out
          ${isOpen ? 'w-48' : 'w-12'}
        `}
      >
        <button
          className={`
            absolute top-5 right-[-12px] p-2 rounded-md shadow-md cursor-pointer z-[1001]
            flex items-center justify-center
            bg-card-bg dark:bg-card-bg
            text-muted-text dark:text-muted-text
            hover:text-text dark:hover:text-text
            border border-border dark:border-border
            transition-all duration-300 ease-in-out
          `}
          onClick={onToggle}
        >
          {isOpen ? <ChevronLeft size={20} /> : <ChevronRight size={20} />}
        </button>
        <div className="py-5 pr-5 pl-12 border-b border-border dark:border-border whitespace-nowrap">
          <h2 className={headerTextClasses(isOpen)}>ALX SE</h2>
        </div>
        <nav className="flex-1 py-5 overflow-y-auto">
          <NavLink to="/planning" className={getNavItemClasses}>
            <span className="nav-icon mr-3 text-lg flex items-center justify-center w-6">📅</span>
            <span className={navItemTextClasses(isOpen)}>My Planning</span>
          </NavLink>
          <NavLink to="/dashboard" className={getNavItemClasses}>
            <span className="nav-icon mr-3 text-lg flex items-center justify-center w-6">📊</span>
            <span className={navItemTextClasses(isOpen)}>Dashboard</span>
          </NavLink>
          <NavLink to="/curriculums" className={getNavItemClasses}>
            <span className="nav-icon mr-3 text-lg flex items-center justify-center w-6">📚</span>
            <span className={navItemTextClasses(isOpen)}>Curriculums</span>
          </NavLink>
          <NavLink to="/projects" className={getNavItemClasses}>
            <span className="nav-icon mr-3 text-lg flex items-center justify-center w-6">💻</span>
            <span className={navItemTextClasses(isOpen)}>Projects</span>
          </NavLink>
          <NavLink to="/evaluation-quizzes" className={getNavItemClasses}>
            <span className="nav-icon mr-3 text-lg flex items-center justify-center w-6">📝</span>
            <span className={navItemTextClasses(isOpen)}>Evaluation Quizzes</span>
          </NavLink>
          <NavLink to="/peers" className={getNavItemClasses}>
            <span className="nav-icon mr-3 text-lg flex items-center justify-center w-6">👥</span>
            <span className={navItemTextClasses(isOpen)}>Peers</span>
          </NavLink>
          <NavLink to="/concepts" className={getNavItemClasses}>
            <span className="nav-icon mr-3 text-lg flex items-center justify-center w-6">💡</span>
            <span className={navItemTextClasses(isOpen)}>Concepts</span>
          </NavLink>
        </nav>
      </div>
    </>
  );
}

export default Sidebar;
