import React, { useState, useEffect } from 'react';
import { ArrowUp } from 'lucide-react';
import { cn } from '../lib/utils';

function ScrollToTopButton() {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setVisible(window.scrollY > 300);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <button
      onClick={scrollToTop}
      aria-label="Scroll to top"
      title="Back to top"
      className={cn(
        'fixed bottom-20 right-6 z-[90] flex items-center justify-center',
        'w-10 h-10 rounded-full shadow-md transition-opacity duration-300',
        'bg-card-bg text-text border border-border hover:bg-hover-bg',
        visible ? 'opacity-100' : 'opacity-0 pointer-events-none'
      )}
    >
      <ArrowUp size={18} />
    </button>
  );
}

export default ScrollToTopButton;
