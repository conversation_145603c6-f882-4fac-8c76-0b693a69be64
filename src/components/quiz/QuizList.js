import React from 'react';
import { Link } from 'react-router-dom';
import { evaluationQuizzes } from '../../data/evaluation-quizzes';

function QuizList() {
  const quizzes = Object.values(evaluationQuizzes);
  const quizAttempts = {};

  // Load attempts for each quiz
  quizzes.forEach(quiz => {
    const savedAttempts = localStorage.getItem(`quiz-${quiz.id}-attempts`);
    if (savedAttempts) {
      quizAttempts[quiz.id] = JSON.parse(savedAttempts);
    }
  });

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Evaluation Quizzes</h1>
        
        <div className="grid gap-6">
          {quizzes.map((quiz) => (
            <div 
              key={quiz.id}
              className="bg-card rounded-lg p-6 border border-border shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-xl font-semibold mb-2">{quiz.title}</h2>
                  <p className="text-muted-foreground">{quiz.description}</p>
                </div>
                <div className="text-sm text-muted-foreground">
                  {Math.floor(quiz.duration / 60)} minutes
                </div>
              </div>

              {quizAttempts[quiz.id] && (
                <div className="border-t border-border pt-4 mt-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="space-y-1">
                      <div>Attempts: {quizAttempts[quiz.id].length}</div>
                      {quizAttempts[quiz.id].length > 0 && (
                        <div>Best Score: {Math.max(...quizAttempts[quiz.id].map(a => 
                          Math.round((a.score / (quiz.totalQuestions * quiz.rules.scoring.correct)) * 100)
                        ))}%</div>
                      )}
                    </div>
                    <Link
                      to={`/evaluation-quizzes/${quiz.id}/latest`}
                      className="text-blue-600 hover:text-blue-700 dark:text-blue-400 hover:underline"
                    >
                      View Past Attempts ({quizAttempts[quiz.id].length})
                    </Link>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between mt-6">
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div>{quiz.totalQuestions} questions</div>
                  <div>•</div>
                  <div>
                    {quiz.rules.scoring.correct} pts correct
                  </div>
                  <div>•</div>
                  <div>
                    {quiz.rules.scoring.incorrect} pts incorrect
                  </div>
                </div>

                <div className="flex gap-2">
                  {quizAttempts[quiz.id]?.length > 0 && (
                    <Link
                      to={`/evaluation-quizzes/${quiz.id}/latest`}
                      className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      Latest Attempt
                    </Link>
                  )}
                  <Link
                    to={`/evaluation-quizzes/${quiz.id}`}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {quizAttempts[quiz.id]?.length > 0 ? 'Retake Quiz' : 'Start Quiz'}
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default QuizList;
