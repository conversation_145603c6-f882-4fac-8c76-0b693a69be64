import React from 'react';
import { Button } from '../ui/button';

function QuizInstructions({ quiz, onStart }) {
  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-3xl mx-auto p-6 space-y-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4 text-foreground">{quiz.title}</h1>
          <h2 className="text-xl font-semibold text-foreground">General Quiz Instructions</h2>
        </div>

        <div className="prose dark:prose-invert max-w-none space-y-6">
          <p className="text-foreground">
            There are no grades associated with this kind of quiz, but it will be scored. 
            This way we can see what kind of experience/knowledge each student is starting off with. 
            This quiz covers everything from basic math, to basic principles in an array of software engineering areas.
          </p>

          <p className="text-foreground">
            You may not be at all familiar with some of the concepts covered in this quiz... that is totally okay. 
            This quiz is not about what you DON'T know, and about what you DO know.
          </p>

          <div className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-900 p-4 rounded-lg">
            <p className="font-semibold text-yellow-800 dark:text-yellow-200">Once you start, you will have exactly 30 minutes to finish.</p>
          </div>

          <p className="text-foreground">
            There are {quiz.totalQuestions} questions and only 30 minutes, so don't spend too much time on any one question.{' '}
            <strong>If you don't know the answer, please choose "I don't know".</strong>
          </p>

          <div className="bg-card p-6 rounded-lg border border-border">
            <h3 className="font-semibold mb-4 text-foreground">Scoring:</h3>
            <ul className="list-disc pl-6 space-y-2">
              <li>Each question you answer correctly is <span className="text-green-600 dark:text-green-400">+1 point</span></li>
              <li>Each question you choose incorrectly is <span className="text-red-600 dark:text-red-400">-1 point</span></li>
              <li>Each question you choose "I don't know" is <span className="text-blue-600 dark:text-blue-400">0 points</span></li>
            </ul>
          </div>

          <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-900 p-4 rounded-lg">
            <p className="font-semibold text-red-800 dark:text-red-200">
              You are not allowed to use external resources aka the Internet / Google / man
            </p>
            <p className="text-red-700 dark:text-red-300 mt-2 italic">
              Seriously, don't even think about it
            </p>
          </div>

          <p className="text-foreground">
            At the bottom of the screen is a progress bar and timer so you can keep track of how many 
            questions you have answered, and how much time you have left.
          </p>
        </div>

        <div className="flex justify-center mt-12">
          <Button
            onClick={onStart}
            className="px-8 py-4 bg-blue-600 text-white rounded-lg text-lg hover:bg-blue-700"
          >
            Start Quiz
          </Button>
        </div>
      </div>
    </div>
  );
}

export default QuizInstructions;
