import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { cn } from '../../lib/utils';

import QuizInstructions from './QuizInstructions';

function QuizLayout({ quiz, initialAttempt, hasStarted: initialHasStarted }) {
  const [attempts, setAttempts] = useState(() => {
    const saved = localStorage.getItem(`quiz-${quiz.id}-attempts`);
    return saved ? JSON.parse(saved) : [];
  });
  const [viewingAttempt, setViewingAttempt] = useState(initialAttempt || null);
  const [isSubmitted, setIsSubmitted] = useState(!!initialAttempt);
  const [hasStarted, setHasStarted] = useState(!initialHasStarted);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState(quiz.duration);
  const [answers, setAnswers] = useState({});
  const [finalScore, setFinalScore] = useState(initialAttempt?.score || 0);
  const [shuffledOptions, setShuffledOptions] = useState({});
  const [shuffledQuestions, setShuffledQuestions] = useState([]);

  // Fisher-Yates shuffle algorithm
  const shuffle = (array) => {
    let currentIndex = array.length,  randomIndex;
    while (currentIndex > 0) {
      randomIndex = Math.floor(Math.random() * currentIndex);
      currentIndex--;
      [array[currentIndex], array[randomIndex]] = [
        array[randomIndex], array[currentIndex]];
    }
    return array;
  }

  useEffect(() => {
    if (initialAttempt) {
      setIsSubmitted(true);
      setViewingAttempt(initialAttempt);
    }
  }, [initialAttempt]);

    useEffect(() => {
    if (shuffledQuestions.length > 0 && !isSubmitted) {
      const currentQuestion = shuffledQuestions[currentQuestionIndex];
      const newShuffledOptions = { ...shuffledOptions };
      newShuffledOptions[currentQuestionIndex] = shuffle([...currentQuestion.options]);
      setShuffledOptions(newShuffledOptions);
    }
  }, [currentQuestionIndex, isSubmitted, shuffledQuestions]);

  const handleRetake = () => {
    setAnswers({});
    setCurrentQuestionIndex(0);
    setIsSubmitted(false);
    setFinalScore(0);
    setTimeLeft(quiz.duration);
    setViewingAttempt(null);
    setShuffledQuestions(shuffle([...quiz.questions]));
  };

  const startQuiz = () => {
    setHasStarted(true);
    setTimeLeft(quiz.duration);
    setShuffledQuestions(shuffle([...quiz.questions]));
  };

    const saveAttempt = (score, correctCount, answers) => {
    const newAttempt = {
      id: attempts.length + 1,
      date: new Date().toISOString(),
      score,
      correctCount,
      totalQuestions: quiz.questions.length,
      answers
    };
    const updatedAttempts = [...attempts, newAttempt];
    setAttempts(updatedAttempts);
    localStorage.setItem(`quiz-${quiz.id}-attempts`, JSON.stringify(updatedAttempts));
    return newAttempt;
  };

  useEffect(() => {
    let timer;
    if (hasStarted && !isSubmitted) {
      timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev <= 0) {
            clearInterval(timer);
            handleSubmit();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [hasStarted, isSubmitted]);

  const handleAnswer = (questionIndex, answer) => {
    setAnswers(prev => ({
      ...prev,
      [questionIndex]: answer
    }));
  };

  const handleSubmit = () => {
    setIsSubmitted(true);
    // Calculate score
    let score = 0;
    let correctCount = 0;
    Object.entries(answers).forEach(([index, answer]) => {
      const question = quiz.questions[index];
      const isCorrect = Array.isArray(question.correctAnswers)
        ? question.correctAnswers.length === answer?.length &&
          answer.every(a => question.correctAnswers.includes(a))
        : answer === question.correctAnswer;
      
      if (isCorrect) correctCount++;

      if (Array.isArray(question.correctAnswers)) {
        // Multi-select question
        if (!Array.isArray(answer)) return; // Skip if no answer
        score += isCorrect ? quiz.rules.scoring.correct : quiz.rules.scoring.incorrect;
      } else {
        // Single-select question
        if (answer === question.correctAnswer) score += quiz.rules.scoring.correct;
        else if (answer === "I don't know") score += quiz.rules.scoring.skip;
        else score += quiz.rules.scoring.incorrect;
      }
    });
    setFinalScore(score);
    const attempt = saveAttempt(score, correctCount, answers);
    setViewingAttempt(attempt);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

    if (!quiz) return null;

  const currentQuestion = shuffledQuestions[currentQuestionIndex];
    const progressPercentage =
    (currentQuestionIndex / shuffledQuestions.length) * 100;


    if (!hasStarted) {
    return <QuizInstructions quiz={quiz} onStart={startQuiz} />;
  }

  return (
    <div className="min-h-screen bg-background p-6">
      {!isSubmitted ? (
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-foreground">{quiz.title}</h1>
            <div className="text-xl font-mono text-foreground">{formatTime(timeLeft)}</div>
          </div>

          {/* Progress */}
          <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
            <div 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>

          {/* Question */}
          <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
            <div className="flex justify-between items-center mb-2">
              <div className="text-sm text-muted-foreground">
                {currentQuestion.category}
              </div>
              <div className="text-sm font-medium text-muted-foreground">
                Question {currentQuestionIndex + 1}/{quiz.questions.length}
              </div>
            </div>
            <p className="text-lg mb-6 whitespace-pre-wrap text-foreground">
              {currentQuestion.question}
            </p>
            <div className="space-y-3">
              {shuffledOptions[currentQuestionIndex]?.map((option, index) => {
                const isMultiSelect = Array.isArray(
                  currentQuestion.correctAnswers
                );
                const currentAnswer = answers[currentQuestionIndex] || [];
                const isSelected = isMultiSelect
                  ? currentAnswer.includes(option)
                  : currentAnswer === option;

                return (
                  <label
                    key={index}
                    className={cn(
                      "flex items-center p-4 rounded-lg border border-border cursor-pointer hover:bg-accent/50 transition-colors text-foreground",
                      isSelected && "bg-accent dark:bg-accent/50"
                    )}
                  >
                    <input
                      type={isMultiSelect ? "checkbox" : "radio"}
                      name="answer"
                      value={option}
                      checked={isSelected}
                      onChange={() => {
                        if (isMultiSelect) {
                          handleAnswer(
                            currentQuestionIndex,
                            isSelected
                              ? currentAnswer.filter((a) => a !== option)
                              : [...currentAnswer, option]
                          );
                        } else {
                          handleAnswer(currentQuestionIndex, option);
                        }
                      }}
                      className="mr-3"
                    />
                    <span className="flex-1">
                      {option.startsWith("```") ? (
                        <pre className="bg-gray-800 text-gray-100 p-4 rounded-md overflow-x-auto">
                          <code>{option.replace(/^```\w*\n|\n```$/g, "")}</code>
                        </pre>
                      ) : (
                        option
                      )}
                    </span>
                  </label>
                );
              })}
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-between">
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentQuestionIndex(0)}
                disabled={currentQuestionIndex === 0}
                className={cn(
                  "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",
                  "disabled:opacity-50 disabled:hover:bg-blue-600 disabled:cursor-not-allowed"
                )}
              >
                First
              </button>
              <button
                onClick={() =>
                  setCurrentQuestionIndex((prev) => Math.max(0, prev - 1))
                }
                disabled={currentQuestionIndex === 0}
                className={cn(
                  "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",
                  "disabled:opacity-50 disabled:hover:bg-blue-600 disabled:cursor-not-allowed"
                )}
              >
                Previous
              </button>
            </div>
            {currentQuestionIndex === shuffledQuestions.length - 1 ? (
              <button
                onClick={handleSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Submit
              </button>
            ) : (
              <div className="flex gap-2">
                <button
                  onClick={() =>
                    setCurrentQuestionIndex((prev) =>
                      Math.min(shuffledQuestions.length - 1, prev + 1)
                    )
                  }
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Next
                </button>
                <button
                  onClick={() =>
                    setCurrentQuestionIndex(shuffledQuestions.length - 1)
                  }
                  disabled={currentQuestionIndex === shuffledQuestions.length - 1}
                  className={cn(
                    "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",
                    "disabled:opacity-50 disabled:hover:bg-blue-600 disabled:cursor-not-allowed"
                  )}
                >
                  Last
                </button>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="max-w-xl mx-auto text-center space-y-6">
          <h2 className="text-3xl font-bold text-foreground">
            Great job! You're all done!
          </h2>
          <div className="mb-8">
            <div className="text-xl mb-4 text-foreground">
              <div className="mb-2">
                Attempt {viewingAttempt.id} - {formatDate(viewingAttempt.date)}
              </div>
              <div>
                You got{" "}
                <span className="font-bold text-blue-600 dark:text-blue-400">
                  {viewingAttempt.correctCount}
                </span>
                /{viewingAttempt.totalQuestions} correct
              </div>
              <div className="text-lg">
                Score:{" "}
                <span className="font-bold text-blue-600 dark:text-blue-400">
                  {Math.round(
                    (viewingAttempt.score /
                      (quiz.questions.length * quiz.rules.scoring.correct)) *
                      100
                  )}
                  %
                </span>
              </div>
            </div>
            {attempts.length > 1 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Previous Attempts</h3>
                <div className="grid gap-2">
                  {attempts
                    .slice()
                    .reverse()
                    .map((attempt) => (
                      <button
                        key={attempt.id}
                        onClick={() => setViewingAttempt(attempt)}
                        className={cn(
                          "p-4 rounded-lg border text-left transition-colors",
                          viewingAttempt.id === attempt.id
                            ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                            : "border-border hover:border-blue-500"
                        )}
                      >
                        <div className="font-medium">Attempt {attempt.id}</div>
                        <div className="text-sm text-muted-foreground">
                          {formatDate(attempt.date)} - Score:{" "}
                          {Math.round(
                            (attempt.score /
                              (quiz.questions.length *
                                quiz.rules.scoring.correct)) *
                              100
                          )}
                          %
                        </div>
                      </button>
                    ))}
                </div>
              </div>
            )}
          </div>
          <div className="space-y-6 mb-8 text-left">
            <h3 className="text-xl font-semibold text-center mb-6">
              Review Your Answers
            </h3>
            {quiz.questions.map((question, index) => {
              const userAnswer = viewingAttempt.answers[index];
              const isCorrect = Array.isArray(question.correctAnswers)
                ? question.correctAnswers.length === userAnswer?.length &&
                  userAnswer.every((a) => question.correctAnswers.includes(a))
                : userAnswer === question.correctAnswer;

              return (
                <div
                  key={index}
                  className={cn(
                    "rounded-lg border overflow-hidden",
                    isCorrect ? "border-green-500" : "border-red-500"
                  )}
                >
                  <div
                    className={cn(
                      "px-4 py-2 text-sm font-medium text-white",
                      isCorrect ? "bg-green-500" : "bg-red-500"
                    )}
                  >
                    Question {index + 1} {isCorrect ? "✓" : "✗"}
                  </div>
                  <div className="p-4 bg-white dark:bg-gray-800">
                    <div className="prose dark:prose-invert max-w-none">
                      <p className="text-base font-medium mb-4 whitespace-pre-wrap">
                        {question.question}
                      </p>
                      <div className="grid grid-cols-1 gap-2 text-sm">
                        <div>
                          <span className="font-medium text-gray-500 dark:text-gray-400">
                            Your answer:{" "}
                          </span>
                          <span
                            className={
                              isCorrect
                                ? "text-green-600 dark:text-green-400"
                                : "text-red-600 dark:text-red-400"
                            }
                          >
                            {Array.isArray(userAnswer)
                              ? userAnswer.join(", ")
                              : userAnswer || "Not answered"}
                          </span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-500 dark:text-gray-400">
                            Correct answer:{" "}
                          </span>
                          <span className="text-green-600 dark:text-green-400">
                            {Array.isArray(question.correctAnswers)
                              ? question.correctAnswers.join(", ")
                              : question.correctAnswer}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          <div className="flex gap-4 justify-center">
            <button
              onClick={handleRetake}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retake Quiz
            </button>
            <Link
              to="/evaluation-quizzes"
              className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Return to Quizzes
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}


export default QuizLayout;
