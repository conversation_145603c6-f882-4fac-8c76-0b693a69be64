import React, { useState, useEffect } from 'react';
import { useParams, useLocation, Navigate } from 'react-router-dom';
import QuizLayout from './QuizLayout';
import { evaluationQuizzes } from '../../data/evaluation-quizzes';

function QuizPage() {
  const { id } = useParams();
  const location = useLocation();
  const quiz = evaluationQuizzes[id];

  // Load initial attempt if viewing past attempts
  const loadInitialAttempt = () => {
    if (!quiz) return null;

    const savedAttempts = localStorage.getItem(`quiz-${quiz.id}-attempts`);
    if (!savedAttempts) return null;

    const attempts = JSON.parse(savedAttempts);
    if (attempts.length === 0) return null;

    // If accessing /latest, show the most recent attempt
    if (location.pathname.endsWith('/latest')) {
      return attempts[attempts.length - 1];
    }

    return null;
  };

  if (!quiz) {
    return <Navigate to="/evaluation-quizzes" replace />;
  }

  const initialAttempt = loadInitialAttempt();

  // If trying to view latest but no attempts exist
  if (location.pathname.endsWith('/latest') && !initialAttempt) {
    return <Navigate to="/evaluation-quizzes" replace />;
  }

  return (
    <QuizLayout 
      quiz={quiz} 
      initialAttempt={initialAttempt}
      hasStarted={!location.pathname.endsWith('/latest')}
    />
  );
}

export default QuizPage;
