import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useConceptContext } from '../../contexts/ConceptContext';
import { ChevronRight } from 'lucide-react';

const ConceptGrid = () => {
  const { categories, isLoading } = useConceptContext();
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div>
        <h1 className="text-2xl font-bold mb-6">ALX Concepts Library</h1>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(9)].map((_, i) => (
            <div key={i} className="p-6 rounded-lg border border-border bg-card shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="h-6 bg-accent/20 rounded w-1/3 animate-pulse"></div>
                <div className="h-5 w-5 bg-accent/20 rounded animate-pulse"></div>
              </div>
              <div className="space-y-2">
                <div className="h-4 bg-accent/20 rounded w-24 animate-pulse"></div>
                <div className="h-4 bg-accent/20 rounded w-2/3 animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">ALX Concepts Library</h1>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {categories.map(category => (
          <div
            key={category.id}
            className="p-6 rounded-lg border border-border bg-card shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200 group cursor-pointer"
            onClick={() => navigate(`/concepts/${category.id}`)}
          >
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-lg font-semibold group-hover:text-primary transition-colors">
                {category.name}
              </h2>
              <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors" />
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              {category.concepts.length} concepts
            </p>
            {category.concepts.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Latest: {category.concepts[0].title}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ConceptGrid;
