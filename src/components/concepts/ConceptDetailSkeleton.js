import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const ConceptDetailSkeleton = () => {
  return (
    <div className="concept-detail">
      <div className="concept-header">
        <div className="concept-navigation">
          <button className="back-button">
            <ChevronLeft size={20} />
            Back to Category
          </button>
          <div className="nav-buttons">
            <button className="nav-button" disabled>
              <ChevronLeft size={20} />
              Previous
            </button>
            <button className="nav-button" disabled>
              Next
              <ChevronRight size={20} />
            </button>
          </div>
        </div>
      </div>

      <div className="concept-content">
        <div className="main-content">
          <div className="h-8 bg-accent/20 w-3/4 rounded mb-6"></div>
          
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-6 bg-accent/20 w-1/3 rounded"></div>
                <div className="h-4 bg-accent/20 w-full rounded"></div>
                <div className="h-4 bg-accent/20 w-full rounded"></div>
                <div className="h-4 bg-accent/20 w-3/4 rounded"></div>
              </div>
            ))}
          </div>

          <div className="my-6 h-px bg-accent/20"></div>

          <div className="space-y-2">
            <div className="h-6 bg-accent/20 w-1/2 rounded"></div>
            <div className="h-24 bg-accent/10 w-full rounded border border-accent/20"></div>
          </div>
        </div>
        
        <div className="table-of-contents">
          <div className="h-6 bg-accent/20 w-1/2 rounded mb-4"></div>
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div 
                key={i} 
                className="h-4 bg-accent/20 rounded" 
                style={{ width: `${80 - i * 10}%` }}
              ></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConceptDetailSkeleton;