/* Concept Detail Styling - Consolidated for Tailwind */

/*
  The following classes were previously defined here but have been migrated
  to Tailwind utility classes directly in ConceptDetail.js or are handled
  by the @tailwindcss/typography plugin:
  
  - .concept-detail
  - .concept-header
  - .concept-navigation
  - .back-button (specific instance, base style moved to JS)
  - .nav-button (specific instance, base style moved to JS)
  - .nav-buttons
  - .concept-content
  - .main-content
  - .table-of-contents (most styles, some scrollbar styles remain)
  - .table-of-contents h2
  - .toc-item
  - .toc (replaced by .table-of-contents Tailwind styling)
  - .toc-link (replaced by .toc-item Tailwind styling)
  - .toc-link-active (handled by hover/focus states in Tailwind)
  - .prose a (handled by @tailwindcss/typography)
  - .prose img (handled by @tailwindcss/typography, max-height might need specific utility if not covered)
  - .prose pre (handled by @tailwindcss/typography)
  - .prose code (handled by @tailwindcss/typography)
  - .prose pre code (handled by @tailwindcss/typography)
  - .prose blockquote (handled by @tailwindcss/typography)

  Responsive adjustments (@media queries) are now handled by Tailwind's responsive prefixes.
*/

/* Styles that might be kept if not fully covered by Tailwind or typography plugin */

/* .concept-nav and .concept-nav-button are used in ConceptDetail.js for the "Not Found" state */
/* These can be kept if they are general utility styles or moved to a global CSS / component-specific if only used here */
.concept-nav {
  @apply sticky top-0 z-10 bg-background; /* Use theme variable */
  @apply border-b border-border; /* Use theme variable */
  @apply px-4 md:px-6 py-2;
  @apply flex items-center justify-between;
  @apply max-w-[100rem] mx-auto;
  @apply backdrop-blur; /* Consider if this is needed or if a simpler bg opacity is fine */
}

.concept-nav-button {
  @apply flex items-center px-3 py-2 text-sm rounded-lg;
  @apply bg-primary/90 hover:bg-primary; /* Use theme primary color */
  @apply transition-colors text-white; /* Ensure contrast or use a themed text color */
}

/* Syntax Highlighter Overrides - These are likely still needed if the plugin doesn't cover them */
.syntax-highlighter {
  @apply bg-code-bg rounded-lg; /* Use theme variable for code background */
}

/* Loading states - These are general utility styles */
.concept-skeleton {
  @apply animate-pulse rounded-lg bg-border dark:bg-border; /* Simplified, ensure dark mode compatibility */
}


/* Custom scrollbar styling for Table of Contents if desired beyond browser defaults */
/* These are webkit specific. For cross-browser, consider Tailwind scrollbar plugins or simpler styling. */
.table-of-contents::-webkit-scrollbar {
  width: 6px; /* Slightly thicker for better usability */
}

.table-of-contents::-webkit-scrollbar-track {
  background: transparent; /* Or use a subtle theme color like bg-card */
}

.table-of-contents::-webkit-scrollbar-thumb {
  @apply bg-border rounded; /* Use theme variable */
}

.table-of-contents::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground; /* Use theme variable for hover */
}

/* If @tailwindcss/typography doesn't handle image max-height as desired: */
.prose img {
  /* max-height: 500px; /* This could be added back if needed */
  /* width: auto; /* Default browser behavior, usually not needed to specify */
  /* Ensure images are responsive within prose */
  @apply max-w-full h-auto rounded-lg border border-border mx-auto;
}