import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useConceptContext } from '../../contexts/ConceptContext';
import { Search, Loader2 } from 'lucide-react';
import { cn } from '../../lib/utils';

const ConceptSearch = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState('');
  const [debouncedQuery, setDebouncedQuery] = useState('');
  const { searchConcepts, getCategoryById } = useConceptContext();
  const navigate = useNavigate();
  const searchRef = useRef(null);

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  // <PERSON><PERSON> click outside to close
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Get search results
  const results = debouncedQuery ? searchConcepts(debouncedQuery) : [];

  const handleSelect = (concept) => {
    const category = getCategoryById(concept.categoryId);
    navigate(\`/concepts/${category.id}/${concept.id}\`);
    setIsOpen(false);
    setQuery('');
  };

  return (
    <div ref={searchRef} className="relative w-full max-w-sm">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <input
          type="text"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            setIsOpen(true);
          }}
          onFocus={() => setIsOpen(true)}
          placeholder="Search concepts..."
          className={cn(
            "w-full bg-background px-10 py-2 text-sm",
            "border border-border rounded-lg",
            "focus:outline-none focus:ring-2 focus:ring-primary"
          )}
        />
        {query && (
          <button
            className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            onClick={() => {
              setQuery('');
              setIsOpen(false);
            }}
          >
            ×
          </button>
        )}
      </div>

      {isOpen && (
        <div className="absolute top-full mt-2 w-full rounded-lg border border-border bg-background shadow-lg">
          {debouncedQuery && results.length === 0 ? (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No concepts found
            </div>
          ) : (
            <ul className="max-h-[300px] overflow-auto p-2">
              {results.map((concept) => {
                const category = getCategoryById(concept.categoryId);
                return (
                  <li key={concept.id}>
                    <button
                      onClick={() => handleSelect(concept)}
                      className={cn(
                        "w-full text-left px-3 py-2 text-sm rounded-md",
                        "hover:bg-accent transition-colors"
                      )}
                    >
                      <div className="font-medium">{concept.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {category?.name}
                      </div>
                    </button>
                  </li>
                );
              })}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default ConceptSearch;