import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useConceptContext } from '../../contexts/ConceptContext';
import { cn } from '../../lib/utils';
import { ChevronRight, ChevronDown } from 'lucide-react';
import ConceptSearch from './ConceptSearch';

const ConceptList = () => {
  const { categories, isLoading } = useConceptContext();
  const [expandedCategories, setExpandedCategories] = useState(new Set());
  const navigate = useNavigate();
  const location = useLocation();

  const toggleCategory = (categoryId) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId);
      } else {
        newSet.add(categoryId);
      }
      return newSet;
    });
  };

  if (isLoading) {
    return (
      <div className="p-4 space-y-4">
        <div className="h-10 bg-accent/20 rounded-lg animate-pulse" />
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="py-2">
      <div className="px-4 mb-4">
        <ConceptSearch />
      </div>
      
      <div className="px-4">
        <h2 className="text-lg font-semibold mb-4 text-foreground">Categories</h2>
      </div>

      <div className="space-y-1">
        {categories.map(category => (
          <div key={category.id} className="px-2">
            <button
              onClick={() => toggleCategory(category.id)}
              className={cn(
                "flex items-center w-full p-2 text-sm rounded-lg",
                "hover:bg-accent/50 transition-colors",
                expandedCategories.has(category.id) && "bg-accent/30"
              )}
            >
              {expandedCategories.has(category.id) ? (
                <ChevronDown className="h-4 w-4 mr-1" />
              ) : (
                <ChevronRight className="h-4 w-4 mr-1" />
              )}
              <span className="flex-1 text-left font-medium">{category.name}</span>
              <span className="text-xs text-muted-foreground">
                {category.concepts.length}
              </span>
            </button>

            {expandedCategories.has(category.id) && (
              <div className="mt-1 ml-4 space-y-1">
                {category.concepts.map(concept => {
                  const isActive = location.pathname === \`/concepts/${category.id}/${concept.id}\`;
                  return (
                    <button
                      key={concept.id}
                      onClick={() => navigate(\`/concepts/${category.id}/${concept.id}\`)}
                      className={cn(
                        "block w-full text-left px-3 py-2 text-sm rounded-lg",
                        "hover:bg-accent/50 transition-colors",
                        isActive && "bg-accent text-accent-foreground"
                      )}
                    >
                      <div className="font-medium">{concept.title}</div>
                      {concept.summary && (
                        <div className="text-xs text-muted-foreground truncate">
                          {concept.summary}
                        </div>
                      )}
                    </button>
                  );
                })}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ConceptList;