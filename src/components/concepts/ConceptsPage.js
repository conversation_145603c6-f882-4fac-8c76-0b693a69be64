import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { useConceptContext } from '../../contexts/ConceptContext';
import ConceptGrid from './ConceptGrid';
import CategoryDetail from './CategoryDetail';

const ConceptsPage = () => {
  const location = useLocation();
  const isRoot = location.pathname === '/concepts';
  const isCategoryView = /^\/concepts\/[^/]+$/.test(location.pathname);

  return (
    <div className="flex min-h-screen bg-background">
      <div className="flex-1 w-full px-4 py-8">
        {isRoot ? (
          <ConceptGrid />
        ) : isCategoryView ? (
          <CategoryDetail />
        ) : (
          <Outlet />
        )}
      </div>
    </div>
  );
};

export default ConceptsPage;
