import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useConceptContext } from '../../contexts/ConceptContext';
import { ChevronLeft } from 'lucide-react';

const CategoryDetail = () => {
  const { categoryId } = useParams();
  const navigate = useNavigate();
  const { getConceptsByCategory, categories } = useConceptContext();
  
  const category = categories.find(cat => cat.id === categoryId);
  const parentCategory = category?.parentId ? categories.find(cat => cat.id === category.parentId)?.name : null;
  const concepts = getConceptsByCategory(categoryId);

  if (!category) {
    return (
      <div className="flex flex-col items-center justify-center h-full space-y-4 p-8">
        <p className="text-lg text-muted-foreground">Category not found</p>
        <button
          onClick={() => navigate('/concepts')}
          className="concept-nav-button"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back to Concepts
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <button
            onClick={() => navigate('/concepts')}
            className="concept-nav-button"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to {parentCategory || 'Concepts'}
          </button>
          <h1 className="text-2xl font-bold">{category.name}</h1>
        </div>
        <p className="text-muted-foreground">{category.description}</p>
      </div>

      <div className="grid gap-4">
        {concepts.map(concept => (
          <button
            key={concept.id}
            onClick={() => navigate(`/concepts/${categoryId}/${concept.id}`)}
            className="p-6 rounded-lg border border-border bg-card shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-200 text-left"
          >
            <h2 className="text-lg font-semibold mb-2">{concept.title}</h2>
            {concept.summary && (
              <p className="text-muted-foreground">{concept.summary}</p>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryDetail;
