import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useConceptContext } from '../../contexts/ConceptContext';
import { cn } from '../../lib/utils';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { processMarkdown, markdownConfig } from '../../utils/markdownProcessor';
import ConceptDetailSkeleton from './ConceptDetailSkeleton';
// import './ConceptDetail.css'; // CSS will be consolidated

const ConceptDetail = () => {
  const { conceptId, categoryId } = useParams();
  const navigate = useNavigate();
  const { getConcept, getConceptsByCategory, setCurrentConcept, concepts } = useConceptContext();
  const [isLoading, setIsLoading] = useState(true);

  const concept = getConcept(conceptId);
  const categoryConcepts = getConceptsByCategory(categoryId);
  
  // Pre-compute valid concepts outside of render to ensure consistency
  const validConcepts = React.useMemo(() => {
    if (!concepts || !categoryConcepts) return [];
    return categoryConcepts.filter(c => {
      const conceptContent = concepts[c.id]?.content;
      return conceptContent && 
             conceptContent.trim().length > 0 && 
             !conceptContent.trim().startsWith('[');
    });
  }, [concepts, categoryConcepts]);

  const currentIndex = validConcepts.findIndex(c => c.id === conceptId);
  const prevConcept = currentIndex > 0 ? validConcepts[currentIndex - 1] : null;
  const nextConcept = currentIndex < validConcepts.length - 1 ? validConcepts[currentIndex + 1] : null;
  
  useEffect(() => {
    const loadConcept = async () => {
      setIsLoading(true);
      if (concept) {
        setCurrentConcept(concept);
        await new Promise(resolve => setTimeout(resolve, 300));
        setIsLoading(false);
      }
    };
    loadConcept();
  }, [conceptId, concept, setCurrentConcept]);

  if (isLoading) {
    return <ConceptDetailSkeleton />;
  }

  if (!concept) {
    return (
      <div className="flex flex-col items-center justify-center h-full space-y-4 p-8">
        <p className="text-lg text-muted-foreground">Concept not found</p>
        {/* Uses concept-nav-button which is already Tailwind via @apply in the CSS file, or could be moved here */}
        <button
          onClick={() => navigate('/concepts')}
          className="concept-nav-button" // This class is defined with @apply in ConceptDetail.css
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back to {concept?.category || 'Concepts'} {/* Added fallback for category */}
        </button>
      </div>
    );
  }

  const handleBack = () => {
    navigate(`/concepts/${categoryId}`);
  };

  const handleNavigation = (direction) => {
    const targetConcept = direction === 'prev' ? prevConcept : nextConcept;
    if (targetConcept) {
      navigate(`/concepts/${categoryId}/${targetConcept.id}`);
    }
  };

  const navButtonBaseClasses = "flex items-center gap-2 px-4 py-2 rounded-md bg-card text-text border border-border cursor-pointer transition-all duration-200 hover:bg-hover-bg disabled:opacity-50 disabled:cursor-not-allowed";

  return (
    <div className="p-4 md:p-8 relative"> {/* Adjusted padding and max-width */}
      <div className="flex justify-between items-center p-4 bg-card border-b border-border mb-8 rounded-t-lg">
        <div className="flex justify-between items-center w-full">
          <button className={cn(navButtonBaseClasses)} onClick={handleBack}>
            <ChevronLeft size={20} />
            Back to {concept.category}
          </button>
          <div className="flex gap-2">
            <button
              className={cn(navButtonBaseClasses)}
              onClick={() => handleNavigation('prev')}
              disabled={!prevConcept}
            >
              <ChevronLeft size={20} />
              Previous
            </button>
            <button
              className={cn(navButtonBaseClasses)}
              onClick={() => handleNavigation('next')}
              disabled={!nextConcept}
            >
              Next
              <ChevronRight size={20} />
            </button>
          </div>
        </div>
      </div>

      {concept.toc && concept.toc.length > 0 && (
        <div className="bg-card rounded-lg p-4 sm:p-6 border border-border mb-8 shadow-sm">
          <h2 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4 pb-2 border-b border-border text-text uppercase tracking-wider">Table of Contents</h2>
          {concept.toc.map((item, index) => (
            <a
              key={index}
              href={`#${item.id}`}
              className="block text-xs sm:text-sm py-1.5 text-muted-foreground hover:text-text hover:bg-hover-bg/50 transition-colors duration-150 rounded px-2"
              style={{ marginLeft: `${(item.level - 1) * 0.75}rem` }}
            >
              {item.text}
            </a>
          ))}
        </div>
      )}

      <div className="prose prose-sm sm:prose-base lg:prose-lg xl:prose-xl dark:prose-invert max-w-none w-full overflow-x-auto prose-pre:!my-2 prose-pre:!mx-0 prose-pre:!p-0 prose-pre:!bg-transparent prose-pre:!border-none prose-pre:!rounded-lg">
        <ReactMarkdown
          {...markdownConfig}
        >
          {processMarkdown(concept.content)}
        </ReactMarkdown>
      </div>
    </div>
  );
};

export default ConceptDetail;
