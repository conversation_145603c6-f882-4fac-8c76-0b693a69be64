import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ChevronLeft } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { processMarkdown, markdownConfig } from '../../utils/markdownProcessor';
import { fetchConceptById, processConceptContent } from '../../utils/directConceptFetch';
import ConceptDetailSkeleton from './ConceptDetailSkeleton';
import './ConceptDetail.css';

const DirectConceptDetail = () => {
  const { conceptId } = useParams();
  const navigate = useNavigate();
  const [concept, setConcept] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadConcept = async () => {
      try {
        setIsLoading(true);
        
        // Fetch the concept content directly
        const content = await fetchConceptById(conceptId);
        
        // Process the content
        const processedConcept = processConceptContent(content, conceptId);
        
        setConcept(processedConcept);
      } catch (err) {
        console.error('Error loading concept:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    loadConcept();
  }, [conceptId]);

  if (isLoading) {
    return <ConceptDetailSkeleton />;
  }

  if (error || !concept) {
    return (
      <div className="flex flex-col items-center justify-center h-full space-y-4 p-8">
        <p className="text-lg text-muted-foreground">
          {error ? `Error: ${error}` : 'Concept not found'}
        </p>
        <button
          onClick={() => navigate(-1)}
          className="concept-nav-button"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Go Back
        </button>
      </div>
    );
  }

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div className="concept-detail">
      <div className="concept-header">
        <div className="concept-navigation">
          <button className="back-button" onClick={handleBack}>
            <ChevronLeft size={20} />
            Back
          </button>
        </div>
      </div>

      <div className="concept-content">
        <div className="main-content">
          <ReactMarkdown
            {...markdownConfig}
          >
            {processMarkdown(concept.content)}
          </ReactMarkdown>
        </div>
        
        {concept.toc && concept.toc.length > 0 && (
          <div className="table-of-contents">
            <h2>Table of Contents</h2>
            {concept.toc.map((item, index) => (
              <a
                key={index}
                href={`#${item.id}`}
                className="toc-item"
                style={{ marginLeft: `${(item.level - 1) * 1}rem` }}
              >
                {item.text}
              </a>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DirectConceptDetail;
