/* Custom radio and checkbox styling */
.quiz-input[type="radio"],
.quiz-input[type="checkbox"] {
  /* Using @apply to keep Tailwind consistency */
  @apply w-4 h-4 mt-0.5; /* Added mt-0.5 for better alignment with text */
  @apply border-2 border-gray-400 dark:border-gray-600;
  @apply bg-white dark:bg-gray-800;
  @apply focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400;
  @apply focus:ring-offset-2 dark:focus:ring-offset-gray-900;
  @apply transition-colors flex-shrink-0; /* Prevents shrinking in flex container */
}

.quiz-input[type="radio"] {
  @apply rounded-full;
}

.quiz-input[type="checkbox"] {
  @apply rounded;
}

.quiz-input[type="radio"]:checked,
.quiz-input[type="checkbox"]:checked {
  @apply bg-blue-600 dark:bg-blue-500;
  @apply border-blue-600 dark:border-blue-500;
}