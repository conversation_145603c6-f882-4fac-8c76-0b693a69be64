import React, { useState } from 'react';
import { Button } from './ui/button';
import { cn } from '../lib/utils';

const UrlInput = () => {
  const [url, setUrl] = useState('');

  const handleSave = () => {
    // TODO: Implement save functionality
    console.log('Saving URL:', url);
  };

  return (
    <div className="space-y-4">
      <div className="relative">
        <input
          type="text"
          value={url}
          onChange={(e) => setUrl(e.target.value)}
          placeholder="Paste your Google Doc link here"
          className={cn(
            "w-full px-3 py-2 rounded-md",
            "bg-background border border-input",
            "text-sm",
            "placeholder:text-muted-foreground",
            "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
            "transition duration-200"
          )}
        />
      </div>
      <Button
        variant="destructive"
        onClick={handleSave}
        className="w-24"
      >
        Save
      </Button>
    </div>
  );
};

export default UrlInput;