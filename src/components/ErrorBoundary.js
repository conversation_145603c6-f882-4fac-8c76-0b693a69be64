import React from 'react';
import { Link } from 'react-router-dom';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center p-5 bg-background text-text">
          <div className="max-w-2xl text-center bg-card p-10 rounded-xl shadow-md max-md:p-6">
            <div className="text-6xl mb-6 max-md:text-5xl">⚠️</div>
            <h1 className="text-3xl text-foreground font-bold mb-3 max-md:text-2xl">Something went wrong</h1>
            <p className="text-lg text-muted-foreground mb-6 leading-relaxed max-md:text-base">
              We're sorry — something went wrong with loading this page.
            </p>
            
            <div className="my-6 text-left">
              {this.state.error && (
                <p className="p-3 bg-red-100 dark:bg-red-900/50 rounded-md text-red-700 dark:text-red-300 font-mono mb-4 text-base mt-4">
                  {this.state.error.toString()}
                </p>
              )}
              {this.state.errorInfo && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-muted-foreground text-sm py-2">
                    Show technical details
                  </summary>
                  <pre className="mt-2 p-3 bg-code text-code-text rounded-md overflow-x-auto text-sm whitespace-pre-wrap font-mono">
                    {this.state.errorInfo.componentStack}
                  </pre>
                </details>
              )}
            </div>

            <div className="flex gap-4 justify-center mt-8 max-md:flex-col max-md:gap-3">
              <button 
                className="py-3 px-6 rounded-md text-base font-medium no-underline transition-all duration-200 ease-in-out cursor-pointer bg-primary hover:bg-primary-dark text-white hover:-translate-y-px max-md:w-full"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </button>
              <Link 
                to="/" 
                className="py-3 px-6 rounded-md text-base font-medium no-underline transition-all duration-200 ease-in-out cursor-pointer bg-muted hover:bg-hover-bg text-muted-foreground border border-border hover:-translate-y-px max-md:w-full"
              >
                Go to Home
              </Link>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
