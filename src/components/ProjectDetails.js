import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { projectContent } from '../data/projectContent';
import { Button } from './ui/button';
import { cn } from '../lib/utils';
import UrlInput from './UrlInput';
import ManualReviewButton from './ManualReviewButton';
import TaskContainer from './TaskContainer';
import ProjectQuiz from './ProjectQuiz';
import { projectQuizzes } from '../data/project-quizzes';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { materialDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import HeaderContainer from './HeaderContainer';

const renderItalics = (text) => {
  if (!text || typeof text !== 'string') return text;

  const italicsRegex = /(?<![/\w])_([^_]+)_(?![/\w])/g;
  const parts = [];
  let lastIndex = 0;
  let match;

  while ((match = italicsRegex.exec(text)) !== null) {
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }
    parts.push(
      <em key={match.index} className="italic">
        {match[1]}
      </em>
    );
    lastIndex = match.lastIndex;
  }

  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }

  return parts;
};

const renderBoldText = (text) => {
  if (!text || typeof text !== 'string') return text;

  const parts = text.split(/(\*\*[^*]+\*\*)/);
  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      return (
        <strong key={index} className="font-bold">
          {part.slice(2, -2)}
        </strong>
      );
    }
    return part;
  });
};

const renderImage = (text) => {
  if (!text || typeof text !== 'string') return text;

  if (text.match(/!\[([^\]]*)\]\(([^)]+)\)\s*!\[([^\]]*)\]\(([^)]+)\)/)) {
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
    const images = [];
    let match;

    while ((match = imageRegex.exec(text)) !== null) {
      images.push(
        <img
          key={match.index}
          src={match[2]}
          alt={match[1]}
          className="inline-image rounded-md shadow-sm border border-border h-auto mx-1 bg-transparent"
          style={{ display: 'inline-block', maxHeight: '30px', verticalAlign: 'middle' }}
        />
      );
    }

    return <div className="badge-container bg-transparent" style={{ display: 'flex', alignItems: 'center', gap: '4px', margin: '8px 0' }}>{images}</div>;
  }

  const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g;
  const parts = [];
  let lastIndex = 0;
  let match;

  while ((match = imageRegex.exec(text)) !== null) {
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }
    const isShieldBadge = match[2].includes('img.shields.io');
    const shieldClasses = isShieldBadge
      ? "inline-block h-auto my-1"
      : "block max-w-[75%] h-auto my-4 md:max-w-full";

    const shieldStyle = isShieldBadge
      ? { transform: 'scale(1.5)', transformOrigin: 'left center', margin: '0' }
      : {};

    parts.push(
      <img
        key={match.index}
        src={match[2]}
        alt={match[1]}
        className={cn(
          "rounded-md shadow-sm border border-border",
          shieldClasses
        )}
        style={shieldStyle}
      />
    );
    lastIndex = match.lastIndex;
  }

  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }

  return parts;
};

const renderVideo = (text) => {
  if (!text || typeof text !== 'string') return text;

  const videoRegex = /\[embed:\s*([^\]]+)\]\((https:\/\/(?:(?:www\.)?youtube\.com\/(?:watch\?v=|live\/|embed\/)|youtu\.be\/)([^&\s)]+)[^)]*)\)/g;
  const parts = [];
  let lastIndex = 0;
  let match;

  while ((match = videoRegex.exec(text)) !== null) {
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }

    const title = match[1];
    const videoId = match[3];

    parts.push(
      <div key={`video-${match.index}`} className="relative my-8">
        <div className="relative pb-[56.25%] h-0 overflow-hidden rounded-lg shadow-lg border border-border/50">
          <iframe
            className="absolute top-0 left-0 w-full h-full rounded-lg"
            src={`https://www.youtube.com/embed/${videoId}`}
            title={title}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>
        <p className="mt-2 text-sm text-muted-foreground text-center">{title}</p>
      </div>
    );

    lastIndex = match.lastIndex;
  }

  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }

  return parts;
};

const renderLink = (text) => {
  if (!text || typeof text !== 'string') return text;

  const parts = text.split(/(?<!!)\[([^\]]+)\]\(([^)]+)\)/);
  return parts.map((part, index) => {
    if (index % 3 === 1) {
      const url = parts[index + 1];
      const isVideo = url.includes('youtube.com') && part.startsWith('embed:');
      const conceptMatch = url.match(/\/concepts\/(\d+)$/);
      if (conceptMatch) {
        return (
          <Link
            key={index}
            to={`/concept/${conceptMatch[1]}`}
            className="text-red-500 dark:text-red-400 hover:underline"
          >
            {part}
          </Link>
        );
      } else if (!isVideo) {
        return (
          <a
            key={index}
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-red-800 dark:text-red-400 hover:underline"
          >
            {part}
          </a>
        );
      }
    } else if (index % 3 === 2) {
      return null;
    }
    return part;
  }).filter(Boolean);
};

// START OF MODIFIED FUNCTION
const renderTextWithInlineCode = (text) => {
  if (!text || typeof text !== 'string') return text;

  const isJsonFormat = text.includes('"USER_ID"') ||
                      text.includes('"username"') ||
                      text.includes('"task"') ||
                      text.includes('"completed"');

  const formatClass = isJsonFormat ? 'format-json-example' : '';

  const parts = text.split(/(`[^`]+`)/);
  return parts.map((part, index) => {
    if (part.startsWith('`') && part.endsWith('`')) {
      return (
        <code key={index} className={cn(
          "font-mono text-sm bg-gray-50 dark:bg-gray-800 text-red-600 dark:text-red-400 py-0.5 px-1.5 rounded-md",
          "inline-code",
          formatClass
        )} style={{
          display: isJsonFormat ? 'block' : 'inline-block',
          width: isJsonFormat ? '100%' : 'auto',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all',
          overflowWrap: 'anywhere'
        }}>
          {part.slice(1, -1)}
        </code>
      );
    }
    return part;
  });
};
// END OF MODIFIED FUNCTION

const processContent = (text) => {
  if (!text || typeof text !== 'string') return text;

  try {
    const withImages = renderImage(text);
    if (!Array.isArray(withImages)) return withImages;

    const processed = withImages.map(part => {
      if (typeof part !== 'string') return part;
      const withVideos = renderVideo(part);
      if (!Array.isArray(withVideos)) return withVideos;
      return withVideos.map(videoPart => {
        if (typeof videoPart !== 'string') return videoPart;
        const withLinks = renderLink(videoPart);
        if (!Array.isArray(withLinks)) return withLinks;
        return withLinks.map(linkPart => {
          if (typeof linkPart !== 'string') return linkPart;
          const withBold = renderBoldText(linkPart);
          if (!Array.isArray(withBold)) return withBold;
          return withBold.map(boldPart => {
            if (typeof boldPart !== 'string') return boldPart;
            const withItalics = renderItalics(boldPart);
            if (!Array.isArray(withItalics)) return withItalics;
            return withItalics.map(italicPart => {
              if (typeof italicPart !== 'string') return italicPart;
              return renderTextWithInlineCode(italicPart);
            });
          });
        });
      });
    });

    return <>{processed.flat(4)}</>;
  } catch (error) {
    console.error('Error processing content:', error);
    return text;
  }
};

const renderCodeBlock = (code) => {
  const cleanCode = code.replace(/^```(\w*)\n/, '').replace(/```$/, '').trim();
  const language = code.match(/^```(\w*)\n/)?.[1] || 'text';

  return (
    <div className="relative my-4 w-full">
      <SyntaxHighlighter
        style={materialDark}
        language={language}
        PreTag="div"
        className="rounded-lg"
        customStyle={{
          margin: '1rem 0',
          padding: '1rem',
          borderRadius: '0.5rem',
          border: '1px solid var(--border)',
          wordBreak: 'break-word',
          whiteSpace: 'pre-wrap',
          overflowWrap: 'break-word',
          backgroundColor: '#263238'
        }}
        showLineNumbers={true}
        wrapLines={true}
        wrapLongLines={true}
      >
        {cleanCode}
      </SyntaxHighlighter>
    </div>
  );
};

const renderActionButtons = () => (
  <div className="bg-gray-50 dark:bg-gray-800/30 border-t border-gray-200 dark:border-gray-700 px-4 py-3 -mx-4 -mb-4 mt-6 rounded-b-lg">
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        className="flex items-center gap-2 text-sm px-3 py-1.5 bg-white dark:bg-gray-900"
      >
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M5 13l4 4L19 7" />
        </svg>
        <span>Done?</span>
      </Button>
      <Button
        variant="outline"
        className="flex items-center gap-2 text-sm px-3 py-1.5 bg-white dark:bg-gray-900"
      >
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Help</span>
      </Button>
      <Button
        variant="outline"
        className="flex items-center gap-2 text-sm px-3 py-1.5 bg-white dark:bg-gray-900"
      >
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
          <path d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span>Check submission</span>
      </Button>
      <Button
        variant="outline"
        className="flex items-center gap-2 text-sm px-3 py-1.5 bg-white dark:bg-gray-900"
      >
        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
        </svg>
        <span>Get a sandbox</span>
      </Button>
    </div>
  </div>
);

const parseContent = (content, id) => {
  const elements = [];
  const lines = content.split('\n');
  let i = 0;

  const parseBodyUntilNextHeading = (startIndex) => {
    const bodyContent = [];
    let currentIndex = startIndex;
    while (currentIndex < lines.length && !lines[currentIndex].startsWith('#')) {
      let currentLine = lines[currentIndex];

      if (currentLine.includes('{{ACTION_BUTTONS}}')) {
          bodyContent.push(<div key={`action-buttons-${currentIndex}`}>{renderActionButtons()}</div>);
          currentIndex++;
          continue;
      }
      if (currentLine.includes('{{URL_INPUT}}')) {
          bodyContent.push(<div key={`url-input-${currentIndex}`}><UrlInput /></div>);
          currentIndex++;
          continue;
      }
      if (currentLine.includes('{{MANUAL_REVIEW_BUTTON}}')) {
          bodyContent.push(<div key={`manual-review-${currentIndex}`}><ManualReviewButton /></div>);
          currentIndex++;
          continue;
      }
      if (currentLine.trim().startsWith('```')) {
        const codeBlockLines = [currentLine];
        let k = currentIndex + 1;
        while (k < lines.length && !lines[k].trim().startsWith('```')) {
          codeBlockLines.push(lines[k]);
          k++;
        }
        if (k < lines.length) {
          codeBlockLines.push(lines[k]);
          k++;
        }
        bodyContent.push(<React.Fragment key={`code-${currentIndex}`}>{renderCodeBlock(codeBlockLines.join('\n'))}</React.Fragment>);
        currentIndex = k;
        continue;
      }
      if (currentLine.startsWith('#### ')) {
        bodyContent.push(
          <h4
            key={`h4-${currentIndex}`}
            className="text-base font-semibold text-blue-700 dark:text-blue-300 mt-6 mb-2 pb-1 border-b border-blue-200 dark:border-blue-700 tracking-wide"
          >
            {processContent(currentLine.slice(5))}
          </h4>
        );
        currentIndex++;
        continue;
      }
      if (currentLine.trim().startsWith('* ') || currentLine.trim().startsWith('- ')) {
        const isHyphen = currentLine.trim().startsWith('- ');
        const listItems = [];
        let k = currentIndex;
        while (k < lines.length && (lines[k].trim().startsWith('* ') || lines[k].trim().startsWith('- '))) {
          const indentLevel = lines[k].match(/^\s*/)[0].length / 2;
          const content = lines[k].trim().slice(2);
          const itemClass = isHyphen
            ? "my-1 relative before:content-['→'] before:absolute before:left-[-1.25rem] text-foreground"
            : "my-1 list-item text-foreground";
          // Apply proper indentation using inline styles
          const indentStyle = indentLevel > 0 ? { marginLeft: `${indentLevel * 1.5}rem` } : {};
          listItems.push(<li key={`li-${k}`} className={itemClass} style={indentStyle}>{processContent(content)}</li>);
          k++;
        }
        bodyContent.push(<div key={`list-${currentIndex}`}><ul className={cn("my-2 pl-12 mb-4", !isHyphen && "list-disc marker:text-foreground")}>{listItems}</ul></div>);
        currentIndex = k;
        continue;
      }
      if (currentLine.trim() !== '') {
        bodyContent.push(<p key={`p-${currentIndex}`} className="text-base leading-relaxed text-foreground my-1">{processContent(currentLine)}</p>);
      }
      currentIndex++;
    }
    return { body: bodyContent, nextIndex: currentIndex };
  };

  while (i < lines.length) {
    let line = lines[i];

    if (line.startsWith('# ')) {
      const title = processContent(line.slice(2));
      const badge = lines[i + 1] && lines[i + 1].startsWith('![') ? processContent(lines[i + 1]) : null;
      const badgeLinesConsumed = badge ? 1 : 0;
      const metadata = [];
      let currentMetaIndex = i + 1 + badgeLinesConsumed + 1;
      while (currentMetaIndex < lines.length) {
        const metaLine = lines[currentMetaIndex];
        const trimmedMetaLine = metaLine.trim();
        const isMetadataLine = trimmedMetaLine.length > 0 && !trimmedMetaLine.startsWith('##') && (trimmedMetaLine.startsWith('👤') || trimmedMetaLine.startsWith('📝') || trimmedMetaLine.startsWith('⚖️') || trimmedMetaLine.startsWith('📅') || trimmedMetaLine.startsWith('⏱️'));
        if (!isMetadataLine) break;
        metadata.push(processContent(metaLine));
        currentMetaIndex++;
      }
      elements.push(<HeaderContainer key="header" title={title} badge={badge} metadata={metadata} />);
      i = currentMetaIndex;
      continue;
    }

    if (line.startsWith('## ')) {
      const headingText = processContent(line.slice(3));
      const currentHeadingKey = line.slice(3).toLowerCase().replace(/\s+/g, '-');
      const sectionChildren = [];
      
      let j = i + 1;
      while (j < lines.length && !lines[j].startsWith('##') && !lines[j].startsWith('# ')) {
        let childLine = lines[j];
        
        if (childLine.startsWith('### ')) {
          const [subTitle, ...subTags] = childLine.slice(4).split(/{{(.*?)}}/);
          const { body: subBody, nextIndex: subNextIndex } = parseBodyUntilNextHeading(j + 1);
          sectionChildren.push(
            <TaskContainer key={`subtask-${j}`} isTaskHeading={true} title={processContent(subTitle.trim())} tag={subTags.length > 0 ? subTags[0] : null}>
              {subBody}
            </TaskContainer>
          );
          j = subNextIndex;
        } else {
          const { body: directBody, nextIndex: directNextIndex } = parseBodyUntilNextHeading(j);
          sectionChildren.push(...directBody);
          j = directNextIndex;
        }
      }

      if (currentHeadingKey === 'tasks') {
        elements.push(<h2 key="tasks-heading" className="text-2xl font-semibold mt-10 mb-6">{headingText}</h2>);
        elements.push(...sectionChildren);
      } else if (currentHeadingKey === 'quiz-questions') {
        const projectId = id.includes('/') ? id.split('/').pop() : id;
        const quiz = projectQuizzes[projectId];
        if (quiz) {
            elements.push(<TaskContainer key={currentHeadingKey} isTaskHeading={true} title={headingText}><ProjectQuiz questions={quiz} /></TaskContainer>);
        }
      } else {
        elements.push(<TaskContainer key={currentHeadingKey} isTaskHeading={true} title={headingText}>{sectionChildren}</TaskContainer>);
      }

      i = j;
      continue;
    }

    if (line.startsWith('### ')) {
      const [title, ...tags] = line.slice(4).split(/{{(.*?)}}/);
      const { body, nextIndex } = parseBodyUntilNextHeading(i + 1);
      elements.push(
        <TaskContainer key={`task-${i}`} isTaskHeading={true} title={processContent(title.trim())} tag={tags.length > 0 ? (tags[0] || "mandatory") : null}>
          {body}
        </TaskContainer>
      );
      i = nextIndex;
      continue;
    }
    
    if (line.startsWith('#### ')) {
      // Render the h4 heading first
      elements.push(
        <h4
          key={`h4-${i}`}
          className="text-base font-semibold text-blue-700 dark:text-blue-300 mt-6 mb-2 pb-1 border-b border-blue-200 dark:border-blue-700 tracking-wide"
        >
          {processContent(line.slice(5))}
        </h4>
      );
      // Then render the body content that follows
      const { body, nextIndex } = parseBodyUntilNextHeading(i + 1);
      elements.push(...body);
      i = nextIndex;
      continue;
    }
    
    i++;
  }

  return elements;
};

const ProjectDetails = () => {
  const { id } = useParams();
  const projectId = id.includes('/') ? id.split('/').pop() : id;
  const projectData = projectContent[projectId];
  const content = typeof projectData === 'string' ? projectData : projectData?.content;

  if (!content) {
    return (
      <div className="min-h-screen m-2.5 flex flex-col bg-background dark:bg-dark-background rounded-lg shadow-md overflow-hidden font-aptos">
        <nav className="sticky top-0 z-10 px-6 py-4 bg-background dark:bg-dark-background border-b border-border dark:border-dark-border shadow-sm">
          <div className="max-w-6xl mx-auto">
            <Button asChild variant="ghost" className="group hover:bg-background/80 dark:hover:bg-dark-background/80">
              <Link to="/projects" className="flex items-center gap-2 text-sm font-medium text-muted-foreground dark:text-dark-muted-foreground group-hover:text-foreground dark:group-hover:text-dark-foreground transition-all duration-200 ease-in-out">
                <span className="transform group-hover:-translate-x-1 transition-transform">←</span>
                Back to Projects List
              </Link>
            </Button>
          </div>
        </nav>
        <div className="flex-grow flex items-center justify-center p-6 text-center">
          <div className="space-y-6">
            <h1 className="text-4xl font-bold text-text dark:text-dark-text tracking-tight">Project Not Found</h1>
            <p className="text-lg text-muted-foreground dark:text-dark-muted-foreground">The project you're looking for doesn't exist or hasn't been added yet.</p>
            <div className="pt-6">
              <Button asChild variant="outline" size="lg" className="border-border dark:border-dark-border text-text dark:text-dark-text hover:bg-muted dark:hover:bg-dark-muted">
                <Link to="/projects">
                  <span className="flex items-center gap-2">
                    <span>←</span>
                    Back to Projects List
                  </span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const statusTag = (typeof projectData === 'object' && projectData.status) ? (
    <div className="inline-block px-3 py-2 text-base rounded bg-red-500 text-white dark:bg-red-700 dark:text-white">
      {projectData.status}
    </div>
  ) : null;

  return (
    <div className="min-h-screen m-2.5 flex flex-col bg-background dark:bg-dark-background rounded-lg shadow-md overflow-hidden font-aptos">
      <nav className="sticky top-0 z-10 backdrop-blur-md bg-background/80 dark:bg-dark-background/80 border-b border-border dark:border-dark-border shadow-sm">
        <div className="max-w-none mx-auto px-2 md:px-4 lg:px-6 py-3">
          <Button asChild variant="ghost" className="group hover:bg-muted dark:hover:bg-dark-muted">
            <Link to="/projects" className="flex items-center gap-2 text-sm font-medium text-muted-foreground dark:text-dark-muted-foreground group-hover:text-foreground dark:group-hover:text-dark-foreground transition-all duration-200 ease-in-out">
              <span className="transform group-hover:-translate-x-1 transition-transform">←</span>
              <span className="text-inherit">Back to Projects List</span>
            </Link>
          </Button>
        </div>
      </nav>
      <main className="flex-grow w-full">
        <div className="max-w-none mx-auto px-2 py-4 md:px-4 lg:px-6">
          {statusTag && (
            <div className="mb-4">
              {statusTag}
            </div>
          )}
          <div className="break-words">
            {parseContent(content, id)}
          </div>
        </div>
      </main>
      <footer className="mt-auto py-6 border-t border-border dark:border-dark-border text-center bg-background dark:bg-dark-background">
        <div className="max-w-none mx-auto px-2 md:px-4 lg:px-6 text-sm text-muted-foreground dark:text-dark-muted-foreground">
          <p>Copyright © 2025 ALX, All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default ProjectDetails;
