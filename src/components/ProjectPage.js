import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { FiGitBranch } from "react-icons/fi"; // For the "no projects" icon
import "./ProjectPage.css";

function ProjectPage() {
  const [expandedSections, setExpandedSections] = useState(() => {
    const saved = localStorage.getItem('expandedSections');
    return saved ? JSON.parse(saved) : {};
  });

  useEffect(() => {
    localStorage.setItem('expandedSections', JSON.stringify(expandedSections));
  }, [expandedSections]);

  // Function to find today's projects
  const getTodayProjects = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day for accurate comparison

    const todayProjects = [];
    Object.values(sections).forEach(sectionProjects => {
      sectionProjects.forEach(project => {
        const [startDateStr, endDateStr] = project.timeline.split(" to ");
        const startDate = new Date(startDateStr);
        const endDate = new Date(endDateStr);
        
        // Set time to start of day for accurate comparison
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(0, 0, 0, 0);
        
        if (today >= startDate && today <= endDate) {
          todayProjects.push(project);
        }
      });
    });

    return todayProjects;
  };

  const sections = {
    "Onboarding": [
      { id: "welcome-onboard", title: "Welcome on board", timeline: "2025-02-10 to 2025-02-10" },
      { id: "slack-intro", title: "Getting hooked on Slack", timeline: "2025-02-10 to 2025-02-10" },
      { id: "intranet", title: "Deep dive into the Intranet", timeline: "2025-02-11 to 2025-02-11" },
      { id: "mindset", title: "Introduction to mindsets (Grit and growth mindsets)", timeline: "2025-02-11 to 2025-02-11" },
      { id: "shell-navigation", title: "0x00. Shell, navigation", timeline: "2025-02-12 to 2025-02-12" },
      { id: "tweet-a-day", title: "A tweet a day keeps the @julienbarbier42 far away", timeline: "2025-02-13 to 2025-02-13" },
      { id: "emacs", title: "0x02. Emacs", timeline: "2025-02-13 to 2025-02-13" },
      { id: "vi", title: "0x03. vi", timeline: "2025-02-13 to 2025-02-13" },
      { id: "map-your-mind", title: "Map your mind", timeline: "2025-02-13 to 2025-02-13" },
      { id: "git", title: "0x01. Git", timeline: "2025-02-14 to 2025-02-14" },
      { id: "learning-community", title: "You and your learning community", timeline: "2025-02-14 to 2025-02-14" },
      { id: "networking", title: "Your network is your net worth", timeline: "2025-02-17 to 2025-02-17" },
      { id: "grit-assignment", title: "Grit Assignment Part 2", timeline: "2025-02-18 to 2025-02-18" },
      { id: "owning-learning", title: "Owning your Learning", timeline: "2025-02-19 to 2025-02-19" },
      { id: "mental-health", title: "Preserving your mental health & Conquering imposter syndrome", timeline: "2025-02-20 to 2025-02-20" }
    ],
    "Onboarding - Getting started": [
      { id: "welcome-onboard", title: "Welcome on board", timeline: "2025-02-10 to 2025-02-10" },
      { id: "slack-intro", title: "Getting hooked on Slack", timeline: "2025-02-10 to 2025-02-10" },
      { id: "intranet", title: "Deep dive into the Intranet", timeline: "2025-02-11 to 2025-02-11" },
      { id: "mindset", title: "Introduction to mindsets (Grit and growth mindsets)", timeline: "2025-02-11 to 2025-02-11" },
      { id: "tweet-a-day", title: "A tweet a day keeps the @julienbarbier42 far away", timeline: "2025-02-13 to 2025-02-13" },
      { id: "map-your-mind", title: "Map your mind", timeline: "2025-02-13 to 2025-02-13" },
      { id: "learning-community", title: "You and your learning community", timeline: "2025-02-14 to 2025-02-14" },
      { id: "networking", title: "Your network is your net worth", timeline: "2025-02-17 to 2025-02-17" },
      { id: "grit-assignment", title: "Grit Assignment Part 2", timeline: "2025-02-18 to 2025-02-18" },
      { id: "owning-learning", title: "Owning your Learning", timeline: "2025-02-19 to 2025-02-19" },
      { id: "mental-health", title: "Preserving your mental health & Conquering imposter syndrome", timeline: "2025-02-20 to 2025-02-20" }
    ],
    "Onboarding - Tools": [
      { id: "shell-navigation", title: "0x00. Shell, navigation", timeline: "2025-02-12 to 2025-02-12" },
      { id: "emacs", title: "0x02. Emacs", timeline: "2025-02-13 to 2025-02-13" },
      { id: "vi", title: "0x03. vi", timeline: "2025-02-13 to 2025-02-13" },
      { id: "git", title: "0x01. Git", timeline: "2025-02-14 to 2025-02-14" }
    ],
    "0-Day": [
      { id: "shell-navigation", title: "0x00. Shell, navigation", timeline: "2025-02-12 to 2025-02-12" },
      { id: "emacs", title: "0x01. Emacs", timeline: "2025-02-13 to 2025-02-13" },
      { id: "vagrant", title: "Setting up your local coding environment", timeline: "2025-02-14 to 2025-02-14" },
      { id: "vi", title: "0x02. vi", timeline: "2025-02-13 to 2025-02-13" },
      { id: "git", title: "0x03. Git", timeline: "2025-02-14 to 2025-02-14" },
      { id: "professional-tech", title: "0x04. Professional Technologies", timeline: "2025-02-14 to 2025-02-14" }
    ],
    "0-Day - 0-Day": [
      { id: "shell-navigation", title: "0x00. Shell, navigation", timeline: "2025-02-12 to 2025-02-12" },
      { id: "emacs", title: "0x01. Emacs", timeline: "2025-02-13 to 2025-02-13" },
      { id: "vagrant", title: "Setting up your local coding environment", timeline: "2025-02-14 to 2025-02-14" },
      { id: "vi", title: "0x02. vi", timeline: "2025-02-13 to 2025-02-13" },
      { id: "git", title: "0x03. Git", timeline: "2025-02-14 to 2025-02-14" },
      { id: "professional-tech", title: "0x04. Professional Technologies", timeline: "2025-02-22 to 2025-02-22" }
    ],
    "Low-level programming & Algorithm": [
      { id: "hello-world", title: "0x00. C - Hello, World", timeline: "2025-03-10 to 2025-03-11" },
      { id: "c-programming-first-day", title: "First Day of C Programming", timeline: "2025-03-10 to 2025-03-14" },
      { id: "variables", title: "0x01. C - Variables, if, else, while", timeline: "2025-03-13 to 2025-03-14" },
      { id: "functions", title: "0x02. C - Functions, nested loops", timeline: "2025-03-17 to 2025-03-18" },
      { id: "debugging", title: "0x03. C - Debugging", timeline: "2025-03-18 to 2025-03-21" },
      { id: "more-functions", title: "0x04. C - More functions, more nested loops", timeline: "2025-03-24 to 2025-03-25" },
      { id: "pointers", title: "0x05. C - Pointers, arrays and strings", timeline: "2025-03-24 to 2025-03-25" },
      { id: "more-pointers", title: "0x06. C - More pointers, arrays and strings", timeline: "2025-03-27 to 2025-03-28" },
      { id: "even-more-pointers", title: "0x07. C - Even more pointers, arrays and strings", timeline: "2025-03-31 to 2025-04-01" },
      { id: "recursion", title: "0x08. C - Recursion", timeline: "2025-04-02 to 2025-04-03" },
      { id: "static-libraries", title: "0x09. C - Static libraries", timeline: "2025-04-07 to 2025-04-10" },
      { id: "argc-argv", title: "0x0A. C - argc, argv", timeline: "2025-04-08 to 2025-04-11" },
      { id: "malloc", title: "0x0B. C - malloc, free", timeline: "2025-04-14 to 2025-04-16" },
      { id: "more-malloc", title: "0x0C. C - More malloc, free", timeline: "2025-04-14 to 2025-04-15" },
      { id: "preprocessor", title: "0x0D. C - Preprocessor", timeline: "2025-04-17 to 2025-04-18" },
      { id: "structures", title: "0x0E. C - Structures, typedef", timeline: "2025-04-21 to 2025-04-22" },
      { id: "function-pointers", title: "0x0F. C - Function pointers", timeline: "2025-04-22 to 2025-04-23" },
      { id: "variadic", title: "0x10. C - Variadic functions", timeline: "2025-04-28 to 2025-04-29" },
      { id: "printf", title: "0x11. C - printf", timeline: "2025-04-30 to 2025-05-05" },
      { id: "singly-linked-lists", title: "0x12. C - Singly linked lists", timeline: "2025-05-01 to 2025-05-02" },
      { id: "more-singly-linked-lists", title: "0x13. C - More singly linked lists", timeline: "2025-05-05 to 2025-05-07" },
      { id: "bit-manipulation", title: "0x14. C - Bit manipulation", timeline: "2025-05-08 to 2025-05-09" },
      { id: "file-io", title: "0x15. C - File I/O", timeline: "2025-05-12 to 2025-05-13" },
      { id: "simple-shell", title: "0x16. C - Simple Shell", timeline: "2025-05-13 to 2025-05-28" },
      { id: "doubly-linked-lists", title: "0x17. C - Doubly linked lists", timeline: "2025-05-19 to 2025-05-20" },
      { id: "dynamic-libraries", title: "0x18. C - Dynamic libraries", timeline: "2025-05-21 to 2025-05-22" },
      { id: "stack-queues", title: "0x19. C - Stacks, Queues - LIFO, FIFO", timeline: "2025-05-23 to 2025-05-26" },
      { id: "hash-tables", title: "0x1A. C - Hash tables", timeline: "2025-05-26 to 2025-05-30" },
      { id: "sorting", title: "0x1B. C - Sorting algorithms & Big O", timeline: "2025-05-27 to 2025-06-03" },
      { id: "makefiles", title: "0x1C. C - Makefiles", timeline: "2025-05-30 to 2025-06-02" },
      { id: "binary-trees", title: "0x1D. C - Binary trees", timeline: "2025-06-02 to 2025-06-06" },
      { id: "search", title: "0x1E. C - Search Algorithms", timeline: "2025-06-05 to 2025-06-06" }
    ],
    "Low-level programming & Algorithm - Hatching out": [
      { id: "hello-world", title: "0x00. C - Hello, World", timeline: "2025-03-10 to 2025-03-11" },
      { id: "c-programming-first-day", title: "First Day of C Programming", timeline: "2025-03-10 to 2025-03-14" },
      { id: "variables", title: "0x01. C - Variables, if, else, while", timeline: "2025-03-13 to 2025-03-14" },
      { id: "functions", title: "0x02. C - Functions, nested loops", timeline: "2025-03-17 to 2025-03-18" },
      { id: "debugging", title: "0x03. C - Debugging", timeline: "2025-03-18 to 2025-03-21" },
      { id: "more-functions", title: "0x04. C - More functions, more nested loops", timeline: "2025-03-24 to 2025-03-25" },
      { id: "pointers", title: "0x05. C - Pointers, arrays and strings", timeline: "2025-03-24 to 2025-03-25" },
      { id: "more-pointers", title: "0x06. C - More pointers, arrays and strings", timeline: "2025-03-27 to 2025-03-28" },
      { id: "even-more-pointers", title: "0x07. C - Even more pointers, arrays and strings", timeline: "2025-03-31 to 2025-04-01" },
      { id: "recursion", title: "0x08. C - Recursion", timeline: "2025-04-02 to 2025-04-03" },
      { id: "static-libraries", title: "0x09. C - Static libraries", timeline: "2025-04-07 to 2025-04-10" },
      { id: "argc-argv", title: "0x0A. C - argc, argv", timeline: "2025-04-08 to 2025-04-11" },
      { id: "malloc", title: "0x0B. C - malloc, free", timeline: "2025-04-14 to 2025-04-16" },
      { id: "more-malloc", title: "0x0C. C - More malloc, free", timeline: "2025-04-14 to 2025-04-15" },
      { id: "preprocessor", title: "0x0D. C - Preprocessor", timeline: "2025-04-17 to 2025-04-18" },
      { id: "structures", title: "0x0E. C - Structures, typedef", timeline: "2025-04-21 to 2025-04-22" },
      { id: "function-pointers", title: "0x0F. C - Function pointers", timeline: "2025-04-22 to 2025-04-23" },
      { id: "variadic", title: "0x10. C - Variadic functions", timeline: "2025-04-28 to 2025-04-29" },
      { id: "printf", title: "0x11. C - printf", timeline: "2025-04-30 to 2025-05-05" },
      { id: "bit-manipulation", title: "0x14. C - Bit manipulation", timeline: "2025-05-08 to 2025-05-09" },
      { id: "dynamic-libraries", title: "0x18. C - Dynamic libraries", timeline: "2025-05-21 to 2025-05-22" },
      { id: "makefiles", title: "0x1C. C - Makefiles", timeline: "2025-05-30 to 2025-06-02" }
    ],
    "Low-level programming & Algorithm - Data structures and Algorithms": [
      { id: "singly-linked-lists", title: "0x12. C - Singly linked lists", timeline: "2025-05-01 to 2025-05-02" },
      { id: "more-singly-linked-lists", title: "0x13. C - More singly linked lists", timeline: "2025-05-05 to 2025-05-07" },
      { id: "bit-manipulation", title: "0x14. C - Bit manipulation", timeline: "2025-05-08 to 2025-05-09" },
      { id: "doubly-linked-lists", title: "0x17. C - Doubly linked lists", timeline: "2025-05-19 to 2025-05-20" },
      { id: "dynamic-libraries", title: "0x18. C - Dynamic libraries", timeline: "2025-05-21 to 2025-05-22" },
      { id: "stack-queues", title: "0x19. C - Stacks, Queues - LIFO, FIFO", timeline: "2025-05-23 to 2025-05-26" },
      { id: "hash-tables", title: "0x1A. C - Hash tables", timeline: "2025-05-26 to 2025-05-30" },
      { id: "sorting", title: "0x1B. C - Sorting algorithms & Big O", timeline: "2025-05-27 to 2025-06-03" },
      { id: "makefiles", title: "0x1C. C - Makefiles", timeline: "2025-05-30 to 2025-06-02" },
      { id: "binary-trees", title: "0x1D. C - Binary trees", timeline: "2025-06-02 to 2025-06-06" },
      { id: "search", title: "0x1E. C - Search Algorithms", timeline: "2025-06-05 to 2025-06-06" }
    ],
    "Low-level programming & Algorithm - Linux and Unix system programming": [
      { id: "file-io", title: "0x15. C - File I/O", timeline: "2025-05-12 to 2025-05-13" },
      { id: "simple-shell", title: "0x16. C - Simple Shell", timeline: "2025-05-13 to 2025-05-28" }
    ],
    "Higher-level programming": [
      { id: "python-hello", title: "0x00. Python - Hello, World", timeline: "2025-06-09 to 2025-06-10" },
      { id: "python-if-else", title: "0x01. Python - if/else, loops, functions", timeline: "2025-06-11 to 2025-06-12" },
      { id: "python-import", title: "0x02. Python - import & modules", timeline: "2025-06-16 to 2025-06-17" },
      { id: "python-data-structures", title: "0x03. Python - Data Structures: Lists, Tuples", timeline: "2025-06-19 to 2025-06-23" },
      { id: "python-more-data", title: "0x04. Python - More Data Structures: Set, Dictionary", timeline: "2025-06-23 to 2025-06-24" },
      { id: "python-exceptions", title: "0x05. Python - Exceptions", timeline: "2025-06-23 to 2025-06-24" },
      { id: "python-classes", title: "0x06. Python - Classes and Objects", timeline: "2025-06-26 to 2025-06-27" },
      { id: "python-test-driven", title: "0x07. Python - Test-driven development", timeline: "2025-07-01 to 2025-07-07" },
      { id: "python-more-classes", title: "0x08. Python - More Classes and Objects", timeline: "2025-07-01 to 2025-07-02" },
      { id: "python-everything-object", title: "0x09. Python - Everything is object", timeline: "2025-07-07 to 2025-07-08" },
      { id: "python-inheritance", title: "0x0A. Python - Inheritance", timeline: "2025-07-07 to 2025-07-08" },
      { id: "python-input-output", title: "0x0B. Python - Input/Output", timeline: "2025-07-10 to 2025-07-11" },
      { id: "python-almost-circle", title: "0x0C. Python - Almost a circle", timeline: "2025-07-16 to 2025-07-21" },
      { id: "airbnb-clone-console", title: "0x00. AirBnB clone - The console", timeline: "2025-07-28 to 2025-08-05" },
      { id: "sql-introduction", title: "0x0D. SQL - Introduction", timeline: "2025-05-21 to 2025-05-22" },
      { id: "sql-more-queries", title: "0x0E. SQL - More queries", timeline: "2025-05-22 to 2025-05-23" },
      { id: "airbnb-clone-web-static", title: "0x01. AirBnB clone - Web static", timeline: "2025-07-30 to 2025-08-04" },
      { id: "javascript-warm-up", title: "0x12. JavaScript - Warm up", timeline: "2025-06-17 to 2025-06-18" },
      { id: "javascript-objects", title: "0x13. JavaScript - Objects, Scopes and Closures", timeline: "2025-06-18 to 2025-06-19" },
      { id: "python-object-relational", title: "0x0F. Python - Object-relational mapping", timeline: "2025-07-17 to 2025-07-21" },
      { id: "airbnb-clone-mysql", title: "0x02. AirBnB clone - MySQL", timeline: "2025-07-31 to 2025-08-07" },
      { id: "python-network-0", title: "0x10. Python - Network #0", timeline: "2025-07-21 to 2025-07-22" },
      { id: "python-network-1", title: "0x11. Python - Network #1", timeline: "2025-07-22 to 2025-07-23" },
      { id: "airbnb-clone-deploy", title: "0x03. AirBnB clone - Deploy static", timeline: "2025-08-04 to 2025-08-06" },
      { id: "airbnb-clone-web-framework", title: "0x04. AirBnB clone - Web framework", timeline: "2025-08-06 to 2025-08-11" },
      { id: "javascript-web-scraping", title: "0x14. JavaScript - Web scraping", timeline: "2025-07-30 to 2025-07-31" },
      { id: "airbnb-clone-restful", title: "0x05. AirBnB clone - RESTful API", timeline: "2025-08-11 to 2025-08-12" },
      { id: "javascript-web-jquery", title: "0x15. JavaScript - Web jQuery", timeline: "2025-08-07 to 2025-08-08" },
      { id: "airbnb-clone-web-dynamic", title: "0x06. AirBnB clone - Web dynamic", timeline: "2025-08-13 to 2025-08-18" }
    ],
    "Higher-level programming - Python": [
      { id: "python-hello-world", title: "0x00. Python - Hello, World", timeline: "2025-06-09 to 2025-06-10" },
      { id: "python-if-else", title: "0x01. Python - if/else, loops, functions", timeline: "2025-06-11 to 2025-06-12" },
      { id: "python-import", title: "0x02. Python - import & modules", timeline: "2025-06-16 to 2025-06-17" },
      { id: "python-data-structures", title: "0x03. Python - Data Structures: Lists, Tuples", timeline: "2025-06-19 to 2025-06-23" },
      { id: "python-more-data", title: "0x04. Python - More Data Structures: Set, Dictionary", timeline: "2025-06-23 to 2025-06-24" },
      { id: "python-exceptions", title: "0x05. Python - Exceptions", timeline: "2025-06-23 to 2025-06-24" },
      { id: "python-classes", title: "0x06. Python - Classes and Objects", timeline: "2025-06-26 to 2025-06-27" },
      { id: "python-test-driven", title: "0x07. Python - Test-driven development", timeline: "2025-07-01 to 2025-07-07" },
      { id: "python-more-classes", title: "0x08. Python - More Classes and Objects", timeline: "2025-07-01 to 2025-07-02" },
      { id: "python-everything-object", title: "0x09. Python - Everything is object", timeline: "2025-07-07 to 2025-07-08" },
      { id: "python-inheritance", title: "0x0A. Python - Inheritance", timeline: "2025-07-07 to 2025-07-07" },
      { id: "python-input-output", title: "0x0B. Python - Input/Output", timeline: "2025-07-10 to 2025-07-11" },
      { id: "python-almost-circle", title: "0x0C. Python - Almost a circle", timeline: "2025-07-16 to 2025-07-21" },
      { id: "python-object-relational", title: "0x0F. Python - Object-relational mapping", timeline: "2025-07-17 to 2025-07-21" },
      { id: "python-network-0", title: "0x10. Python - Network #0", timeline: "2025-07-21 to 2025-07-22" },
      { id: "python-network-1", title: "0x11. Python - Network #1", timeline: "2025-07-22 to 2025-07-23" }
    ],
    "Higher-level programming - AirBnB clone": [
      { id: "airbnb-clone-console", title: "0x00. AirBnB clone - The console", timeline: "2025-07-28 to 2025-08-05" },
      { id: "airbnb-clone-web-static", title: "0x01. AirBnB clone - Web static", timeline: "2025-07-30 to 2025-08-04" },
      { id: "airbnb-clone-mysql", title: "0x02. AirBnB clone - MySQL", timeline: "2025-07-31 to 2025-08-07" },
      { id: "airbnb-clone-deploy", title: "0x03. AirBnB clone - Deploy static", timeline: "2025-08-04 to 2025-08-06" },
      { id: "airbnb-clone-web-framework", title: "0x04. AirBnB clone - Web framework", timeline: "2025-08-06 to 2025-08-11" },
      { id: "airbnb-clone-restful", title: "0x05. AirBnB clone - RESTful API", timeline: "2025-08-11 to 2025-08-12" },
      { id: "airbnb-clone-web-dynamic", title: "0x06. AirBnB clone - Web dynamic", timeline: "2025-08-13 to 2025-08-18" }
    ],
    "System engineering & DevOps": [
      { id: "shell-basics", title: "0x00. Shell, basics", timeline: "2025-02-26 to 2025-02-27" },
      { id: "shell-permissions", title: "0x01. Shell, permissions", timeline: "2025-03-03 to 2025-03-04" },
      { id: "shell-redirections", title: "0x02. Shell, I/O Redirections and filters", timeline: "2025-03-04 to 2025-03-05" },
      { id: "shell-variables", title: "0x03. Shell, init files, variables and expansions", timeline: "2025-08-18 to 2025-08-20" },
      { id: "shell-loops", title: "0x04. Loops, conditions and parsing", timeline: "2025-08-18 to 2025-08-19" },
      { id: "shell-processes", title: "0x05. Processes and signals", timeline: "2025-08-20 to 2025-08-21" },
      { id: "shell-regex", title: "0x06. Regular expression", timeline: "2025-08-25 to 2025-08-26" },
      { id: "networking-basics-0", title: "0x07. Networking basics #0", timeline: "2025-08-26 to 2025-08-28" },
      { id: "networking-basics-1", title: "0x08. Networking basics #1", timeline: "2025-09-01 to 2025-09-03" },
      { id: "web-infrastructure", title: "0x09. Web infrastructure design", timeline: "2025-09-01 to 2025-09-05" },
      { id: "configuration-management", title: "0x0A. Configuration management", timeline: "2025-09-04 to 2025-09-05" },
      { id: "ssh", title: "0x0B. SSH", timeline: "2025-09-08 to 2025-09-11" },
      { id: "web-stack-debugging-0", title: "0x0D. Web stack debugging #0", timeline: "2025-09-09 to 2025-09-11" },
      { id: "web-server", title: "0x0C. Web server", timeline: "2025-09-15 to 2025-09-17" },
      { id: "load-balancer", title: "0x0F. Load balancer", timeline: "2025-09-15 to 2025-09-16" },
      { id: "web-stack-debugging-1", title: "0x0E. Web stack debugging #1", timeline: "2025-09-18 to 2025-09-23" },
      { id: "https-ssl", title: "0x10. HTTPS SSL", timeline: "2025-09-22 to 2025-09-23" },
      { id: "what-happens-url", title: "0x11. What happens when you type google.com in your browser and press Enter", timeline: "2025-09-24 to 2025-10-02" },
      { id: "firewall", title: "0x13. Firewall", timeline: "2025-09-29 to 2025-09-30" },
      { id: "web-stack-debugging-2", title: "0x12. Web stack debugging #2", timeline: "2025-09-30 to 2025-10-02" },
      { id: "mysql", title: "0x14. MySQL", timeline: "2025-10-06 to 2025-10-07" },
      { id: "api", title: "0x15. API", timeline: "2025-10-06 to 2025-10-07" },
      { id: "postmortem", title: "0x19. Postmortem", timeline: "2025-10-10 to 2025-10-13" },
      { id: "api-advanced", title: "0x16. API advanced", timeline: "2025-10-13 to 2025-10-14" },
      { id: "web-stack-debugging-3", title: "0x17. Web stack debugging #3", timeline: "2025-10-14 to 2025-10-17" },
      { id: "webstack-monitoring", title: "0x18. Webstack monitoring", timeline: "2025-10-17 to 2025-10-20" },
      { id: "application-server", title: "0x1A. Application server", timeline: "2025-10-20 to 2025-10-24" },
      { id: "web-stack-debugging-4", title: "0x1B. Web stack debugging #4", timeline: "2025-10-23 to 2025-10-27" }
    ],
    "System engineering & DevOps - Bash": [
      { id: "shell-basics", title: "0x00. Shell, basics", timeline: "2025-02-26 to 2025-02-27" },
      { id: "shell-permissions", title: "0x01. Shell, permissions", timeline: "2025-03-03 to 2025-03-04" },
      { id: "shell-redirections", title: "0x02. Shell, I/O Redirections and filters", timeline: "2025-03-04 to 2025-03-05" },
      { id: "shell-variables", title: "0x03. Shell, init files, variables and expansions", timeline: "2025-08-18 to 2025-08-20" },
      { id: "shell-loops", title: "0x04. Loops, conditions and parsing", timeline: "2025-08-18 to 2025-08-19" },
      { id: "shell-processes", title: "0x05. Processes and signals", timeline: "2025-08-20 to 2025-08-21" }
    ],
    "System engineering & DevOps - Web stack": [
      { id: "web-infrastructure", title: "0x09. Web infrastructure design", timeline: "2025-09-01 to 2025-09-05" },
      { id: "web-server", title: "0x0C. Web server", timeline: "2025-09-15 to 2025-09-17" },
      { id: "load-balancer", title: "0x0F. Load balancer", timeline: "2025-09-15 to 2025-09-16" },
      { id: "https-ssl", title: "0x10. HTTPS SSL", timeline: "2025-09-22 to 2025-09-23" },
      { id: "what-happens-url", title: "0x11. What happens when you type google.com in your browser and press Enter", timeline: "2025-09-24 to 2025-10-02" },
      { id: "mysql", title: "0x14. MySQL", timeline: "2025-10-06 to 2025-10-07" },
      { id: "webstack-monitoring", title: "0x18. Webstack monitoring", timeline: "2025-10-17 to 2025-10-20" },
      { id: "application-server", title: "0x1A. Application server", timeline: "2025-10-20 to 2025-10-24" }
    ],
    "More!": [
      { id: "rsa-factoring", title: "RSA Factoring Challenge", timeline: "2025-10-27 to 2025-11-10" },
      { id: "command-line-win", title: "Command line for the win", timeline: "2025-10-29 to 2025-11-13" },
      { id: "fix-my-code-0", title: "0x00. Fix my code", timeline: "2025-11-03 to 2025-11-17" },
      { id: "attack-is-best-defense", title: "Attack is the best defense", timeline: "2025-11-04 to 2025-11-19" },
      { id: "fix-my-code-1", title: "0x01. Fix my code", timeline: "2025-11-07 to 2025-11-21" }
    ],
    "More! - More!": [
      { id: "rsa-factoring", title: "RSA Factoring Challenge", timeline: "2025-10-27 to 2025-11-10" },
      { id: "command-line-win", title: "Command line for the win", timeline: "2025-10-29 to 2025-11-13" },
      { id: "fix-my-code-0", title: "0x00. Fix my code", timeline: "2025-11-03 to 2025-11-17" },
      { id: "attack-is-best-defense", title: "Attack is the best defense", timeline: "2025-11-04 to 2025-11-19" },
      { id: "fix-my-code-1", title: "0x01. Fix my code", timeline: "2025-11-07 to 2025-11-21" }
    ],
    "Portfolio Project": [
      { id: "portfolio-research-1", title: "Research & Project approval (Part 1)", timeline: "2025-11-10 to 2025-11-11" },
      { id: "portfolio-research-2", title: "Research & Project approval (Part 2)", timeline: "2025-11-13 to 2025-11-20" },
      { id: "portfolio-research-3", title: "Research & Project approval (Part 3)", timeline: "2025-11-17 to 2025-11-24" },
      { id: "portfolio-week-1", title: "Build your portfolio project (Week 1): Making Progress", timeline: "2025-11-18 to 2025-12-16" },
      { id: "portfolio-week-2", title: "Build your portfolio project (Week 2): MVP Complete", timeline: "2025-11-21 to 2025-12-17" },
      { id: "portfolio-landing-page", title: "Build your portfolio project (Week 3): Project Landing Page", timeline: "2025-11-24 to 2025-12-01" },
      { id: "portfolio-presentation", title: "Build your portfolio project (Week 3): Presentation", timeline: "2025-11-27 to 2025-12-17" },
      { id: "portfolio-cleanup", title: "Cleanup your Portfolio Project", timeline: "2025-12-02 to 2025-12-15" },
      { id: "portfolio-blog-post", title: "Portfolio Project Blog post", timeline: "2025-12-03 to 2025-12-16" }
    ],
    "Portfolio Project - Portfolio Project": [
      { id: "portfolio-research-1", title: "Research & Project approval (Part 1)", timeline: "2025-11-10 to 2025-11-11" },
      { id: "portfolio-research-2", title: "Research & Project approval (Part 2)", timeline: "2025-11-13 to 2025-11-20" },
      { id: "portfolio-research-3", title: "Research & Project approval (Part 3)", timeline: "2025-11-17 to 2025-11-24" },
      { id: "portfolio-week-1", title: "Build your portfolio project (Week 1): Making Progress", timeline: "2025-11-18 to 2025-12-16" },
      { id: "portfolio-week-2", title: "Build your portfolio project (Week 2): MVP Complete", timeline: "2025-11-21 to 2025-12-17" },
      { id: "portfolio-landing-page", title: "Build your portfolio project (Week 3): Project Landing Page", timeline: "2025-11-24 to 2025-12-01" },
      { id: "portfolio-presentation", title: "Build your portfolio project (Week 3): Presentation", timeline: "2025-11-27 to 2025-12-17" },
      { id: "portfolio-cleanup", title: "Cleanup your Portfolio Project", timeline: "2025-12-02 to 2025-12-15" },
      { id: "portfolio-blog-post", title: "Portfolio Project Blog post", timeline: "2025-12-03 to 2025-12-16" }
    ]
  };

  const toggleSection = (sectionName) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  };

  const expandAll = () => {
    const allExpanded = {};
    Object.keys(sections).forEach(section => {
      allExpanded[section] = true;
    });
    setExpandedSections(allExpanded);
  };

  const collapseAll = () => {
    setExpandedSections({});
  };

  return (
    <div className="project-page">
      <div className="my-projects">
        <FiGitBranch className="my-projects-icon" />
        {getTodayProjects().length > 0 ? (
          <div className="current-projects">
            <h3>Today's Projects</h3>
            <div className="project-grid">
              {getTodayProjects().map(project => (
                <Link
                  key={project.id}
                  to={`/projects/${project.id}`}
                  className="project-card"
                >
                  <h4 className="project-title">{project.title}</h4>
                  <div className="project-timeline">{project.timeline}</div>
                </Link>
              ))}
            </div>
          </div>
        ) : (
          <p className="no-projects-text">No current project</p>
        )}
      </div>

      <div className="all-projects">
        <div className="all-projects-header">
          <h2>All projects</h2>
          <div className="expand-collapse-buttons">
            <button onClick={expandAll}>Expand all</button>
            <button onClick={collapseAll}>Collapse all</button>
          </div>
        </div>

        <div className="sections">
          {/* Onboarding Group */}
          <div className="project-group">
            {["Onboarding", "Onboarding - Getting started", "Onboarding - Tools"].map(sectionName => (
            <div 
              key={sectionName} 
              className={"section" + (expandedSections[sectionName] ? " expanded" : "")}
            >
              <div 
                className="section-header" 
                onClick={() => toggleSection(sectionName)}
              >
                <h3>{sectionName}</h3>
                <span className="expand-icon">
                  {expandedSections[sectionName] ? "▼" : "▶"}
              </span>
            </div>

              {expandedSections[sectionName] && (
                <div className="project-grid">
                    {sections[sectionName].map(project => (
                    <Link
                      key={project.id}
                      to={`/projects/${project.id}`}
                      className="project-card"
                    >
                        <h4 className="project-title">{project.title}</h4>
                        {project.timeline && (
                          <div className="project-timeline">{project.timeline}</div>
                        )}
                    </Link>
                ))}
              </div>
              )}
            </div>
        ))}
          </div>

          {/* 0-Day Group */}
          <div className="project-group">
            {["0-Day", "0-Day - 0-Day"].map(sectionName => (
              <div 
                key={sectionName} 
                className={"section" + (expandedSections[sectionName] ? " expanded" : "")}
              >
                <div 
                  className="section-header" 
                  onClick={() => toggleSection(sectionName)}
                >
                  <h3>{sectionName}</h3>
                  <span className="expand-icon">
                    {expandedSections[sectionName] ? "▼" : "▶"}
                  </span>
                </div>

                {expandedSections[sectionName] && (
                  <div className="project-grid">
                    {sections[sectionName].map(project => (
                      <Link
                        key={project.id}
                        to={`/projects/${project.id}`}
                        className="project-card"
                      >
                        <h4 className="project-title">{project.title}</h4>
                        {project.timeline && (
                          <div className="project-timeline">{project.timeline}</div>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Low-level programming Group */}
          <div className="project-group">
            {[
              "Low-level programming & Algorithm",
              "Low-level programming & Algorithm - Hatching out",
              "Low-level programming & Algorithm - Data structures and Algorithms",
              "Low-level programming & Algorithm - Linux and Unix system programming"
            ].map(sectionName => (
              <div 
                key={sectionName} 
                className={"section" + (expandedSections[sectionName] ? " expanded" : "")}
              >
                <div 
                  className="section-header" 
                  onClick={() => toggleSection(sectionName)}
                >
                  <h3>{sectionName}</h3>
                  <span className="expand-icon">
                    {expandedSections[sectionName] ? "▼" : "▶"}
                  </span>
                </div>

                {expandedSections[sectionName] && (
                  <div className="project-grid">
                    {sections[sectionName].map(project => (
                      <Link
                        key={project.id}
                        to={`/projects/${project.id}`}
                        className="project-card"
                      >
                        <h4 className="project-title">{project.title}</h4>
                        {project.timeline && (
                          <div className="project-timeline">{project.timeline}</div>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Higher-level programming Group */}
          <div className="project-group">
            {[
              "Higher-level programming",
              "Higher-level programming - Python",
              "Higher-level programming - AirBnB clone"
            ].map(sectionName => (
              <div 
                key={sectionName} 
                className={"section" + (expandedSections[sectionName] ? " expanded" : "")}
              >
                <div 
                  className="section-header" 
                  onClick={() => toggleSection(sectionName)}
                >
                  <h3>{sectionName}</h3>
                  <span className="expand-icon">
                    {expandedSections[sectionName] ? "▼" : "▶"}
                  </span>
                </div>

                {expandedSections[sectionName] && (
                  <div className="project-grid">
                    {sections[sectionName].map(project => (
                      <Link
                        key={project.id}
                        to={`/projects/${project.id}`}
                        className="project-card"
                      >
                        <h4 className="project-title">{project.title}</h4>
                        {project.timeline && (
                          <div className="project-timeline">{project.timeline}</div>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* System engineering & DevOps Group */}
          <div className="project-group">
            {[
              "System engineering & DevOps",
              "System engineering & DevOps - Bash",
              "System engineering & DevOps - Web stack"
            ].map(sectionName => (
              <div 
                key={sectionName} 
                className={"section" + (expandedSections[sectionName] ? " expanded" : "")}
              >
                <div 
                  className="section-header" 
                  onClick={() => toggleSection(sectionName)}
                >
                  <h3>{sectionName}</h3>
                  <span className="expand-icon">
                    {expandedSections[sectionName] ? "▼" : "▶"}
                  </span>
                </div>

                {expandedSections[sectionName] && (
                  <div className="project-grid">
                    {sections[sectionName].map(project => (
                      <Link
                        key={project.id}
                        to={`/projects/${project.id}`}
                        className="project-card"
                      >
                        <h4 className="project-title">{project.title}</h4>
                        {project.timeline && (
                          <div className="project-timeline">{project.timeline}</div>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* More! Group */}
          <div className="project-group">
            {[
              "More!",
              "More! - More!"
            ].map(sectionName => (
              <div 
                key={sectionName} 
                className={"section" + (expandedSections[sectionName] ? " expanded" : "")}
              >
                <div 
                  className="section-header" 
                  onClick={() => toggleSection(sectionName)}
                >
                  <h3>{sectionName}</h3>
                  <span className="expand-icon">
                    {expandedSections[sectionName] ? "▼" : "▶"}
                  </span>
                </div>

                {expandedSections[sectionName] && (
                  <div className="project-grid">
                    {sections[sectionName].map(project => (
                      <Link
                        key={project.id}
                        to={`/projects/${project.id}`}
                        className="project-card"
                      >
                        <h4 className="project-title">{project.title}</h4>
                        {project.timeline && (
                          <div className="project-timeline">{project.timeline}</div>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Portfolio Project Group */}
          <div className="project-group">
            {[
              "Portfolio Project",
              "Portfolio Project - Portfolio Project"
            ].map(sectionName => (
              <div 
                key={sectionName} 
                className={"section" + (expandedSections[sectionName] ? " expanded" : "")}
              >
                <div 
                  className="section-header" 
                  onClick={() => toggleSection(sectionName)}
                >
                  <h3>{sectionName}</h3>
                  <span className="expand-icon">
                    {expandedSections[sectionName] ? "▼" : "▶"}
                  </span>
                </div>

                {expandedSections[sectionName] && (
                  <div className="project-grid">
                    {sections[sectionName].map(project => (
                      <Link
                        key={project.id}
                        to={`/projects/${project.id}`}
                        className="project-card"
                      >
                        <h4 className="project-title">{project.title}</h4>
                        {project.timeline && (
                          <div className="project-timeline">{project.timeline}</div>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProjectPage;
