.not-found {
  min-height: calc(100vh - 40px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  margin: -20px;
  background: white;
}

.not-found .content {
  text-align: center;
  max-width: 500px;
  padding: 40px;
  border-radius: 12px;
  background: white;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.error-code {
  font-size: 6rem;
  font-weight: 800;
  color: #3b82f6;
  line-height: 1;
  text-shadow: 2px 2px 0 #bfdbfe;
  margin-bottom: 16px;
  font-family: monospace;
  letter-spacing: -4px;
}

.not-found h1 {
  font-size: 2rem;
  color: #1e293b;
  margin-bottom: 12px;
  font-weight: 700;
}

.not-found p {
  font-size: 1.1rem;
  color: #64748b;
  margin-bottom: 32px;
  line-height: 1.6;
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.home-link,
.back-button {
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.home-link {
  background: #3b82f6;
  color: white;
  border: none;
}

.home-link:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.back-button {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.back-button:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  .not-found {
    background: #1e293b;
  }

  .not-found .content {
    background: #1e293b;
  }

  .error-code {
    color: #60a5fa;
    text-shadow: 2px 2px 0 #1e40af;
  }

  .not-found h1 {
    color: #f8fafc;
  }

  .not-found p {
    color: #94a3b8;
  }

  .home-link {
    background: #2563eb;
  }

  .home-link:hover {
    background: #1d4ed8;
  }

  .back-button {
    background: #334155;
    color: #e2e8f0;
    border-color: #475569;
  }

  .back-button:hover {
    background: #475569;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .not-found {
    margin: -12px;
  }

  .not-found .content {
    padding: 24px;
  }

  .error-code {
    font-size: 4rem;
  }

  .not-found h1 {
    font-size: 1.75rem;
  }

  .not-found p {
    font-size: 1rem;
  }

  .actions {
    flex-direction: column;
    gap: 12px;
  }

  .home-link,
  .back-button {
    width: 100%;
  }
}
