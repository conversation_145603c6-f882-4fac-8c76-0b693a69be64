# TaskContentContainer

A React component that provides consistent text formatting and spacing for task content.

## Features

- Moderate paragraph spacing
- Proper list indentation
- Code block formatting
- Inline code styling
- Blockquote formatting

## Usage

Import and use the component to wrap task content:

```jsx
import TaskContentContainer from './TaskContentContainer';
import TaskContainer from './TaskContainer';

// Basic usage inside a task
<TaskContainer isTaskHeading title="Example Task">
  <TaskContentContainer>
    <p>First paragraph with proper spacing.</p>
    <p>Second paragraph with controlled spacing between paragraphs.</p>
    
    <ol>
      <li>Properly indented numbered list</li>
      <li>With consistent spacing</li>
    </ol>

    <pre><code>
    # Code block with proper formatting
    def example():
        return "Hello World"
    </code></pre>

    <p>Text with <code>inline code</code> properly styled.</p>

    <blockquote>
      Blockquote with proper formatting and indentation
    </blockquote>
  </TaskContentContainer>
</TaskContainer>
```

## Props

| Prop      | Type      | Description                                    |
|-----------|-----------|------------------------------------------------|
| children  | ReactNode | Content to be formatted                        |
| className | string?   | Optional additional classes for customization  |

## Styling Notes

1. Paragraph Spacing
   - Moderate spacing (0.5rem) between paragraphs
   - Normal line height for better readability

2. List Formatting
   - 1.5rem left padding for both ordered and unordered lists
   - 0.125rem vertical spacing between list items

3. Code Formatting
   - Light gray background for code blocks and inline code
   - Proper padding and rounded corners
   - Overflow handling for long code blocks

4. Blockquotes
   - Left border for visual distinction
   - Subtle indentation
   - Italic text style