/* Social Inputs */
.social-inputs {
  margin: 1rem 0;
  padding: 1.5rem;
  /* background: #f8fafc; */ /* Handled by Tailwind bg-card */
  border-radius: 8px; /* Handled by Tailwind rounded-lg */
  /* border: 1px solid #e2e8f0; */ /* Handled by Tailwind border-border */
}

.input-group {
  margin-bottom: 1rem;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-group label {
  display: block;
  margin-bottom: 0.5rem;
  /* color: #475569; */ /* Handled by Tailwind text-muted-foreground */
  font-size: 0.95rem; /* Tailwind: text-sm or text-base */
}

.input-group input {
  width: 100%; /* Handled by Tail<PERSON> w-full */
  padding: 0.75rem; /* Handled by Tailwind p-3 */
  font-size: 0.95rem; /* Tailwind: text-sm or text-base */
  /* border: 1px solid #e2e8f0; */ /* Handled by Tailwind border-border */
  /* border-radius: 6px; */ /* Handled by Tailwind rounded-md */
  /* background: white; */ /* Handled by Tailwind bg-input */
  /* color: #1e293b; */ /* Handled by Tailwind text-text */
  transition: all 0.2s ease; /* Tailwind: transition-all duration-200 ease-in-out */
}

.input-group input:focus {
  outline: none; /* Tailwind: focus:outline-none */
  /* border-color: #60a5fa; */ /* Tailwind: focus:border-primary */
  /* box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1); */ /* Tailwind: focus:ring-2 focus:ring-primary/50 */
}

.input-group input::placeholder {
  /* color: #94a3b8; */ /* Handled by Tailwind placeholder:text-muted-foreground */
}


/* Image styling */
.inline-image {
  display: block;
  max-width: 75%;
  height: auto;
  margin: 1rem auto;
  border-radius: 0.5rem; /* Tailwind: rounded-lg */
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); /* Tailwind: shadow-md */
}

/* Video container styling */
.relative > iframe {
  max-width: 75% !important; /* Keep if specific override needed beyond Tailwind's max-w- */
  margin: 0 auto !important;
  display: block !important;
}

.relative {
  text-align: center; /* Tailwind: text-center */
}

/* Responsive */
@media (max-width: 768px) {
  .inline-image,
  .relative > iframe {
    max-width: 100% !important; /* Keep if specific override needed */
  }
}
