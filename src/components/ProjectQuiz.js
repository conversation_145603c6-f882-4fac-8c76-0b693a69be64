import React, { useState } from "react";
import { cn } from "../lib/utils";
import ReactMarkdown from 'react-markdown';
import { processMarkdown, markdownConfig } from '../utils/markdownProcessor';
import "./ProjectQuiz.css";

function ProjectQuiz({ questions }) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [answers, setAnswers] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [score, setScore] = useState(0);
  const [allCorrect, setAllCorrect] = useState(false);

  const renderText = (text) => {
    return (
      <ReactMarkdown
        {...markdownConfig}
        components={{
          ...markdownConfig.components,
          // Override paragraph styling for quiz context
          p: ({ children, ...props }) => (
            <span className="inline" {...props}>
              {children}
            </span>
          ),
          // Ensure code blocks are properly styled in quiz context
          code: ({ node, inline, className, children, ...props }) => {
            const match = /language-(\w+)/.exec(className || '');
            if (!inline && match) {
              // Custom styling for code blocks in quiz context
              const SyntaxHighlighter = require('react-syntax-highlighter').Prism;
              const { materialOceanic } = require('react-syntax-highlighter/dist/esm/styles/prism');
              
              return (
                <SyntaxHighlighter
                  style={materialOceanic}
                  language={match[1]}
                  PreTag="div"
                  customStyle={{
                    margin: '0.5rem -1rem 0.5rem 0',
                    padding: '0.5rem',
                    backgroundColor: '#282828 !important',
                    color: '#ebdbb2 !important',
                    borderRadius: '0.5rem',
                    border: 'none',
                    boxShadow: 'none',
                    width: 'calc(100% + 1rem)',
                    maxWidth: 'calc(100% + 1rem)',
                    wordBreak: 'break-word',
                    whiteSpace: 'pre-wrap',
                    overflowWrap: 'break-word',
                    fontFamily: 'monospace !important'
                  }}
                  showLineNumbers={false}
                  wrapLines={true}
                  wrapLongLines={true}
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              );
            }
            return (
              <code className="bg-gray-50 dark:bg-gray-900/50 text-red-600 dark:text-red-400 px-1 py-0.5 rounded text-sm font-mono" {...props}>
                {children}
              </code>
            );
          }
        }}
      >
        {processMarkdown(text)}
      </ReactMarkdown>
    );
  };

  const handleAnswerChange = (questionIndex, answer, isMultiChoice) => {
    setAnswers(prev => {
      const newAnswers = { ...prev };
      if (isMultiChoice) {
        if (!Array.isArray(prev[questionIndex])) {
          newAnswers[questionIndex] = [answer];
        } else {
          const currentAnswers = prev[questionIndex];
          if (currentAnswers.includes(answer)) {
            newAnswers[questionIndex] = currentAnswers.filter(a => a !== answer);
          } else {
            newAnswers[questionIndex] = [...currentAnswers, answer];
          }
        }
      } else {
        newAnswers[questionIndex] = answer;
      }
      return newAnswers;
    });
  };

  const toggleQuiz = () => {
    setIsCollapsed(prev => !prev);
  };

  const isAnswered = (questionIndex) => {
    const answer = answers[questionIndex];
    if (Array.isArray(answer)) {
      return answer.length > 0;
    }
    return !!answer;
  };

  const isQuestionCorrect = (questionIndex) => {
    const question = questions[questionIndex];
    const userAnswer = answers[questionIndex];
    
    if (question.isMulti) {
      return (
        Array.isArray(userAnswer) &&
        Array.isArray(question.correctAnswer) &&
        userAnswer.length === question.correctAnswer.length &&
        userAnswer.every(ans => question.correctAnswer.includes(ans))
      );
    } else {
      return userAnswer === question.correctAnswer;
    }
  };

  const handleSubmit = () => {
    let correctCount = 0;
    questions.forEach((question, index) => {
      if (isQuestionCorrect(index)) {
        correctCount++;
      }
    });

    setScore(correctCount);
    setAllCorrect(correctCount === questions.length);
    setIsSubmitted(true);
  };

  const resetQuiz = () => {
    setAnswers({});
    setIsSubmitted(false);
    setScore(0);
    setAllCorrect(false);
  };

  if (!questions || questions.length === 0) {
    return null;
  }

  return (
    <div className="mt-6">
      {(!isCollapsed || !allCorrect) && (
        <>
          <div className="space-y-4">
            {questions.map((question, index) => {
              const isMulti = question.isMulti || false;
              const currentAnswer = answers[index] || (isMulti ? [] : "");
              return (
                <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  {/* Question Header */}
                  <div className="bg-gray-50 dark:bg-gray-800/30 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <p className="font-semibold text-gray-600 dark:text-gray-400">
                      Question {index + 1}: {renderText(question.question)}
                    </p>
                    {question.tips && (
                      <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                        <div className="text-sm text-blue-800 dark:text-blue-200">
                          {renderText(question.tips)}
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-4 space-y-3">
                    {question.options.map((option, optionIndex) => (
                      <label
                        key={optionIndex}
                        className="flex items-center space-x-3 cursor-pointer"
                      >
                        <input
                          type={isMulti ? "checkbox" : "radio"}
                          name={`question-${index}`}
                          checked={
                            isMulti
                              ? Array.isArray(currentAnswer) && currentAnswer.includes(option)
                              : currentAnswer === option
                          }
                          onChange={() => handleAnswerChange(index, option, isMulti)}
                          className="quiz-input"
                        />
                        <span className="text-gray-700 dark:text-gray-300">
                          {renderText(option)}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
    
          <div className="flex justify-end mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            {!isSubmitted && (
              <div className="flex flex-col items-end gap-2">
                <button
                  onClick={handleSubmit}
                  disabled={!questions.every((_, index) => isAnswered(index))}
                  className={cn(
                    "inline-flex items-center gap-2 px-6 py-2 font-medium rounded-md transition-colors shadow-sm",
                    questions.every((_, index) => isAnswered(index))
                      ? "bg-blue-600 text-white hover:bg-blue-700"
                      : "bg-gray-400 text-white cursor-not-allowed"
                  )}
                >
                  Submit Quiz
                </button>
                {!questions.every((_, index) => isAnswered(index)) && (
                  <p className="text-sm text-muted-foreground">
                    Answer all questions to submit.
                  </p>
                )}
              </div>
            )}
          </div>
        </>
      )}
      
      {isSubmitted && (
        <div
          className={cn(
            "p-4 rounded-lg my-4 border",
            allCorrect
              ? "bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-200 dark:border-green-900"
              : "bg-red-50 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-200 dark:border-red-900"
          )}
        >
          {allCorrect ? (
            <div className="flex flex-col space-y-2">
              <p className="font-semibold">
                <div className="flex items-center gap-2">
                  <svg
                    className="w-5 h-5 text-green-600 dark:text-green-400"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>Congratulations! You got all questions correct!</span>
                </div>
              </p>
              <p>
                You can now proceed to the tasks.{" "}
                (<span
                  className="underline cursor-pointer hover:text-green-700 dark:hover:text-green-300"
                  onClick={toggleQuiz}
                  role="button"
                  tabIndex={0}
                  onKeyPress={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      toggleQuiz();
                    }
                  }}
                >
                  {isCollapsed ? "Show quiz" : "Hide quiz"}
                </span>)
              </p>
            </div>
          ) : (
            <div className="flex flex-col space-y-4">
              <div className="flex items-center gap-2">
                <svg
                  className="w-5 h-5 text-red-600 dark:text-red-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>
                  You got {score} out of {questions.length} questions correct.
                </span>
              </div>
              {/* Question Results */}
              <div className="space-y-2">
                <p className="font-medium text-sm text-gray-700 dark:text-gray-300">
                  Question Results:
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {questions.map((question, index) => {
                    const isCorrect = isQuestionCorrect(index);
                    return (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        {isCorrect ? (
                          <svg className="w-4 h-4 text-green-600 dark:text-green-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        ) : (
                          <svg className="w-4 h-4 text-red-600 dark:text-red-400 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                          </svg>
                        )}
                        <span className={cn(
                          "truncate",
                          isCorrect ? "text-green-700 dark:text-green-300" : "text-red-700 dark:text-red-300"
                        )}>
                          Question {index + 1}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
              <p>
                Please review and correct your answers before proceeding to the tasks.
              </p>
              <button
                onClick={resetQuiz}
                className="inline-flex items-center gap-2 text-red-700 dark:text-red-300 hover:text-red-800 dark:hover:text-red-400 cursor-pointer w-fit px-4 py-2 rounded-md hover:bg-red-50 dark:hover:bg-red-900/10 transition-colors"
              >
                <svg className="w-4 h-4" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v4a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>Try Again</span>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ProjectQuiz;
