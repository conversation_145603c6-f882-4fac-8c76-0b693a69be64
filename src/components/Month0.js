import React, { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { februaryProjects } from "../data/months/february";
import { useNavigate } from "react-router-dom";
import { Button } from "./ui/button";
import "./Month0.css";

function Month0({ 
  onPrevMonth, 
  onNextMonth, 
  onResetMonth, 
  isFirstMonth, 
  isLastMonth 
}) {
  const navigate = useNavigate();
  const days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday"
  ];
  
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [currentView, setCurrentView] = useState('month');
  const [selectedDate, setSelectedDate] = useState(new Date(2025, 1, 1));

  const handleProjectClick = (projectId) => {
    navigate(`/projects/${projectId}`);
  };

  const renderDayView = () => {
    const dayProjects = februaryProjects[selectedDate.getDate().toString()] || [];
    return (
      <div className="day-view">
        <h3>{selectedDate.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</h3>
        <div className="day-events">
          {dayProjects.map((project, index) => (
            <div
              key={`${selectedDate.getDate()}-${index}`}
              className={`event bg-blue ${
                selectedEvent === `${selectedDate.getDate()}-${project.id}` ? "selected" : ""
              }`}
              onClick={() => handleProjectClick(project.id)}
            >
              {project.title}
            </div>
          ))}
          {dayProjects.length === 0 && (
            <div className="no-events">No projects scheduled for this day</div>
          )}
        </div>
      </div>
    );
  };

  const renderWeekView = () => {
    const currentDate = new Date(selectedDate);
    const firstDayOfWeek = new Date(currentDate.setDate(currentDate.getDate() - currentDate.getDay()));
    const week = [];

    for (let i = 0; i < 7; i++) {
      const date = new Date(firstDayOfWeek);
      date.setDate(firstDayOfWeek.getDate() + i);
      const dayProjects = februaryProjects[date.getDate().toString()] || [];
      
      week.push(
        <div key={i} className="week-day">
          <div className="week-date">
            <div className="week-day-name">{days[i]}</div>
            <div className="week-day-number">{date.getDate()}</div>
          </div>
          <div className="week-events">
            {dayProjects.map((project, index) => (
              <div
                key={`${date.getDate()}-${index}`}
                className={`event bg-blue ${
                  selectedEvent === `${date.getDate()}-${project.id}` ? "selected" : ""
                }`}
                onClick={() => handleProjectClick(project.id)}
              >
                {project.title}
              </div>
            ))}
          </div>
        </div>
      );
    }
    return <div className="week-view">{week}</div>;
  };

  const renderCalendarDays = () => {
    const firstDayOfMonth = new Date(2025, 1, 1); // February 1, 2025
    const startDayOffset = firstDayOfMonth.getDay();
    const cells = [];

    // Add empty cells for days before February 1st
    for (let i = 0; i < startDayOffset; i++) {
      cells.push(
        <div key={`empty-${i}`} className="calendar-cell empty">
          <div className="cell-header">
            <div className="day-name">{days[i]}</div>
          </div>
        </div>
      );
    }

    // Add cells for each day of February
    for (let day = 1; day <= 28; day++) {
      const date = new Date(2025, 1, day);
      const dayOfWeek = date.getDay();
      const dayProjects = februaryProjects[day.toString()] || [];
      const isToday = day === new Date().getDate() && new Date().getMonth() === 1;
      const isSelected = day === selectedDate.getDate();

      cells.push(
        <div
          key={day}
          className={`
            calendar-cell 
            ${dayProjects.length > 0 ? "has-events" : ""} 
            ${isToday ? "today" : ""}
            ${isSelected ? "selected" : ""}
          `}
          onClick={() => setSelectedDate(new Date(2025, 1, day))}
        >
          <div className="cell-header">
            <div className="day-name">{days[dayOfWeek]}</div>
            <div className="day-number">{day}</div>
          </div>
          <div className="cell-content">
            {dayProjects.map((project, index) => (
              <div
                key={`${day}-${index}`}
                className={`event bg-blue ${
                  selectedEvent === `${day}-${project.id}` ? "selected" : ""
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  handleProjectClick(project.id);
                }}
              >
                {project.title}
              </div>
            ))}
          </div>
        </div>
      );
    }

    return cells;
  };

  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  const handleTodayClick = () => {
    const today = new Date();
    setSelectedDate(new Date(2025, 1, today.getDate()));
    onResetMonth();
  };

  const renderView = () => {
    switch (currentView) {
      case 'day':
        return renderDayView();
      case 'week':
        return renderWeekView();
      default:
        return <div className="calendar-grid">{renderCalendarDays()}</div>;
    }
  };

  return (
    <div className="calendar">
      <div className="calendar-header flex justify-between items-center">
        <div className="view-controls">
          <Button
            variant={currentView === 'day' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleViewChange('day')}
          >
            Day
          </Button>
          <Button
            variant={currentView === 'week' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleViewChange('week')}
          >
            Week
          </Button>
          <Button
            variant={currentView === 'month' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleViewChange('month')}
          >
            Month
          </Button>
        </div>
        <div className="title">February 2025</div>
        <div className="navigation flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTodayClick}
          >
            Today
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={onPrevMonth}
            disabled={isFirstMonth}
          >
            <ChevronLeft size={20} />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={onNextMonth}
            disabled={isLastMonth}
          >
            <ChevronRight size={20} />
          </Button>
        </div>
      </div>
      {renderView()}
    </div>
  );
}

export default Month0;
