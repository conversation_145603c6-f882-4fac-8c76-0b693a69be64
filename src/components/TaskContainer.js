import React from 'react';
import { cn } from '../lib/utils';

const TaskContainer = ({
  children,
  isTaskHeading = false,
  tag = null,
  title = null
}) => {
  return (
    <div
      className={cn(
        "w-full max-w-none",
        "rounded-lg",
        "border border-gray-200 dark:border-gray-700",
        "bg-white dark:bg-gray-800", // The main body of the card is white
        "shadow-sm",
        "mb-6" // Use the larger margin for spacing
      )}
    >
      {isTaskHeading && title && (
        <div
          className={cn(
            "px-4 py-2", // Padding for the header
            "border-b border-gray-200 dark:border-gray-700",
            "flex items-center justify-between gap-2",
            // FIX: Use the subtle grey background for the header
            "bg-gray-50 dark:bg-gray-800/50"
          )}
        >
          {/* FIX: Use a darker text color for the title */}
          <h3 className="text-base font-semibold text-black dark:text-gray-200">
            {title}
          </h3>
          {tag && (
            <span
              className={cn(
                "text-xs font-medium whitespace-nowrap",
                "px-2.5 py-0.5",
                "rounded-full",
                 // FIX: Using styles from the original 'mandatory' tag screenshot
                "bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
              )}
            >
              {tag}
            </span>
          )}
        </div>
      )}
      {/* The main content area with padding */}
      <div className={cn("p-4", "space-y-2")}>
        {children}
      </div>
    </div>
  );
};

export default TaskContainer;