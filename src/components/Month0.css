.calendar {
  padding: 20px;
  background-color: var(--background);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* Day View Styles */
.day-view {
  padding: 20px;
  background-color: var(--card-bg);
  border-radius: 8px;
}

.day-view h3 {
  margin-bottom: 20px;
  color: var(--text);
}

.day-events {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.no-events {
  color: var(--muted-text);
  text-align: center;
  padding: 20px;
}

/* Week View Styles */
.week-view {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10px;
  padding: 10px;
}

.week-day {
  min-height: 150px;
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 10px;
  background-color: var(--card-bg);
}

.week-date {
  margin-bottom: 10px;
  text-align: center;
}

.week-day-name {
  font-size: 0.875rem;
  color: var(--muted-text);
}

.week-day-number {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text);
}

.week-events {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* Month View Styles */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10px;
  background-color: var(--background);
  padding: 10px;
  border-radius: 8px;
}

.calendar-cell {
  min-height: 100px;
  border: 1px solid var(--border);
  border-radius: 4px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--card-bg);
}

.calendar-cell:hover {
  background-color: var(--hover-bg);
}

.calendar-cell.selected {
  background-color: var(--hover-bg);
  border-color: #3b82f6;
}

.calendar-cell.empty {
  background-color: var(--card-bg);
  cursor: default;
  opacity: 0.5;
}

.calendar-cell.today {
  background-color: var(--card-bg);
  border-color: #3b82f6;
  border-width: 2px;
}

.cell-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.day-name {
  font-size: 0.875rem;
  color: var(--muted-text);
}

.day-number {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text);
}

.cell-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.event {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.event.bg-blue {
  background-color: rgba(59, 130, 246, 0.15);
  color: #60a5fa;
}

.event.selected {
  background-color: #3b82f6;
  color: white;
}

.btn {
  padding: 8px 16px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: var(--card-bg);
  color: var(--text);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn:hover {
  background-color: var(--hover-bg);
}

.btn.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text);
}

.nav-btn {
  padding: 8px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background-color: var(--card-bg);
  color: var(--text);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nav-btn:hover {
  background-color: var(--hover-bg);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .calendar-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .week-view {
    display: flex;
    flex-direction: column;
  }
  
  .week-day {
    min-height: auto;
  }
  
  .calendar-cell {
    min-height: 80px;
  }
  
  .day-name {
    display: none;
  }
}
