import React from 'react';
import { cn } from '../lib/utils';

const TaskContentContainer = ({ children, className }) => {
  return (
    <div className={cn(
      "task-content",
      // Base typography
      "prose prose-stone",
      "max-w-none w-full",
      "text-base",
      
      // Paragraph spacing
      "[&>p]:my-2",
      "[&>p+p]:mt-2",
      "[&_p]:leading-normal",
      
      // List formatting
      "[&>ol]:pl-6",
      "[&>ol>li]:my-0.5",
      "[&>ul]:pl-6",
      "[&>ul>li]:my-0.5",
      
      // Code blocks
      "[&>pre]:my-3",
      "[&>pre]:p-3",
      "[&>pre]:bg-muted/50",
      "[&>pre]:rounded-md",
      "[&>pre]:overflow-x-auto",
      
      // Inline code
      "[&>:not(pre)>code]:px-1.5",
      "[&>:not(pre)>code]:py-0.5",
      "[&>:not(pre)>code]:bg-muted/50",
      "[&>:not(pre)>code]:rounded",
      "[&>:not(pre)>code]:text-sm",
      
      // Blockquotes
      "[&>blockquote]:pl-4",
      "[&>blockquote]:border-l-4",
      "[&>blockquote]:border-muted",
      "[&>blockquote]:italic",
      "[&>blockquote]:my-2",
      
      // Additional spacing control
      "[&>*]:first:mt-0",
      "[&>*]:last:mb-0",
      
      className
    )}>
      {children}
    </div>
  );
};

export default TaskContentContainer;