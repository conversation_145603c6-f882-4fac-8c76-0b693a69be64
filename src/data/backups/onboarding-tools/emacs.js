// Original file from: src/data/projects/onboarding-tools/emacs.js
// Backup created: March 7, 2025
// Reason: Content consolidated with tools/emacs.js

export const emacsProject = \`# 0x01. Emacs
![Emacs](https://img.shields.io/badge/Emacs-Text%20Editor-brightgreen)

By <PERSON>
Weight: 1

## Project Overview
Welcome to Emacs! This project introduces students to the Emacs text editor, a powerful and customizable tool often used in Unix/Linux environments.

## Resources
**Read or watch:**
* [A Guided Tour of Emacs](https://www.gnu.org/software/emacs/tour/)

## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google:**

### General
* What is Emacs
* Who is <PERSON>
* How to open and save files
* What is a buffer and how to switch from one to the other
* How to use the mark and the point to set the region
* How to cut and paste lines and regions
* How to search forward and backward
* How to invoke commands by name
* How to undo
* How to cancel half-entered commands
* How to quit Emacs

## Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work
* You are not allowed to publish any content of this project
* Any form of plagiarism is strictly forbidden and will result in removal from the program

## Requirements
### General
* All tasks must be completed within a sandbox (Ubuntu 20.04)
* The sandbox must be available throughout the project
* The Checker will access the solution file at project end for correction
* Answer files must contain only the command to execute in Emacs for solving the task
  * Example: "What is the command to write buffer to a specified file?" --> the file should contain only: \`C-x C-w\`

## Quiz Questions\`;