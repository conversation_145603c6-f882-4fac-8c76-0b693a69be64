// Original file from: src/data/projects/onboarding-tools/vi-02.js
// Backup created: March 7, 2025
// Reason: Content consolidated with tools/vi.js

export const vi02 = \`# 0x02. vi

![vi](https://img.shields.io/badge/vi-Text%20Editor-brightgreen)

By <PERSON>
Weight: 1

## Project Overview

After Emacs, it's time to play with vi! This project introduces \`vi\`, another powerful text editor commonly found in Unix/Linux systems.

## Resources

**Read or watch:**

* [Basic vi Commands](https://www.cs.colostate.edu/helpdocs/vi.html)

## Learning Objectives

At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google:**

### General

* What is \`vi\`
* Who is Bill Joy
* How to start and exit \`vi\`
* What are the command and insert modes, and how to switch from one to the other
* How to edit text
* How to cut and paste lines
* How to search forward and backward
* How to undo
* How to quit \`vi\`

## Copyright - Plagiarism

* You are tasked to come up with solutions for the tasks below yourself to meet the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements

### General

* All tasks must be done inside the sandbox (Ubuntu 20.04).
* Your sandbox must be available at the end of this project.
* The answer of a task must be in a specific file.
* Each answer file must contain only the command to execute in \`vi\` for solving the task.
  * Example: "What is the command to quit without saving changes?" --> the file should contain only: \`:q!\`

## Quiz Questions\`;