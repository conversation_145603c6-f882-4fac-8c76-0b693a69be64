// Original file from: src/data/projects/onboarding-tools/professional-tech.js
// Backup created: March 7, 2025
// Reason: Quiz content moved to markdown files

export const professionalTech = \`# 0x04. Professional Technologies

![Tech](https://img.shields.io/badge/Tech-Professional-4C51BF)
![Tools](https://img.shields.io/badge/Tools-Slack%20%26%20Chrome-805AD5)

By <PERSON><PERSON> and <PERSON>: 1

## Project Overview
This project focuses on professional technologies and soft skills, specifically introducing students to essential tools like Slack and Chrome that they'll use throughout their journey.

## Concepts
For this project, we expect you to look at these concepts:
* Slack
* Chrome

## In a nutshell...
* Auto QA review: 1.0/1 mandatory
* Altogether: 100.0%
  * Mandatory: 100.0%
  * Optional: No optional tasks
* Project timeline:
  * Project over
  * Duration: Feb 7, 2023 6:00 AM to Feb 10, 2023 6:00 AM
  * An auto review was launched at the deadline

## Resources
**Read or watch:**
* [Slack Getting Started Guide](resource-url)
* [Chrome Browser Features](resource-url)

## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google:**

### General
* How to effectively use Slack for professional communication
* Best practices for using Chrome in a professional setting
* Proper etiquette for online professional communication
* How to optimize your browser workflow for productivity

## Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work
* You are not allowed to publish any content of this project
* Any form of plagiarism is strictly forbidden and will result in removal from the program

## Quiz Questions
**Question #0**
When answering someone's question in Slack channel, I should use a ________ to answer them - without alerting all members of the channel and offering your answer to all members too.
* [ ] private message
* [x] thread
* [ ] message on the channel

**Question #1**
In Slack, \`@channel\` should never be used.
* [ ] True
* [x] False

**Question #2**
You get an email and an invitation to a mandatory meeting with a staff member. What should you do?
* [x] RSVP affirmatively or contact the staff member to reschedule the meeting
* [ ] Ignore the invitation
* [ ] Delete the email

**Question #3**
You open and close the same resource multiple times while completing various projects. What should you do to streamline your workflow?
* [ ] Never close the tab
* [ ] Save the resource as your homepage when you open your browser
* [x] Bookmark the resource that you constantly used

**Question #4**
Which Slack channel could you use to ask a question about a project?
* [x] Local cohort channel
* [ ] \`#random\`
* [ ] \`#general\`

**Question #5**
Google Chrome is the only browser that allows access to ALX's Intranet.
* [ ] True
* [x] False

### 0. Quiz Passed! {{mandatory}}
Score: 100.0%
Nothing else to do - you made it!

{{ACTION_BUTTONS}}\`;