// Original file from: src/data/projects/onboarding-tools/emacs-01.js
// Backup created: March 7, 2025
// Reason: Content consolidated with emacs.js

export const emacs01 = \`# 0x01. Emacs

![Emacs](https://img.shields.io/badge/Emacs-Text%20Editor-7F5AB6)

By <PERSON>
Weight: 1

## Concepts
* Shell
* Using Emacs as editor
* The Framework

## Resources

**Read or watch:**
* [A Guided Tour of Emacs](resource-url)

## Learning Objectives

At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google:**

### General

* What is Emacs
* Who is <PERSON>
* How to open and save files
* What is a buffer and how to switch from one to the other
* How to use the mark and the point to set the region
* How to cut and paste lines and regions
* How to search forward and backward
* How to invoke commands by name
* How to undo
* How to cancel half-entered commands
* How to quit Emacs

## Requirements

### General

* All tasks must be completed within a sandbox (Ubuntu 20.04)
* The sandbox must be available throughout the project
* The Checker will access the solution file at the project end for correction
* Answers must be in a specific file
* Each answer file should contain *only* the command to execute in Emacs for solving the task
  * Example: "What is the command to write buffer to a specified file?" --> the file should contain only: \`C-x C-w\`

## Quiz Questions

**Question #0:** In Emacs' documentation, what does \`M\` in a shortcut command stand for?

* [x] Meta
* [ ] Shift + M
* [ ] Alt

**Question #1:** In Emacs' documentation, what does \`C\` in a shortcut command stand for?

* [ ] Shift + C
* [x] Ctrl
* [ ] Command

**Question #2:** You can only have one buffer open in Emacs at a time.

* [ ] True
* [x] False

**Question #3:** In Emacs, a buffer is an object that a file's text is held in.

* [x] True
* [ ] False

## Tasks

### Task 0: Create your answer directory {{mandatory}}

Navigate to \`/root\` and create a directory named \`0x01-emacs\`.

{{ACTION_BUTTONS}}

### Task 1: Opening {{mandatory}}

What is the command to open a file from within Emacs?
Write the answer into the file \`/root/0x01_emacs/opening\`.

{{ACTION_BUTTONS}}

### Task 2: Saving {{mandatory}}

What is the command to save a file?
Write the answer into the file \`/root/0x01_emacs/saving\`.

{{ACTION_BUTTONS}}

### Task 3: Cutting {{mandatory}}

What is the command to cut an entire line?
Write the answer into the file \`/root/0x01_emacs/cutting\`.

{{ACTION_BUTTONS}}

### Task 4: Pasting {{mandatory}}

What is the command to paste?
Write the answer into the file \`/root/0x01_emacs/pasting\`.

{{ACTION_BUTTONS}}

### Task 5: Searching {{mandatory}}

What is the command to search forward?
Write the answer into the file \`/root/0x01_emacs/searching\`.

{{ACTION_BUTTONS}}

### Task 6: Undoing {{mandatory}}

What is the command to undo?
Write the answer into the file \`/root/0x01_emacs/undoing\`.

{{ACTION_BUTTONS}}

### Task 7: Quitting {{mandatory}}

What is the command to quit Emacs?
Write the answer into the file \`/root/0x01_emacs/quitting\`.

{{ACTION_BUTTONS}}
\`;