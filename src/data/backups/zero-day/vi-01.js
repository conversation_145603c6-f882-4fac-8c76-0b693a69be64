// Original file from: src/data/projects/zero-day/vi-01.js
// Backup created: March 7, 2025
// Reason: Consolidating into alx-zero_day

export const viAdvanced = {
  title: "0x03. Vi",
  program: "SE Foundations",
  status: "zero-day",
  content: \`# 0x03. Vi

## In a nutshell…
* Auto QA review: 8.0/8 mandatory
* Altogether: 100.0% Mandatory: 100.0% Optional: no optional tasks
* Mandatory: 100.0%
* Optional: no optional tasks

## Resources
**Read or watch**:
* [Basic vi Commands](https://intranet.alxswe.com/rltoken/fLGlME92y7ZFJhHUK9pKqQ)

## Learning Objectives
At the end of this project, you are expected to be able to explain to anyone, without the help of Google:

### General
* What is vi
* Who is Bill Joy
* How to start and exit vi
* What are the command and insert modes, and how to switch from one to the other
* How to edit text
* How to cut and paste lines
* How to search forward and backward
* How to undo
* How to quit vi

### Tasks
* Create your answer directory
* Inserting text
* Cutting text
* Pasting text
* Undoing actions
* Quitting vi
* Moving to beginning of line
* Moving to end of line\`
};