// Original file from: src/data/projects/zero-day/vi.js
// Backup created: March 7, 2025
// Reason: Consolidating into alx-zero_day

export const vi = {
  title: "0x02. Vi",
  program: "SE Foundations",
  status: "zero-day",
  content: \`# 0x02. Vi

## In a nutshell…
* Auto QA review: 8.0/8 mandatory
* Altogether: 100.0% Mandatory: 100.0% Optional: no optional tasks
* Mandatory: 100.0%
* Optional: no optional tasks

## Resources
**Read or watch**:
* [Basic vi Commands](https://intranet.alxswe.com/rltoken/xCkhzajLX2pF5Sk7fFs0hQ)

## Learning Objectives
At the end of this project, you are expected to be able to explain to anyone, without the help of Google:

### General
* What is vi
* Who is Bill <PERSON>
* How to start and exit vi
* What are the command and insert modes, and how to switch from one to the other
* How to edit text
* How to cut and paste lines
* How to search forward and backward
* How to undo
* How to quit vi

### Tasks
* Create your answer directory
* Inserting text
* Cutting text
* Pasting text
* Undoing actions
* Quitting vi
* Moving to beginning of line
* Moving to end of line\`
};