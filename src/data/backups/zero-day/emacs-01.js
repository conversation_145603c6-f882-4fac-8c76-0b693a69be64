// Original file from: src/data/projects/zero-day/emacs-01.js
// Backup created: March 7, 2025
// Reason: Consolidating into alx-zero_day

export const emacsAdvanced = {
  title: "0x02. Emacs",
  program: "SE Foundations",
  status: "zero-day",
  content: \`# 0x02. Emacs

## In a nutshell…
* Auto QA review: 8.0/8 mandatory
* Altogether: 100.0% Mandatory: 100.0% Optional: no optional tasks
* Mandatory: 100.0%
* Optional: no optional tasks

## Resources
**Read or watch**:
* [A Guided Tour of Emacs](https://intranet.alxswe.com/rltoken/h4EcO1npNAdmIoatlIovow)

## Learning Objectives
At the end of this project, you are expected to be able to explain to anyone, without the help of Google:

### General
* What is Emacs
* Who is <PERSON>
* How to open and save files
* What is a buffer and how to switch from one to the other
* How to use the mark and the point to set the region
* How to cut and paste lines and regions
* How to search forward and backward
* How to invoke commands by name
* How to undo
* How to cancel half-entered commands
* How to quit Emacs

### Tasks
* Create your answer directory
* Opening files
* Saving files
* Cutting text
* Pasting text
* Searching
* Undoing
* Quitting\`
};