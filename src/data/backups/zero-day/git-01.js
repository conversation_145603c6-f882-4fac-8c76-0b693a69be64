// Original file from: src/data/projects/zero-day/git-01.js
// Backup created: March 7, 2025
// Reason: Consolidating into alx-zero_day

export const gitAdvanced = {
  title: "0x03. Git",
  program: "SE Foundations",
  status: "zero-day",
  content: \`# 0x03. Git

## Concepts
For this project, we expect you to look at these concepts:
* [Source code management](https://intranet.alxswe.com/concepts/22)
* [Right-engineering, right-documenting](https://intranet.alxswe.com/concepts/6)
* [Git and Github cheat sheet - Everything in less than 30 seconds](https://intranet.alxswe.com/concepts/57)

## Learning Objectives
At the end of this project, you are expected to be able to explain to anyone, without the help of Google:

### General
* Understanding branches and their purpose
* Creating and managing branches effectively
* Handling merge conflicts
* Working with remote repositories
* Team collaboration workflows
* Git best practices

### Tasks
* Repository Setup and Management
* Create multiple branches
* Practice collaboration workflows
* Handle merge conflicts
* Project cleanup and documentation
* Advanced Git operations
* Maintaining clean repository history\`
};