import { web_infrastructure_quizQuiz as webInfrastructureQuiz } from "../../project-quizzes/system-engineering-devops/web-infrastructure-quiz";

export const webInfrastructure = {
  title: "0x09. Web infrastructure design",
  status: "system-engineering-devops",
  content: `# 0x09. Web infrastructure design

👤 By ALX
📝 Advanced
⚖️ Weight: 1
📅 Start: Aug 19, 2025 6:00 AM
📅 End: Aug 21, 2025 6:00 AM
⏱️ Duration: 48 hours

#### In a nutshell…
* **Manual QA review:** 0.0/42 mandatory & 0.0/5 optional
* **Altogether:**  **0.0%**
  * **Mandatory:** 0.0%
  * **Optional:** 0.0%
* **Calculation:**  0.0% + (0.0% * 0.0%)  == **0.0%**

## Concepts

For this project, we expect you to look at these concepts:
* [DNS](/concepts/12)
* [Monitoring](/concepts/13)
* [Web Server](/concepts/17)
* [Network basics](/concepts/33)
* [Load balancer](/concepts/46)
* [Server](/concepts/67)

[embed: web infrastructure video](https://youtu.be/lQNEW76KdYg)

## Resources

**Read or watch**:
* **Network basics** concept page
* **Server** concept page
* **Web server** concept page
* **DNS** concept page
* **Load balancer** concept page
* **Monitoring** concept page
* [What is a database](https://www.oracle.com/ke/database/what-is-database/)
* [What's the difference between a web server and an app server?](https://www.infoworld.com/article/2171154/app-server-web-server-what-s-the-difference.html)
* [DNS record types](https://www.site24x7.com/learn/dns-record-types.html)
* [Single point of failure](https://avinetworks.com/glossary/single-point-of-failure/)
* [How to avoid downtime when deploying new code](https://softwareengineering.stackexchange.com/questions/35063/how-do-you-update-your-production-codebase-database-schema-without-causing-downt#answers-header)
* [High availability cluster (active-active/active-passive)](https://docs.oracle.com/cd/E17904_01/core.1111/e10106/intro.htm#ASHIA712)
* [What is HTTPS](https://www.sectigo.com/resource-library/http-vs-https?utm_source=redirect&utm_medium=tier2&utm_campaign=instantssl)
* [What is a firewall](https://www.webopedia.com/definitions/firewall/)

## Learning Objectives

At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:

### General
* You must be able to draw a diagram covering the web stack you built with the sysadmin/devops track projects
* You must be able to explain what each component is doing
* You must be able to explain system redundancy
* Know all the mentioned acronyms: LAMP, SPOF, QPS

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements
* A \`README.md\` file, at the root of the folder of the project, is mandatory
* For each task, once you are done whiteboarding (on a whiteboard, piece of paper or software or your choice), take a picture/screenshot of your diagram
* Upload your picture/screenshot to an image hosting service (I personally use [imgur](https://imgur.com/) but feel free to use anything you want).
* For the following tasks, insert the link from of your screenshot into the answer file
* After pushing your answer file to GitHub, insert the GitHub file link into the URL box
* You will also have to whiteboard each task in front of a mentor, staff or student - no computer or notes will be allowed during the whiteboarding session
* Focus on what you are being asked:
* Cover what the requirements mention, we will explore details in a later project
* Keep in mind that you will have 30 minutes to perform the exercise, you will get points for what is asked in requirements
* Similarly in a job interview, you should answer what the interviewer asked for, be careful about being too verbose - always ask the interviewer if going into details is necessary - speaking too much can play against you
* In this project, again, avoid going in details if not asked

## Quiz Questions

## Tasks

### 0. Simple web stack

A lot of websites are powered by simple web infrastructure, a lot of time it is composed of a single server with a LAMP stack.

On a whiteboard, design a one server web infrastructure that hosts the website that is reachable via \`www.foobar.com\`. Start your explanation by having a user wanting to access your website.

Requirements:
* You must use:
  * 1 server
  * 1 web server (Nginx)
  * 1 application server
  * 1 application files (your code base)
  * 1 database (MySQL)
  * 1 domain name \`foobar.com\` configured with a \`www\` record that points to your server IP \`*******\`
* You must be able to explain some specifics about this infrastructure:
  * What is a server
  * What is the role of the domain name
  * What type of DNS record \`www\` is in \`www.foobar.com\`
  * What is the role of the web server
  * What is the role of the application server
  * What is the role of the database
  * What is the server using to communicate with the computer of the user requesting the website
* You must be able to explain what the issues are with this infrastructure:
  * SPOF
  * Downtime when maintenance needed (like deploying new code web server needs to be restarted)
  * Cannot scale if too much incoming traffic

Please, remember that everything must be written in English to further your technical ability in a variety of settings.
{{URL_INPUT}}
**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x09-web_infrastructure_design\`
* File: \`0-simple_web_stack\`
{{ACTION_BUTTONS}}
### 1. Distributed web infrastructure

On a whiteboard, design a three server web infrastructure that hosts the website \`www.foobar.com\`.

Requirements:
* You must add:
  * 2 servers
  * 1 web server (Nginx)
  * 1 application server
  * 1 load-balancer (HAproxy)
  * 1 set of application files (your code base)
  * 1 database (MySQL)
* You must be able to explain some specifics about this infrastructure:
  * For every additional element, why you are adding it
  * What distribution algorithm your load balancer is configured with and how it works
  * Is your load-balancer enabling an Active-Active or Active-Passive setup, explain the difference between both
  * How a database Primary-Replica (Master-Slave) cluster works
  * What is the difference between the Primary node and the Replica node in regard to the application
* You must be able to explain what the issues are with this infrastructure:
  * Where are SPOF
  * Security issues (no firewall, no HTTPS)
  * No monitoring

Please, remember that everything must be written in English to further your technical ability in a variety of settings.

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x09-web_infrastructure_design\`
* File: \`1-distributed_web_infrastructure\`
{{ACTION_BUTTONS}}
### 2. Secured and monitored web infrastructure

On a whiteboard, design a three server web infrastructure that hosts the website \`www.foobar.com\`, it must be secured, serve encrypted traffic, and be monitored.

Requirements:
* You must add:
  * 3 firewalls
  * 1 SSL certificate to serve \`www.foobar.com\` over HTTPS
  * 3 monitoring clients (data collector for Sumologic or other monitoring services)
* You must be able to explain some specifics about this infrastructure:
  * For every additional element, why you are adding it
  * What are firewalls for
  * Why is the traffic served over HTTPS
  * What monitoring is used for
  * How the monitoring tool is collecting data
  * Explain what to do if you want to monitor your web server QPS
* You must be able to explain what the issues are with this infrastructure:
  * Why terminating SSL at the load balancer level is an issue
  * Why having only one MySQL server capable of accepting writes is an issue
  * Why having servers with all the same components (database, web server and application server) might be a problem

Please, remember that everything must be written in English to further your technical ability in a variety of settings.

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x09-web_infrastructure_design\`
* File: \`2-secured_and_monitored_web_infrastructure\`
{{ACTION_BUTTONS}}
### 3. Scale up

Readme
* [Application server vs web server](https://www.nginx.com/resources/glossary/application-server-vs-web-server/)

Requirements:
* You must add:
  * 1 server
  * 1 load-balancer (HAproxy) configured as cluster with the other one
  * Split components (web server, application server, database) with their own server
* You must be able to explain some specifics about this infrastructure:
  * For every additional element, why you are adding it

Please, remember that everything must be written in English to further your technical ability in a variety of settings.

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x09-web_infrastructure_design\`
* File: \`3-scale_up\`
{{ACTION_BUTTONS}}
## Ready for manual review
Now that you are ready to be reviewed, share your link to your peers... and review one of them.
{{MANUAL_REVIEW_BUTTON}}`,
  quiz: webInfrastructureQuiz,
};
