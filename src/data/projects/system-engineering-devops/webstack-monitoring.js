// import { webstackMonitoringQuiz } from "../../project-quizzes/system-engineering-devops/webstack-monitoring-quiz";

export const webstackMonitoring = {
  title: "0x18. Webstack monitoring",
  status: "system-engineering-devops",
  content: `# 0x18. Webstack monitoring
![Webstack](https://img.shields.io/badge/Webstack-red?style=flat-square&logoWidth=30)
👤 By ALX
📝 Advanced
⚖️ Weight: 1
📅 Start: Aug 25, 2025 6:00 AM
📅 End: Aug 28, 2025 6:00 AM
⏱️ Duration: 72 hours

### In a nutshell…
* Auto QA review: Auto QA review: 1.5/12 mandatory
* Altogether: Altogether: 12.5% Mandatory: 12.5% Optional: no optional tasks
  * Mandatory: Mandatory: 12.5%
  * Optional: Optional: no optional tasks
* Calculation: 
## Concepts

For this project, we expect you to look at these concepts:
* [Monitoring](/concepts/13)
* [Server](/concepts/67)
![](/images/281/assets/hb3pAsO.png)
### Background Context

"You cannot fix or improve what you cannot measure" is a famous saying in the Tech industry. In the age of the data-ism, monitoring how our Software systems are doing is an important thing. In this project, we will implement one of many tools to measure what is going on our servers.

Web stack monitoring can be broken down into 2 categories:
* Application monitoring: getting data about your running software and making sure it is behaving as expected
* Server monitoring: getting data about your virtual or physical server and making sure they are not overloaded (could be CPU, memory, disk or network overload)

![](/images/281/assets/ktCXnhE.jpg)

## Resources

**Read or watch**:
* [What is server monitoring](https://www.sumologic.com/glossary/server-monitoring/)
* [What is application monitoring](https://en.wikipedia.org/wiki/Application_performance_management)
* [System monitoring by Google](https://sre.google/sre-book/monitoring-distributed-systems/)
* [Nginx logging and monitoring](https://docs.nginx.com/nginx/admin-guide/monitoring/logging/)

## Learning Objectives

At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:

### General
* Why is monitoring needed
* What are the 2 main area of monitoring
* What are access logs for a web server (such as Nginx)
* What are error logs for a web server (such as Nginx)

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements

### Your servers

| Name | Username | IP | State |  |
| ----- | ----- | ----- | ----- | ----- |
| 132747-web-01 |  |  |  |  |
| 132747-web-02 |  |  |  |  |
| 132747-lb-01 |  |  |  |  |

## Ubuntu 16.04

Basic Ubuntu 16.04 with vim, emacs, git and curl

### Ubuntu 16.04 - Python 3.5 / Fabric / Puppet

Ubuntu 16.04 with Python 3.5, Fabric and Puppet installed

## Tasks

### 0. Sign up for Datadog and install datadog-agent {{mandatory}}

Score: 0.0% (Checks completed: 0.0%)

For this task head to [https://www.datadoghq.com/](https://www.datadoghq.com/) and sign up for a free \`Datadog\` account. When signing up, you'll have the option of selecting statistics from your current stack that \`Datadog\` can monitor for you. Once you have an account set up, follow the instructions given on the website to install the \`Datadog\` agent.
![](/images/281/assets/6a4551974aadc181e97a.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250331%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250331T183857Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=285ac39dc86a5fd29e159796b75c2424d15df8b86777dac011342b00fdae438e)

* Sign up for Datadog - **Please make sure you are using the US website of Datagog (https://app.datadoghq.com)**
* Use the **US1** region
* Install \`datadog-agent\` on \`web-01\`
* Create an \`application key\`
* Copy-paste in your Intranet user profile ([here](https://savanna.alxafrica.com/users/my_profile)) your DataDog \`API key\` and your DataDog \`application key\`.
* Your server \`web-01\` should be visible in Datadog under the host name \`XX-web-01\`
* You can validate it by using this [API](https://docs.datadoghq.com/api/latest/hosts/)
* If needed, you will need to update the hostname of your server

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x18-webstack_monitoring\`
{{ACTION_BUTTONS}}

### 1. Monitor some metrics {{mandatory}}

Score: 0.0% (Checks completed: 0.0%)

Among the litany of data your monitoring service can report to you are system metrics. You can use these metrics to determine statistics such as reads/writes per second, which can help your company determine if/how they should scale. Set up some \`monitors\` within the \`Datadog\` dashboard to monitor and alert you of a few. You can read about the various system metrics that you can monitor here: [System Check](https://docs.datadoghq.com/integrations/system/).

![](/images/281/assets/6a4551974aadc181e97a.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233623Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=31487b702ca25787e403337021e8d1e4c20d7bc36095219ff1bed9adeef1fce5)
* Set up a monitor that checks the number of read requests issued to the device per second.
* Set up a monitor that checks the number of write requests issued to the device per second.

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x18-webstack_monitoring\`
{{ACTION_BUTTONS}}

### 2. Create a dashboard

Score: 50.0% (Checks completed: 100.0%)

Now create a dashboard with different metrics displayed in order to get a few different visualizations.
* Create a new \`dashboard\`
* Add at least 4 \`widgets\` to your dashboard. They can be of any type and monitor whatever you'd like
* Create the answer file \`2-setup_datadog\` which has the \`dashboard_id\` on the first line. **Note**: in order to get the id of your dashboard, you may need to use [Datadog's API](https://docs.datadoghq.com/api/latest/?tab=java)

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x18-webstack_monitoring\`
* File: \`2-setup_datadog\`
{{ACTION_BUTTONS}}
`,
//   quiz: webstackMonitoringQuiz,
};