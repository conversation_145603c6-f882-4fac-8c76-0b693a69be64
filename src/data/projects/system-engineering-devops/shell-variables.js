import { shellVariablesQuiz } from "../../project-quizzes/system-engineering-devops/shell-variables-quiz";

export const shellVariables = {
  title: "0x03. Shell, init files, variables and expansions",
  status: "system-engineering-devops",
  content: `# 0x03. Shell, init files, variables and expansions
![Bash](https://img.shields.io/badge/Bash-Shell-green)
👤 By <PERSON>
📝 Beginner
⚖️ Weight: 1
📅 Start: Mar 16, 2024 6:00 AM
📅 End: Mar 17, 2024 6:00 AM
⏱️ Duration: 24 hours

## In a nutshell…
* Auto QA review: 70.0/70 mandatory & 20.0/20 optional
* Altogether: 200.0% Mandatory: 100.0% Optional: 100.0% Calculation: 100.0% + (100.0% * 100.0%) == 200.0%
* Mandatory: 100.0%
* Optional: 100.0%
* Calculation: 100.0% + (100.0% * 100.0%) == 200.0%

## About Bash projects
Unless stated, all your projects will be auto-corrected with Ubuntu 20.04 LTS.

### Concepts
_For this project, we expect you to look at this concept:_

* [Struggling with the sandbox? Try this: Using Docker & WSL on your local host](/concepts/100039)
### Resources

**Read or watch**:
* [Expansions](https://linuxcommand.org/lc3_lts0080.php)
* [Shell Arithmetic](https://www.gnu.org/software/bash/manual/html_node/Shell-Arithmetic.html)
* [Variables](https://tldp.org/LDP/Bash-Beginners-Guide/html/sect_03_02.html)
* [Shell initialization files](https://tldp.org/LDP/Bash-Beginners-Guide/html/sect_03_01.html)
* [The alias Command](https://www.linfo.org/alias.html)
* [Technical Writing](/alx-assets/misc/2021/6/9112669886fd446a2aa3113c31319d1f468dc160.pdf)

**man or help**:
* \`printenv\`
* \`set\`
* \`unset\`
* \`export\`
* \`alias\`
* \`unalias\`
* \`.\`
* \`source\`
* \`printf\`

## Learning Objectives

At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/?fbclid=IwAR2K5_BGPVo0QjJXkOIIqNsqcXK4lTskPWJvA0asKQIGtCPWaQBdKmj1Ztg), **without the help of Google**:

### General
* What happens when you type \`$ ls -l *.txt\`

### Shell Initialization Files
* What are the \`/etc/profile\` file and the \`/etc/profile.d\` directory
* What is the \`~/.bashrc\` file

### Variables
* What is the difference between a local and a global variable
* What is a reserved variable
* How to create, update and delete shell variables
* What are the roles of the following reserved variables: HOME, PATH, PS1
* What are special parameters
* What is the special parameter \`$?\`?

### Expansions
* What is expansion and how to use them
* What is the difference between single and double quotes and how to use them properly
* How to do command substitution with \`$()\` and backticks

### Shell Arithmetic
* How to perform arithmetic operations with the shell

### The \`alias\` Command
* How to create an alias
* How to list aliases
* How to temporarily disable an alias

### Other \`help\` pages
* How to execute commands from a file in the current shell

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements

### General
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your scripts will be tested on Ubuntu 20.04 LTS
* All your scripts should be exactly two lines long (\`$ wc -l file\` should print 2)
* All your files should end with a new line
* The first line of all your files should be exactly \`#!/bin/bash\`
* A \`README.md\` file at the root of the project folder, describing what each script is doing
* You are not allowed to use \`&&\`, \`||\` or \`;\`
* You are not allowed to use \`bc\`, \`sed\` or \`awk\`
* All your files must be executable

### More Info

Read your \`/etc/profile\`, \`/etc/inputrc\` and \`~/.bashrc\` files.

Look at some files in the \`/etc/profile.d\` directory.

Note: You do not have to learn about \`awk\`, \`tar\`, \`bzip2\`, \`date\`, \`scp\`, \`ulimit\`, \`umask\`, or shell scripting, yet.

## Quiz Questions## Quiz Questions
## Tasks

### 0. <o>

Score: 100.0% (Checks completed: 100.0%)

Create a script that creates an alias.
* Name: \`ls\`
* Value: \`rm *\`

\`\`\`
julien@ubuntu:/tmp/0x03$ ls
0-alias  file1  file2
julien@ubuntu:/tmp/0x03$ source ./0-alias 
julien@ubuntu:/tmp/0x03$ ls
julien@ubuntu:/tmp/0x03$ \\ls
julien@ubuntu:/tmp/0x03$ 
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`0-alias\`
{{ACTION_BUTTONS}}
### 1. Hello you

Score: 100.0% (Checks completed: 100.0%)

Create a script that prints \`hello user\`, where user is the current Linux user.

\`\`\`
julien@ubuntu:/tmp/0x03$ id
uid=1000(julien) gid=1000(julien) groups=1000(julien),4(adm),24(cdrom),27(sudo),30(dip),46(plugdev),113(lpadmin),128(sambashare)
julien@ubuntu:/tmp/0x03$ ./1-hello_you 
hello julien
julien@ubuntu:/tmp/0x03$ 
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`1-hello_you\`
{{ACTION_BUTTONS}}
### 2. The path to success is to take massive, determined action

Score: 100.0% (Checks completed: 100.0%)

Add \`/action\` to the \`PATH\`. \`/action\` should be the last directory the shell looks into when looking for a program.

\`\`\`
julien@ubuntu:/tmp/0x03$ echo $PATH
/home/<USER>/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin
julien@ubuntu:/tmp/0x03$ source ./2-path 
julien@ubuntu:/tmp/0x03$ echo $PATH
/home/<USER>/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/action
julien@ubuntu:/tmp/0x03$ 
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`2-path\`
{{ACTION_BUTTONS}}  
### 3. If the path be beautiful, let us not ask where it leads

Score: 100.0% (Checks completed: 100.0%)

Create a script that counts the number of directories in the \`PATH\`.

\`\`\`
julien@ubuntu:/tmp/0x03$ echo $PATH
/home/<USER>/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin
julien@ubuntu:/tmp/0x03$ . ./3-paths 
11
julien@ubuntu:/tmp/0x03$ PATH=/home/<USER>/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:::::/hello
julien@ubuntu:/tmp/0x03$ . ./3-paths 
12
julien@ubuntu:/tmp/0x03$ 
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`3-paths\`
{{ACTION_BUTTONS}} 
### 4. Global variables

Score: 100.0% (Checks completed: 100.0%)

Create a script that lists environment variables.

\`\`\`
julien@ubuntu:/tmp/0x03$ source ./4-global_variables
CC=gcc
CDPATH=.:~:/usr/local:/usr:/
CFLAGS=-O2 -fomit-frame-pointer
COLORTERM=gnome-terminal
CXXFLAGS=-O2 -fomit-frame-pointer
DISPLAY=:0
DOMAIN=hq.garrels.be
e=
TOR=vi
FCEDIT=vi
FIGNORE=.o:~
G_BROKEN_FILENAMES=1
GDK_USE_XFT=1
GDMSESSION=Default
GNOME_DESKTOP_SESSION_ID=Default
GTK_RC_FILES=/etc/gtk/gtkrc:/nethome/franky/.gtkrc-1.2-gnome2
GWMCOLOR=darkgreen
GWMTERM=xterm
HISTFILESIZE=5000
history_control=ignoredups
HISTSIZE=2000
HOME=/nethome/franky
HOSTNAME=octarine.hq.garrels.be
INPUTRC=/etc/inputrc
IRCNAME=franky
JAVA_HOME=/usr/java/j2sdk1.4.0
LANG=en_US
LDFLAGS=-s
LD_LIBRARY_PATH=/usr/lib/mozilla:/usr/lib/mozilla/plugins
LESSCHARSET=latin1
LESS=-edfMQ
LESSOPEN=|/usr/bin/lesspipe.sh %s
LEX=flex
LOCAL_MACHINE=octarine
LOGNAME=franky
[...]
julien@ubuntu:/tmp/0x03$ 
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`4-global_variables\`
{{ACTION_BUTTONS}}
### 5. Local variables

Score: 100.0% (Checks completed: 100.0%)

Create a script that lists all local variables and environment variables, and functions.

\`\`\`
julien@ubuntu:/tmp/0x03$ . ./5-local_variables
BASH=/bin/bash
BASHOPTS=checkwinsize:cmdhist:complete_fullquote:expand_aliases:extglob:extquote:force_fignore:histappend:interactive_comments:progcomp:promptvars:sourcepath
BASH_ALIASES=()
BASH_ARGC=()
BASH_ARGV=()
BASH_CMDS=()
BASH_COMPLETION_COMPAT_DIR=/etc/bash_completion.d
BASH_LINENO=()
BASH_REMATCH=()
BASH_SOURCE=()
BASH_VERSINFO=([0]="4" [1]="3" [2]="46" [3]="1" [4]="release" [5]="x86_64-pc-linux-gnu")
BASH_VERSION='4.3.46(1)-release'
CLUTTER_IM_MODULE=xim
COLUMNS=133
COMPIZ_CONFIG_PROFILE=ubuntu
COMP_WORDBREAKS=$' \\t\\n"\\\'><=;|&(:'
DBUS_SESSION_BUS_ADDRESS=unix:abstract=/tmp/dbus-Fg27Lr20bq
DEFAULTS_PATH=/usr/share/gconf/ubuntu.default.path
DESKTOP_SESSION=ubuntu
[...]
julien@ubuntu:/tmp/0x03$ 
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`5-local_variables\`
{{ACTION_BUTTONS}}
### 6. Local variable

Score: 100.0% (Checks completed: 100.0%)

Create a script that creates a new local variable.
* Name: \`BEST\`
* Value: \`School\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`6-create_local_variable\`
{{ACTION_BUTTONS}}
### 7. Global variable

Score: 100.0% (Checks completed: 100.0%)

Create a script that creates a new global variable.
* Name: \`BEST\`
* Value: \`School\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`7-create_global_variable\`
{{ACTION_BUTTONS}}
### 8. Every addition to true knowledge is an addition to human power

Score: 100.0% (Checks completed: 100.0%)

Write a script that prints the result of the addition of 128 with the value stored in the environment variable \`TRUEKNOWLEDGE\`, followed by a new line.

\`\`\`
julien@production-503e7013:~$ export TRUEKNOWLEDGE=1209
julien@production-503e7013:~$ ./8-true_knowledge 
1337
julien@production-503e7013:~$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`8-true_knowledge\`
{{ACTION_BUTTONS}}  
### 9. Divide and rule

Score: 100.0% (Checks completed: 100.0%)

Write a script that prints the result of \`POWER\` divided by \`DIVIDE\`, followed by a new line.
* \`POWER\` and \`DIVIDE\` are environment variables

\`\`\`
julien@production-503e7013:~$ export POWER=42784
julien@production-503e7013:~$ export DIVIDE=32
julien@production-503e7013:~$ ./9-divide_and_rule 
1337
julien@production-503e7013:~$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`9-divide_and_rule\`
{{ACTION_BUTTONS}}
### 10. Love is anterior to life, posterior to death, initial of creation, and the exponent of breath

Score: 100.0% (Checks completed: 100.0%)

Write a script that displays the result of \`BREATH\` to the power \`LOVE\`
* \`BREATH\` and \`LOVE\` are environment variables
* The script should display the result, followed by a new line

\`\`\`
julien@production-503e7013:~/$ export BREATH=4
julien@production-503e7013:~/$ export LOVE=3
julien@production-503e7013:~/$ ./10-love_exponent_breath
64
julien@production-503e7013:~/$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`10-love_exponent_breath\`
{{ACTION_BUTTONS}}  
### 11. There are 10 types of people in the world -- Those who understand binary, and those who don't

Score: 100.0% (Checks completed: 100.0%)

Write a script that converts a number from base 2 to base 10.
* The number in base 2 is stored in the environment variable \`BINARY\`
* The script should display the number in base 10, followed by a new line

\`\`\`
julien@production-503e7013:~/$ export BINARY=10100111001
julien@production-503e7013:~/$ ./11-binary_to_decimal
1337
julien@production-503e7013:~/$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`11-binary_to_decimal\`
{{ACTION_BUTTONS}}
### 12. Combination

Score: 100.0% (Checks completed: 100.0%)

Create a script that prints all possible combinations of two letters, except \`oo\`.
* Letters are lower cases, from \`a\` to \`z\`
* One combination per line
* The output should be alpha ordered, starting with \`aa\`
* Do not print \`oo\`
* Your script file should contain maximum 64 characters

\`\`\`
julien@ubuntu:/tmp/0x03$ echo $((26 ** 2 - 1))
675
julien@ubuntu:/tmp/0x03$ ./12-combinations | wc -l
675
julien@ubuntu:/tmp/0x03$ 
julien@ubuntu:/tmp/0x03$ ./12-combinations | tail -n 5
yz
za
zb
zc
zz
julien@ubuntu:/tmp/0x03$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`12-combinations\`
{{ACTION_BUTTONS}}  
### 13. Floats

Score: 100.0% (Checks completed: 100.0%)

Write a script that prints a number with two decimal places, followed by a new line.

The number will be stored in the environment variable \`NUM\`.

\`\`\`
ubuntu@ip-172-31-63-244:~/0x03$ export NUM=0
ubuntu@ip-172-31-63-244:~/0x03$ ./13-print_float
0.00
ubuntu@ip-172-31-63-244:~/0x03$ export NUM=98
ubuntu@ip-172-31-63-244:~/0x03$ ./13-print_float
98.00
ubuntu@ip-172-31-63-244:~/0x03$ export NUM=3.14159265359
ubuntu@ip-172-31-63-244:~/0x03$ ./13-print_float
3.14
ubuntu@ip-172-31-63-244:~/0x03$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`13-print_float\`
{{ACTION_BUTTONS}}
### 14. Decimal to Hexadecimal

Score: 100.0% (Checks completed: 100.0%)

Write a script that converts a number from base 10 to base 16.
* The number in base 10 is stored in the environment variable \`DECIMAL\`
* The script should display the number in base 16, followed by a new line

\`\`\`
julien@production-503e7013:~/$ export DECIMAL=16
julien@production-503e7013:~/$ ./100-decimal_to_hexadecimal
10
julien@production-503e7013:~/$ export DECIMAL=1337
julien@production-503e7013:~/$ ./100-decimal_to_hexadecimal | cat -e
539$
julien@production-503e7013:~/$ export DECIMAL=15
julien@production-503e7013:~/$ ./100-decimal_to_hexadecimal | cat -e
f$
julien@production-503e7013:~/$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`100-decimal_to_hexadecimal\`
{{ACTION_BUTTONS}}
### 15. Everyone is a proponent of strong encryption

Score: 100.0% (Checks completed: 100.0%)

Write a script that encodes and decodes text using the rot13 encryption. Assume ASCII.

\`\`\`
julien@production-503e7013:~/shell/fun_with_the_shell$ cat quote
"Everyone is a proponent of strong encryption."
- Dorothy E. Denning
julien@production-503e7013:~/shell/fun_with_the_shell$ ./101-rot13 < quote
"Rirelbar vf n cebcbarag bs fgebat rapelcgvba."
- Qbebgul R. Qraavat
julien@production-503e7013:~/shell/fun_with_the_shell$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`101-rot13\`
{{ACTION_BUTTONS}}  
### 16. The eggs of the brood need to be an odd number

Score: 100.0% (Checks completed: 100.0%)

Write a script that prints every other line from the input, starting with the first line.

\`\`\`
ubuntu@ip-172-31-63-244:~/0x03$ \cat -n acrostic 
     1  Elizabeth it is in vain you say
     2  Love not"—thou sayest it in so sweet a way:
     3  In vain those words from thee or L.E.L.
     4  Zantippe's talents had enforced so well:
     5  Ah! if that language from thy heart arise,
     6  Breath it less gently forth—and veil thine eyes.
     7  Endymion, recollect, when Luna tried
     8  To cure his love—was cured of all beside—
     9  His follie—pride—and passion—for he died.
ubuntu@ip-172-31-63-244:~/0x03$ ./102-odd
Elizabeth it is in vain you say
In vain those words from thee or L.E.L.
Ah! if that language from thy heart arise,
Endymion, recollect, when Luna tried
His follie—pride—and passion—for he died.
ubuntu@ip-172-31-63-244:~/0x03$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`102-odd\`
{{ACTION_BUTTONS}}  
### 17. I'm an instant star. Just add water and stir.

Score: 100.0% (Checks completed: 100.0%)

Write a shell script that adds the two numbers stored in the environment variables \`WATER\` and \`STIR\` and prints the result.
* \`WATER\` is in base \`water\`
* \`STIR\` is in base \`stir.\`
* The result should be in base \`bestchol\`

\`\`\`
julien@production-503e7013:~$ export WATER="ewwatratewa"
julien@production-503e7013:~$ export STIR="ti.itirtrtr"
julien@production-503e7013:~$ ./103-water_and_stir
shtbeolhc
julien@production-503e7013:~$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x03-shell_variables_expansions\`
* File: \`103-water_and_stir\`
{{ACTION_BUTTONS}}`,
  quiz: shellVariablesQuiz,
};