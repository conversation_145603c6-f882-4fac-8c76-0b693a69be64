import { shellRegexQuiz } from "../../project-quizzes/system-engineering-devops/shell-regex-quiz.js";

export const shellRegex = {
  title: "0x06. Regular expression",
  status: "system-engineering-devops",
  content: `# 0x06. Regular expression
![Shell](https://img.shields.io/badge/Shell-Regex-green)
👤 By ALX
📝 Advanced
⚖️ Weight: 1
📅 Start: Jul 17, 2025 6:00 AM
📅 End: Jul 18, 2025 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell
* Auto QA review: 35.0/35 mandatory & 0.0/0 optional
* Altogether: 100.0% Mandatory: 100.0% Optional: 0.0% Calculation: 100.0% + (0.0% * 100.0%) == 100.0%
  * Mandatory: 100.0%
  * Optional: 0.0%
* Calculation: 100.0% + (0.0% * 100.0%) == 100.0%

## Concepts
_For this project, we expect you to look at this concept:_
* [Regular Expression](/concepts/29)

## Background Context
For this project, you have to build your regular expression using Oniguruma, a regular expression library that which is used by <PERSON> by default. Note that other regular expression libraries sometimes have different properties.

Because the focus of this exercise is to play with regular expressions (regex), here is the Ruby code that you should use, just replace the regexp part, meaning the code in between the \`//\`:

\`\`\`
sylvain@ubuntu$ cat example.rb
#!/usr/bin/env ruby
puts ARGV[0].scan(/127.0.0.[0-9]/).join
sylvain@ubuntu$
sylvain@ubuntu$ ./example.rb 127.0.0.2
127.0.0.2
sylvain@ubuntu$ ./example.rb 127.0.0.1
127.0.0.1
sylvain@ubuntu$ ./example.rb 127.0.0.a
\`\`\`

## Resources

**Read or watch**:

* [Regular expressions - basics](https://www.slideshare.net/slideshow/introducing-regular-expressions/63676155)
* [Regular expressions - advanced](https://www.slideshare.net/slideshow/advanced-regular-expressions-80296518/80296518)
* [Rubular is your best friend](https://rubular.com/)
* [Use a regular expression against a problem: now you have 2 problems](https://blog.codinghorror.com/regular-expressions-now-you-have-two-problems/)
* [Learn Regular Expressions with simple, interactive exercises](https://regexone.com/)

## Requirements
### General
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your files will be interpreted on Ubuntu 20.04 LTS
* All your files should end with a new line
* A \`README.md\` file, at the root of the folder of the project, is mandatory
* All your script files must be executable
* The first line of all your Bash scripts should be exactly \`#!/usr/bin/env ruby\`
* All your regex must be built for the Oniguruma library

## Quiz Questions

## Tasks

### 0. Simply matching School {{mandatory}}

![](/images/78/assets/ec65557f0da1fbfbff6659413885e4d4822f5b1d.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233554Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=5f88e60268807ed2df7f6523f08a7ec8ec108242aeea24362c938c253c99bc1e)

Requirements:
* The regular expression must match \`School\`
* Using the project instructions, create a Ruby script that accepts one argument and pass it to a regular expression matching method

Example:
\`\`\`
sylvain@ubuntu$ ./0-simply_match_school.rb School | cat -e
School$
sylvain@ubuntu$ ./0-simply_match_school.rb "Best School" | cat -e
School$
sylvain@ubuntu$ ./0-simply_match_school.rb "School Best School" | cat -e
SchoolSchool$
sylvain@ubuntu$ ./0-simply_match_school.rb "Grace Hopper" | cat -e
$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`0-simply_match_school.rb\`
{{ACTION_BUTTONS}}

### 1. Repetition Token #0 {{mandatory}}

![](/images/78/assets/e7db3c377d46453588fc84f3a975661d142fee91.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233554Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=9f88ed43026ba2f781f36724c47df9ce420dcecc96546324d627ab9c391f8444)

Requirements:
* Find the regular expression that will match the above cases
* Using the project instructions, create a Ruby script that accepts one argument and pass it to a regular expression matching method

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`1-repetition_token_0.rb\`

### 2. Repetition Token #1 {{mandatory}}

![](/images/78/assets/c59ff11db195d5cf17d1790a5141ae2f234786d2.png?X-Amz-Algorithm=AWS4-HMAC-SSHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233554Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=dc2772f3dae08a2d31372e74cf0dac5d0fb722f018e94c3e6202a9db66b6d310)

Requirements:
* Find the regular expression that will match the above cases
* Using the project instructions, create a Ruby script that accepts one argument and pass it to a regular expression matching method

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`2-repetition_token_1.rb\`

### 3. Repetition Token #2 {{mandatory}}

![](/images/78/assets/3b6bf4aeca6a0c2de584e7f5d68d11eef57ce205.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233554Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=23cb8fcc0417ba84f59c89e7d08729c46d6608fbb61afdd525568108a48aa181)

Requirements:
* Find the regular expression that will match the above cases
* Using the project instructions, create a Ruby script that accepts one argument and pass it to a regular expression matching method

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`3-repetition_token_2.rb\`

### 4. Repetition Token #3 {{mandatory}}

![](/images/78/assets/f8dbcb9cf5ae569a8645027dc46e81cb372ce28e.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233554Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=c16eb49b26d2dbb1c7301c9721ce340d3ece160685c282289c99791b2d130ebe)

Requirements:
* Find the regular expression that will match the above cases
* Using the project instructions, create a Ruby script that accepts one argument and pass it to a regular expression matching method
* Your regex should not contain square brackets

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`4-repetition_token_3.rb\`

### 5. Not quite HBTN yet {{mandatory}}

Requirements:
* The regular expression must be exactly matching a string that starts with \`h\` ends with \`n\` and can have any single character in between
* Using the project instructions, create a Ruby script that accepts one argument and pass it to a regular expression matching method

Example:
\`\`\`
sylvain@ubuntu$ ./5-beginning_and_end.rb 'hn' | cat -e
$
sylvain@ubuntu$ ./5-beginning_and_end.rb 'hbn' | cat -e
hbn$
sylvain@ubuntu$ ./5-beginning_and_end.rb 'hbtn' | cat -e
$
sylvain@ubuntu$ ./5-beginning_and_end.rb 'h8n' | cat -e
h8n$
sylvain@ubuntu$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`5-beginning_and_end.rb\`

### 6. Call me maybe {{mandatory}}

This task is brought to you by a professional advisor Neha Jain, Senior Software Engineer at LinkedIn.

Requirement:
* The regular expression must match a 10 digit phone number

Example:
\`\`\`
sylvain@ubuntu$ ./6-phone_number.rb 4155049898 | cat -e
4155049898$
sylvain@ubuntu$ ./6-phone_number.rb " 4155049898" | cat -e
$
sylvain@ubuntu$ ./6-phone_number.rb "************" | cat -e
$
sylvain@ubuntu$ ./6-phone_number.rb "************" | cat -e
$
sylvain@ubuntu$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`6-phone_number.rb\`

### 7. OMG WHY ARE YOU SHOUTING? {{mandatory}}


Score: 100.0% (Checks completed: 100.0%)

![](/images/contents/sysadmin/projects/78/shouting.jpg)

Requirement:
* The regular expression must be only matching: capital letters

Example:
\`\`\`
sylvain@ubuntu$ ./7-OMG_WHY_ARE_YOU_SHOUTING.rb "I realLy hOpe VancouvEr posseSs Yummy Soft vAnilla Dupper Mint Ice Nutella cream" | cat -e
ILOVESYSADMIN$
sylvain@ubuntu$ ./7-OMG_WHY_ARE_YOU_SHOUTING.rb "WHAT do you SAY?" | cat -e
WHATSAY$
sylvain@ubuntu$ ./7-OMG_WHY_ARE_YOU_SHOUTING.rb "cannot read you" | cat -e
$
sylvain@ubuntu$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`7-OMG_WHY_ARE_YOU_SHOUTING.rb\`

### 8. Textme {{#advanced}}

Score: 100.0% (Checks completed: 100.0%)
This exercise was prepared for you by Guillaume Plessis, VP of Infrastructure at TextMe. It is something he uses daily. You can thank Guillaume for his project on Twitter.

For this task, you'll be taking over Guillaume's responsibilities: one afternoon, a TextMe VoIP Engineer comes to you and explains she wants to run some statistics on the TextMe app text messages transactions.

Requirements:
* Your script should output: [SENDER],[RECEIVER],[FLAGS]
* The sender phone number or name (including country code if present)
* The receiver phone number or name (including country code if present)
* The flags that were used

Example:
\`\`\`
$ ./100-textme.rb 'Feb 1 11:00:00 ip-10-0-0-11 mdr: 2016-02-01 11:00:00 Receive SMS [SMSC:SYBASE1] [SVC:] [ACT:] [BINF:] [FID:] [from:Google] [to:+16474951758] [flags:-1:0:-1:0:-1] [msg:127:This planet has - or rather had - a problem, which was this: most of the people on it were unhappy for pretty much of the time.] [udh:0:]'
Google,+16474951758,-1:0:-1:0:-1$
$
$ ./100-textme.rb 'Feb 1 11:00:00 ip-10-0-64-10 mdr: 2016-02-01 11:00:00 Receive SMS [SMSC:SYBASE2] [SVC:] [ACT:] [BINF:] [FID:] [from:+17272713208] [to:+19172319348] [flags:-1:0:-1:0:-1] [msg:136:Orbiting this at a distance of roughly ninety-two million miles is an utterly insignificant little blue green planet whose ape-descended] [udh:0:]'
+17272713208,+19172319348,-1:0:-1:0:-1$
\`\`\`

**Repo:**
* GitHub repository: \`alx-system_engineering-devops\`
* Directory: \`0x06-regular_expressions\`
* File: \`100-textme.rb\`
`,
  quiz: shellRegexQuiz,
};
