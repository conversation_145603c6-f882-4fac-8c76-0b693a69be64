export const whatHappensWhen = {
  title: "0x11. What happens when you type google.com in your browser and press Enter",
  status: "system-engineering-devops",
  content: `# 0x11. What happens when you type google.com in your browser and press Enter
![System Engineering & DevOps](https://img.shields.io/badge/System%20Engineering-DevOps-brightgreen)
👤 By <PERSON>
📝 Intermediate
⚖️ Weight: 1
📅 Start: Mar 16, 2024 6:00 AM
📅 End: Mar 17, 2024 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell...
* **Auto QA review:** Manual QA review
* **Altogether:** 
  * **Mandatory:** 100%
  * **Optional:** 0%
  * **Calculation:** 100%

This question is a classic and still widely used interview question for many types of software engineering position. It is used to assess a candidate's general knowledge of how the web stack works on top of the internet. One important guideline to begin answering this question is that you should ask your interviewer whether they would like you to focus in on one specific area of the workflow. For a front-end position they may want you to talk at length about how the DOM is rendering. For an SRE position they may want you to go into the load balancing mechanism.

This question is a good test of whether you understand DNS. Many software engineering candidates struggle with this concept, so if you do well on this question, you are already way ahead of the curve. If you take this project seriously and write an excellent article, it may be something that will grab the attention of future employers.

## Resources
**Read or watch**:
* [What happens when you type a URL in your browser](https://intranet.alxswe.com/)
* [Journey to the Center of the Internet](https://intranet.alxswe.com/)

## Requirements

### General
* Blog post explaining what happens when you type google.com in your browser and press Enter
* Blog post must cover:
  * DNS request
  * TCP/IP
  * Firewall
  * HTTPS/SSL
  * Load-balancer
  * Web server
  * Application server
  * Database

## Tasks

### 0. What happens when... {{mandatory}}
Write a blog post explaining what happens when you type \`https://www.google.com\` in your browser and press \`Enter\`.

Requirements, your post must cover:
* DNS request
* TCP/IP
* Firewall
* HTTPS/SSL
* Load-balancer
* Web server
* Application server
* Database

Publish your blog post on Medium or LinkedIn; share the URL of your blog post in your answer file and in the field below.

### 1. Everything's better with a pretty diagram {{mandatory}}
Add a schema to your blog post illustrating the flow of the request created when you type \`https://www.google.com\` in your browser and press \`Enter\`.

The diagram should show:
* DNS resolution
* that the request hitting server IP on the appropriate port
* that the traffic is encrypted
* that the traffic goes through a firewall
* that the request is distributed via a load balancer
* that the web server answers the request by serving a web page
* that the application server generates the web page
* that the application server request data from the database

### 2. Contribute!
Folks on the Internet have been trying to put together a comprehensive answer to the question. Help them by submitting a pull request. Paste the link in your answer file.

[https://github.com/alex/what-happens-when#the-g-key-is-pressed](https://github.com/alex/what-happens-when#the-g-key-is-pressed)

Requirements:
* The pull request must bring meaningful value (not a typo correction or style improvement)
* Share the pull request URL in your answer file and in the field below
`,
};
