import { shellBasics } from './shell-basics';
import { shellPermissions } from './shell-permissions';
import { shellIoRedirections } from './shell-io-redirections';
import { shellVariables } from './shell-variables';
import { shellLoops } from './shell-loops';
import { shellProcesses } from './shell-processes';
import { shellRegex } from './shell-regex';
import { networkingBasics0 } from './networking-basics-0';
import { networkingBasics1 } from './networking-basics-1';
import { webInfrastructure } from './web-infrastructure';
import { configManagement } from './config-management';
import { ssh } from './ssh';
import { webServer } from './web-server';
import { webStackDebugging0 } from './web-stack-debugging-0';
import { webStackDebugging1 } from './web-stack-debugging-1';
import { loadBalancer } from './load-balancer';
import { httpsSsl } from './https-ssl';
import { webStackDebugging2 } from './web-stack-debugging-2';
import { webStackDebugging3 } from './web-stack-debugging-3';
import { webStackDebugging4 } from './web-stack-debugging-4';
import { firewall } from './firewall';
import { mysql } from './mysql';
import { api } from './api';
import { apiAdvanced } from './api-advanced';
import { webstackMonitoring } from './webstack-monitoring';
import { applicationServer } from './application-server';
import { whatHappensWhen } from './what-happens-when';
import { postmortem } from './postmortem';

export const systemEngineeringDevops = [
  shellBasics,
  shellPermissions,
  shellIoRedirections,
  shellVariables,
  shellLoops,
  shellProcesses,
  shellRegex,
  networkingBasics0,
  networkingBasics1,
  webInfrastructure,
  configManagement,
  ssh,
  webServer,
  webStackDebugging0,
  webStackDebugging1,
  loadBalancer,
  httpsSsl,
  webStackDebugging2,
  webStackDebugging3,
  webStackDebugging4,
  firewall,
  mysql,
  api,
  apiAdvanced,
  webstackMonitoring,
  applicationServer,
  whatHappensWhen,
  postmortem
];