// import { emacsQuiz } from "project-quizzes/tools/emacs";
export const emacs = {
  title: "0x02. Emacs",
  status: "onboarding-tools",
  content: `# 0x02. Emacs
![Emacs](https://img.shields.io/badge/Emacs-Text%20Editor-brightgreen)
👤 By <PERSON>
📝 Intermediate
⚖️ Weight: 1
📅 Start: Feb 25, 2025 6:00 AM
📅 End: Feb 25, 2025 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell...
* Auto QA review: 0.0/3 mandatory
* Altogether: 0.0%
  * Mandatory: 0.0%
  * Optional: N/A
  * Calculation: 0.0% + (0.0% * 0.0%) == 0.0%
## Concepts
For this project, we expect you to look at these concepts:
* [The Framework](/concepts/559)
* [Shell](/concepts/9)
* [Using Em<PERSON><PERSON> as editor](/concepts/54)
## Congrats!
Now you know how to navigate into a Unix system!
It's time to test and decide what will be your favorite text editor on your sandbox: **Emacs** or **Vi**.
During this project you will play with **Emacs**.
## Resources
**Read or watch:**
* [A Guided Tour of Emacs](https://www.gnu.org/software/emacs/tour/)
## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google:**
### General
* What is Emacs
* Who is Richard Stallman
* How to open and save files
* What is a buffer and how to switch from one to the other
* How to use the mark and the point to set the region
* How to cut and paste lines and regions
* How to search forward and backward
* How to invoke commands by name
* How to undo
* How to cancel half-entered commands
* How to quit Emacs
## Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work
* You are not allowed to publish any content of this project
* Any form of plagiarism is strictly forbidden and will result in removal from the program
## Requirements
### General
* All tasks must be completed within a sandbox cUbuntu 20.04)
* The sandbox will be available throughout the project
* The Checker will access the solution file at project end for correction
* Answer files must contain only the command to execute in Emacs for solving the task
  * Example: "What is the command to write buffer to a specified file?" --> the file should contain only: \`C-x C-w\`
## Quiz Questions
## Tasks
### 0. Create your answer directory {{mandatory}}
Navigate to \`/root\` and create a directory named \`0x01_emacs\`
{{ACTION_BUTTONS}}

### 1. Opening {{mandatory}}
What is the command to open a file from within Emacs?
Write the answer into the file \`/root/0x01_emacs/opening\`
You can validate if the format of your answer is correct by displaying the file information:
\`\`\`
root@hex:~# ls -l /root/0x02_emacs/opening
-rw-r--r-- 1 <USER> <GROUP> 9 Nov 11 04:34 /root/0x02_emacs/opening
root@hex:~# 
\`\`\`
{{ACTION_BUTTONS}}

### 2. Saving {{mandatory}}
What is the command to save a file?
Write the answer into the file \`/root/0x01_emacs/saving\`
{{ACTION_BUTTONS}}

### 3. Cutting {{mandatory}}
What is the command to cut an entire line?
Write the answer into the file \`/root/0x01_emacs/cutting\`

{{ACTION_BUTTONS}}

### 4. Pasting {{mandatory}}
What is the command to paste?
Write the answer into the file \`/root/0x01_emacs/pasting\`
{{ACTION_BUTTONS}}

### 5. Searching {{mandatory}}
What is the command to search forward?
Write the answer into the file \`/root/0x01_emacs/searching\`
{{ACTION_BUTTONS}}

### 6. Undoing {{mandatory}}
What is the command to undo?
Write the answer into the file \`/root/0x01_emacs/undoing\`
{{ACTION_BUTTONS}}

### 7. Quitting {{mandatory}}
What is the command to quit Emacs?
Write the answer into the file \`/root/0x01_emacs/quitting\`
{{ACTION_BUTTONS}}`,
//   quiz: emacsQuiz,
};
