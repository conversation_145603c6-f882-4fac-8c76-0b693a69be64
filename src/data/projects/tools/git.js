// import { gitQuiz } from "../../project-quizzes/tools/git";

export const gitProject = {
	title: "0x01. Git",
	program: "SE Foundations",
	status: "zero-day",
	content: `# 0x01. Git
![Git](https://img.shields.io/badge/Git-Version%20Control-brightgreen) ![Code versioning](https://img.shields.io/badge/Code-Versioning-blue) ![Github](https://img.shields.io/badge/Github-Repository-lightgrey)
👤 By ALX
📝 Beginner
⚖️ Weight: 17
📅 Start: Jan 24, 2023 6:00 AM
📅 End: Jan 25, 2023 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell...
* **Auto QA review:** 17.0/17.0 mandatory & 0.0/0.0 optional
* **Altogether:** 100.0%
  * **Mandatory:** 100.0%
  * **Optional:** No optional tasks
* **Calculation:** 100.0% + (0.0% * 0.0%) == 100.0%

## Concepts
For this project, we expect you to look at these concepts:
*   [Struggling with the sandbox? Try this: Using Docker & WSL on your local host](/concepts/100039)
*   [Authenticating Git](/concepts/100035)

## Resources
**Read or watch:**
* [Resources to learn Git](https://docs.github.com/en/get-started/git-basics/set-up-git)
* [About READMEs](https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-readmes)
* [How to write a Git commit message](https://cbea.ms/git-commit/)

**Resources for advanced tasks** (Read only after finishing the mandatory tasks):
* [Learning branching](https://learngitbranching.js.org/)
* [Effective pull requests and other good practices for teams using GitHub](https://codeinthehole.com/tips/pull-requests-and-other-good-practices-for-teams-using-github/)

## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-technique/):

### General
* What is source code management
* What is Git
* What is GitHub
* What is the difference between Git and GitHub
* How to create a repository
* What is a README
* How to write good READMEs
* How to commit
* How to write helpful commit messages
* How to push code
* How to pull updates
* How to create a branch
* How to merge branches
* How to work as collaborators on a project
* Which files should and which files should not appear in your repo

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements
### General
* A  \`README.md\` file at the root of the \`alx-zero_day\` repo, containing a description of the repository
* A \`README.md\` file, at the root of the folder of *this* project (i.e. \`0x03-git\`), describing what this project is about
* **Do not use GitHub's web UI**, but the command line to perform the exercise (except for operations that can not possibly be done any other way than through the web UI). You won't be able to perform many of the task requirements on the web UI, and you should start getting used to the command line for simple tasks because many complex tasks can only be done via the command line.
* Your answer files should only contain the command, and nothing else

## More Info
### Basic usage
At the end of this project you should be able to reproduce and understand these command lines:
\`\`\`shell
$ git clone <repo>
$ touch test
$ git add test
$ git commit -m "Initial commit"
$ git push origin main
\`\`\`
## Quiz Questions

## Tasks
### 0\. Create and setup your Git and GitHub account
Score: 100.0% (Checks completed: 100.0%)
#### Step 0 - Create an account on GitHub \[if you do not have one already\]
You will need a GitHub account for all your projects at ALX. If you do not already have a github.com account, you can create an account for free [here](https://github.com/)
#### Step 1 - Create a Personal Access Token on Github
To have access to your repositories and authenticate yourself, you need to create a Personal Access Token on Github.
You can follow [this tutorial](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens) to create a token.
Once it's created, you should have a token that looks like this:
![Project Image](/images/1106/assets/a449483cd76a72cef1b42df831e686c64faa1cf6.png)
#### Step 2 - Update your profile on the Intranet
Update your Intranet profile by adding your Github username [here](https://intranet.alxswe.com/users/my_profile)
If it's not done **the Checker won't be able to correct your work**
![](/images/1106/assets/6270480a0a982cd1846b877eda2ee405d2e8f575.png)
#### Step 3 - Create your first repository
Using the graphic interface on the [github website](https://github.com/), create your first repository.
*   Name: \`alx-pre_course\`
*   Description: \`I'm now a ALX Student, this is my first repository as a full-stack engineer\`
*   Public repo
*   No \`README\`, \`.gitignore\`, or license
![](/images/1106/assets/2340a2d0f7c74b5dd6f8fc2aa58f94d13ea2c775.png)
#### Step 4 - Open the sandbox
On the intranet, just under the task, click on the button ![](/images/1106/assets/9db8eece71455dfddf4b7d8585c037c535f1d18d.png) and \run\ to start the machine.
Once the container is started, click on ![](/images/1106/assets/be9d1fbfb3d97e6924a4d2af7df9290ad7ae77df.png) to open a shell where you can start work from.
#### Step 5 - Clone your repository
On the webterm of the sandbox, do the following:
*   Clone your repository
\`\`\`
root@896cf839cf9a:/# git clone https://{YOUR_PERSONAL_TOKEN}@github.com/{YOUR_USERNAME}/alx-pre_course.git                  
Cloning into 'alx-pre_course'...
warning: You appear to have cloned an empty repository.       
\`\`\`
**Replace {YOUR\_PERSONAL\_TOKEN} with your token from step 1**
**Replace {YOUR\_USERNAME} with your username from step 0 and 1**
**Pro-Tip:** On windows, use CTRL + A + V to paste in the web terminal
#### Step 6 - Create the README.md and push the modifications
-   Navigate to this new directory. [Tips](https://askubuntu.com/questions/232442/how-do-i-navigate-between-directories-in-terminal)
\`\`\`
root@896cf839cf9a:/# cd alx-pre_course/
root@896cf839cf9a:/alx-pre_course#
\`\`\`
*   Create the file \`README.md\` with the content \`My first readme\`. [Tips](https://forum.howtoforge.com/threads/echo-into-a-file.115/)
\`\`\`
root@896cf839cf9a:/alx-pre_course# echo 'My first readme' > README.md                                                                 
root@896cf839cf9a:/alx-pre_course# cat README.md                                                                                      
My first readme                                                                                                                       
\`\`\`
*   Update your git identity
\`\`\`
root@896cf839cf9a:/alx-pre_course# git config --global user.email "<EMAIL>"
root@896cf839cf9a:/alx-pre_course# git config --global user.name "Your Name"
\`\`\`
*   Add this new file to git, commit the change with this message "My first commit" and push to the remote server / origin
\`\`\`
root@896cf839cf9a:/alx-pre_course# git add .
root@896cf839cf9a:/alx-pre_course# git commit -m 'My first commit'
[master (root-commit) 98eef93] My first commit
 1 file changed, 1 insertion(+)
 create mode 100644 README.md
root@896cf839cf9a:/alx-pre_course# git push                                                                                           
Enumerating objects: 3, done.                                                                                                         
Counting objects: 100% (3/3), done.                                                                                                   
Writing objects: 100% (3/3), 212 bytes | 212.00 KiB/s, done.                                                                          
Total 3 (delta 0), reused 0 (delta 0)                                                                                                 
To https://github.com/{YOUR_USERNAME}/alx-pre_course.git                                                                                       
 * [new branch]      master -> master              
\`\`\`
Good job!
You pushed your first file in your **first repository** of the **first task** of your **first ALX School project**.
You can now check your repository on GitHub to see if everything is good.
**Repo:**
*   GitHub repository: \`alx-pre_course\`
*   File: \`README.md\`
{{ACTION_BUTTONS}}

### 1. Repo-session
Score: 100.0% (Checks completed: 100.0%)

Create a new directory called \`0x01-git\` in your \`alx-pre_course\` repo.

Make sure you include a non empty \`README.md\` in your directory:
* at the root of your repository \`alx-pre_course\`
* AND in the directory \`0x01-git\`

And important part: **Make sure your commit and push your code to Github - otherwise the Checker will always fail.**

**Repo:**
*   GitHub repository: \`alx-pre_course\`
*   Directory: \`0x01-git\`
*   File: \`README.md\`
{{ACTION_BUTTONS}}

### 2. Coding fury road
Score: 100.0% (Checks completed: 100.0%)

For the moment we have an empty project directory containing only a README.md. It's time to code!

* Create these directories at the root of your project: \`bash\`, \`c\`, \`js\`
* Create these empty files:
    * \`c/c_is_fun.c\`
    * \`js/main.js\`
    * \`js/index.js\`
* Create a file \`bash/alx\` with these two lines inside: \`#!/bin/bash\` and \`echo "ALX"\`
* Create a file \`bash/school\` with these two lines inside: \`#!/bin/bash\` and \`echo "School"\`
* Add all these new files to git
* Commit your changes (message: "Starting to code today, so cool") and push to the remote server

**Repo:**
*   GitHub repository: \`alx-pre_course\`
*   Directory: \`0x01-git\`
*   File: \`bash/alx, bash/school, c/c_is_fun.c, js/main.js, js/index.js\`
{{ACTION_BUTTONS}}

### 3. Collaboration is the base of a company
Score: 100.0% (Checks completed: 100.0%)

A branch is like a copy of your project. It's used mainly for:

* adding a feature in development
* collaborating on the same project with other developers
* not breaking your entire repository
* not upsetting your co-workers

The purpose of a branch is to isolate your work from the main code base of your project and/or from your co-workers' work.

For this project, create a branch \`update_script\` which will be used to:

* Update the file \`bash/alx\` by replacing \`echo "ALX"\` with \`echo "ALX School"\`
* Update the file \`bash/school\` by replacing \`echo "School"\` with \`echo "The school is open!"\`

**Repo:**
* GitHub repository: \`alx-pre_course\`
* Directory: \`0x01-git\`
* File: \`bash/alx\`, \`bash/school\`, \`bash/98\`
{{ACTION_BUTTONS}}

### 4. Collaboration: be up to date
Score: 100.0% (Checks completed: 100.0%)

Of course, you can also work on the same branch as your co-workers and it's best if you keep up to date with their changes.

For this task – **and only for this task** – please update your file \`README.md\` in the main branch from GitHub.com. It's the **only time** you are allowed to update and commit from GitHub interface.

After you have done that, in your terminal:

* Get all changes of the main branch locally (i.e. your \`README.md\` file will be updated)
* Create a new file \`up_to_date\` at the root of your directory and in it, write the git command line used
* Add \`up_to_date\` to git, commit (message: "How to be up to date in git"), and push to the origin

**Repo:**
*   GitHub repository: \`alx-pre_course\`
*   Directory: \`0x01-git\`
*   File: \`README.md, up_to_date\`
{{ACTION_BUTTONS}}

### 5. HAAA what did you do???
Score: 100.0% (Checks completed: 100.0%)

Merge the branch \`update_script\` to \`main\`: "Cool, all my changes will be now part of the main branch, ready to be deployed!"

OUPS

As a consequence, there are conflicts (some files have the same name but different content):

* Your \`bash/alx\` file is not the same in \`update_script\` and \`main\`

Conflict resolution is an essential aspect of working with Git. It allows you to merge branches with conflicting changes and resolve those conflicts manually.

**Repo:**
*   GitHub repository: \`alx-pre_course\`
*   Directory: \`0x01-git\`
{{ACTION_BUTTONS}}

### 6. Never push too much
Score: 100.0% (Checks completed: 100.0%)

Create a \`.gitignore\` file and define a rule to never push \`~\` files (generated by Emacs). [Tips](https://intranet.alxswe.com/)

**Repo:**
*   GitHub repository: \`alx-pre_course\`
*   Directory: \`0x01-git\`
*   File: \`.gitignore\`

{{ACTION_BUTTONS}}`,
	//   quiz: gitQuiz,
};
