// import { viQuiz } from "../../project-quizzes/tools/vi";

export const vi = {
	title: "0x02. Vi",
	status: "onboarding-tools",
	content: `# 0x02. Vi
![vi](https://img.shields.io/badge/vi-Text%20Editor-brightgreen)
👤 By <PERSON>
📝 Intermediate
⚖️ Weight: 1
📅 Start: Feb 25, 2025 6:00 AM
📅 End: Feb 25, 2025 6:00 AM
⏱️ Duration: 1 day
### In a nutshell...
* **Auto QA review:** 0.0/3 mandatory
* **Altogether:** 0.0%
  * **Mandatory:** 0.0%
  * **Optional:** N/A
  * **Calculation:** 0.0% + (0.0% * 0.0%) == 0.0%
## Another text editor
After **Emacs**, it is time to play with **Vi**.
## Resources
**Read or watch**:
*   [Basic vi Commands](https://www.cs.colostate.edu/helpdocs/vi.html)
## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/?fbclid=IwAR2K5_BGPVo0QjJXkOIIqNsqcXK4lTskPWJvA0asKQIGtCPWaQBdKmj1Ztg), **without the help of Google**:
### General
*   What is vi
*   Who is Bill Joy
*   How to start and exit vi
*   What are the command and insert modes, and how to switch from one to the other
*   How to edit text
*   How to cut and paste lines
*   How to search forward and backward
*   How to undo
*   How to quit vi
### Copyright - Plagiarism
*   You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
*   You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
*   You are not allowed to publish any content of this project.
*   Any form of plagiarism is strictly forbidden and will result in removal from the program.
## Requirements
* All tasks must be done inside the sandbox \`Ubuntu 20.04\`
* Your sandbox **must be available at the end of this project** - the Checker will access to it just after the deadline to run the correction!
* The answer of a task must be in a specific file
* Each answer file must contain only the command to execute in \`vi\` for solving the task. Example: "What is the command to quit without saving changes?" -> the file should contain only \`:q!\`

## Quiz Questions
## Tasks
### 0. Create your answer directory {{mandatory}}
Navigate to \`/root\` and create a directory named \`0x02_vi\`
{{ACTION_BUTTONS}}

### 1. Inserting {{mandatory}}
What is the command to insert text before the cursor?
Write the answer into the file \`/root/0x02_vi/inserting\`
You can validate if the format of your answer is correct by displaying the file information:
\`\`\`shell
root@hex:~# ls -l /root/0x03_vi/inserting
-rw-r--r-- 1 <USER> <GROUP> 2 Nov 11 04:34 /root/0x03_vi/inserting
root@hex:~# 
\`\`\`
{{ACTION_BUTTONS}}

### 2. Cutting {{mandatory}}
What is the command to delete and cut the current line?
Write the answer into the file \`/root/0x02_vi/cutting\`

**Tips:**
* [How to Copy, Cut and Paste](https://linuxize.com/post/how-to-copy-cut-paste-in-vim/)

{{ACTION_BUTTONS}}

### 3. Pasting {{mandatory}}
What is the command to paste the lines in the buffer into the text after the current line?
Write the answer into the file \`/root/0x02_vi/pasting\`

{{ACTION_BUTTONS}}

### 4. Undoing {{mandatory}}
What is the command to undo what you just did?
Write the answer into the file \`/root/0x02_vi/undoing\`

{{ACTION_BUTTONS}}

### 5. Exiting {{mandatory}}
What is the command to quit vi even though latest changes have not been saved for this vi call?
Write the answer into the file \`/root/0x02_vi/exiting\`

{{ACTION_BUTTONS}}

### 6. Beginning of the line {{mandatory}}
What is the command to move the cursor to the start of the current line?
Write the answer into the file \`/root/0x02_vi/beginning_of_the_line\`

{{ACTION_BUTTONS}}

### 7. End of the line {{mandatory}}
What is the command to move the cursor to the end of the line?
Write the answer into the file \`/root/0x02_vi/end_of_the_line\`

{{ACTION_BUTTONS}}`,
	//   quiz: viQuiz,
};
