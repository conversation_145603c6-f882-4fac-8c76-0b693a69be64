// import { shellNavigationQuiz } from "../../project-quizzes/tools/shell-navigation";

export const shellNavigation = {
  title: "0x00. Shell, navigation",
  status: "onboarding-tools",
  content: `# 0x00. Shell, navigation
![Shell](https://img.shields.io/badge/Shell-Navigation-brightgreen)
👤 By <PERSON>
📝 Novice
⚖️ Weight: 1
📅 Start: Feb 25, 2025 6:00 AM
📅 End: Feb 25, 2025 6:00 AM
⏱️ Duration: 1 day
### In a nutshell...
* **Auto QA review:** 0.0/3 mandatory
* **Altogether:** 0.0%
  * **Mandatory:** 0.0%
  * **Optional:** N/A
* **Calculation:** 0.0% + (0.0% * 0.0%) == 0.0%
## Welcome to the Matrix!
![Project Image](/images/1103/assets/cdc24ca146228a91564ccd17686cf1d13764896e.gif)
This project will (probably) be your first experience with an Unix terminal! For this project, please start and connect to your Sandbox \`Ubuntu 20.04\`.
## Resources
**Read or watch:**
* [Linux navigation](https://linuxcommand.org/lc3_lts0020.php)
* [Linux - looking around](https://linuxcommand.org/lc3_lts0030.php)
* [Linux - manipulating files](https://linuxcommand.org/lc3_lts0050.php)
* [Linux - /tmp](https://tldp.org/LDP/Linux-Filesystem-Hierarchy/html/tmp.html)
**Or in your terminal:**
* \`man pwd\`
* \`man ls\`
* \`man cd\`
* \`man less\`
* \`man touch\`
* \`man cp\`
* \`man mv\`
* \`man rm\`
* \`man mkdir\`
* \`man rmdir\`
## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google:**
### General
* How to navigate in an Unix system
* How to list files and directories
* How to display the content of a file
* How to create a file or directory
* How to remove a file or directory
* How to move or copy a file or directory
## Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work
* You are not allowed to publish any content of this project
* Any form of plagiarism is strictly forbidden and will result in removal from the program
## Requirements
### General
* All tasks must be completed within a sandbox \`Ubuntu 20.04\`
* Your sandbox must be available at the end of this project - the Checker will access to it at midnight for running the correction!
## Quiz Questions
## Tasks
### 0. Create me! {{mandatory}}
Access your sandbox and:
* Change your working directory to \`/root\`
* Then, create an empty file \`so_cool\`

**Advices:**
* Don't forget to validate your current working directory
* Don't forget to display the list of files of your current directory to validate the creation of the new file

{{ACTION_BUTTONS}}

### 1. More of me {{mandatory}}
Access your sandbox and:
* Change your working directory to \`/root\`
* Then, copy the file \`school\` to \`/tmp\`

**Advices:**
* Don't forget to validate your current working directory
* Don't forget to display the list of files of your current directory to validate the copy of the file

{{ACTION_BUTTONS}}

### 2. To old {{mandatory}}
Access your sandbox and:
* Change your working directory to \`/root\`
* Then, rename the file \`old_school\` to \`new_school\` (in the same directory)

**Advices:**
* Don't forget to validate your current working directory
* Don't forget to display the list of files of your current directory to validate the renaming of the file

{{ACTION_BUTTONS}}

### 3. Not here {{mandatory}}
Access your sandbox and:
* Change your working directory to \`/root\`
* Then, move the file \`not_here\` to \`/tmp/right_school\`

**Advices:**
* Don't forget to validate your current working directory
* Don't forget to display the list of files of your current directory to validate the move of the file

{{ACTION_BUTTONS}}

### 4. Not anymore {{mandatory}}
Access your sandbox and:
* Change your working directory to \`/root\`
* Then, delete the file \`ready_to_be_removed\`

**Advices:**
* Don't forget to validate your current working directory
* Don't forget to display the list of files of your current directory to validate the removal of the file

{{ACTION_BUTTONS}}

### 5. Organization is key! {{mandatory}}
Access your sandbox and:
* Change your working directory to \`/root\`
* Then, create a directory \`school_is_amazing\`

**Advices:**
* Don't forget to validate your current working directory
* Don't forget to display the list of directories of your current directory to validate the creation of the directory

{{ACTION_BUTTONS}}

### 6. No need {{mandatory}}
Access your sandbox and:
* Change your working directory to \`/root\`
* Then, remove the directory \`empty_directory\`

**Advices:**
* Don't forget to validate your current working directory
* Don't forget to display the list of directories of your current directory to validate the removal of the directory

{{ACTION_BUTTONS}}`,
//   quiz: shellNavigationQuiz,
};
