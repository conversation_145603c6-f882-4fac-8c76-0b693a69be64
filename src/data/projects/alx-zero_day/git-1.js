import { gitQuiz } from "../../project-quizzes/zero-day/git";

export const git = {
	title: "0x01. Git",
	status: "onboarding-tools",
	content: `# 0x01. Git
![Git](https://img.shields.io/badge/Git-Version%20Control-brightgreen)
👤 By <PERSON>
📝 Novice
⚖️ Weight: 1
📅 Start: Feb 25, 2025 6:00 AM
📅 End: Feb 25, 2025 6:00 AM
⏱️ Duration: 1 day

### In a nutshell...
* ** Auto QA review:** 0.0/3 mandatory
* **Altogether:** 0.0%
  * **Mandatory:** 0.0%
  * **Optional:** N/A
* **Calculation:** 0.0% + (0.0% * 0.0%) == 0.0%

## Project Overview
This project introduces the fundamentals of Git, a version control system widely used in software development.

## Concepts
For this project, we expect you to look at these concepts:
* [Struggling with the sandbox? Try this: Using Docker & WSL on your local host](/concepts/100039)
* [Authenticating Git](/concepts/100035)

## Resources
**Read or watch:**
* [Resources to learn Git](https://docs.github.com/en/get-started/getting-started-with-git/set-up-git)
* [About READMEs](https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-readmes)
* [How to write a Git commit message](https://cbea.ms/git-commit/)
**Resources for Advanced Tasks** (Read only after finishing the mandatory tasks)
* [Learning branching](https://learngitbranching.js.org/)
* [Effective pull requests and other good practices for teams using GitHub](https://codeinthehole.com/tips/pull-requests-and-other-good-practices-for-teams-using-github/)

## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google:**

### General
* What is source code management
* What is Git
* What is GitHub
* What is the difference between Git and GitHub
* How to create a repository
* What is a README
* How to write good READMEs
* How to commit
* How to write helpful commit messages
* How to push code
* How to pull updates
* How to create a branch
* How to merge branches
* How to work as collaborators on a project
* Which files should and which files should not appear in your repo

## Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work
* You are not allowed to publish any content of this project
* Any form of plagiarism is strictly forbidden and will result in removal from the program
## Setup and Basic Usage
### Step 1: Git Configuration
Configure your Git username and email:
\`\`\`bash
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
\`\`\`
### Step 2: Repository Creation
1. Create your first repository
2. Add a README.md file
3. Clone the repository:
\`\`\`bash
git clone <repository-url>
\`\`\`
### Step 3: Basic Git Workflow
1. Create or modify files
2. Add files to staging area:
   \`\`\`bash
   git add <filename>
   # or
   git add .  # for all files
   \`\`\`
3. Commit your changes:
   \`\`\`bash
   git commit -m "descriptive message"
   \`\`\`
4. Push to remote repository:
   \`\`\`bash
   git push origin main
   \`\`\`
## Working with Branches
### Creating and Managing Branches
1. Create a new branch:
   \`\`\`bash
   git checkout -b <branch-name>
   \`\`\`
2. Switch between branches:
   \`\`\`bash
   git checkout <branch-name>
   \`\`\`
3. Merge branches:
   \`\`\`bash
   git checkout main
   git merge <branch-name>
   \`\`\`
### Collaboration
1. Pull latest changes:
   \`\`\`bash
   git pull origin main
   \`\`\`
2. Resolve conflicts when they occur
3. Push your changes
4. Create pull requests for review
## Tasks

### 0. Create and setup your Git and GitHub account {{mandatory}}
#### Step 0 - Create an account on GitHub
You will need a GitHub account for all your projects. If you do not have a github.com account, you can create one [here](https://github.com/signup).
{{ACTION_BUTTONS}}

### 1. Repo-session {{mandatory}}
Create a new directory called \`0x01-git\` in your repo.
Make sure you include a not empty \`README.md\` in your directory:
* at the root of your repository
* AND in the directory \`0x01-git\`
{{ACTION_BUTTONS}}

### 2. Coding fury road {{mandatory}}
For the moment we have an empty project directory containing only a \`README.md\`. It's time to code!
* Create these directories at the root of your project: \`bash\`, \`c\`, \`js\`
* Create these empty files:
  * \`c/c_is_fun.c\`
  * \`js/main.js\`
  * \`js/index.js\`
* Create a file \`bash/alx\` with these two lines inside: \`#!/bin/bash\` and \`echo "ALX"\`
* Create a file \`bash/school\` with these two lines inside: \`#!/bin/bash\` and \`echo "School"\`
* Add all these new files to git
* Commit your changes (message: "Starting to code today, so cool") and push to the remote server
{{ACTION_BUTTONS}}

### 3. Best Practices
1. Write clear, descriptive commit messages
2. Create a .gitignore file for files that shouldn't be tracked
3. Keep commits focused and atomic
4. Pull before pushing to avoid conflicts
5. Review changes before committing
{{ACTION_BUTTONS}}`,
  quiz: gitQuiz,
};
