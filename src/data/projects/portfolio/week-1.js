// import { week1Quiz } from "../../project-quizzes/portfolio/week-1";

export const week1 = {
  title: "Build your portfolio project (Week 1): Making Progress",
  status: "portfolio",
  content: `# Build your portfolio project (Week 1): Making Progress
![Portfolio](https://img.shields.io/badge/Portfolio-Project-purple)
👤 By Staff
📝 Advanced
⚖️ Weight: 2
📅 Start: Sep 11, 2025 6:00 AM
📅 End: Sep 18, 2025 6:00 AM
⏱️ Duration: 7 days

### Background Context

This is the first week of building your portfolio project. The focus is on making tangible progress and establishing the foundation of your project.

## Requirements

### General
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS
* All your files should end with a new line
* A \`README.md\` file, at the root of the folder of the project, is mandatory
* Your code should use the \`pycodestyle\` style (version 2.8.*)
* The length of your files will be tested using \`wc\`

## Tasks

### 0. Development Environment Setup
Complete your development environment setup:

* Install all necessary tools and dependencies
* Set up version control
* Configure development tools
* Set up testing framework
* Configure CI/CD pipeline
* Set up project documentation

### 1. Core Features Implementation
Begin implementing core features:

* Set up basic project structure
* Implement data models
* Create database migrations
* Build essential API endpoints
* Develop basic UI components
* Implement core business logic

### 2. Testing Framework
Establish testing infrastructure:

* Write unit tests
* Set up integration tests
* Create test data fixtures
* Implement CI pipeline tests
* Document testing procedures
* Set up code coverage reporting

### 3. Documentation
Start documenting your project:

* Update README with setup instructions
* Document API endpoints
* Create code documentation
* Write development guides
* Document testing procedures
* Create user guides

### 4. Progress Tracking
Track and report progress:

* Update project timeline
* Document completed features
* List known issues
* Plan next steps
* Report blockers
* Update risk assessment

Remember:
* Commit code regularly
* Write clear commit messages
* Keep documentation up to date
* Test as you build
* Seek help when stuck
* Stay focused on MVP features`,
//   quiz: week1Quiz,
};
