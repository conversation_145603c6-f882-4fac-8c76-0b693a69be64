// import { research2Quiz } from "../../project-quizzes/portfolio/research-2";

export const research2 = {
  title: "Research & Project approval (Part 2)",
  status: "portfolio",
  content: `# Research & Project approval (Part 2)
![Portfolio](https://img.shields.io/badge/Portfolio-Research-purple)
👤 By Staff
📝 Advanced
⚖️ Weight: 2
📅 Start: Aug 21, 2025 6:00 AM
📅 End: Aug 28, 2025 6:00 AM
⏱️ Duration: 7 days

### Background Context

This is the second part of the project approval process. Now that you have a general idea of what you want to build, it's time to dive deeper into the technical details.

## Requirements

### General
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS
* All your files should end with a new line
* A \`README.md\` file, at the root of the folder of the project, is mandatory
* Your code should use the \`pycodestyle\` style (version 2.8.*)
* The length of your files will be tested using \`wc\`

## Tasks

### 0. Technical Stack Deep Dive
Document your technical stack in detail:

* Frontend technologies and frameworks
* Backend technologies and frameworks
* Databases and storage solutions
* APIs and external services
* Deployment and hosting strategy
* Testing frameworks and tools

### 1. Architecture Design
Create a detailed architecture design that includes:

* System components and their interactions
* Data flow diagrams
* Database schema
* API endpoints
* User flows
* Security considerations

### 2. Development Environment
Set up your development environment:

* Version control system
* Development tools and IDEs
* Testing frameworks
* CI/CD pipeline
* Code quality tools
* Documentation tools

### 3. Project Structure
Create the initial project structure:

* Directory organization
* File naming conventions
* Code style guidelines
* Documentation standards
* Git workflow

### 4. MVP Features
Define the Minimum Viable Product (MVP) features:

* Core functionality
* User interface design
* Data models
* API specifications
* Testing strategy

Remember:
* Document all technical decisions and their rationale
* Consider scalability and maintainability
* Plan for security from the start
* Keep the MVP scope manageable`,
//   quiz: research2Quiz,
};
