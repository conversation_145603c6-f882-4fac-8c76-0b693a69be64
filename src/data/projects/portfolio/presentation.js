// import { presentationQuiz } from "../../project-quizzes/portfolio/presentation";

export const presentation = {
  title: "Build your portfolio project (Week 3): Presentation",
  status: "portfolio",
  content: `# Build your portfolio project (Week 3): Presentation
![Portfolio](https://img.shields.io/badge/Portfolio-Project-purple)
👤 By Staff
📝 Advanced
⚖️ Weight: 2
📅 Start: Oct 2, 2025 6:00 AM
📅 End: Oct 9, 2025 6:00 AM
⏱️ Duration: 7 days

### Background Context

The presentation is your opportunity to showcase your project to the world. It should effectively communicate your project's value, technical achievements, and future potential.

## Requirements

### General
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS
* All your files should end with a new line
* A \`README.md\` file, at the root of the folder of the project, is mandatory
* Your code should use the \`pycodestyle\` style (version 2.8.*)
* The length of your files will be tested using \`wc\`

## Tasks

### 0. Presentation Structure
Create a compelling presentation structure:

* Introduction
* Problem statement
* Solution overview
* Technical architecture
* Demo
* Future plans
* Q&A preparation

### 1. Content Development
Develop presentation content:

* Project journey
* Technical challenges
* Solutions implemented
* Key features
* Lessons learned
* Team contributions

### 2. Visual Materials
Create supporting materials:

* Slides
* Screenshots
* Diagrams
* Demo video
* Code snippets
* Live demo script

### 3. Delivery Preparation
Prepare for presentation delivery:

* Practice timing
* Rehearse transitions
* Test equipment
* Prepare backup plans
* Anticipate questions
* Plan audience engagement

### 4. Documentation
Prepare supplementary documentation:

* Technical specifications
* User guides
* API documentation
* Deployment guides
* Future roadmap
* Contact information

Remember:
* Tell a compelling story
* Focus on value delivered
* Demonstrate technical expertise
* Be prepared for questions
* Show enthusiasm
* Stay within time limits`,
//   quiz: presentationQuiz,
};
