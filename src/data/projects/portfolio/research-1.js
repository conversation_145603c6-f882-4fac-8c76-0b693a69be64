// import { research1Quiz } from "../../project-quizzes/portfolio/research-1";

export const research1 = {
  title: "Research & Project approval (Part 1)",
  status: "portfolio",
  content: `# Research & Project approval (Part 1)
![Portfolio](https://img.shields.io/badge/Portfolio-Research-purple)
👤 By Staff
📝 Advanced
⚖️ Weight: 2
📅 Start: Aug 14, 2025 6:00 AM
📅 End: Aug 21, 2025 6:00 AM
⏱️ Duration: 7 days

### Background Context

This project is your chance to work on a project that YOU choose. This is your opportunity to shine and show what you've learned!

In the next month, you will be working on your own project. This project can be:
* A personal passion project
* A problem you want to solve
* A company need
* A customer request
* A business idea you want to implement

The goal of this first week is to identify what you want to work on and get feedback and approval from staff members.

## Requirements

### General
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS
* All your files should end with a new line
* A \`README.md\` file, at the root of the folder of the project, is mandatory
* Your code should use the \`pycodestyle\` style (version 2.8.*)
* The length of your files will be tested using \`wc\`

## Tasks

### 0. Share your project proposal
Share your project proposal with the staff. Your proposal should include:

* What the project is about
* What problem it solves
* Who the project is for
* What technologies you plan to use
* Timeline and milestones
* Any risks or challenges you foresee

### 1. Get feedback and iterate
Based on the feedback you receive:

* Refine your project scope
* Adjust your technical approach
* Update your timeline
* Address any concerns raised

### 2. Final proposal
Submit your final project proposal that includes:

* Detailed project description
* Technical requirements
* Project timeline
* Risk assessment
* Success criteria

### 3. Project approval
Get final approval from staff to proceed with your project.

Remember:
* Be realistic about what you can achieve in the given timeframe
* Choose technologies you're comfortable with or can learn quickly
* Consider the end user/customer in your design decisions
* Plan for potential roadblocks`,
//   quiz: research1Quiz,
};
