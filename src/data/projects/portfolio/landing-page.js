// import { landingPageQuiz } from "../../project-quizzes/portfolio/landing-page";

export const landingPage = {
  title: "Build your portfolio project (Week 3): Project Landing Page",
  status: "portfolio",
  content: `# Build your portfolio project (Week 3): Project Landing Page
![Portfolio](https://img.shields.io/badge/Portfolio-Project-purple)
👤 By Staff
📝 Advanced
⚖️ Weight: 2
📅 Start: Sep 25, 2025 6:00 AM
📅 End: Oct 2, 2025 6:00 AM
⏱️ Duration: 7 days

### Background Context

The landing page is your project's first impression. It should effectively communicate what your project does, who it's for, and why it matters.

## Requirements

### General
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS
* All your files should end with a new line
* A \`README.md\` file, at the root of the folder of the project, is mandatory
* Your code should use the \`pycodestyle\` style (version 2.8.*)
* The length of your files will be tested using \`wc\`

## Tasks

### 0. Landing Page Design
Create an engaging landing page:

* Clear value proposition
* Project overview
* Key features
* Screenshots/demos
* Call to action
* Contact information

### 1. Content Creation
Develop compelling content:

* Project description
* Feature highlights
* Use cases
* Technical details
* Team information
* FAQ section

### 2. Visual Design
Implement attractive visual design:

* Color scheme
* Typography
* Layout
* Images and graphics
* Responsive design
* Animation effects

### 3. User Experience
Ensure excellent user experience:

* Navigation
* Page load speed
* Mobile responsiveness
* Accessibility
* Cross-browser compatibility
* Error handling

### 4. SEO and Analytics
Implement SEO best practices:

* Meta tags
* Site structure
* Analytics setup
* Performance monitoring
* Social media integration
* Search optimization

Remember:
* Keep it simple and clear
* Focus on user needs
* Test across devices
* Optimize performance
* Include clear calls to action
* Make it memorable`,
//   quiz: landingPageQuiz,
};
