import { pythonClassesObjects } from './classes-objects';
import { pythonMoreClasses } from './more-classes';
import { pythonEverythingIsObject } from './everything-is-object';
import { pythonInheritance } from './inheritance';
import { pythonInputOutput } from './input-output';
import { pythonAlmostCircle } from './almost-circle';
import { pythonHelloWorld } from './hello-world';
import { pythonIfElseLoopsFunctions } from './if-else-loops-functions';
import { pythonImportModules } from './import-modules';
import { pythonDataStructures } from './data-structures';
import { pythonMoreDataStructures } from './more-data-structures';
import { pythonExceptions } from './exceptions';
import { pythonTestDrivenDevelopment } from './test-driven-development';
import { pythonORM } from './orm';
import { pythonNetwork0 } from './network-0';
import { pythonNetwork1 } from './network-1';

export const pythonProjects = {
  // Core Python Concepts
  helloWorld: pythonHelloWorld,
  ifElseLoopsFunctions: pythonIfElseLoopsFunctions,
  importModules: pythonImportModules,
  dataStructures: pythonDataStructures,
  moreDataStructures: pythonMoreDataStructures,
  exceptions: pythonExceptions,

  // Object-Oriented Programming
  classesObjects: pythonClassesObjects,
  moreClasses: pythonMoreClasses,
  everythingIsObject: pythonEverythingIsObject,
  inheritance: pythonInheritance,
  
  // Testing and Development
  testDrivenDevelopment: pythonTestDrivenDevelopment,
  
  // Input/Output and Advanced Concepts
  inputOutput: pythonInputOutput,
  almostCircle: pythonAlmostCircle,
  
  // Databases and Networking
  orm: pythonORM,
  network0: pythonNetwork0,
  network1: pythonNetwork1
};

export {
  pythonHelloWorld,
  pythonIfElseLoopsFunctions,
  pythonImportModules,
  pythonDataStructures,
  pythonMoreDataStructures,
  pythonExceptions,
  pythonClassesObjects,
  pythonMoreClasses,
  pythonEverythingIsObject,
  pythonInheritance,
  pythonTestDrivenDevelopment,
  pythonInputOutput,
  pythonAlmostCircle,
  pythonORM,
  pythonNetwork0,
  pythonNetwork1
};