// import { pythonNetwork0Quiz } from "../../../project-quizzes/higher-level/python/network-0-quiz";

export const pythonNetwork0 = {
  title: "0x10. Python - Network #0",
  status: "higher-level-programming",
  content: `# 0x10. Python - Network #0
![Python](https://img.shields.io/badge/Python-Programming-blue)
👤 By Guillaume
📝 Intermediate
⚖️ Weight: 1
📅 Start: Jul 5, 2025 6:00 AM
📅 End: Jul 6, 2025 6:00 AM
⏱️ Duration: 24 hours

### Project Summary
* **Auto QA review:** 41.0/47 mandatory & 23.0/23 optional
* **Altogether:** 174.46%
* **Mandatory:** 87.23%
* **Optional:** 100.0%
* **Calculation:** 87.23% + (87.23% * 100.0%) == 174.46%

## Resources

**Read or watch**:
* [HTTP (HyperText Transfer Protocol)](https://www3.ntu.edu.sg/home/<USER>/programming/webprogramming/HTTP_Basics.html)
* [HTTP Cookies](https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies)
* [Using cURL to debug](https://www.youtube.com/watch?v=WxUVU0b95Oc)
* [HTTP Status Codes](https://www.restapitutorial.com/httpstatuscodes.html)

## Learning Objectives

At the end of this project, you are expected to be able to explain to anyone, without the help of Google:

### General
* What a URL is
* What HTTP is
* How to read a URL
* The scheme for a HTTP URL
* What a domain name is
* What a sub-domain is
* How to define a port number in a URL
* What a query string is
* What an HTTP request is
* What an HTTP response is
* What HTTP headers are
* What the HTTP message body is
* What an HTTP request method is
* What an HTTP response status code is
* What an HTTP Cookie is
* How to make a request with cURL
* What happens when you type google.com in your browser

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements

### General
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your scripts will be tested on Ubuntu 20.04 LTS
* All your scripts should be exactly 3 lines long (\`wc -l file\` should print 3)
* All your files should end with a new line
* The first line of all your bash files should be exactly \`#!/bin/bash\`
* All your files must be executable
* The second line of all your Bash scripts should be a comment explaining what is the script doing
* All \`curl\` commands must have the option \`-s\` (silent mode)
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS using python3 (version 3.8.5)
* The first line of all your Python files should be exactly \`#!/usr/bin/python3\`
* Your code should use the pycodestyle (version 2.8.*)
* All your modules should have a documentation (\`python3 -c 'print(__import__("my_module").__doc__)'\`)
* All your classes should have a documentation (\`python3 -c 'print(__import__("my_module").MyClass.__doc__)'\`)
* All your functions (inside and outside a class) should have a documentation (\`python3 -c 'print(__import__("my_module").my_function.__doc__)'\` and \`python3 -c 'print(__import__("my_module").MyClass.my_function.__doc__)'\`)
* A documentation is not a simple word, it's a real sentence explaining what's the purpose of the module, class or method (the length of it will be verified)

## Quiz Questions
Test your understanding of HTTP and networking concepts!

## Tasks

### 0. cURL body size
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that takes in a URL, sends a request to that URL, and displays the size of the body of the response
* The size must be displayed in bytes
* You have to use \`curl\`

Please test your script in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ ./0-body_size.sh 0.0.0.0:5000
10
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 0-body_size.sh

{{ACTION_BUTTONS}}

### 1. cURL to the end
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that takes in a URL, sends a \`GET\` request to the URL, and displays the body of the response
* Display only body of a \`200\` status code response
* You have to use \`curl\`

Please test your script in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ ./1-body.sh 0.0.0.0:5000/route_1 ; echo ""
Route 2
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 1-body.sh

{{ACTION_BUTTONS}}

### 2. cURL Method
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that sends a \`DELETE\` request to the URL passed as the first argument and displays the body of the response
* You have to use \`curl\`

Please test your script in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ ./2-delete.sh 0.0.0.0:5000/route_3 ; echo ""
I'm a DELETE request
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 2-delete.sh

{{ACTION_BUTTONS}}

### 3. cURL only methods
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that takes in a URL and displays all HTTP methods the server will accept.
* You have to use \`curl\`

Please test your script in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ ./3-methods.sh 0.0.0.0:5000/route_4
OPTIONS, HEAD, PUT
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 3-methods.sh

{{ACTION_BUTTONS}}

### 4. cURL headers
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that takes in a URL as an argument, sends a \`GET\` request to the URL, and displays the body of the response
* A header variable \`X-School-User-Id\` must be sent with the value \`98\`
* You have to use \`curl\`

Please test your script in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ ./4-header.sh 0.0.0.0:5000/route_5 ; echo ""
Hello School!
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 4-header.sh

{{ACTION_BUTTONS}}

### 5. cURL POST parameters
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that takes in a URL, sends a \`POST\` request to the passed URL, and displays the body of the response
* A variable \`email\` must be sent with the value \`<EMAIL>\`
* A variable \`subject\` must be sent with the value \`I will always be here for PLD\`
* You have to use \`curl\`

Please test your script in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ ./5-post_params.sh 0.0.0.0:5000/route_6 ; echo ""
POST params:
    email: <EMAIL>
    subject: I will always be here for PLD
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 5-post_params.sh

{{ACTION_BUTTONS}}

### 6. Find a peak
Score: 45.45% (Checks completed: 45.45%)

**Technical interview preparation**:
* You are not allowed to google anything
* Whiteboard first

Write a function that finds **a peak** in a list of unsorted integers.
* Prototype: \`def find_peak(list_of_integers):\`
* You are not allowed to import any module
* Your algorithm must have the lowest complexity (*hint: you don't need to go through all numbers to find a peak*)
* \`6-peak.py\` must contain the function
* \`6-peak.txt\` must contain the complexity of your algorithm: \`O(log(n))\`, \`O(n)\`, \`O(nlog(n))\` or \`O(n2)\`
* **Note**: there may be more than one peak in the list

\`\`\`python
#!/usr/bin/python3
""" Test function find_peak """
find_peak = __import__('6-peak').find_peak

print(find_peak([1, 2, 4, 6, 3]))
print(find_peak([4, 2, 1, 2, 3, 1]))
print(find_peak([2, 2, 2]))
print(find_peak([]))
print(find_peak([-2, -4, 2, 1]))
print(find_peak([4, 2, 1, 2, 2, 2, 3, 1]))

guillaume@ubuntu:~/0x10$ ./6-main.py
6
3
2
None
2
4
guillaume@ubuntu:~/0x10$ wc -l 6-peak.txt 
2 6-peak.txt
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 6-peak.py, 6-peak.txt

{{ACTION_BUTTONS}}

### 7. Only status code
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that sends a request to a URL passed as an argument, and displays only the status code of the response.
* You are not allowed to use any pipe, redirection, etc.
* You are not allowed to use \`;\` and \`&&\`
* You have to use \`curl\`

Please test your script in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ ./100-status_code.sh 0.0.0.0:5000 ; echo ""
200
guillaume@ubuntu:~/0x10$ 
guillaume@ubuntu:~/0x10$ ./100-status_code.sh 0.0.0.0:5000/nop ; echo ""
404
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 100-status_code.sh

{{ACTION_BUTTONS}}

### 8. cURL a JSON file
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that sends a JSON \`POST\` request to a URL passed as the first argument, and displays the body of the response.
* Your script must send a \`POST\` request with the contents of a file, passed with the filename as the second argument of the script, in the body of the request
* You have to use \`curl\`

Please test your scripts in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ cat my_json_0
{
    "name": "John Doe",
    "age": 33
}
guillaume@ubuntu:~/0x10$ ./101-post_json.sh 0.0.0.0:5000/route_json my_json_0 ; echo ""
Valid JSON
guillaume@ubuntu:~/0x10$ 
guillaume@ubuntu:~/0x10$ cat my_json_1
I'm a JSON! really!
guillaume@ubuntu:~/0x10$ ./101-post_json.sh 0.0.0.0:5000/route_json my_json_1 ; echo ""
Not a valid JSON
guillaume@ubuntu:~/0x10$ 
guillaume@ubuntu:~/0x10$ cat my_json_2
{
    "name": "John Doe",
    "age": 33,
}
guillaume@ubuntu:~/0x10$ ./101-post_json.sh 0.0.0.0:5000/route_json my_json_2 ; echo ""
Not a valid JSON
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 101-post_json.sh

{{ACTION_BUTTONS}}

### 9. Catch me if you can!
Score: 100.0% (Checks completed: 100.0%)

Write a Bash script that makes a request to \`0.0.0.0:5000/catch_me\` that causes the server to respond with a message containing \`You got me!\`, in the body of the response.
* You have to use \`curl\`
* You are not allow to use \`echo\`, \`cat\`, etc. to display the final result

Please test your script in the sandbox provided, using the web server running on port 5000

\`\`\`bash
guillaume@ubuntu:~/0x10$ ./102-catch_me.sh ; echo ""
You got me!
guillaume@ubuntu:~/0x10$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x10-python-network_0
* File: 102-catch_me.sh

{{ACTION_BUTTONS}}`,
//   quiz: pythonNetwork0Quiz,
};