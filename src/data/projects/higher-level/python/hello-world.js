// import { pythonHelloWorldQuiz } from "../../../project-quizzes/higher-level/python/hello-world-quiz";

export const pythonHelloWorld = {
  title: "0x00. Python - Hello, World",
  status: "higher-level-programming",
  content: `# 0x00. Python - Hello, World
![Python](https://img.shields.io/badge/Python-Programming-blue)
👤 By <PERSON>
📝 Beginner
⚖️ Weight: 1
📅 Start: Jun 09, 2025 6:00 AM
📅 End: Jun 10, 2025 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell...
* **Auto QA review**: 65.0/100 mandatory
* **Altogether**: 65.0%
  * **Mandatory**: 65.0%
  * **Optional**: No optional tasks

## Resources

**Read or watch**:
* [The Python tutorial](https://docs.python.org/3/tutorial/index.html)
* [Whetting Your Appetite](https://docs.python.org/3/tutorial/appetite.html)
* [Using the Python Interpreter](https://docs.python.org/3/tutorial/interpreter.html)
* [An Informal Introduction to Python](https://docs.python.org/3/tutorial/introduction.html)
* [How To Use String Formatters in Python 3](https://realpython.com/python-f-strings/)
* [Learn to Program](https://www.youtube.com/playlist?list=PLGLfVvz_LVvTn3cK5e6LjhgGiSeVlIRwt)
* [PEP 8 – Style Guide for Python Code](https://intranet.alxswe.com/)

## Learning Objectives

At the end of this project, you are expected to be able to explain to anyone, without the help of Google:

### General
* Why Python programming is awesome
* Who created Python
* Who is Guido van Rossum
* Where does the name 'Python' come from
* What is the Zen of Python
* How to use the Python interpreter
* How to print text and variables using print
* How to use strings
* What are indexing and slicing in Python
* What is the official Python coding style and how to check your code with pycodestyle


### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.
## Requirements

### Python Scripts
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS using python3 (version 3.8.5)
* All your files should end with a new line
* The first line of all your files should be exactly \`#!/usr/bin/python3\`
* A \`README.md\` file at the root of the repo, containing a description of the repository
* A \`README.md\` file, at the root of the folder of this project, is mandatory
* Your code should use the pycodestyle (version \`2.8.*\`)
* All your files must be executable
* The length of your files will be tested using \`wc\`

### Shell Scripts
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your scripts will be tested on Ubuntu 20.04 LTS
* All your scripts should be exactly two lines long (\`wc -l file\` should print 2)
* All your files should end with a new line
* The first line of all your files should be exactly \`#!/bin/bash\`
* All your files must be executable

### C Scripts
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your files will be compiled on Ubuntu 20.04 LTS using gcc, using the options -Wall -Werror -Wextra -pedantic -std=gnu89
* All your files should end with a new line
* Your code should use the \`Betty\` style. It will be checked using [betty-style.pl](https://github.com/alx-tools/Betty/blob/master/betty-style.pl) and [betty-doc.pl](https://github.com/alx-tools/Betty/blob/master/betty-doc.pl)
* You are not allowed to use global variables
* No more than 5 functions per file
* The prototypes of all your functions should be included in your header file called \`lists.h\`
* Don't forget to push your header file
* All your header files should be include guarded

## Quiz Questions
Test your understanding of Python basics with these questions!

## Tasks

### 0. Run Python file

Score: 65.0% (Checks completed: 100.0%)

Write a Shell script that runs a Python script.

The Python file name will be saved in the environment variable \`$PYFILE\`

\`\`\`bash
guillaume@ubuntu:~/py/0x00$ cat main.py 
#!/usr/bin/python3
print("Best School")

guillaume@ubuntu:~/py/0x00$ export PYFILE=main.py
guillaume@ubuntu:~/py/0x00$ ./0-run
Best School
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x00-python-hello_world\`
* File: \`0-run\`
{{ACTION_BUTTONS}}

### 1. Run inline
Score: 100.0% (Checks completed: 100.0%)

Write a Shell script that runs Python code.
* The Python code will be saved in the environment variable \`$PYCODE\`

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ export PYCODE='print(f"Best School: {88+10}")'
guillaume@ubuntu:~/py/0x00$ ./1-run_inline 
Best School: 98
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 1-run_inline

{{ACTION_BUTTONS}}

### 2. Hello, print
Score: 100.0% (Checks completed: 100.0%)

Write a Python script that prints exactly \`"Programming is like building a multilingual puzzle"\`, followed by a new line.
* Use the function \`print\`

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./2-print.py 
"Programming is like building a multilingual puzzle"
guillaume@ubuntu:~/py/0x00$
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 2-print.py

{{ACTION_BUTTONS}}

### 3. Print integer
Score: 100.0% (Checks completed: 100.0%)

Complete this [source code](https://github.com/alx-tools/0x00.py/blob/master/3-print_number.py) to print the integer stored in the variable \`number\`, followed by \`Battery street\`, followed by a new line.
* You can find the source code [here](https://github.com/alx-tools/0x00.py/blob/master/3-print_number.py)
* The output of the script should be:
    * the number, followed by \`Battery street\`,
    * followed by a new line
* You are not allowed to cast the variable \`number\` into a string
* Your code must be 3 lines long
* You have to use f-strings [tips](https://realpython.com/python-f-strings/)

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./3-print_number.py
98 Battery street
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 3-print_number.py

{{ACTION_BUTTONS}}

### 4. Print float
Score: 100.0% (Checks completed: 100.0%)

Complete the [source code](https://github.com/alx-tools/0x00.py/blob/master/4-print_float.py) to print the float stored in the variable \`number\` with a precision of 2 digits.
* You can find the source code [here](https://github.com/alx-tools/0x00.py/blob/master/4-print_float.py)
* The output of the program should be:
    * \`Float:\`, followed by the float with only 2 digits
    * followed by a new line
* You are not allowed to cast \`number\` to string
* You have to use f-strings

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./4-print_float.py
Float: 3.14
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 4-print_float.py

{{ACTION_BUTTONS}}

### 5. Print string
Score: 100.0% (Checks completed: 100.0%)

Complete this [source code](https://github.com/alx-tools/0x00.py/blob/master/5-print_string.py) to print 3 times a string stored in the variable \`str\`, followed by its first 9 characters.
* You can find the source code [here](https://github.com/alx-tools/0x00.py/blob/master/5-print_string.py)
* The output of the program should be:
    * 3 times the value of \`str\`
    * followed by a new line
    * followed by the 9 first characters of \`str\`
    * followed by a new line
* You are not allowed to use any loops or conditional statement
* Your program should be maximum 5 lines long

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./5-print_string.py 
Holberton SchoolHolberton SchoolHolberton School
Holberton
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 5-print_string.py

{{ACTION_BUTTONS}}

### 6. Play with strings
Score: 100.0% (Checks completed: 100.0%)

Complete this [source code](https://github.com/alx-tools/0x00.py/blob/master/6-concat.py) to print \`Welcome to Holberton School!\`
* You can find the source code [here](https://github.com/alx-tools/0x00.py/blob/master/6-concat.py)
* You are not allowed to use any loops or conditional statements.
* You have to use the variables \`str1\` and \`str2\` in your new line of code
* Your program should be exactly 5 lines long

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./6-concat.py
Welcome to Holberton School!
guillaume@ubuntu:~/py/0x00$ wc -l 6-concat.py
5 6-concat.py
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 6-concat.py

{{ACTION_BUTTONS}}

### 7. Copy - Cut - Paste
Score: 100.0% (Checks completed: 100.0%)

Complete this [source code](https://github.com/alx-tools/0x00.py/blob/master/7-edges.py)
* You can find the source code [here](https://github.com/alx-tools/0x00.py/blob/master/7-edges.py)
* You are not allowed to use any loops or conditional statements
* Your program should be exactly 8 lines long
* \`word_first_3\` should contain the first 3 letters of the variable \`word\`
* \`word_last_2\` should contain the last 2 letters of the variable \`word\`
* \`middle_word\` should contain the value of the variable \`word\` without the first and last letters

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./7-edges.py
First 3 letters: Hol
Last 2 letters: on
Middle word: olberto
guillaume@ubuntu:~/py/0x00$ wc -l 7-edges.py
8 7-edges.py
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 7-edges.py

{{ACTION_BUTTONS}}

### 8. Create a new sentence
Score: 100.0% (Checks completed: 100.0%)

Complete this [source code](https://github.com/alx-tools/0x00.py/blob/master/8-concat_edges.py) to print \`object-oriented programming with Python\`, followed by a new line.
* You can find the source code [here](https://github.com/alx-tools/0x00.py/blob/master/8-concat_edges.py)
* You are not allowed to use any loops or conditional statements
* Your program should be exactly 5 lines long
* You are not allowed to create new variables
* You are not allowed to use string literals

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./8-concat_edges.py
object-oriented programming with Python
guillaume@ubuntu:~/py/0x00$ wc -l 8-concat_edges.py
5 8-concat_edges.py
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 8-concat_edges.py

{{ACTION_BUTTONS}}

### 9. Easter Egg
Score: 100.0% (Checks completed: 100.0%)

Write a Python script that prints "The Zen of Python", by TimPeters, followed by a new line.
* Your script should be maximum 98 characters long (please check with \`wc -m 9-easter_egg.py\`)

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./9-easter_egg.py
The Zen of Python, by Tim Peters

Beautiful is better than ugly.
Explicit is better than implicit.
Simple is better than complex.
Complex is better than complicated.
Flat is better than nested.
Sparse is better than dense.
Readability counts.
Special cases aren't special enough to break the rules.
Although practicality beats purity.
Errors should never pass silently.
Unless explicitly silenced.
In the face of ambiguity, refuse the temptation to guess.
There should be one-- and preferably only one --obvious way to do it.
Although that way may not be obvious at first unless you're Dutch.
Now is better than never.
Although never is often better than *right* now.
If the implementation is hard to explain, it's a bad idea.
If the implementation is easy to explain, it may be a good idea.
Namespaces are one honking great idea -- let's do more of those!
guillaume@ubuntu:~/py/0x00$
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 9-easter_egg.py

{{ACTION_BUTTONS}}

### 10. Linked list cycle
Score: 100.0% (Checks completed: 100.0%)

**Technical interview preparation:**
* You are not allowed to google anything
* Whiteboard first
* This task and all future technical interview prep tasks will include checks for the efficiency of your solution, i.e. is your solution's runtime fast enough, does your solution require extra memory usage / mallocs, etc.

Write a function in C that checks if a singly linked list has a cycle in it.
* Prototype: \`int check_cycle(listint_t *list);\`
* Return: \`0\` if there is no cycle, \`1\` if there is a cycle

Requirements:
* Only these functions are allowed: \`write\`, \`printf\`, \`putchar\`, \`puts\`, \`malloc\`, \`free\`

Example:
\`\`\`c
#include <stdlib.h>
#include <string.h>
#include <stdio.h>
#include "lists.h"

/**
 * main - check the code
 *
 * Return: Always 0.
 */
int main(void)
{
    listint_t *head;
    listint_t *current;
    listint_t *temp;
    int i;

    head = NULL;
    add_nodeint(&head, 0);
    add_nodeint(&head, 1);
    add_nodeint(&head, 2);
    add_nodeint(&head, 3);
    add_nodeint(&head, 4);
    add_nodeint(&head, 98);
    add_nodeint(&head, 402);
    add_nodeint(&head, 1024);
    print_listint(head);

    if (check_cycle(head) == 0)
        printf("Linked list has no cycle\\n");
    else if (check_cycle(head) == 1)
        printf("Linked list has a cycle\\n");

    current = head;
    for (i = 0; i < 4; i++)
        current = current->next;
    temp = current->next;
    current->next = head;

    if (check_cycle(head) == 0)
        printf("Linked list has no cycle\\n");
    else if (check_cycle(head) == 1)
        printf("Linked list has a cycle\\n");

    current = head;
    for (i = 0; i < 4; i++)
        current = current->next;
    current->next = temp;

    free_listint(head);

    return (0);
}
\`\`\`

Output:
\`\`\`bash
$ gcc -Wall -Werror -Wextra -pedantic -std=gnu89 10-main.c 10-check_cycle.c 10-linked_lists.c -o cycle
$ ./cycle 
1024
402
98
4
3
2
1
0
Linked list has no cycle
Linked list has a cycle
$
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 10-check_cycle.c, lists.h

{{ACTION_BUTTONS}}

## Advanced Tasks

### 11. Hello, write
Score: 100.0% (Checks completed: 100.0%)

Write a Python script that prints exactly \`and that piece of art is useful - Dora Korpar, 2015-10-19\`, followed by a new line.
* Use the function \`write\` from the \`sys\` module
* You are not allowed to use \`print\`
* Your script should print to \`stderr\`
* Your script should exit with the status code \`1\`

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ ./100-write.py
and that piece of art is useful - Dora Korpar, 2015-10-19
guillaume@ubuntu:~/py/0x00$ echo $?
1
guillaume@ubuntu:~/py/0x00$ ./100-write.py 2> q
guillaume@ubuntu:~/py/0x00$ cat q
and that piece of art is useful - Dora Korpar, 2015-10-19
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 100-write.py

{{ACTION_BUTTONS}}

### 12. Compile
Score: 100.0% (Checks completed: 100.0%)

Write a script that compiles a Python script file.

The Python file name will be stored in the environment variable \`$PYFILE\`

The output filename has to be \`$PYFILEc\` (ex: \`export PYFILE=my_main.py\` => output filename: \`my_main.pyc\`)

Example:
\`\`\`bash
guillaume@ubuntu:~/py/0x00$ cat main.py 
#!/usr/bin/python3
print("Best School")

guillaume@ubuntu:~/py/0x00$ export PYFILE=main.py
guillaume@ubuntu:~/py/0x00$ ./101-compile
Compiling main.py ...
guillaume@ubuntu:~/py/0x00$ ls
101-compile  main.py  main.pyc
guillaume@ubuntu:~/py/0x00$ cat main.pyc | zgrep -c "Best School"
1
guillaume@ubuntu:~/py/0x00$ od -t x1 main.pyc # SYSTEM DEPENDANT => CAN BE DIFFERENT
0000000 ee 0c 0d 0a 91 26 3e 58 31 00 00 00 e3 00 00 00
0000020 00 00 00 00 00 00 00 00 00 02 00 00 00 40 00 00
0000040 00 73 0e 00 00 00 65 00 00 64 00 00 83 01 00 01
0000060 64 01 00 53 29 02 7a 0b 42 65 73 74 20 53 63 68
0000100 6f 6f 6c 4e 29 01 da 05 70 72 69 6e 74 a9 00 72
0000120 02 00 00 00 72 02 00 00 00 fa 07 6d 61 69 6e 2e
0000140 70 79 da 08 3c 6d 6f 64 75 6c 65 3e 02 00 00 00
0000160 73 00 00 00 00
0000165
guillaume@ubuntu:~/py/0x00$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 101-compile

{{ACTION_BUTTONS}}

### 13. ByteCode -> Python #1
Score: 100.0% (Checks completed: 100.0%)

Write the Python function \`def magic_calculation(a, b):\` that does exactly the same as the following Python bytecode:

\`\`\`python
  3           0 LOAD_CONST               1 (98)
              3 LOAD_FAST                0 (a)
              6 LOAD_FAST                1 (b)
              9 BINARY_POWER
             10 BINARY_ADD
             11 RETURN_VALUE
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x00-python-hello_world
* File: 102-magic_calculation.py

{{ACTION_BUTTONS}}

## Quiz questions
Quiz content is maintained separately in quiz files.`,
//   quiz: pythonHelloWorldQuiz,
};