// import { pythonORMQuiz } from "../../../project-quizzes/higher-level/python/orm-quiz";

export const pythonORM = {
  title: "0x0F. Python - Object-relational mapping",
  status: "higher-level-programming",
  content: `# 0x0F. Python - Object-relational mapping
![Python](https://img.shields.io/badge/Python-Programming-blue)
👤 By Guillaume
📝 Advanced
⚖️ Weight: 1
📅 Start: Jul 7, 2025 6:00 AM
📅 End: Jul 10, 2025 6:00 AM
⏱️ Duration: 3 days

### Project Summary
* **Auto QA review:** 65.0/100 mandatory
* **Altogether:** 65.0%
* **Mandatory:** 65.0%
* **Optional:** No optional tasks

## Resources

**Read or watch**:
* [Object-relational mappers](https://www.fullstackpython.com/object-relational-mappers-orms.html)
* [mysqlclient/MySQLdb documentation](https://mysqlclient.readthedocs.io/)
* [MySQLdb tutorial](https://www.tutorialspoint.com/python/python_database_access.htm)
* [SQLAlchemy tutorial](https://docs.sqlalchemy.org/en/13/orm/tutorial.html)
* [SQLAlchemy](https://docs.sqlalchemy.org/en/13/)
* [Introduction to SQLAlchemy](https://www.youtube.com/watch?v=woKYyhLCcnU)
* [Flask SQLAlchemy](https://www.youtube.com/playlist?list=PLXmMXHVSvS-BlLA5beNJojJLlpE0PJgCW)
* [10 common stumbling blocks for SQLAlchemy newbies](http://alextechrants.blogspot.com/2013/11/10-common-stumbling-blocks-for.html)
* [Python SQLAlchemy Cheatsheet](https://www.pythonsheets.com/notes/python-sqlalchemy.html)
* [SQLAlchemy ORM Tutorial for Python Developers](https://auth0.com/blog/sqlalchemy-orm-tutorial-for-python-developers/)

## Learning Objectives

At the end of this project, you are expected to be able to explain to anyone, without the help of Google:

### General
* Why Python programming is awesome
* How to connect to a MySQL database from a Python script
* How to SELECT rows in a MySQL table from a Python script
* How to INSERT rows in a MySQL table from a Python script
* What ORM means
* How to map a Python Class to a MySQL table
* How to create a Python Virtual Environment


### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.
## Requirements

### General
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS using python3 (version 3.8.5)
* Your files will be executed with \`MySQLdb\` version 2.0.x
* Your files will be executed with \`SQLAlchemy\` version 1.4.x
* All your files should end with a new line
* The first line of all your files should be exactly \`#!/usr/bin/python3\`
* A \`README.md\` file, at the root of the folder of the project, is mandatory
* Your code should use the pycodestyle (version 2.8.*)
* All your files must be executable
* The length of your files will be tested using \`wc\`
* All your modules should have a documentation (\`python3 -c 'print(__import__("my_module").__doc__)'\`)
* All your classes should have a documentation (\`python3 -c 'print(__import__("my_module").MyClass.__doc__)'\`)
* All your functions (inside and outside a class) should have a documentation (\`python3 -c 'print(__import__("my_module").my_function.__doc__)'\` and \`python3 -c 'print(__import__("my_module").MyClass.my_function.__doc__)'\`)
* A documentation is not a simple word, it's a real sentence explaining what's the purpose of the module, class or method (the length of it will be verified)
* You are not allowed to use \`execute\` with sqlalchemy

## Quiz Questions
Test your understanding of Python ORM concepts!

## Tasks

### 0. Get all states

Score: 50.0% (Checks completed: 100.0%)

Write a script that lists all \`states\` from the database \`hbtn_0e_0_usa\`:
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\` (no argument validation needed)
* You must use the module \`MySQLdb\` (\`import MySQLdb\`)
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Results must be sorted in ascending order by \`states.id\`
* Results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 0-select_states.sql
-- Create states table in hbtn_0e_0_usa with some data
CREATE DATABASE IF NOT EXISTS hbtn_0e_0_usa;
USE hbtn_0e_0_usa;
CREATE TABLE IF NOT EXISTS states ( 
    id INT NOT NULL AUTO_INCREMENT, 
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id)
);
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

guillaume@ubuntu:~/0x0F$ cat 0-select_states.sql | mysql -uroot -p
Enter password: 
guillaume@ubuntu:~/0x0F$ ./0-select_states.py root root hbtn_0e_0_usa
(1, 'California')
(2, 'Arizona')
(3, 'Texas')
(4, 'New York')
(5, 'Nevada')
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`0-select_states.py\`

### 1. Filter states

Score: 50.0% (Checks completed: 100.0%)

Write a script that lists all \`states\` with a \`name\` starting with \`N\` (upper N) from the database \`hbtn_0e_0_usa\`:
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\` (no argument validation needed)
* You must use the module \`MySQLdb\` (\`import MySQLdb\`)
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Results must be sorted in ascending order by \`states.id\`
* Results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 0-select_states.sql
-- Create states table in hbtn_0e_0_usa with some data
CREATE DATABASE IF NOT EXISTS hbtn_0e_0_usa;
USE hbtn_0e_0_usa;
CREATE TABLE IF NOT EXISTS states ( 
    id INT NOT NULL AUTO_INCREMENT, 
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id)
);
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

guillaume@ubuntu:~/0x0F$ cat 0-select_states.sql | mysql -uroot -p
Enter password: 
guillaume@ubuntu:~/0x0F$ ./1-filter_states.py root root hbtn_0e_0_usa
(4, 'New York')
(5, 'Nevada')
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`1-filter_states.py\`

### 2. Filter states by user input

Score: 50.0% (Checks completed: 100.0%)

Write a script that takes in an argument and displays all values in the \`states\` table of \`hbtn_0e_0_usa\` where \`name\` matches the argument.
* Your script should take 4 arguments: \`mysql username\`, \`mysql password\`, \`database name\` and \`state name searched\` (no argument validation needed)
* You must use the module \`MySQLdb\` (\`import MySQLdb\`)
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* You must use \`format\` to create the SQL query with the user input
* Results must be sorted in ascending order by \`states.id\`
* The results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 0-select_states.sql
-- Create states table in hbtn_0e_0_usa with some data
CREATE DATABASE IF NOT EXISTS hbtn_0e_0_usa;
USE hbtn_0e_0_usa;
CREATE TABLE IF NOT EXISTS states ( 
    id INT NOT NULL AUTO_INCREMENT, 
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id)
);
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

guillaume@ubuntu:~/0x0F$ cat 0-select_states.sql | mysql -uroot -p
Enter password: 
guillaume@ubuntu:~/0x0F$ ./2-my_filter_states.py root root hbtn_0e_0_usa 'Arizona'
(2, 'Arizona')
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`2-my_filter_states.py\`

### 3. SQL Injection...

Score: 50.0% (Checks completed: 100.0%)

Wait, do you remember the previous task? Did you test \`"Arizona'; TRUNCATE TABLE states ; SELECT * FROM states WHERE name = '"\` as an input?

\`\`\`
guillaume@ubuntu:~/0x0F$ ./2-my_filter_states.py root root hbtn_0e_0_usa "Arizona'; TRUNCATE TABLE states ; SELECT * FROM states WHERE name = '"
(2, 'Arizona')
guillaume@ubuntu:~/0x0F$ ./0-select_states.py root root hbtn_0e_0_usa
guillaume@ubuntu:~/0x0F$ 
\`\`\`

What? Empty?

Yes, it's an SQL injection to delete all records of a table…

Once again, write a script that takes in arguments and displays all values in the \`states\` table of \`hbtn_0e_0_usa\` where \`name\` matches the argument. But this time, write one that is safe from MySQL injections!
* Your script should take 4 arguments: \`mysql username\`, \`mysql password\`, \`database name\` and \`state name searched\` (safe from MySQL injection)
* You must use the module \`MySQLdb\` (\`import MySQLdb\`)
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Results must be sorted in ascending order by \`states.id\`
* The results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 0-select_states.sql
-- Create states table in hbtn_0e_0_usa with some data
CREATE DATABASE IF NOT EXISTS hbtn_0e_0_usa;
USE hbtn_0e_0_usa;
CREATE TABLE IF NOT EXISTS states ( 
    id INT NOT NULL AUTO_INCREMENT, 
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id)
);
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

guillaume@ubuntu:~/0x0F$ cat 0-select_states.sql | mysql -uroot -p
Enter password: 
guillaume@ubuntu:~/0x0F$ ./3-my_safe_filter_states.py root root hbtn_0e_0_usa 'Arizona'
(2, 'Arizona')
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`3-my_safe_filter_states.py\`

### 4. Cities by states

Score: 50.0% (Checks completed: 100.0%)

Write a script that lists all \`cities\` from the database \`hbtn_0e_4_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`MySQLdb\` (\`import MySQLdb\`)
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Results must be sorted in ascending order by \`cities.id\`
* You can use only \`execute()\` once
* Results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 4-cities_by_state.sql
-- Create states table in hbtn_0e_4_usa with some data
CREATE DATABASE IF NOT EXISTS hbtn_0e_4_usa;
USE hbtn_0e_4_usa;
CREATE TABLE IF NOT EXISTS states ( 
    id INT NOT NULL AUTO_INCREMENT, 
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id)
);
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

CREATE TABLE IF NOT EXISTS cities ( 
    id INT NOT NULL AUTO_INCREMENT, 
    state_id INT NOT NULL,
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY(state_id) REFERENCES states(id)
);
INSERT INTO cities (state_id, name) VALUES (1, "San Francisco"), (1, "San Jose"), (1, "Los Angeles"), (1, "Fremont"), (1, "Livermore");
INSERT INTO cities (state_id, name) VALUES (2, "Page"), (2, "Phoenix");
INSERT INTO cities (state_id, name) VALUES (3, "Dallas"), (3, "Houston"), (3, "Austin");
INSERT INTO cities (state_id, name) VALUES (4, "New York");
INSERT INTO cities (state_id, name) VALUES (5, "Las Vegas"), (5, "Reno"), (5, "Henderson"), (5, "Carson City");

guillaume@ubuntu:~/0x0F$ cat 4-cities_by_state.sql | mysql -uroot -p
Enter password: 
guillaume@ubuntu:~/0x0F$ ./4-cities_by_state.py root root hbtn_0e_4_usa
(1, 'San Francisco', 'California')
(2, 'San Jose', 'California')
(3, 'Los Angeles', 'California')
(4, 'Fremont', 'California')
(5, 'Livermore', 'California')
(6, 'Page', 'Arizona')
(7, 'Phoenix', 'Arizona')
(8, 'Dallas', 'Texas')
(9, 'Houston', 'Texas')
(10, 'Austin', 'Texas')
(11, 'New York', 'New York')
(12, 'Las Vegas', 'Nevada')
(13, 'Reno', 'Nevada')
(14, 'Henderson', 'Nevada')
(15, 'Carson City', 'Nevada')
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`4-cities_by_state.py\`

### 5. All cities by state

Score: 50.0% (Checks completed: 100.0%)

Write a script that takes in the name of a state as an argument and lists all \`cities\` of that state, using the database \`hbtn_0e_4_usa\`
* Your script should take 4 arguments: \`mysql username\`, \`mysql password\`, \`database name\` and \`state name\` (SQL injection free!)
* You must use the module \`MySQLdb\` (\`import MySQLdb\`)
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Results must be sorted in ascending order by \`cities.id\`
* You can use only \`execute()\` once
* The results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 4-cities_by_state.sql
-- Create states table in hbtn_0e_4_usa with some data
CREATE DATABASE IF NOT EXISTS hbtn_0e_4_usa;
USE hbtn_0e_4_usa;
CREATE TABLE IF NOT EXISTS states ( 
    id INT NOT NULL AUTO_INCREMENT, 
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id)
);
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

CREATE TABLE IF NOT EXISTS cities ( 
    id INT NOT NULL AUTO_INCREMENT, 
    state_id INT NOT NULL,
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY(state_id) REFERENCES states(id)
);
INSERT INTO cities (state_id, name) VALUES (1, "San Francisco"), (1, "San Jose"), (1, "Los Angeles"), (1, "Fremont"), (1, "Livermore");
INSERT INTO cities (state_id, name) VALUES (2, "Page"), (2, "Phoenix");
INSERT INTO cities (state_id, name) VALUES (3, "Dallas"), (3, "Houston"), (3, "Austin");
INSERT INTO cities (state_id, name) VALUES (4, "New York");
INSERT INTO cities (state_id, name) VALUES (5, "Las Vegas"), (5, "Reno"), (5, "Henderson"), (5, "Carson City");

guillaume@ubuntu:~/0x0F$ ./5-filter_cities.py root root hbtn_0e_4_usa 'Texas'

guillaume@ubuntu:~/0x0F$ cat 4-cities_by_state.sql | mysql -uroot -p
Enter password: 
guillaume@ubuntu:~/0x0F$ ./5-filter_cities.py root root hbtn_0e_4_usa 'Texas'
Dallas, Houston, Austin
guillaume@ubuntu:~/0x0F$ ./5-filter_cities.py root root hbtn_0e_4_usa 'Hawaii'

guillaume@ubuntu:~/0x0F$  
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`5-filter_cities.py\`

### 6. First state model

Score: 50.0% (Checks completed: 100.0%)

![](/images/283/assets/f84fe6edb9436c8560996c6d72e17ea51dab28e1.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233519Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=e80db9b15f822edaa1c5cf65dacfcabbaef30b0dbe8245ca23e1c6f48181811a)

Write a python file that contains the class definition of a \`State\` and an instance \`Base = declarative_base()\`:
* \`State\` class:
* inherits from \`Base\` [Tips](https://docs.sqlalchemy.org/en/13/orm/extensions/declarative/basic_use.html)
* links to the MySQL table \`states\`
* class attribute \`id\` that represents a column of an auto-generated, unique integer, can't be null and is a primary key
* class attribute \`name\` that represents a column of a string with maximum 128 characters and can't be null
* You must use the module \`SQLAlchemy\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* **WARNING:** all classes who inherit from \`Base\` **must** be imported before calling \`Base.metadata.create_all(engine)\`

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 6-model_state.sql
-- Create database hbtn_0e_6_usa
CREATE DATABASE IF NOT EXISTS hbtn_0e_6_usa;
USE hbtn_0e_6_usa;
SHOW CREATE TABLE states;

guillaume@ubuntu:~/0x0F$ cat 6-model_state.sql | mysql -uroot -p
Enter password: 
ERROR 1146 (42S02) at line 4: Table 'hbtn_0e_6_usa.states' doesn't exist
guillaume@ubuntu:~/0x0F$ cat 6-model_state.py
#!/usr/bin/python3
"""Start link class to table in database 
"""
import sys
from model_state import Base, State

from sqlalchemy import (create_engine)

if __name__ == "__main__":
    engine = create_engine('mysql+mysqldb://{}:{}@localhost/{}'.format(sys.argv[1], sys.argv[2], sys.argv[3]), pool_pre_ping=True)
    Base.metadata.create_all(engine)

guillaume@ubuntu:~/0x0F$ ./6-model_state.py root root hbtn_0e_6_usa
guillaume@ubuntu:~/0x0F$ cat 6-model_state.sql | mysql -uroot -p
Enter password: 
Table   Create Table
states  CREATE TABLE \`states\` (\n  \`id\` int(11) NOT NULL AUTO_INCREMENT,\n  \`name\` varchar(128) NOT NULL,\n  PRIMARY KEY (\`id\`)\n) ENGINE=InnoDB DEFAULT CHARSET=latin1
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`model_state.py\`

{{ACTION_BUTTONS}}

### 7. All states via SQLAlchemy

Score: 50.0% (Checks completed: 100.0%)

Write a script that lists all \`State\` objects from the database \`hbtn_0e_6_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Results must be sorted in ascending order by \`states.id\`
* The results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 7-model_state_fetch_all.sql
-- Insert states
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

guillaume@ubuntu:~/0x0F$ cat 7-model_state_fetch_all.sql | mysql -uroot -p hbtn_0e_6_usa
Enter password: 
guillaume@ubuntu:~/0x0F$ ./7-model_state_fetch_all.py root root hbtn_0e_6_usa
1: California
2: Arizona
3: Texas
4: New York
5: Nevada
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`7-model_state_fetch_all.py\`

### 8. First state

Score: 50.0% (Checks completed: 100.0%)

Write a script that prints the first \`State\` object from the database \`hbtn_0e_6_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* The state you display must be the first in \`states.id\`
* You are not allowed to fetch all states from the database before displaying the result
* The results must be displayed as they are in the example below
* If the table \`states\` is empty, print \`Nothing\` followed by a new line
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ ./8-model_state_fetch_first.py root root hbtn_0e_6_usa
1: California
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`8-model_state_fetch_first.py\`

### 9. Contains \`a\`

Score: 50.0% (Checks completed: 100.0%)

Write a script that lists all \`State\` objects that contain the letter \`a\` from the database \`hbtn_0e_6_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Results must be sorted in ascending order by \`states.id\`
* The results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ ./9-model_state_filter_a.py root root hbtn_0e_6_usa
1: California
2: Arizona
3: Texas
5: Nevada
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`9-model_state_filter_a.py\`

### 10. Get a state

Score: 50.0% (Checks completed: 100.0%)

Write a script that prints the \`State\` object with the \`name\` passed as argument from the database \`hbtn_0e_6_usa\`
* Your script should take 4 arguments: \`mysql username\`, \`mysql password\`, \`database name\` and \`state name to search\` (SQL injection free)
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* You can assume you have one record with the state name to search
* Results must display the \`states.id\`
* If no state has the name you searched for, display \`Not found\`
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ ./10-model_state_my_get.py root root hbtn_0e_6_usa Texas
3
guillaume@ubuntu:~/0x0F$ ./10-model_state_my_get.py root root hbtn_0e_6_usa Illinois
Not found
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`10-model_state_my_get.py\`

### 11. Add a new state

Score: 50.0% (Checks completed: 100.0%)

Write a script that adds the \`State\` object "Louisiana" to the database \`hbtn_0e_6_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Print the new \`states.id\` after creation
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ ./11-model_state_insert.py root root hbtn_0e_6_usa 
6
guillaume@ubuntu:~/0x0F$ ./7-model_state_fetch_all.py root root hbtn_0e_6_usa 
1: California
2: Arizona
3: Texas
4: New York
5: Nevada
6: Louisiana
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`11-model_state_insert.py\`

### 12. Update a state

Score: 50.0% (Checks completed: 100.0%)

Write a script that changes the name of a \`State\` object from the database \`hbtn_0e_6_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Change the name of the \`State\` where \`id = 2\` to \`New Mexico\`
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ ./12-model_state_update_id_2.py root root hbtn_0e_6_usa 
guillaume@ubuntu:~/0x0F$ ./7-model_state_fetch_all.py root root hbtn_0e_6_usa 
1: California
2: New Mexico
3: Texas
4: New York
5: Nevada
6: Louisiana
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`12-model_state_update_id_2.py\`

### 13. Delete states

Score: 50.0% (Checks completed: 100.0%)

Write a script that deletes all \`State\` objects with a name containing the letter \`a\` from the database \`hbtn_0e_6_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ ./13-model_state_delete_a.py root root hbtn_0e_6_usa 
guillaume@ubuntu:~/0x0F$ ./7-model_state_fetch_all.py root root hbtn_0e_6_usa 
2: New Mexico
4: New York
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`13-model_state_delete_a.py\`

### 14. Cities in state

Score: 50.0% (Checks completed: 100.0%)

Write a Python file similar to \`model_state.py\` named \`model_city.py\` that contains the class definition of a \`City\`.
* \`City\` class:
* inherits from \`Base\` (imported from \`model_state\`)
* links to the MySQL table \`cities\`
* class attribute \`id\` that represents a column of an auto-generated, unique integer, can't be null and is a primary key
* class attribute \`name\` that represents a column of a string of 128 characters and can't be null
* class attribute \`state_id\` that represents a column of an integer, can't be null and is a foreign key to \`states.id\`
* You must use the module \`SQLAlchemy\`

Next, write a script \`14-model_city_fetch_by_state.py\` that prints all \`City\` objects from the database \`hbtn_0e_14_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* Results must be sorted in ascending order by \`cities.id\`
* Results must be display as they are in the example below (\`<state name>: (<city id>) <city name>\`)
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 14-model_city_fetch_by_state.sql
-- Create database hbtn_0e_14_usa, tables states and cities + some data
CREATE DATABASE IF NOT EXISTS hbtn_0e_14_usa;
USE hbtn_0e_14_usa;

CREATE TABLE IF NOT EXISTS states ( 
    id INT NOT NULL AUTO_INCREMENT, 
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id)
);
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

CREATE TABLE IF NOT EXISTS cities ( 
    id INT NOT NULL AUTO_INCREMENT, 
    state_id INT NOT NULL,
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY(state_id) REFERENCES states(id)
);
INSERT INTO cities (state_id, name) VALUES (1, "San Francisco"), (1, "San Jose"), (1, "Los Angeles"), (1, "Fremont"), (1, "Livermore");
INSERT INTO cities (state_id, name) VALUES (2, "Page"), (2, "Phoenix");
INSERT INTO cities (state_id, name) VALUES (3, "Dallas"), (3, "Houston"), (3, "Austin");
INSERT INTO cities (state_id, name) VALUES (4, "New York");
INSERT INTO cities (state_id, name) VALUES (5, "Las Vegas"), (5, "Reno"), (5, "Henderson"), (5, "Carson City");

guillaume@ubuntu:~/0x0F$ cat 100-relationship_states_cities.sql | mysql -uroot -p
Enter password: 
guillaume@ubuntu:~/0x0F$ ./14-model_city_fetch_by_state.py root root hbtn_0e_14_usa
California: (1) San Francisco
California: (2) San Jose
California: (3) Los Angeles
California: (4) Fremont
California: (5) Livermore
Arizona: (6) Page
Arizona: (7) Phoenix
Texas: (8) Dallas
Texas: (9) Houston
Texas: (10) Austin
New York: (11) New York
Nevada: (12) Las Vegas
Nevada: (13) Reno
Nevada: (14) Henderson
Nevada: (15) Carson City
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`model_city.py, 14-model_city_fetch_by_state.py\`

{{ACTION_BUTTONS}}

### 15. City relationship

Score: 50.0% (Checks completed: 100.0%)

Improve the files \`model_city.py\` and \`model_state.py\`, and save them as \`relationship_city.py\` and \`relationship_state.py\`:
* \`City\` class:
* No change
* \`State\` class:
* In addition to previous requirements, the class attribute \`cities\` must represent a relationship with the class \`City\`. If the \`State\` object is deleted, all linked \`City\` objects must be automatically deleted. Also, the reference from a \`City\` object to his \`State\` should be named \`state\`
* You must use the module \`SQLAlchemy\`

Write a script that creates the \`State\` "California" with the \`City\` "San Francisco" from the database \`hbtn_0e_100_usa\`: (\`100-relationship_states_cities.py\`)
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* You must import \`State\` and \`Base\` from \`model_state\` - \`from model_state import Base, State\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* You must use the \`cities\` relationship for all \`State\` objects
* Your code should not be executed when imported

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 100-relationship_states_cities.sql
-- Create the database hbtn_0e_100_usa
CREATE DATABASE IF NOT EXISTS hbtn_0e_100_usa;
USE hbtn_0e_100_usa;

SELECT * FROM states;
SELECT * FROM cities;

guillaume@ubuntu:~/0x0F$ cat 100-relationship_states_cities.sql | mysql -uroot -p
Enter password: 
ERROR 1146 (42S02) at line 5: Table 'hbtn_0e_100_usa.states' doesn't exist
guillaume@ubuntu:~/0x0F$ ./100-relationship_states_cities.py root root hbtn_0e_100_usa
guillaume@ubuntu:~/0x0F$ cat 100-relationship_states_cities.sql | mysql -uroot -p
Enter password: 
id  name
1   California
id  name    state_id
1   San Francisco   1
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`relationship_city.py, relationship_state.py, 100-relationship_states_cities.py\`

{{ACTION_BUTTONS}}

### 16. List relationship

Score: 50.0% (Checks completed: 100.0%)

Write a script that lists all \`State\` objects, and corresponding \`City\` objects, contained in the database \`hbtn_0e_101_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* The connection to your MySQL server must be to \`localhost\` on port \`3306\`
* You must only use one query to the database
* You must use the \`cities\` relationship for all \`State\` objects
* Results must be sorted in ascending order by \`states.id\` and \`cities.id\`
* Results must be display as they are in the example below
* Your code should not be executed when imported

\`\`\`
<state id>: <state name>
<tabulation><city id>: <city name>
\`\`\`

\`\`\`
guillaume@ubuntu:~/0x0F$ cat 101-relationship_states_cities_list.sql
-- Create states table in hbtn_0e_101_usa with some data
CREATE DATABASE IF NOT EXISTS hbtn_0e_101_usa;
USE hbtn_0e_101_usa;
CREATE TABLE IF NOT EXISTS states ( 
    id INT NOT NULL AUTO_INCREMENT, 
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id)
);
INSERT INTO states (name) VALUES ("California"), ("Arizona"), ("Texas"), ("New York"), ("Nevada");

CREATE TABLE IF NOT EXISTS cities ( 
    id INT NOT NULL AUTO_INCREMENT, 
    state_id INT NOT NULL,
    name VARCHAR(256) NOT NULL,
    PRIMARY KEY (id),
    FOREIGN KEY(state_id) REFERENCES states(id)
);
INSERT INTO cities (state_id, name) VALUES (1, "San Francisco"), (1, "San Jose"), (1, "Los Angeles"), (1, "Fremont"), (1, "Livermore");
INSERT INTO cities (state_id, name) VALUES (2, "Page"), (2, "Phoenix");
INSERT INTO cities (state_id, name) VALUES (3, "Dallas"), (3, "Houston"), (3, "Austin");
INSERT INTO cities (state_id, name) VALUES (4, "New York");
INSERT INTO cities (state_id, name) VALUES (5, "Las Vegas"), (5, "Reno"), (5, "Henderson"), (5, "Carson City");

guillaume@ubuntu:~/0x0F$ cat 101-relationship_states_cities_list.sql | mysql -uroot -p
guillaume@ubuntu:~/0x0F$ ./101-relationship_states_cities_list.py root root hbtn_0e_101_usa
1: California
    1: San Francisco
    2: San Jose
    3: Los Angeles
    4: Fremont
    5: Livermore
2: Arizona
    6: Page
    7: Phoenix
3: Texas
    8: Dallas
    9: Houston
    10: Austin
4: New York
    11: New York
5: Nevada
    12: Las Vegas
    13: Reno
    14: Henderson
    15: Carson City
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`101-relationship_states_cities_list.py\`

{{ACTION_BUTTONS}}

### 17. From city

Score: 50.0% (Checks completed: 100.0%)

Write a script that lists all \`City\` objects from the database \`hbtn_0e_101_usa\`
* Your script should take 3 arguments: \`mysql username\`, \`mysql password\` and \`database name\`
* You must use the module \`SQLAlchemy\`
* Your script should connect to a MySQL server running on \`localhost\` at port \`3306\`
* You must use only one query to the database
* You must use the \`state\` relationship to access to the \`State\` object linked to the \`City\` object
* Results must be sorted in ascending order by \`cities.id\`
* Results must be displayed as they are in the example below
* Your code should not be executed when imported

\`\`\`
<city id>: <city name> -> <state name>
\`\`\`

\`\`\`
guillaume@ubuntu:~/0x0F$ ./102-relationship_cities_states_list.py root root hbtn_0e_101_usa
1: San Francisco -> California
2: San Jose -> California
3: Los Angeles -> California
4: Fremont -> California
5: Livermore -> California
6: Page -> Arizona
7: Phoenix -> Arizona
8: Dallas -> Texas
9: Houston -> Texas
10: Austin -> Texas
11: New York -> New York
12: Las Vegas -> Nevada
13: Reno -> Nevada
14: Henderson -> Nevada
15: Carson City -> Nevada
guillaume@ubuntu:~/0x0F$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: \`alx-higher_level_programming\`
* Directory: \`0x0F-python-object_relational_mapping\`
* File: \`102-relationship_cities_states_list.py\`
{{ACTION_BUTTONS}}
`,
  // quiz: pythonORMQuiz,
};