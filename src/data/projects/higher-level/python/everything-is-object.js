// import { pythonEverythingIsObjectQuiz } from "../../../project-quizzes/higher-level/python/everything-is-object-quiz";

export const pythonEverythingIsObject = {
  title: "0x09. Python - Everything is object",
  status: "higher-level-programming",
  content: `# 0x09. Python - Everything is object
![Python](https://img.shields.io/badge/Python-Programming-blue)
👤 By Guillaume
📝 Intermediate
⚖️ Weight: 1
📅 Start: Jul 25, 2025 6:00 AM
📅 End: Jul 26, 2025 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell...
* **Auto QA review**: 100.0/100 mandatory
* **Altogether**: 100.0%
  * **Mandatory**: 100.0%
  * **Optional**: No optional tasks

## Background Context

Now that we understand that everything is an object and have a little bit of knowledge, let's pause and look a little bit closer at how Python works with different types of objects.

BTW, have you ever modified a variable without knowing it or wanting to? I mean:

\`\`\`python
>>> a = 1
>>> b = a
>>> a = 2
>>> b
1
>>> 
\`\`\`

OK. But what about this?

\`\`\`python
>>> l = [1, 2, 3]
>>> m = l
>>> l[0] = 'x'
>>> m
['x', 2, 3]
>>> 
\`\`\`

This project is a little bit different than the usual projects. The first part is only questions about Python's specificity like "What would be the result of...". You should read all documentation first (as usual :)), then take the time to think and brainstorm with your peers about what you think and why. Try to do this without coding anything for a few hours.

Trying examples in the Python interpreter will help you test your assumptions but not understand. They can be executed directly in your terminal or in Jupyter notebooks.

## Resources

**Read or watch**:
* [9.10. Objects and values](http://www.openbookproject.net/thinkcs/python/english2e/ch09.html#objects-and-values)
* [9.11. Aliasing](http://www.openbookproject.net/thinkcs/python/english2e/ch09.html#aliasing)
* [Immutable vs mutable types](https://stackoverflow.com/questions/8056130/immutable-vs-mutable-types)
* [Mutation](http://composingprograms.com/pages/24-mutable-data.html#sequence-objects)
* [9.12. Cloning lists](http://www.openbookproject.net/thinkcs/python/english2e/ch09.html#cloning-lists)
* [Python tuples: immutable but potentially changing](http://radar.oreilly.com/2014/10/python-tuples-immutable-but-potentially-changing.html)

## Learning Objectives

At the end of this project, you are expected to be able to explain to anyone, without the help of Google:

### General
* Why Python programming is awesome
* What is an object
* What is the difference between a class and an object or instance
* What is the difference between immutable object and mutable object
* What is a reference
* What is an assignment
* What is an alias
* How to know if two variables are identical
* How to know if two variables are linked to the same object
* How to display the variable identifier (which is the memory address in the CPython implementation)
* What is mutable and immutable
* What are the built-in mutable types
* What are the built-in immutable types
* How does Python pass variables to functions

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements

### Python Scripts
* Allowed editors: vi, vim, emacs
* All your files will be interpreted/compiled on Ubuntu 20.04 LTS using python3 (version 3.8.5)
* All your files should end with a new line
* The first line of all your files should be exactly #!/usr/bin/python3
* A README.md file, at the root of the folder of the project, is mandatory
* Your code should use the pycodestyle (version 2.8.*)
* All your files must be executable
* The length of your files will be tested using wc

### .txt Answer Files
* Only one line
* No Shebang
* All your files should end with a new line

## Tasks

### 0. Who am I?
Score: 100.0% (Checks completed: 100.0%)

What function would you use to get the type of an object?

Write the name of the function in the file, without ().

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 0-answer.txt

{{ACTION_BUTTONS}}

### 1. Where are you?
Score: 100.0% (Checks completed: 100.0%)

How do you get the variable identifier (which is the memory address in the CPython implementation)?

Write the name of the function in the file, without ().

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 1-answer.txt

{{ACTION_BUTTONS}}

### 2. Right count
Score: 100.0% (Checks completed: 100.0%)

In the following code, do \`a\` and \`b\` point to the same object? Answer with Yes or No.

\`\`\`python
>>> a = 89
>>> b = 100
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 2-answer.txt

{{ACTION_BUTTONS}}

### 3. Right count =
Score: 100.0% (Checks completed: 100.0%)

In the following code, do \`a\` and \`b\` point to the same object? Answer with Yes or No.

\`\`\`python
>>> a = 89
>>> b = 89
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 3-answer.txt

{{ACTION_BUTTONS}}

### 4. Right count =
Score: 100.0% (Checks completed: 100.0%)

In the following code, do \`a\` and \`b\` point to the same object? Answer with Yes or No.

\`\`\`python
>>> a = 89
>>> b = a
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 4-answer.txt

{{ACTION_BUTTONS}}

### 5. Right count =+
Score: 100.0% (Checks completed: 100.0%)

In the following code, do \`a\` and \`b\` point to the same object? Answer with Yes or No.

\`\`\`python
>>> a = 89
>>> b = a + 1
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 5-answer.txt

{{ACTION_BUTTONS}}

### 6. Is equal
Score: 100.0% (Checks completed: 100.0%)

What do these 3 lines print?

\`\`\`python
>>> s1 = "Best School"
>>> s2 = s1
>>> print(s1 == s2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 6-answer.txt

{{ACTION_BUTTONS}}

### 7. Is the same
Score: 100.0% (Checks completed: 100.0%)

What do these 3 lines print?

\`\`\`python
>>> s1 = "Best"
>>> s2 = s1
>>> print(s1 is s2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 7-answer.txt

{{ACTION_BUTTONS}}

### 8. Is really equal
Score: 100.0% (Checks completed: 100.0%)

What do these 3 lines print?

\`\`\`python
>>> s1 = "Best School"
>>> s2 = "Best School"
>>> print(s1 == s2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 8-answer.txt

{{ACTION_BUTTONS}}

### 9. Is really the same
Score: 100.0% (Checks completed: 100.0%)

What do these 3 lines print?

\`\`\`python
>>> s1 = "Best School"
>>> s2 = "Best School"
>>> print(s1 is s2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 9-answer.txt

{{ACTION_BUTTONS}}

### 10. And with a list, is it equal
Score: 100.0% (Checks completed: 100.0%)

What do these 3 lines print?

\`\`\`python
>>> l1 = [1, 2, 3]
>>> l2 = [1, 2, 3] 
>>> print(l1 == l2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 10-answer.txt

{{ACTION_BUTTONS}}

### 11. And with a list, is it the same
Score: 100.0% (Checks completed: 100.0%)

What do these 3 lines print?

\`\`\`python
>>> l1 = [1, 2, 3]
>>> l2 = [1, 2, 3] 
>>> print(l1 is l2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 11-answer.txt

{{ACTION_BUTTONS}}

### 12. And with a list, is it really equal
Score: 100.0% (Checks completed: 100.0%)

What do these 3 lines print?

\`\`\`python
>>> l1 = [1, 2, 3]
>>> l2 = l1
>>> print(l1 == l2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 12-answer.txt

{{ACTION_BUTTONS}}

### 13. And with a list, is it really the same
Score: 100.0% (Checks completed: 100.0%)

What do these 3 lines print?

\`\`\`python
>>> l1 = [1, 2, 3]
>>> l2 = l1
>>> print(l1 is l2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 13-answer.txt

{{ACTION_BUTTONS}}

### 14. List append
Score: 100.0% (Checks completed: 100.0%)

What does this script print?

\`\`\`python
l1 = [1, 2, 3]
l2 = l1
l1.append(4)
print(l2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 14-answer.txt

{{ACTION_BUTTONS}}

### 15. List add
Score: 100.0% (Checks completed: 100.0%)

What does this script print?

\`\`\`python
l1 = [1, 2, 3]
l2 = l1
l1 = l1 + [4]
print(l2)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 15-answer.txt

{{ACTION_BUTTONS}}

### 16. Integer incrementation
Score: 100.0% (Checks completed: 100.0%)

What does this script print?

\`\`\`python
def increment(n):
    n += 1

a = 1
increment(a)
print(a)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 16-answer.txt

{{ACTION_BUTTONS}}

### 17. List incrementation
Score: 100.0% (Checks completed: 100.0%)

What does this script print?

\`\`\`python
def increment(n):
    n.append(4)

l = [1, 2, 3]
increment(l)
print(l)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 17-answer.txt

{{ACTION_BUTTONS}}

### 18. List assignation
Score: 100.0% (Checks completed: 100.0%)

What does this script print?

\`\`\`python
def assign_value(n, v):
    n = v

l1 = [1, 2, 3]
l2 = [4, 5, 6]
assign_value(l1, l2)
print(l1)
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 18-answer.txt

{{ACTION_BUTTONS}}

### 19. Copy a list object
Score: 100.0% (Checks completed: 100.0%)

Write a function def copy_list(l): that returns a copy of a list.

* The input list can contain any type of objects
* Your file should be maximum 3 lines long
* You are not allowed to import any module

\`\`\`python
guillaume@ubuntu:~/0x09$ cat 19-main.py
#!/usr/bin/python3
copy_list = __import__('19-copy_list').copy_list

my_list = [1, 2, 3]
print(my_list)

new_list = copy_list(my_list)

print(my_list)
print(new_list)

print(new_list == my_list)
print(new_list is my_list)

guillaume@ubuntu:~/0x09$ ./19-main.py
[1, 2, 3]
[1, 2, 3]
[1, 2, 3]
True
False
guillaume@ubuntu:~/0x09$ wc -l 19-copy_list.py 
3 19-copy_list.py
guillaume@ubuntu:~/0x09$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 19-copy_list.py

{{ACTION_BUTTONS}}

### 20. Tuple or not?
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
a = ()
\`\`\`

Is \`a\` a tuple? Answer with Yes or No.

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 20-answer.txt

{{ACTION_BUTTONS}}

### 21. Tuple or not?
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
a = (1, 2)
\`\`\`

Is \`a\` a tuple? Answer with Yes or No.

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 21-answer.txt

{{ACTION_BUTTONS}}

### 22. Tuple or not?
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
a = (1)
\`\`\`

Is \`a\` a tuple? Answer with Yes or No.

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 22-answer.txt

{{ACTION_BUTTONS}}

### 23. Tuple or not?
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
a = (1, )
\`\`\`

Is \`a\` a tuple? Answer with Yes or No.

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 23-answer.txt

{{ACTION_BUTTONS}}

### 24. Who I am?
Score: 100.0% (Checks completed: 100.0%)

What does this script print?

\`\`\`python
a = (1)
b = (1)
a is b
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 24-answer.txt

{{ACTION_BUTTONS}}

### 25. Tuple or not
Score: 100.0% (Checks completed: 100.0%)

What does this script print?

\`\`\`python
a = (1, 2)
b = (1, 2)
a is b
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 25-answer.txt

{{ACTION_BUTTONS}}

### 26. Empty is not empty
Score: 100.0% (Checks completed: 100.0%)

What does this script print?

\`\`\`python
a = ()
b = ()
a is b
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 26-answer.txt

{{ACTION_BUTTONS}}

### 27. Still the same?
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
>>> id(a)
139926795932424
>>> a
[1, 2, 3, 4]
>>> a = a + [5]
>>> id(a)
\`\`\`

Will the last line of this script print 139926795932424? Answer with Yes or No.

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 27-answer.txt

{{ACTION_BUTTONS}}

### 28. Same or not?
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
>>> a
[1, 2, 3]
>>> id (a)
139926795932424
>>> a += [4]
>>> id(a)
\`\`\`

Will the last line of this script print 139926795932424? Answer with Yes or No.

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 28-answer.txt

{{ACTION_BUTTONS}}

### 29. pythonic

Score: 100.0% (Checks completed: 100.0%)

Write a function \`magic_string()\` that returns a string "BestSchool" n times the number of the iteration (see code):
* Format: see example
* Your file should be maximum 4-line long (no documentation needed)
* You are not allowed to import any module

\`\`\`
guillaume@ubuntu:~/0x09$ cat 100-main.py
#!/usr/bin/python3
magic_string = __import__('100-magic_string').magic_string

for i in range(10):
    print(magic_string())

guillaume@ubuntu:~/0x09$ ./100-main.py | cat -e
BestSchool$
BestSchool, BestSchool$
BestSchool, BestSchool, BestSchool$
BestSchool, BestSchool, BestSchool, BestSchool$
BestSchool, BestSchool, BestSchool, BestSchool, BestSchool$
BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool$
BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool$
BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool$
BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool$
BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool, BestSchool$
guillaume@ubuntu:~/0x09$ wc -l 100-magic_string.py 
4 100-magic_string.py
guillaume@ubuntu:~/0x09$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 100-magic_string.py

{{ACTION_BUTTONS}}

### 30. Low memory cost
Score: 100.0% (Checks completed: 100.0%)

Write a class LockedClass with no class or object attribute, that prevents the user from dynamically creating new instance attributes, except if the new instance attribute is called first_name.

\`\`\`python
guillaume@ubuntu:~/0x09$ cat 101-main.py
#!/usr/bin/python3
LockedClass = __import__('101-locked_class').LockedClass

lc = LockedClass()
lc.first_name = "John"
try:
    lc.last_name = "Snow"
except Exception as e:
    print("[{}] {}".format(e.__class__.__name__, e))

guillaume@ubuntu:~/0x09$ ./101-main.py
[AttributeError] 'LockedClass' object has no attribute 'last_name'
guillaume@ubuntu:~/0x09$ 
\`\`\`

**No test cases needed**

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 101-locked_class.py

{{ACTION_BUTTONS}}

### 31. int 1/3
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
julien@ubuntu:/python3$ cat int.py 
a = 1
b = 1
julien@ubuntu:/python3$ 
\`\`\`

Assuming we are using a CPython implementation of Python3 with default options/configuration:
* How many int objects are created by the execution of the first line of the script? (103-line1.txt)
* How many int objects are created by the execution of the second line of the script (103-line2.txt)

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 103-line1.txt, 103-line2.txt

{{ACTION_BUTTONS}}

### 32. int 2/3
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
julien@ubuntu:/python3$ cat int.py 
a = 1024
b = 1024
del a
del b
c = 1024
julien@ubuntu:/python3$ 
\`\`\`

Assuming we are using a CPython implementation of Python3 with default options/configuration:
* How many int objects are created by the execution of the first line of the script? (104-line1.txt)
* How many int objects are created by the execution of the second line of the script (104-line2.txt)
* After the execution of line 3, is the int object pointed by a deleted? Answer with Yes or No (104-line3.txt)
* After the execution of line 4, is the int object pointed by b deleted? Answer with Yes or No (104-line4.txt)
* How many int objects are created by the execution of the last line of the script (104-line5.txt)

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 104-line1.txt, 104-line2.txt, 104-line3.txt, 104-line4.txt, 104-line5.txt

{{ACTION_BUTTONS}}

### 33. int 3/3
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
julien@twix:/tmp/so$ cat int.py 
print("I")
print("Love")
print("Python")
julien@ubuntu:/tmp/so$ 
\`\`\`

Assuming we are using a CPython implementation of Python3 with default options/configuration:
* Before the execution of line 2 (print("Love")), how many int objects have been created and are still in memory? (105-line1.txt)
* Why? (optional blog post :))

Hint: NSMALLPOSINTS, NSMALLNEGINTS
![](/images/252/assets/70f9ea0e969dfcc407a7427aba4786d87a920494.gif?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233456Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=b532c7f33818c8d78bfbd18cf17c635332f313479674d9f7586b5640399a4d22)
**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 105-line1.txt

{{ACTION_BUTTONS}}

### 34. Clear strings
Score: 100.0% (Checks completed: 100.0%)

\`\`\`python
guillaume@ubuntu:/python3$ cat string.py 
a = "SCHL"
b = "SCHL"
del a
del b
c = "SCHL"
guillaume@ubuntu:/python3$ 
\`\`\`

Assuming we are using a CPython implementation of Python3 with default options/configuration (For answers with numbers use integers, don't spell out the word):
* How many string objects are created by the execution of the first line of the script? (106-line1.txt)
* How many string objects are created by the execution of the second line of the script (106-line2.txt)
* After the execution of line 3, is the string object pointed by a deleted? Answer with Yes or No (106-line3.txt)
* After the execution of line 4, is the string object pointed by b deleted? Answer with Yes or No (106-line4.txt)
* How many string objects are created by the execution of the last line of the script (106-line5.txt)

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x09-python-everything_is_object
* File: 106-line1.txt, 106-line2.txt, 106-line3.txt, 106-line4.txt, 106-line5.txt

{{ACTION_BUTTONS}}`,
//   quiz: pythonEverythingIsObjectQuiz,
};
