// import { airbnbWebDynamicQuiz } from "../../../project-quizzes/higher-level/airbnb/web-dynamic-quiz";

export const airbnbWebDynamic = {
  title: "0x06. AirBnB clone - Web dynamic",
  status: "airbnb",
  content: `# 0x06. AirBnB clone - Web dynamic
![AirBnB](https://img.shields.io/badge/AirBnB-Web_Dynamic-red)
👤 By Guillaume
📝 Advanced
⚖️ Weight: 1
📅 Start: Mar 19, 2025 6:00 AM
📅 End: Mar 22, 2025 6:00 AM
⏱️ Duration: 3 days

### In a nutshell...
* **Auto QA review**: 3.0/6 mandatory
* **Manual QA review**: 0.0/117 mandatory & 0.0/63 optional
* **Altogether**: 2.44%
  * **Mandatory**: 2.44%
  * **Optional**: 0.0%

## Concepts

For this project, we expect you to look at these concepts:
* [AirBnB clone](/concepts/74)

## Resources

**Read or watch**:
* [Selector](https://jquery-tutorial.net/selectors/using-elements-ids-and-classes/)
* [Get and set content](https://jquery-tutorial.net/dom-manipulation/getting-and-setting-content/)
* [Manipulate CSS classes](https://jquery-tutorial.net/dom-manipulation/getting-and-setting-css-classes/)
* [Manipulate DOM elements](https://jquery-tutorial.net/dom-manipulation/the-append-and-prepend-methods/)
* [Document ready](https://learn.jquery.com/using-jquery-core/document-ready/)
* [Introduction](https://jquery-tutorial.net/ajax/introduction/)
* [GET & POST request](https://jquery-tutorial.net/ajax/the-get-and-post-methods/)
* [HTTP access control (CORS)](https://developer.mozilla.org/en-US/docs/Web/HTTP/Guides/CORS)

## Learning Objectives

At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:

### General
* How cool it is to request your own API
* How to modify an HTML element style
* How to get and update an HTML element content
* How to modify the DOM
* How to make a \`GET\` request with JQuery Ajax
* How to make a \`POST\` request with JQuery Ajax
* How to listen/bind to DOM events
* How to listen/bind to user events

## Requirements

### GitHub
**There should be one project repository per group. If you clone/fork/whatever a project repository with the same name before the second deadline, you risk a 0% score.**

## More Info

### Import JQuery

\`\`\`
<head>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
</head>
\`\`\`

### Before starting the project…

You will work on a codebase using [Flasgger](https://github.com/flasgger/flasgger), you will need to install it locally first before starting the RestAPI:

\`\`\`
$ sudo apt-get install -y python3-lxml
$ sudo pip3 install flask_cors # if it was not installed yet
$ sudo pip3 install flasgger
\`\`\`

If the RestAPI is not starting, please read the error message. Based on the(ses) error message(s), you will have to troubleshoot potential dependencies issues.

Here some solutions:

#### \`jsonschema\` exception

\`\`\`
$ sudo pip3 uninstall -y jsonschema 
$ sudo pip3 install jsonschema==3.0.1
\`\`\`

#### \`No module named 'pathlib2'\`

\`\`\`
$ sudo pip3 install pathlib2
\`\`\`

### Expose ports from your Vagrant

In your \`Vagrantfile\`, add this line for each port forwarded:

\`\`\`
config.vm.network :forwarded_port, guest: 5001, host: 5001 
\`\`\`

It's important in your project, to use the AirBnB API with the port \`5001\`

## Tasks

### 0. Last clone!

Score: 50.0% (Checks completed: 100.0%)

A new codebase again? Yes!

For this project you will fork this [codebase](https://github.com/justinmajetich/AirBnB_clone_v3):
* Update the repository name to \`AirBnB_clone_v4\`
* Update the \`README.md\`:
  * Add yourself as an author of the project
  * Add new information about your new contribution
  * Make it better!
* If you're the owner of this codebase, create a new repository called \`AirBnB_clone_v4\` and copy over all files from \`AirBnB_clone_v3\`
* If you didn't install Flasgger from the previous project, it's time! \`sudo pip3 install flasgger\`

**Repo:**
* GitHub repository: AirBnB_clone_v4

{{ACTION_BUTTONS}}

### 1. Cash only

Score: 0.0% (Checks completed: 0.0%)

Write a script that starts a Flask web application:
* Based on \`web_flask\`, copy: \`web_flask/static\`, \`web_flask/templates/100-hbnb.html\`, \`web_flask/__init__.py\` and \`web_flask/100-hbnb.py\` into the \`web_dynamic\` folder
* Rename \`100-hbnb.py\` to \`0-hbnb.py\`
* Rename \`100-hbnb.html\` to \`0-hbnb.html\`
* Update \`0-hbnb.py\` to replace the existing route to \`/0-hbnb/\`

**If \`100-hbnb.html\` is not present, use \`8-hbnb.html\` instead**

\`\`\`
guillaume@ubuntu:~/AirBnB_v4$ HBNB_MYSQL_USER=hbnb_dev HBNB_MYSQL_PWD=hbnb_dev_pwd HBNB_MYSQL_HOST=localhost HBNB_MYSQL_DB=hbnb_dev_db HBNB_TYPE_STORAGE=db python3 -m web_dynamic.0-hbnb
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

To avoid asset caching by Flask:
* In \`0-hbnb.py\`, add a variable \`cache_id\` to the \`render_template\`. The value of this variable must be an UUID (\`uuid.uuid4()\`)
* In \`0-hbnb.html\`, add this variable \`cache_id\` as query string to each \`<link>\` tag URL

**Repo:**
* GitHub repository: AirBnB_clone_v4
* Directory: web_dynamic
* File: 0-hbnb.py, templates/0-hbnb.html

{{ACTION_BUTTONS}}

### 2. Select some Amenities to be comfortable!

Score: 0.0% (Checks completed: 0.0%)

For the moment the filters section is static, let's make it dynamic!

Replace the route \`0-hbnb\` with \`1-hbnb\` in the file \`1-hbnb.py\` (based on \`0-hbnb.py\`)

Create a new template \`1-hbnb.html\` (based on \`0-hbnb.html\`) and update it:
* Import JQuery in the \`<head>\` tag
* Import the JavaScript \`static/scripts/1-hbnb.js\` in the \`<head>\` tag
* Add this variable cache_id as query string to the above \`<script>\` tag
* Add a \`<input type="checkbox">\` tag to the \`li\` tag of each amenity
* The new checkbox must be at 10px on the left of the Amenity name
* Add to the \`input\` tags of each amenity (\`<li>\` tag) the attribute \`data-id=":amenity_id"\`
* Add to the \`input\` tags of each amenity (\`<li>\` tag) the attribute \`data-name=":amenity_name"\`

Write a JavaScript script (\`static/scripts/1-hbnb.js\`):
* Your script must be executed only when DOM is loaded
* You must use JQuery
* Listen for changes on each \`input\` checkbox tag:
  * if the checkbox is checked, store the Amenity ID in a variable (dictionary or list)
  * if the checkbox is unchecked, remove the Amenity ID from the variable
  * update the \`h4\` tag inside the \`div\` Amenities with the list of Amenities checked

As example:

![](/images/309/assets/8e3c27078d62806b8ad1c1a682fbb3a48636ab89.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233537Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=56f53433ad1ab60c56721820316bdf778932b98dda7077ff5da95b2a90c1a5e8) ![](/images/309/assets/4e5cecdd82a70f07cd283ef8e242ad325c95b564.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233537Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=fa8f7c45bf0285a3ede9c01538d2ba0f6bcb52538ece280ef32fb0ad5560637e) ![](/images/309/assets/fb54e3081e229654db6e71ba919db753a791dcc3.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250219%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250219T233537Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=c32827fd04fc552ab0e1e9df1756c6102b124cee65d57e3f0fd47b3f91469721)

**Repo:**
* GitHub repository: AirBnB_clone_v4
* Directory: web_dynamic
* File: 1-hbnb.py, templates/1-hbnb.html, static/scripts/1-hbnb.js

{{ACTION_BUTTONS}}

### 3. API status
mandatory

Before requesting the HBNB API, it's better to know the status of this one.

Update the API entry point (\`api/v1/app.py\`) by replacing the current CORS \`CORS(app, origins="0.0.0.0")\` by \`CORS(app, resources={r"/api/v1/*": {"origins": "*"}})\`.

Change the route \`1-hbnb\` to \`2-hbnb\` in the file \`2-hbnb.py\` (based on \`1-hbnb.py\`)

Create a new template \`2-hbnb.html\` (based on \`1-hbnb.html\`) and update it:
* Import the JavaScript \`static/scripts/2-hbnb.js\` in the \`<head>\` tag (instead of \`1-hbnb.js\`)
* Add a new \`div\` element in the \`header\` tag:
  * Attribute ID should be \`api_status\`
  * Align to the right
  * Circle of 40px diameter
  * Center vertically
  * At 30px of the right border
  * Background color #cccccc
* Also add a class \`available\` for this new element in \`web_dynamic/static/styles/3-header.css\`:
  * Background color #ff545f

Write a JavaScript script (\`static/scripts/2-hbnb.js\`):
* Based on \`1-hbnb.js\`
* Request \`http://0.0.0.0:5001/api/v1/status/\`:
  * If in the status is "OK", add the class \`available\` to the \`div#api_status\`
  * Otherwise, remove the class \`available\` to the \`div#api_status\`

**Repo:**
* GitHub repository: AirBnB_clone_v4
* File: api/v1/app.py, web_dynamic/2-hbnb.py, web_dynamic/templates/2-hbnb.html, web_dynamic/static/styles/3-header.css, web_dynamic/static/scripts/2-hbnb.js

{{ACTION_BUTTONS}}

### 4. Fetch places
mandatory

Replace the route \`2-hbnb\` with \`3-hbnb\` in the file \`3-hbnb.py\` (based on \`2-hbnb.py\`)

Create a new template \`3-hbnb.html\` (based on \`2-hbnb.html\`) and update it:
* Import the JavaScript \`static/scripts/3-hbnb.js\` in the \`<head>\` tag (instead of \`2-hbnb.js\`)
* Remove the entire Jinja section of displaying all places (all \`article\` tags)

Write a JavaScript script (\`static/scripts/3-hbnb.js\`):
* Based on \`2-hbnb.js\`
* Request \`http://0.0.0.0:5001/api/v1/places_search/\`:
  * Description of this endpoint [here](https://intranet.alxswe.com/). If this endpoint is not available, you will have to add it to the API (you can work all together for creating this endpoint)
  * Send a POST request with Content-Type: application/json and an empty dictionary in the body - cURL version: \`curl "http://0.0.0.0:5001/api/v1/places_search" -XPOST -H "Content-Type: application/json" -d '{}'\`
  * Loop into the result of the request and create an \`article\` tag representing a Place in the \`section.places\`. (you can remove the Owner tag in the place description)

The final result must be the same as previously, but now loading places from the front-end, not from the back-end.

**Repo:**
* GitHub repository: AirBnB_clone_v4
* File: web_dynamic/3-hbnb.py, web_dynamic/templates/3-hbnb.html, web_dynamic/static/scripts/3-hbnb.js

{{ACTION_BUTTONS}}

### 5. Filter places by Amenity
mandatory

Replace the route \`3-hbnb\` with \`4-hbnb\` in the file \`4-hbnb.py\` (based on \`3-hbnb.py\`)

Create a new template \`4-hbnb.html\` (based on \`3-hbnb.html\`) and update it:
* Import the JavaScript \`static/scripts/4-hbnb.js\` in the \`<head>\` tag (instead of \`3-hbnb.js\`)

Write a JavaScript script (\`static/scripts/4-hbnb.js\`):
* Based on \`3-hbnb.js\`
* When the \`button\` tag is clicked, a new POST request to \`places_search\` should be made with the list of Amenities checked

Now you have the first filter implemented, enjoy!

**Repo:**
* GitHub repository: AirBnB_clone_v4
* File: web_dynamic/4-hbnb.py, web_dynamic/templates/4-hbnb.html, web_dynamic/static/scripts/4-hbnb.js

{{ACTION_BUTTONS}}`,
//   quiz: airbnbWebDynamicQuiz,
};
