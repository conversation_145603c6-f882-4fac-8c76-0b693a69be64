// import { airbnbWebFrameworkQuiz } from "../../../project-quizzes/higher-level/airbnb/web-framework-quiz";

export const airbnbWebFramework = {
  title: "0x04. AirBnB clone - Web framework",
  status: "mandatory",
  content: `# 0x04. AirBnB clone - Web framework
![Python](https://img.shields.io/badge/Python-Web%20Framework-blue)
👤 By <PERSON>
📝 Intermediate
⚖️ Weight: 1
📅 Start: Mar 16, 2024 6:00 AM
📅 End: Mar 17, 2024 6:00 AM
⏱️ Duration: 24 hours

## Background Context

Before using Flask to display our HBNB data, you will need to update some part of our engine:

Update \`FileStorage\`: (\`models/engine/file_storage.py\`)
* Add a public method \`def close(self):\`: call \`reload()\` method for deserializing the JSON file to objects

Update \`DBStorage\`: (\`models/engine/db_storage.py\`)
* Add a public method \`def close(self):\`: call \`remove()\` method on the private session attribute (\`self.__session\`) or \`close()\` on the class \`Session\`

Update \`State\`: (\`models/state.py\`) - If it's not already present
* If your storage engine is not \`DBStorage\`, add a public getter method \`cities\` to return the list of \`City\` objects from \`storage\` linked to the current \`State\`

In this project, you will learn how to use a Python web framework called Flask. You will create your first web application using this micro-framework: your AirBnB clone!

This web application will be composed by:
* A web server managed by Flask
* HTML/CSS templating for web pages
* Dynamic content using Jinja2 templates
* Form handling and validation
* Database integration through SQLAlchemy

## Resources

**Read or watch**:
* [What is a Web Framework?](https://pythonbasics.org/what-is-flask-python/) - Learn about web frameworks and their role
* [Flask Tutorial](https://flask.palletsprojects.com/en/2.0.x/tutorial/) - Official Flask tutorial
* [Flask Course](https://www.youtube.com/watch?v=Z1RJmh_OqeA) - Comprehensive video course on Flask
* [Jinja2 Documentation](https://jinja.palletsprojects.com/en/2.11.x/) - Template engine documentation
* [Flask-SQLAlchemy](https://flask-sqlalchemy.palletsprojects.com/en/2.x/) - Database integration
* [HTTP Methods](https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods) - Understanding HTTP methods

## Learning Objectives

At the end of this project, you are expected to be able to explain:

### General
* What is a Web Framework
* How to build a web framework with Flask
* How to define routes in Flask
* What is a route
* How to handle variables in a route
* What is a template
* How to create an HTML response in Flask by using a template
* How to create a dynamic template (loops, conditions...)
* How to display data from a MySQL database in HTML

## Tasks

### 0. Hello Flask!

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* Routes:
* \`/\`: display "Hello HBNB!"
* You must use the option \`strict_slashes=False\` in your route definition

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ python3 -m web_flask.0-hello_route
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000 ; echo "" | cat -e
Hello HBNB!$
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* Directory: web_flask
* File: 0-hello_route.py, __init__.py

### 1. HBNB

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* Routes:
* \`/\`: display "Hello HBNB!"
* \`/hbnb\`: display "HBNB"
* You must use the option \`strict_slashes=False\` in your route definition

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ python3 -m web_flask.1-hbnb_route
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000/hbnb ; echo "" | cat -e
HBNB$
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* Directory: web_flask
* File: 1-hbnb_route.py

### 2. C is fun!

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* Routes:
* \`/\`: display "Hello HBNB!"
* \`/hbnb\`: display "HBNB"
* \`/c/<text>\`: display "C " followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* You must use the option \`strict_slashes=False\` in your route definition

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ python3 -m web_flask.2-c_route
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000/c/is_fun ; echo "" | cat -e
C is fun$
guillaume@ubuntu:~$ curl 0.0.0.0:5000/c/cool ; echo "" | cat -e
C cool$
guillaume@ubuntu:~$ curl 0.0.0.0:5000/c
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* Directory: web_flask
* File: 2-c_route.py

### 3. Python is cool!

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* Routes:
* \`/\`: display "Hello HBNB!"
* \`/hbnb\`: display "HBNB"
* \`/c/<text>\`: display "C ", followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* \`/python/<text>\`: display "Python ", followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* The default value of \`text\` is "is cool"
* You must use the option \`strict_slashes=False\` in your route definition

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ python3 -m web_flask.3-python_route
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl -Ls 0.0.0.0:5000/python/is_magic ; echo "" | cat -e
Python is magic$
guillaume@ubuntu:~$ curl -Ls 0.0.0.0:5000/python ; echo "" | cat -e
Python is cool$
guillaume@ubuntu:~$ curl -Ls 0.0.0.0:5000/python/ ; echo "" | cat -e
Python is cool$
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* Directory: web_flask
* File: 3-python_route.py

### 4. Is it a number?

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* Routes:
* \`/\`: display "Hello HBNB!"
* \`/hbnb\`: display "HBNB"
* \`/c/<text>\`: display "C ", followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* \`/python/(<text>)\`: display "Python ", followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* The default value of \`text\` is "is cool"
* \`/number/<n>\`: display "\`n\` is a number" **only** if \`n\` is an integer
* You must use the option \`strict_slashes=False\` in your route definition

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ python3 -m web_flask.4-number_route
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number/89 ; echo "" | cat -e
89 is a number$
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number/8.9
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number/python
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* Directory: web_flask
* File: 4-number_route.py

### 5. Number template

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* Routes:
* \`/\`: display "Hello HBNB!"
* \`/hbnb\`: display "HBNB"
* \`/c/<text>\`: display "C ", followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* \`/python/(<text>)\`: display "Python ", followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* The default value of \`text\` is "is cool"
* \`/number/<n>\`: display "\`n\` is a number" **only** if \`n\` is an integer
* \`/number_template/<n>\`: display a HTML page **only** if \`n\` is an integer:
* \`H1\` tag: "Number: \`n\`" inside the tag \`BODY\`
* You must use the option \`strict_slashes=False\` in your route definition

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ python3 -m web_flask.5-number_template
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number_template/89 ; echo ""
<!DOCTYPE html>
<HTML lang="en">
    <HEAD>
        <TITLE>HBNB</TITLE>
    </HEAD>
    <BODY>
        <H1>Number: 89</H1>
    </BODY>
</HTML>
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number_template/8.9
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number_template/python
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* Directory: web_flask
* File: 5-number_template.py, templates/5-number.html

### 6. Odd or even?

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* Routes:
* \`/\`: display "Hello HBNB!"
* \`/hbnb\`: display "HBNB"
* \`/c/<text>\`: display "C ", followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* \`/python/(<text>)\`: display "Python ", followed by the value of the \`text\` variable (replace underscore \`_\` symbols with a space )
* The default value of \`text\` is "is cool"
* \`/number/<n>\`: display "\`n\` is a number" **only** if \`n\` is an integer
* \`/number_template/<n>\`: display a HTML page **only** if \`n\` is an integer:
* \`H1\` tag: "Number: \`n\`" inside the tag \`BODY\`
* \`/number_odd_or_even/<n>\`: display a HTML page **only** if \`n\` is an integer:
* \`H1\` tag: "Number: \`n\` is \`even|odd\`" inside the tag \`BODY\`
* You must use the option \`strict_slashes=False\` in your route definition

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ python3 -m web_flask.6-number_odd_or_even
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number_odd_or_even/89 ; echo ""
<!DOCTYPE html>
<HTML lang="en">
    <HEAD>
        <TITLE>HBNB</TITLE>
    </HEAD>
    <BODY>
        <H1>Number: 89 is odd</H1>
    </BODY>
</HTML>
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number_odd_or_even/32 ; echo ""
<!DOCTYPE html>
<HTML lang="en">
    <HEAD>
        <TITLE>HBNB</TITLE>
    </HEAD>
    <BODY>
        <H1>Number: 32 is even</H1>
    </BODY>
</HTML>
guillaume@ubuntu:~$ curl 0.0.0.0:5000/number_odd_or_even/python
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2 Final//EN">
<title>404 Not Found</title>
<h1>Not Found</h1>
<p>The requested URL was not found on the server.  If you entered the URL manually please check your spelling and try again.</p>
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* Directory: web_flask
* File: 6-number_odd_or_even.py, templates/6-number_odd_or_even.html

### 7. Improve engines

Score: 50.0% (Checks completed: 100.0%)

Before using Flask to display our HBNB data, you will need to update some part of our engine:

Update \`FileStorage\`: (\`models/engine/file_storage.py\`)
* Add a public method \`def close(self):\`: call \`reload()\` method for deserializing the JSON file to objects

Update \`DBStorage\`: (\`models/engine/db_storage.py\`)
* Add a public method \`def close(self):\`: call \`remove()\` method on the private session attribute (\`self.__session\`) [tips](https://docs.sqlalchemy.org/en/13/orm/contextual.html) or \`close()\` on the class \`Session\` [tips](https://docs.sqlalchemy.org/en/13/orm/session_api.html)

Update \`State\`: (\`models/state.py\`) - If it's not already present
* If your storage engine is not \`DBStorage\`, add a public getter method \`cities\` to return the list of \`City\` objects from \`storage\` linked to the current \`State\`

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ HBNB_MYSQL_USER=hbnb_dev HBNB_MYSQL_PWD=hbnb_dev_pwd HBNB_MYSQL_HOST=localhost HBNB_MYSQL_DB=hbnb_dev_db HBNB_TYPE_STORAGE=db python3
>>> from models import storage
>>> from models.state import State
>>> len(storage.all(State))
5
>>> len(storage.all(State))
5
>>> # Time to insert new data!
\`\`\`

At this moment, in another tab:

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ echo 'INSERT INTO \`states\` VALUES ("421a55f1-7d82-45d9-b54c-a76916479545","2017-03-25 19:42:40","2017-03-25 19:42:40","Alabama");' | mysql -uroot -p hbnb_dev_db
Enter password:
guillaume@ubuntu:~/AirBnB_v2$
\`\`\`

And let's go back the Python console:

\`\`\`
>>> # Time to insert new data!
>>> len(storage.all(State))
5
>>> # normal: the SQLAlchemy didn't reload his \`Session\`
>>> # to force it, you must remove the current session to create a new one:
>>> storage.close()
>>> len(storage.all(State))
6
>>> # perfect!
\`\`\`

And for the getter \`cities\` in the \`State\` model:

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ cat main.py
#!/usr/bin/python3
"""
 Test cities access from a state
"""
from models import storage
from models.state import State
from models.city import City

"""
 Objects creations
"""
state_1 = State(name="California")
print("New state: {}".format(state_1))
state_1.save()
state_2 = State(name="Arizona")
print("New state: {}".format(state_2))
state_2.save()

city_1_1 = City(state_id=state_1.id, name="Napa")
print("New city: {} in the state: {}".format(city_1_1, state_1))
city_1_1.save()
city_1_2 = City(state_id=state_1.id, name="Sonoma")
print("New city: {} in the state: {}".format(city_1_2, state_1))
city_1_2.save()
city_2_1 = City(state_id=state_2.id, name="Page")
print("New city: {} in the state: {}".format(city_2_1, state_2))
city_2_1.save()

"""
 Verification
"""
print("")
all_states = storage.all(State)
for state_id, state in all_states.items():
    for city in state.cities:
        print("Find the city {} in the state {}".format(city, state))

guillaume@ubuntu:~/AirBnB_v2$
guillaume@ubuntu:~/AirBnB_v2$ rm file.json ; HBNB_TYPE_STORAGE=fs ./main.py
New state: [State] (5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45) {'name': 'California', 'id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 509954), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 509950)}
New state: [State] (a5e5311a-3c19-4995-9485-32c74411b416) {'name': 'Arizona', 'id': 'a5e5311a-3c19-4995-9485-32c74411b416', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510256), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510252)}
New city: [City] (e3e36ded-fe56-44f5-bf08-8a27e2b30672) {'name': 'Napa', 'id': 'e3e36ded-fe56-44f5-bf08-8a27e2b30672', 'state_id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510797), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510791)} in the state: [State] (5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45) {'name': 'California', 'id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510038), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 509950)}
New city: [City] (12a58d70-e255-4c1e-8a68-7d5fb924d2d2) {'name': 'Sonoma', 'id': '12a58d70-e255-4c1e-8a68-7d5fb924d2d2', 'state_id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 511437), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 511432)} in the state: [State] (5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45) {'name': 'California', 'id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510038), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 509950)}
New city: [City] (a693bdb9-e0ca-4521-adfd-e1a93c093b4b) {'name': 'Page', 'id': 'a693bdb9-e0ca-4521-adfd-e1a93c093b4b', 'state_id': 'a5e5311a-3c19-4995-9485-32c74411b416', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 511873), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 511869)} in the state: [State] (a5e5311a-3c19-4995-9485-32c74411b416) {'name': 'Arizona', 'id': 'a5e5311a-3c19-4995-9485-32c74411b416', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510373), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510252)}

Find the city [City] (e3e36ded-fe56-44f5-bf08-8a27e2b30672) {'name': 'Napa', 'id': 'e3e36ded-fe56-44f5-bf08-8a27e2b30672', 'state_id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510953), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510791)} in the state [State] (5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45) {'name': 'California', 'id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510038), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 509950)}
Find the city [City] (12a58d70-e255-4c1e-8a68-7d5fb924d2d2) {'name': 'Sonoma', 'id': '12a58d70-e255-4c1e-8a68-7d5fb924d2d2', 'state_id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 511513), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 511432)} in the state [State] (5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45) {'name': 'California', 'id': '5b8f1d55-e49c-44dd-ba6f-a3cf8489ae45', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510038), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 509950)}
Find the city [City] (a693bdb9-e0ca-4521-adfd-e1a93c093b4b) {'name': 'Page', 'id': 'a693bdb9-e0ca-4521-adfd-e1a93c093b4b', 'state_id': 'a5e5311a-3c19-4995-9485-32c74411b416', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 512073), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 511869)} in the state [State] (a5e5311a-3c19-4995-9485-32c74411b416) {'name': 'Arizona', 'id': 'a5e5311a-3c19-4995-9485-32c74411b416', 'updated_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510373), 'created_at': datetime.datetime(2017, 12, 11, 19, 27, 52, 510252)}
guillaume@ubuntu:~/AirBnB_v2$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* File: models/engine/file_storage.py, models/engine/db_storage.py, models/state.py

### 8. List of states

Score: 15.63% (Checks completed: 31.25%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* You must use \`storage\` for fetching data from the storage engine (\`FileStorage\` or \`DBStorage\`) => \`from models import storage\` and \`storage.all(...)\`
* After each request you must remove the current SQLAlchemy Session:
* Declare a method to handle \`@app.teardown_appcontext\`
* Call in this method \`storage.close()\`
* Routes:
* \`/states_list\`: display a HTML page: (inside the tag \`BODY\`)
* \`H1\` tag: “States”
* \`UL\` tag: with the list of all \`State\` objects present in \`DBStorage\` **sorted by \`name\`** (A->Z) [tip](https://jinja.palletsprojects.com/en/stable/templates/)
* \`LI\` tag: description of one \`State\`: \`<state.id>: <B><state.name></B>\`
* Import this [7-dump](/alx-assets/s3.amazonaws.com/intranet-projects-files/holbertonschool-higher-level_programming+/290/7-states_list.sql) to have some data
* You must use the option \`strict_slashes=False\` in your route definition

**IMPORTANT**
* Make sure you have a running and valid \`setup_mysql_dev.sql\` in your \`AirBnB_clone_v2\` repository ([Task](https://savanna.alxafrica.com/tasks/1609))
* Make sure all tables are created when you run \`echo "quit" | HBNB_MYSQL_USER=hbnb_dev HBNB_MYSQL_PWD=hbnb_dev_pwd HBNB_MYSQL_HOST=localhost HBNB_MYSQL_DB=hbnb_dev_db HBNB_TYPE_STORAGE=db ./console.py\`

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ curl -o 7-dump.sql "/alx-assets/s3.amazonaws.com/intranet-projects-files/holbertonschool-higher-level_programming+/290/7-states_list.sql"
guillaume@ubuntu:~/AirBnB_v2$ cat 7-dump.sql | mysql -uroot -p
Enter password:
guillaume@ubuntu:~/AirBnB_v2$ HBNB_MYSQL_USER=hbnb_dev HBNB_MYSQL_PWD=hbnb_dev_pwd HBNB_MYSQL_HOST=localhost HBNB_MYSQL_DB=hbnb_dev_db HBNB_TYPE_STORAGE=db python3 -m web_flask.7-states_list
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000/states_list ; echo ""
<!DOCTYPE html>
<HTML lang="en">
    <HEAD>
        <TITLE>HBNB</TITLE>
    </HEAD>
    <BODY>
        <H1>States</H1>
        <UL>

            <LI>421a55f4-7d82-47d9-b54c-a76916479545: <B>Alabama</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479546: <B>Arizona</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479547: <B>California</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479548: <B>Colorado</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479549: <B>Florida</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479550: <B>Georgia</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479551: <B>Hawaii</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479552: <B>Illinois</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479553: <B>Indiana</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479554: <B>Louisiana</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479555: <B>Minnesota</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479556: <B>Mississippi</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479557: <B>Oregon</B></LI>

        </UL>
    </BODY>
</HTML>
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* File: web_flask/7-states_list.py, web_flask/templates/7-states_list.html

### 9. Cities by states

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* You must use \`storage\` for fetching data from the storage engine (\`FileStorage\` or \`DBStorage\`) => \`from models import storage\` and \`storage.all(...)\`
* To load all cities of a \`State\`:
* If your storage engine is \`DBStorage\`, you must use \`c cities\` relationship
* Otherwise, use the public getter method \`cities\`
* After each request you must remove the current SQLAlchemy Session:
* Declare a method to handle \`@app.teardown_appcontext\`
* Call in this method \`storage.close()\`
* Routes:
* \`/cities_by_states\`: display a HTML page: (inside the tag \`BODY\`)
* \`H1\` tag: "States"
* \`UL\` tag: with the list of all \`State\` objects present in \`DBStorage\` **sorted by \`name\`** (A->Z) [tip](https://jinja.palletsprojects.com/en/stable/templates/)
* \`LI\` tag: description of one \`State\`: \`<state.id>: <B><state.name></B>\` + \`UL\` tag: with the list of \`City\` objects linked to the \`State\` **sorted by \`name\`** (A->Z)
* \`LI\` tag: description of one \`City\`: \`<city.id>: <B><city.name></B>\`
* Import this [7-dump](/alx-assets/s3.amazonaws.com/intranet-projects-files/holbertonschool-higher-level_programming+/290/7-states_list.sql) to have some data
* You must use the option \`strict_slashes=False\` in your route definition

**IMPORTANT**
* Make sure you have a running and valid \`setup_mysql_dev.sql\` in your \`AirBnB_clone_v2\` repository ([Task](https://savanna.alxafrica.com/tasks/1609))
* Make sure all tables are created when you run \`echo "quit" | HBNB_MYSQL_USER=hbnb_dev HBNB_MYSQL_PWD=hbnb_dev_pwd HBNB_MYSQL_HOST=localhost HBNB_MYSQL_DB=hbnb_dev_db HBNB_TYPE_STORAGE=db ./console.py\`

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ curl -o 7-dump.sql "/alx-assets/s3.amazonaws.com/intranet-projects-files/holbertonschool-higher-level_programming+/290/7-states_list.sql"
guillaume@ubuntu:~/AirBnB_v2$ cat 7-dump.sql | mysql -uroot -p
Enter password:
guillaume@ubuntu:~/AirBnB_v2$ HBNB_MYSQL_USER=hbnb_dev HBNB_MYSQL_PWD=hbnb_dev_pwd HBNB_MYSQL_HOST=localhost HBNB_MYSQL_DB=hbnb_dev_db HBNB_TYPE_STORAGE=db python3 -m web_flask.8-cities_by_states
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000/cities_by_states ; echo ""
<!DOCTYPE html>
<HTML lang="en">
    <HEAD>
        <TITLE>HBNB</TITLE>
    </HEAD>
    <BODY>
        <H1>States</H1>
        <UL>

            <LI>421a55f4-7d82-47d9-b54c-a76916479545: <B>Alabama</B>
                <UL>

                        <LI>521a55f4-7d82-47d9-b54c-a76916479545: <B>Akron</B></LI>

                        <LI>531a55f4-7d82-47d9-b54c-a76916479545: <B>Babbie</B></LI>

                        <LI>541a55f4-7d82-47d9-b54c-a76916479545: <B>Calera</B></LI>

                        <LI>551a55f4-7d82-47d9-b54c-a76916479545: <B>Fairfield</B></LI>

                </UL>
            </LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479546: <B>Arizona</B>
                <UL>

                        <LI>521a55f4-7d82-47d9-b54c-a76916479546: <B>Douglas</B></LI>

                        <LI>531a55f4-7d82-47d9-b54c-a76916479546: <B>Kearny</B></LI>

                        <LI>541a55f4-7d82-47d9-b54c-a76916479546: <B>Tempe</B></LI>

                </UL>
            </LI>
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* File: web_flask/8-cities_by_states.py, web_flask/templates/8-cities_by_states.html
{{ACTION_BUTTON}}

### 10. States and State

Score: 50.0% (Checks completed: 100.0%)

Write a script that starts a Flask web application:
* Your web application must be listening on \`0.0.0.0\`, port \`5000\`
* You must use \`storage\` for fetching data from the storage engine (\`FileStorage\` or \`DBStorage\`) => \`from models import storage\` and \`storage.all(...)\`
* To load all cities of a \`State\`:
* If your storage engine is \`DBStorage\`, you must use \`cities\` relationship
* Otherwise, use the public getter method \`cities\`
* After each request you must remove the current SQLAlchemy Session:
* Declare a method to handle \`@app.teardown_appcontext\`
* Call in this method \`storage.close()\`
* Routes:
* \`/states\`: display a HTML page: (inside the tag \`BODY\`)
* \`H1\` tag: "States"
* \`UL\` tag: with the list of all \`State\` objects present in \`DBStorage\` **sorted by \`name\`** (A->Z) [tip](https://jinja.palletsprojects.com/en/stable/templates/)
* \`LI\` tag: description of one \`State\`: \`<state.id>: <B><state.name></B>\`
* \`/states/<id>\`: display a HTML page: (inside the tag \`BODY\`)
* If a \`State\` object is found with this \`id\`:
* \`H1\` tag: "State: "
* \`H3\` tag: "Cities:"
* \`UL\` tag: with the list of \`City\` objects linked to the \`State\` **sorted by \`name\`** (A->Z)
* \`LI\` tag: description of one \`City\`: \`<city.id>: <B><city.name></B>\`
* Otherwise:
* \`H1\` tag: "Not found!"
* You must use the option \`strict_slashes=False\` in your route definition
* Import this [7-dump](/alx-assets/s3.amazonaws.com/intranet-projects-files/holbertonschool-higher-level_programming+/290/7-states_list.sql) to have some data

**IMPORTANT**
* Make sure you have a running and valid \`setup_mysql_dev.sql\` in your \`AirBnB_clone_v2\` repository ([Task](https://savanna.alxafrica.com/tasks/1609))
* Make sure all tables are created when you run \`echo "quit" | HBNB_MYSQL_USER=hbnb_dev HBNB_MYSQL_PWD=hbnb_dev_pwd HBNB_MYSQL_HOST=localhost HBNB_MYSQL_DB=hbnb_dev_db HBNB_TYPE_STORAGE=db ./console.py\`

\`\`\`
guillaume@ubuntu:~/AirBnB_v2$ curl -o 7-dump.sql "/alx-assets/s3.amazonaws.com/intranet-projects-files/holbertonschool-higher-level_programming+/290/7-states_list.sql"
guillaume@ubuntu:~/AirBnB_v2$ cat 7-dump.sql | mysql -uroot -p
Enter password:
guillaume@ubuntu:~/AirBnB_v2$ HBNB_MYSQL_USER=hbnb_dev HBNB_MYSQL_PWD=hbnb_dev_pwd HBNB_MYSQL_HOST=localhost HBNB_MYSQL_DB=hbnb_dev_db HBNB_TYPE_STORAGE=db python3 -m web_flask.9-states
* Running on http://0.0.0.0:5000/ (Press CTRL+C to quit)
....
\`\`\`

In another tab:

\`\`\`
guillaume@ubuntu:~$ curl 0.0.0.0:5000/states ; echo ""
<!DOCTYPE html>
<HTML lang="en">
    <HEAD>
        <TITLE>HBNB</TITLE>
    </HEAD>
    <BODY>
        <H1>States</H1>
        <UL>

            <LI>421a55f4-7d82-47d9-b54c-a76916479545: <B>Alabama</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479546: <B>Arizona</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479547: <B>California</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479548: <B>Colorado</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479549: <B>Florida</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479550: <B>Georgia</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479551: <B>Hawaii</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479552: <B>Illinois</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479553: <B>Indiana</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479554: <B>Louisiana</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479555: <B>Minnesota</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479556: <B>Mississippi</B></LI>

            <LI>421a55f4-7d82-47d9-b54c-a76916479557: <B>Oregon</B></LI>

        </UL>
    </BODY>
</HTML>
guillaume@ubuntu:~$
\`\`\`

**Repo:**
* GitHub repository: AirBnB_clone_v2
* File: web_flask/9-states.py, web_flask/templates/9-states.html

{{ACTION_BUTTONS}}`,
//   quiz: airbnbWebFrameworkQuiz,
};
