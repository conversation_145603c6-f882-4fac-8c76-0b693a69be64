import { airbnbConsole } from './console';
import { airbnbWebStatic } from './web-static';
import { airbnbMysql } from './mysql';
import { airbnbDeployStatic } from './deploy-static';
import { airbnbWebFramework } from './web-framework';
import { airbnbRestApi } from './rest-api';
import { airbnbWebDynamic } from './web-dynamic';

export const airbnbProjects = {
  console: airbnbConsole,
  webStatic: airbnbWebStatic,
  mysql: airbnbMysql,
  deployStatic: airbnbDeployStatic,
  webFramework: airbnbWebFramework,
  restApi: airbnbRestApi,
  webDynamic: airbnbWebDynamic
};

// Organized by development phase
export const airbnbPhases = {
  backend: [
    airbnbConsole,    // Command interpreter
    airbnbMysql      // Database storage
  ],
  frontend: [
    airbnbWebStatic,  // Static HTML/CSS
    airbnbWebFramework // Dynamic content with Flask
  ],
  deployment: [
    airbnbDeployStatic // Server deployment
  ],
  api: [
    airbnbRestApi,    // RESTful API
    airbnbWebDynamic  // Dynamic web with API integration
  ]
};

export {
  airbnbConsole,
  airbnbWebStatic,
  airbnbMysql,
  airbnbDeployStatic,
  airbnbWebFramework,
  airbnbRestApi,
  airbnbWebDynamic
};