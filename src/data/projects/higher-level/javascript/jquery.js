// import { javascriptJqueryQuiz } from "../../../project-quizzes/higher-level/javascript/jquery-quiz";

export const javascriptJquery = {
  title: "0x15. JavaScript - Web jQuery",
  status: "higher-level-programming",
  content: `# 0x15. JavaScript - Web jQuery
![JavaScript](https://img.shields.io/badge/JavaScript-Programming-yellow)
👤 By Julien Barbier
📝 Intermediate
⚖️ Weight: 1
📅 Start: Mar 16, 2024 6:00 AM
📅 End: Mar 17, 2024 6:00 AM
⏱️ Duration: 24 hours

## In a nutshell…
* Auto QA review: Manual QA review: 0.0/86 mandatory & 0.0/40 optional
* Altogether: Altogether: 0.0% Mandatory: 0.0% Optional: 0.0% Calculation: 0.0% + (0.0% * 0.0%) == 0.0%
* Mandatory: Mandatory: 0.0%
* Optional: Optional: 0.0%
* Calculation: Calculation: 0.0% + (0.0% * 0.0%) == 0.0%

## Concepts

For this project, we expect you to look at these concepts:
* [JavaScript in the browser](/concepts/3)
* [Dealing with data statically versus dynamically](/concepts/35)
SE Foundations Average: 89.39%

#### In a nutshell…
* **Manual QA review:** 0.0/86 mandatory & 0.0/40 optional
* **Altogether:**  **0.0%**
* Mandatory: 0.0%
* Optional: 0.0%
* Calculation:  0.0% + (0.0% \\* 0.0%)  == **0.0%**

**Overall comment:**

Project was failed automatically.

### Resources

**Read or watch**:
* [What is JavaScript?](https://developer.mozilla.org/en-US/docs/Learn_web_development/Core/Scripting/What_is_JavaScript)
* [Selector](https://jquery-tutorial.net/selectors/using-elements-ids-and-classes/)
* [Get and set content](https://jquery-tutorial.net/dom-manipulation/getting-and-setting-content/)
* [Manipulate CSS classes](https://jquery-tutorial.net/dom-manipulation/getting-and-setting-css-classes/)
* [Manipulate DOM elements](https://jquery-tutorial.net/dom-manipulation/the-append-and-prepend-methods/)
* [API](https://oscarotero.com/jquery/)
* [Introduction](https://jquery-tutorial.net/ajax/introduction/)
* [GET & POST request](https://jquery-tutorial.net/ajax/the-get-and-post-methods/)
* [JQuery Ajax Tutorial #1 - Using AJAX & API's](https://www.youtube.com/watch?v=fEYx8dQr_cQ)
* [What went wrong? Troubleshooting JavaScript](https://developer.mozilla.org/en-US/docs/Learn_web_development/Core/Scripting/What_went_wrong)
* [JQuery](https://jquery.com/)
* [JQuery API](https://api.jquery.com/)
* [JQuery Ajax](https://learn.jquery.com/ajax/)

## Learning Objectives

At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:

### General
* Why JQuery make front-end programming so easy (don't forget to tweet today, with the hashtag #ilovejquery :))
* How to select HTML elements in JavaScript
* How to select HTML elements with JQuery
* What are differences between \`ID\`, \`class\` and \`tag name\` selectors
* How to modify an HTML element style
* How to get and update an HTML element content
* How to modify the DOM
* How to make a \`GET\` request with JQuery Ajax
* How to make a \`POST\` request with JQuery Ajax
* How to listen/bind to DOM events
* How to listen/bind to user events

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements

### More Info

### Import JQuery

\`\`\`
<head>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
</head>
\`\`\`

![](/alx-assets/s3.amazonaws.com/intranet-projects-files/holbertonschool-higher-level_programming+/305/1f1ihd.jpg)

### Manual QA Review

**It is your responsibility to request a review for this project from a peer before the project's deadline. If no peers have been reviewed, you should request a review from a TA or staff member.**

## Tasks

### 0. No JQuery

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that updates the text color of the \`<header>\` element to red (\`#FF0000\`):
* You must use \`document.querySelector\` to select the HTML tag
* You can't use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 0-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
  </head>
  <body>
    <header> 
      First HTML page
    </header>
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="0-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 0-script.js

### 1. With JQuery

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that updates the text color of the \`<header>\` element to red (\`#FF0000\`):
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 1-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
  </head>
  <body>
    <header> 
      First HTML page
    </header>
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="1-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 1-script.js

### 2. Click and turn red

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that updates the text color of the \`<header>\` element to red (\`#FF0000\`) when the user clicks on the tag \`DIV#red_header\`:
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 2-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
  </head>
  <body>
    <header> 
      First HTML page
    </header>
    <div id="red_header">Red header</div>
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="2-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 2-script.js

### 3. Add \`.red\` class

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that adds the class \`red\` to the \`<header>\` element when the user clicks on the tag \`DIV#red_header\`
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 3-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
    <style>
      .red {
        color: #FF0000;
      }
    </style>
  </head>
  <body>
    <header> 
      First HTML page
    </header>
    <div id="red_header">Red header</div>
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="3-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 3-script.js

### 4. Toggle classes

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that toggles the class of the \`<header>\` element when the user clicks on the tag \`DIV#toggle_header\`:
* The \`<header>\` element must always have one class: \`red\` or \`green\`, never both in the same time and never empty.
* If the current class is \`red\`, when the user click on \`DIV#toggle_header\`, the class must be updated to \`green\` ; and the reverse.
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 4-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
    <style>
      .red {
        color: #FF0000;
      }
      .green {
        color: #00FF00;
      }
    </style>
  </head>
  <body>
    <header class="green"> 
      First HTML page
    </header>
    <div id="toggle_header">Toggle header</div>
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="4-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 4-script.js

### 5. List of elements

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that adds a \`<li>\` element to a list when the user clicks on the tag \`DIV#add_item\`:
* The new element must be: \`<li>Item</li>\`
* The new element must be added to \`UL.my_list\`
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 5-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
  </head>
  <body>
    <header> 
      First HTML page
    </header>
    <br />
    <div id="add_item">Add item</div>
    <br />
    <ul class="my_list">
      <li>Item</li>
    </ul>
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="5-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 5-script.js

### 6. Change the text

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that updates the text of the \`<header>\` element to \`New Header!!!\` when the user clicks on \`DIV#update_header\`
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 6-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
  </head>
  <body>
    <header> 
      First HTML page
    </header>
    <br />
    <div id="update_header">Update the header</div>
    <br />
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="6-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 6-script.js

### 7. Star wars character

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that fetches the character \`name\` from this URL: \`https://swapi-api.alx-tools.com/api/people/5/?format=json\`
* The name must be displayed in the HTML tag \`DIV#character\`
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 7-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
  </head>
  <body>
    <header> 
      Star Wars character
    </header>
    <br />
    <div id="character"></div>
    <br />
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="7-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 7-script.js

### 8. Star Wars movies

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that fetches and lists the \`title\` for all movies by using this URL: \`https://swapi-api.alx-tools.com/api/films/?format=json\`
* All movie titles must be list in the HTML tag \`UL#list_movies\`
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 8-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
  </head>
  <body>
    <header> 
      Star Wars movies
    </header>
    <br />
    <ul id="list_movies">
    </ul>
    <br />
    <footer>
      ALX - 2017
    </footer>
    <script type="text/javascript" src="8-script.js"></script>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 8-script.js

### 9. Say Hello!

Score: 0.0% (Checks completed: 0.0%)

Write a JavaScript script that fetches from \`https://hellosalut.stefanbohacek.dev/?lang=fr\` and displays the value of \`hello\` from that fetch in the HTML tag \`DIV#hello\`.
* The translation of "hello" must be displayed in the HTML tag \`DIV#hello\`
* You can't use \`document.querySelector\` to select the HTML tag
* You must use the JQuery API
* Your script must work when it is imported from the \`<head>\` tag

Please test with this HTML file in your browser:

\`\`\`
guillaume@ubuntu:~/0x15$ cat 9-main.html 
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>ALX</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js"></script>
    <script type="text/javascript" src="9-script.js"></script>
  </head>
  <body>
    <header> 
      Say Hello!
    </header>
    <br />
    <div id="hello"></div>
    <br />
    <footer>
      ALX - 2017
    </footer>
  </body>
</html>
guillaume@ubuntu:~/0x15$ 
\`\`\`

**Repo:**
* GitHub repository: alx-higher_level_programming
* Directory: 0x15-javascript-web_jquery
* File: 9-script.js

## Quiz questions

{{ACTION_BUTTONS}}`,
//   quiz: javascriptJqueryQuiz,
};