import { javascriptWarmup } from './warmup';
import { javascriptObjects } from './objects';
import { javascriptWebScraping } from './web-scraping';
import { javascriptJquery } from './jquery';

export const javascriptProjects = {
  warmup: javascriptWarmup,
  objects: javascriptObjects,
  webScraping: javascriptWebScraping,
  jquery: javascriptJquery
};

// Organized by learning path
export const javascriptLevels = {
  fundamentals: [
    javascriptWarmup,    // Basic JavaScript concepts
    javascriptObjects    // Object-oriented programming
  ],
  webDevelopment: [
    javascriptWebScraping, // Working with APIs and files
    javascriptJquery      // DOM manipulation and AJAX
  ]
};

export {
  javascriptWarmup,
  javascriptObjects,
  javascriptWebScraping,
  javascriptJquery
};