import { pythonProjects } from './python';
import { airbnbProjects } from './airbnb';
import { sqlProjects } from './sql';
import { javascriptProjects } from './javascript';

export const higherLevelProjects = {
  python: {
    title: "Higher-level Programming - Python",
    description: "Advanced programming concepts using Python",
    projects: pythonProjects,
    sections: [
      {
        title: "Object-Oriented Programming",
        projects: [
          pythonProjects.classesObjects,
          pythonProjects.moreClasses,
          pythonProjects.everythingIsObject,
          pythonProjects.inheritance
        ]
      },
      {
        title: "File Operations and Data Handling",
        projects: [
          pythonProjects.inputOutput,
          pythonProjects.almostCircle
        ]
      }
    ]
  },
  airbnb: {
    title: "AirBnB Clone",
    description: "Building a full web application - The AirBnB Clone",
    projects: airbnbProjects,
    sections: [
      {
        title: "Backend Development",
        projects: [
          airbnbProjects.console,
          airbnbProjects.webStatic,
          airbnbProjects.mysql,
        ]
      },
      {
        title: "Frontend & Deployment",
        projects: [
          airbnbProjects.deployStatic,
          airbnbProjects.webFramework,
          airbnbProjects.restApi,
          airbnbProjects.webDynamic
        ]
      }
    ]
  },
  sql: {
    title: "SQL & Databases",
    description: "Working with Relational Databases",
    projects: sqlProjects,
    sections: [
      {
        title: "SQL Fundamentals",
        projects: [
          sqlProjects.introduction,
          sqlProjects.moreQueries
        ]
      }
    ]
  },
  javascript: {
    title: "JavaScript Programming",
    description: "Frontend Development with JavaScript",
    projects: javascriptProjects,
    sections: [
      {
        title: "JavaScript Basics",
        projects: [
          javascriptProjects.warmup,
          javascriptProjects.objects
        ]
      },
      {
        title: "Web Development",
        projects: [
          javascriptProjects.webScraping,
          javascriptProjects.jquery
        ]
      }
    ]
  }
};

// Export individual projects for direct access
export {
  pythonProjects,
  airbnbProjects,
  sqlProjects,
  javascriptProjects
};