export const attackIsBestDefense = `# Attack is the best defense
![Security](https://img.shields.io/badge/Security-Networking-red)
👤 By <PERSON><PERSON><PERSON><PERSON>
📝 Advanced
⚖️ Weight: 1
📅 Start: Jul 10, 2025 6:00 AM
📅 End: Jul 24, 2025 6:00 AM
⏱️ Duration: 14 days

### Background Context

**This project is NOT mandatory** at all. It is 100% optional. Doing any part of this project will add a project grade of over 100% to your average. Your score won't get hurt if you don't do it, but if your current average is greater than your score on this project, your average might go down. Have fun!

## Tasks

### 0. ARP spoofing and sniffing unencrypted traffic

Security is a vast topic, and Network Security is an important part of it. A lot of very sensitive information goes over networks that are used by many people, and some people might have bad intentions. Traffic going through a network can be intercepted by a malicious machine pretending to be another network device. Once the traffic is redirected to the malicious machine, the hacker can keep a copy of it and analyze it for potential interesting information. It is important to note that the traffic must then be forwarded to the actual device it was supposed to go (so that users and the system keep going as if nothing happened).

Any information that is not encrypted and sniffed by an attacker can be seen by the attacker - that could be your email password or credit card information. While today's network security is much stronger than it used to be, there are still some legacy systems that are using unencrypted communication means. A popular one is \`telnet\`.

In this project, we will not go over ARP spoofing, but we'll start by sniffing unencrypted traffic and getting information out of it.

[Sendgrid offers](https://sendgrid.com/en-us) is an emailing service that provides API and SMTP solutions to send emails.

For this task, we are accessing this [Sendgrid API](https://intranet.alxswe.com/) to make this exercise more fun. Let's assume that you are a hacker (you are ethical now - you're practicing for good causes only) and you want to hijack some emails going through Sendgrid to steal some important information.

To achieve this, you will need to:

* Install docker on your machine
* Pull and run the Docker image \`ufoym/tcpdump\`
* Run the Docker container using \`--net=host\` to capture host network traffic
* Use \`tcpdump\` to capture network traffic
* Look for sensitive information in the captured traffic
* Find clear-text credentials and API keys

### 1. Dictionary attack

Password-based authentication systems can be easily broken by using a dictionary attack (you'll have to find your own password dictionary). Let's try it on an SSH account.

* Install Docker on your machine
* Pull and run the Docker image \`vimagick/hydra\`
* Run the Docker container using appropriate flags
* Use Hydra to try to brute force the authentication system
* Use a password dictionary (you might need multiple of them)
* Use different variations of the username and password lists to maximize your chances
* Document your findings

Requirements:

* Your answer file should contain the password
* Your answer file should explain how you found the password
* Your answer file should end with a new line`; 