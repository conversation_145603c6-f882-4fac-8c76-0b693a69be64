// import { mapYourMindQuiz } from "../../project-quizzes/onboarding/map-your-mind";

export const mapYourMind = {
  title: "Map your mind",
  status: "onboarding",
  content: `# Map your mind
![Algorithms](https://img.shields.io/badge/Algorithms-Flowcharts-38B2AC) ![Skills](https://img.shields.io/badge/Skills-PseudoCode-38B2AC)
👤 By ALX Staff
📝 Beginner
⚖️ Weight: 30
📅 Start: Feb 10, 2025 6:00 AM
📅 End: Feb 10, 2025 6:00 AM
⏱️ Duration: 1 day

### In a nutshell...
* **Auto QA review:** Manual QA review: 20.0/30.0 mandatory
* **Altogether:** 66.67%
  * **Mandatory:** 66.67%
  * **Optional:** no optional tasks
* **Calculation:** 66.67% + (0.0% * 0.0%) == 66.67%
## Concepts
For this project, we expect you to look at these concepts:
* [White boarding](/concepts/100000)
* [Flowcharts](/concepts/130)
## Resources
**Read/watch**
* [How to think like a programmer](https://www.youtube.com/watch?v=rWMuEIcdJP4)
* [What is Pseudocode](https://www.youtube.com/watch?v=PwGA4Lm8zuE)
* [what is an algorithm](https://www.youtube.com/watch?v=6hfOvs8pY1k)
* [How to write pseudocode](https://www.geeksforgeeks.org/how-to-write-a-pseudo-code/)
* [Real life examples of pseudocode](https://www.youtube.com/watch?v=preyTbKXDoQ&t=400s)
* [Practice examples of Pseudocode](https://www.youtube.com/watch?v=preyTbKXDoQ&t=657s)
* (Optional) [deepdive](https://profiles.canterbury.ac.nz/Tim-Bell)
* [More practice examples](https://computersciencewiki.org/images/e/ea/Pseudo_Code_Practice_Problems.pdf)
## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:
### General:
* What is a Pseudocode?
* why is Pseudocoding important in programming?
* How do you write Pseudocodes?


### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Quiz Questions

## Tasks

### 0. Being a square keeps you from going around in circles. {{mandatory}}
Score: 0.0% (Checks completed: 0.0%)

To develop an algorithm

**calculate the sum of squares of given input of numbers**

you are going to create a flow chart and pseudo code.
* Create the algorithm in a flow chart save a screen shot and upload it to google drive, and give access to anyone with the link
* Create pseudo code in a google doc and and give access to anyone with the link

**Add flowchart URL here:**
{{URL_INPUT}}
{{ACTION_BUTTONS}}

**Add pseudo code URL here:**
{{URL_INPUT}}
{{ACTION_BUTTONS}}

### 1. Order marches with weighty and measured strides. Disorder is always in a hurry. {{mandatory}}
Score: 100.0% (Checks completed: 100.0%)

To develop an algorithm that

**reads in three numbers and writes them all in sorted order**

you are going to create a flow chart and pseudo code.
* Create the algorithm in a flow chart save a screen shot and upload it to google drive, and give access to anyone with the link
* Create pseudo code in a google doc and and give access to anyone with the link

**Add flowchart URL here:**
{{URL_INPUT}}
{{ACTION_BUTTONS}}

**Add pseudo code URL here:**
{{URL_INPUT}}
{{ACTION_BUTTONS}}

### 2. It is not enough to run; one must start in time {{mandatory}}
Score: 100.0% (Checks completed: 100.0%)

To develop an algorithm

**Calculate a running sum**

A user will enter numbers that will be added to the sum and when a negative number is encountered, stop adding numbers and write out the final result

you are going to create a flow chart and pseudo code.
* Create the algorithm in a flow chart save a screen shot and upload it to google drive, and give access to anyone with the link
* Create pseudo code in a google doc and and give access to anyone with the link

**Add flowchart URL here:**
{{URL_INPUT}}
{{ACTION_BUTTONS}}

**Add pseudo code URL here:**
{{URL_INPUT}}
{{ACTION_BUTTONS}}

## Ready for manual review
Now that you are ready to be reviewed, share your link to your peers... and review one of them.
{{MANUAL_REVIEW_BUTTON}}`,
//   quiz: mapYourMindQuiz,
};
