// import { owningLearningQuiz } from "../../project-quizzes/onboarding/owning-learning";

export const owningLearning = {
  title: "Owning your Learning",
  status: "onboarding",
  content: `# Owning your Learning
![Learning](https://img.shields.io/badge/Learning-Ownership-38B2AC)
👤 By ALX
📝 Beginner
⚖️ Weight: 1
📅 Start: Jan 24, 2023 6:00 AM
📅 End: Jan 25, 2023 6:00 AM
⏱️ Duration: 24 hours


### In a nutshell
* **Auto QA review:** 0.5/1.0 mandatory & 0.0/0.0 optional
* **Altogether:** 50.0%
  * **Mandatory:** 50.0%
  * **Optional:** no optional tasks
* **Calculation:** 50.0% + (0.0% * 0.0%) == 50.0%
### Concepts
For this project, we expect you to look at these concepts:
* [Technical Questioning](/concepts/100006)
* [The Framework](/concepts/75)
* [Peer Learning Day - How To Get Started](/concepts/58)
* [Approaching a Project](/concepts/350)
## Resources
**Read or watch**
* [The Feynman Learning technique](https://fs.blog/feynman-learning-technique/)
* [How I tackle projects at ALX (Learn from Firdaus Salim)](https://medium.com/alx-africa/how-i-tackle-my-software-engineering-projects-at-alx-610f3f5a6448)
* [Focus and diffuse mode of thinking watch](https://www.youtube.com/watch?v=WTr12dK2Se0)
* [Focus and diffuse mode of thinking read](https://www.brainscape.com/academy/focused-vs-diffuse-thinking-learning/)
## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:
* what steps do i need to learn how to learn?
* What is the Feynman learning technique and how can I implement it?
* What does it mean to own my own learning journey?
* In what ways can I crack through if I don't get it***:)


## Quiz Questions
## Tasks
### 0. Quiz passed! {{mandatory}}
Score: 50.0% (Checks completed: 100.0%)
Nothing else, you made it!
{{ACTION_BUTTONS}}`,
//   quiz: owningLearningQuiz,
};
