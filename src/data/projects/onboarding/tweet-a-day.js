// import { tweetADayQuiz } from "../../project-quizzes/onboarding/tweet-a-day";

export const tweetADay = {
  title: "A tweet a day keeps the @julienbarbier42 far away",
  status: "onboarding",
  content: `# A tweet a day keeps the @julienbarbier42 far away
![Social](https://img.shields.io/badge/Socialmedia-Twitter-1DA1F2)
👤 By ALX Staff
📝 Beginner
⚖️ Weight: 4
📅 Start: Feb 10, 2025 6:00 AM
📅 End: Feb 10, 2025 6:00 AM
⏱️ Duration: 1 day

### In a nutshell
* **Auto QA review:** 4.0/4.0 mandatory & 0.0/0.0 optional
* **Altogether:** 100.0%
  * **Mandatory:** 100.0%
  * **Optional:** no optional tasks
* Calculation: 100.0% + (0.0% * 0.0%) == 100.0%
## Project Overview
A software engineer may find it beneficial to have a Twitter account for a number of reasons.
These may include:
* Staying informed about industry news and trends by following relevant accounts
* Networking with other professionals in the field
* Sharing updates on personal projects and contributions to open-source software
* Finding job opportunities through connections made on the platform
* Participating in technical discussions and learning from other engineers
Having a Twitter account can also be a good way to establish a personal brand and showcase your expertise to potential employers or collaborators. It's good to start early and to connect with your peers as soon as possible to start building your network.

![Project Image](/images/100043/assets/7d5b22a7bb943b00ba557ffef754914fe5d7b695.png)


## Quiz Questions
## Tasks
### 0. Create your Twitter account and add it to the Intranet {{mandatory}}
Score: 100.0% (Checks completed: 100.0%)

If you don't have one already, create your [Twitter](https://twitter.com) account.
Most people only have one account, but some prefer to have two separate accounts:
* one personal
* one professional

When done, update your profile page, section "**Social presence**", "**Twitter username**". You do not need to put the @.

{{ACTION_BUTTONS}}

### 1. Follow 10 new accounts {{mandatory}}
Score: 100.0% (Checks completed: 100.0%)

Following 10 new accounts (you can do more if you would like of course). It could be:
* your friends
* the ALX SE staff (see list bellow in 3.)
* your peers
* your favorite tech influencers

If you do not know where to start, you can follow these two accounts if you want:
* [Julien Barbier](https://twitter.com/julienbarbier42): Co-Creator of the ALX SE curriculum, and Founder of ALX
* [Faces of ALX SE](https://twitter.com/FacesofALXSE) which tells the story of current ALX SE students as well as alumni.

Note: No matter how many accounts you already follow, try to follow 10 more.

{{ACTION_BUTTONS}}

### 2. Get 10 new followers {{mandatory}}
Score: 100.0% (Checks completed: 100.0%)

Acquire 10 (or more) additional followers. If you haven't chatted with your peers yet and don't know who to ask, try Slack :)

{{ACTION_BUTTONS}}

### 3. Don't be a stranger {{mandatory}}
Score: 100.0% (Checks completed: 100.0%)

Here are a few of the many people making ALX SE possible and who are on Twitter as well. They work tirelessly to make this program the best in the world and help you succeed. Come say hi to them on Twitter if you would like to:
* [Lynn](https://twitter.com/LynnJordann) - Operations & Community Specialist
* [Joy](https://twitter.com/JoyUzuegbu) - Host of the Faces of ALX SE show
* [Firdaus](https://twitter.com/dausyy_) - SE Technical Mentor Fellow
* [Maarten](https://twitter.com/MaartenMtK) - Software Engineer and Technical Mentor
* [Dr Obed](https://twitter.com/obedparla) - Curriculum analyst
* [Vincent](https://twitter.com/vvnaik_) - Software Engineer
* [Alfred](https://twitter.com/Alfred_Augstine) - SE Technical Mentor Fellow
* [Charles](https://twitter.com/thecarlstar) - SE Technical Mentor Fellow
* [Yosef](https://twitter.com/yosefworku_) - SE Technical Mentor Fellow
* [Cole](https://twitter.com/collinmutai_) - Software Engineer and Technical Mentor
* [Nehemiah](https://twitter.com/nehemiah_bro) - SE Technical Mentor Fellow
* [Alex](https://twitter.com/alexaorrico) - Software Engineer
* [Guillaume](https://twitter.com/guillaumesalva) - CTO and Co-Creator of the ALX SE curriculum
* [Leykun](https://twitter.com/leykunz) - SE Technical Mentor Fellow
* [Andrew](https://twitter.com/andrewhungwe) - SE Technical Mentor Fellow
* [Zubeir](https://twitter.com/zuu_been) - Curriculum Integrator
* [Aunty Betty](https://twitter.com/BettyBackspace) - C Syntax Specialist and Extra White Space Tracker
* [Julien](https://twitter.com/julienbarbier42) - Co-Creator of the ALX SE curriculum and Founder of ALX
{{ACTION_BUTTONS}}`,
//   quiz: tweetADayQuiz,
};
