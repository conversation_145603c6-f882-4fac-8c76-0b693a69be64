// import { welcomeOnboardQuiz } from "../../project-quizzes/onboarding/welcome-onboard";

export const welcomeOnboard = {
  title: "Welcome on board",
  status: "onboarding",
  content: `# Welcome on board
![Onboarding](https://img.shields.io/badge/Onboarding-Welcome-38B2AC)
👤 By ALX Staff
📝 Beginner
⚖️ Weight: 1
📅 Start: Feb 10, 2025 6:00 AM
📅 End: Feb 10, 2025 6:00 AM
⏱️ Duration: 1 day

### In a nutshell...
* ** Auto QA review:** 5.0/5 mandatory
* **Manual QA review:** 5.0/5 mandatory
* **Altogether:** 100.0%
	* **Mandatory:** 100.0%
	* **Optional:** no optional tasks
## Concepts
For this project, we expect you to look at these concepts:
* [Web Stack Portfolio Project Criteria](/concepts/102912)
* [BrandU Challenge - 2nd challenge: Revamp Your CV & Cover Letter](/concepts/108825)
* [SE Face-Off Cup: Round of 11 💻🏆](/concepts/107041)
* [BrandU Challenge - 3rd Challenge: Crafting Your Professional Story](/concepts/108835)
* [Guidelines: Take the Hot Seat - Live Interview and Leetcode Problem DEMOs #ALXLevelUp](/concepts/106541)
* [ALX Software Engineer Program Cheatsheet](/concepts/104541)
* [The ALX SE Tutors Program: A guide](/concepts/107371)
* [SE Face-Off Cup: Semi Finals 💻🏆](/concepts/107372)
* [Portfolio Project Review: Introducing Automated Project Review System](/concepts/107416)
* [BrandU Challenge - Focus Draft Elevate](/concepts/108711)
* [BrandU Challenge - 1st challenge: Draft your Personal Mission Statement](/concepts/108713)
* [ALX SE Program: Code of conduct](/concepts/100010)
* [ALX SE Program: Code of conduct - Community Guidelines](/concepts/100011)
* [ALX SE Program: Code of Conduct - Enforcement of Community Guidelines](/concepts/100012)
* [Mentor Yellow Pages](/concepts/100031)
* [The Essential Cheat Sheet for Open Source Projects #ALXLevelUp](/concepts/105416)
* [SE Face-off Cup Starter Pack 💻🏆](/concepts/106962)
* [SE Face-off Cup : Qualifications 💻🏆](/concepts/106963)
* [The Framework](/concepts/75)

[embed: You can do hard things](https://www.youtube.com/watch?v=-QObEqKvSIg)
## You can do hard things
[embed: What's the mindset you need to succeed in this program](https://www.youtube.com/watch?v=smaIXWG1EDY)
Take a deep breath. You're here - you made it. Take note of how you're feeling after watching that video, and give that feeling a name. Think about where in your body you felt it and what it felt like.
You are joining a movement of thousands of people across Africa who know in the marrow of their bones how great Africa's potential is; who hold in their hearts her greatest hopes, dreams, and ambitions; and whose hands are ready to be a part of building the future that they know is possible. 
Joining the Software Engineering Community means that you recognize the immense potential that technology has for transforming Africa and solving grand challenges ranging from agriculture and food security to access to quality healthcare and education. 
You are willing to contribute your most important resources, your time and energy, to build a future where tech unlocks this potential. 
You are willing and able to do the hard things - even when no one believes in you.
## Quiz Questions
## Tasks
### 0. Do hard things {{mandatory}}
Score: 100.0% (Checks completed: 100.0%)
**You can do hard things**
You have watched Fred's video. 
Of all the hard things we have to do everyday, the most difficult one of them all is to keep going". In less than 100 words, what is your understanding of the above quote from the video. In what ways have you lived by this quote in the past. 
Create a google document and add it to the url box. It is easiest to allow access to anyone with the link. 
Ask a peer to review your project.

**Important**:
* Don't forget to click the red Save button on the right to save your Google document URL
* Please make sure you have set the permissions of your Google document to "**Anyone with the link**" before you share it with your peers.
![Project Image](/images/100007/assets/fb7392226d8f1a1df033f00492f45a4273aea5dd.png)
**Add URL here:**
{{URL_INPUT}}
{{ACTION_BUTTONS}}

## Ready for manual review
Now that you are ready to be reviewed, share your link to your peers... and review one of them.
{{MANUAL_REVIEW_BUTTON}}`,
//   quiz: welcomeOnboardQuiz,
};
