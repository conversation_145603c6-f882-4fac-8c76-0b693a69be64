// import { intranetQuiz } from "../../project-quizzes/onboarding/intranet";

export const intranet = {
  title: "Deep dive into the Intranet",
  status: "onboarding",
  content: `# Deep dive into the Intranet
![Intranet](https://img.shields.io/badge/Intranet-Onboarding-38B2AC)
👤 By ALX Staff
📝 Novice
⚖️ Weight: 1
📅 Start: Feb 10, 2025 6:00 AM
📅 End: Feb 10, 2025 6:00 AM
⏱️ Duration: 1 day

### In a nutshell...
* **Auto QA review:** 1.0/1.0 mandatory
* **Altogether:** 100.0%
  * **Mandatory:** 100.0%
  * **Optional:** no optional tasks
* **Calculation:** 100.0% + (0.0% * 0.0%) == 100.0%
## Concepts
For this project, we expect you to look at these concepts:
* [Peer Learning Day - How To Get Started](/concepts/58)
## Resources
**Read or watch**:
* [The Dashboard](https://www.youtube.com/watch?v=LtjCpUF5Hcg)
* [Projects and Tasks](https://www.youtube.com/watch?v=8b6xkPoYh4E)
* [Peer Learning Day](https://www.youtube.com/watch?v=cZcBzP5Ll2M)
* [The sandbox as a virtual environment](https://www.loom.com/share/a554b9dcca76492abbaec40112c27fac)
* [The checker](https://www.youtube.com/watch?v=xDGmZ65u9N0)
* [Scoring.pdf](/alx-assets/misc/2023/1/d3860f97c2cbe4f50b76d2607acae54cb8c78786.pdf)
* [Reviewing](https://www.youtube.com/watch?v=lkLW8fLXdT4)
## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:
### General
* What are peer learning days
* How does the checker work
* How are my projects graded
* How does the sandbox work
* How to submit a manual review
* How to review others
### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.
### Example for grading
The following participant scored this for the project C - Hello World.
Mandatory tasks:
* Task 0 worth 5 points: Finished before first deadline: 5 * 100% = 5 points
* Task 1 worth 5 points: Finished before first deadline: 5 * 100% = 5 points
* Task 2 worth 5 points: Finished before first deadline: 5 * 100% = 5 points
* Task 3 worth 5 points: Finished before first deadline: 5 * 100% = 5 points
* Task 4 worth 7 points: Finished before first deadline: 7 * 100% = 7 points
* Task 5 worth 7 points: Finished before first deadline: 7 * 100% = 7 points
* Task 6 worth 7 points: Finished before second deadline: 7 * 65% = 4,55 points
Optional tasks:
* Task 7 worth 5 points: Finished before second deadline: 5 * 65% = 3,25 points
* Task 8 worth 7 points: Finished before second deadline: 7 * 65% = 4,55 points
![](/images/100009/assets/8685ef0bae103b876ebc6839706b655fa98467d7.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIARDDGGGOUSBVO6H7D%2F20250307%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250307T200006Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=43e1d88b6205e0b517a40a2fb2f38f129c8093bc29b6d5f6d0c3171185ae84c1)


## Quiz Questions
## Tasks
### 0. Quiz passed! {{mandatory}}
Score: 100.0% (Checks completed: 100.0%)
Nothing else, you made it!
{{ACTION_BUTTONS}}`,
//   quiz: intranetQuiz,
};
