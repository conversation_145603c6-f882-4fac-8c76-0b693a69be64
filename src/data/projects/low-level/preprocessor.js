// import { preprocessorQuiz } from "../../project-quizzes/low-level/preprocessor-quiz";

export const preprocessor = {
  title: "0x0D. C - Preprocessor",
  status: "low-level-programming",
  content: `# 0x0D. C - Preprocessor
![C Programming](https://img.shields.io/badge/C-Programming-blue)
👤 By <PERSON>
📝 Beginner
⚖️ Weight: 1
📅 Start: Mar 19, 2024 6:00 AM
📅 End: Mar 20, 2024 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell...
* **Auto QA review:** 24.7/38 mandatory
* **Altogether:**
  * **Mandatory:** 65.0%
  * **Optional:** No optional tasks
* **Calculation:** 65.0%

## Resources
**Read or watch**:
* [Understanding C program Compilation Process](https://www.youtube.com/watch?v=eW5he5uFBNM)
* [Object-like Macros](https://gcc.gnu.org/onlinedocs/gcc-5.1.0/cpp/Object-like-Macros.html#Object-like-Macros)
* [Macro Arguments](https://gcc.gnu.org/onlinedocs/gcc-5.1.0/cpp/Macro-Arguments.html#Macro-Arguments)
* [Pre Processor Directives in C](https://www.youtube.com/watch?v=X6HiYbY3Uak)
* [The C Preprocessor](https://www.cprogramming.com/tutorial/cpreprocessor.html)
* [Standard Predefined Macros](https://gcc.gnu.org/onlinedocs/gcc-5.1.0/cpp/Standard-Predefined-Macros.html#Standard-Predefined-Macros)
* [include guard](https://en.wikipedia.org/wiki/Include_guard)
* [Common Predefined Macros](https://gcc.gnu.org/onlinedocs/gcc-5.1.0/cpp/Common-Predefined-Macros.html#Common-Predefined-Macros)

## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:

### General
* What are macros and how to use them
* What are the most common predefined macros
* How to include guard your header files

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements

### General
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your files will be compiled on Ubuntu 20.04 LTS using \`gcc\`, using the options \`-Wall -Werror -Wextra -pedantic -std=gnu89\`
* All your files should end with a new line
* A \`README.md\` file, at the root of the folder of the project is mandatory
* Your code should use the \`Betty\` style
* You are not allowed to use global variables
* No more than 5 functions per file
* The only C standard library functions allowed are \`malloc\`, \`free\` and \`exit\`. Any use of functions like \`printf\`, \`puts\`, \`calloc\`, \`realloc\` etc… is forbidden
* You are allowed to use [_putchar](https://github.com/alx-tools/_putchar.c/blob/master/_putchar.c)
* You don't have to push \`_putchar.c\`, we will use our file. If you do it won't be taken into account
* In the following examples, the \`main.c\` files are shown as examples. You can use them to test your functions, but you don't have to push them to your repo (if you do, we will not take them into account). We will use our own \`main.c\` files at compilation. Our \`main.c\` files might be different from the one shown in the examples
* Don't forget to push your header file
* All your header files should be include guarded

## Quiz Questions

## Tasks

### 0. Object-like Macro {{mandatory}}

Score: 65.0% (Checks completed: 100.0%)

Create a header file that defines a macro named \`SIZE\` as an abbreviation for the token \`1024\`.

\`\`\`c
julien@ubuntu:~/0x0c. macro, structures$ cat 0-main.c
#include "0-object_like_macro.h"
#include "0-object_like_macro.h"
#include <stdio.h>

/**
* main - check the code
* * Return: Always 0.
 */
int main(void)
{
    int s;

    s = 98 + SIZE;
    printf("%d\\n", s);
    return (0);
}
julien@ubuntu:~/0x0c. macro, structures$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 0-main.c -o a
julien@ubuntu:~/0x0c. macro, structures$ ./a 
1122
\`\`\`

**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x0D-preprocessor\`
* File: \`0-object_like_macro.h\`
{{ACTION_BUTTONS}}

### 1. Pi {{mandatory}}

Score: 65.0% (Checks completed: 100.0%)

Create a header file that defines a macro named \`PI\` as an abbreviation for the token \`3.14159265359\`.

\`\`\`c
julien@ubuntu:~/0x0c. macro, structures$ cat 1-main.c
#include "1-pi.h"
#include "1-pi.h"
#include <stdio.h>

/**
* main - check the code
* * Return: Always 0.
 */
int main(void)
{
    float a;
    float r;

    r = 98;
    a = PI * r * r;
    printf("%.3f\\n", a);
    return (0);
}
julien@ubuntu:~/0x0c. macro, structures$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 1-main.c -o b
julien@ubuntu:~/0x0c. macro, structures$ ./b
30171.855
\`\`\`

**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x0D-preprocessor\`
* File: \`1-pi.h\`
{{ACTION_BUTTONS}}

### 2. File name {{mandatory}}

Score: 65.0% (Checks completed: 100.0%)

Write a program that prints the name of the file it was compiled from, followed by a new line.
* You are allowed to use the standard library

\`\`\`c
julien@ubuntu:~/0x0c. macro, structures$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 2-main.c -o c
julien@ubuntu:~/0x0c. macro, structures$ ./c 
2-main.c
julien@ubuntu:~/0x0c. macro, structures$ cp 2-main.c 02-main.c
julien@ubuntu:~/0x0c. macro, structures$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 02-main.c -o cc
julien@ubuntu:~/0x0c. macro, structures$ ./cc
02-main.c
\`\`\`

**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x0D-preprocessor\`
* File: \`2-main.c\`
{{ACTION_BUTTONS}}

### 3. Function-like macro {{mandatory}}

Score: 65.0% (Checks completed: 100.0%)

Write a function-like macro \`ABS(x)\` that computes the absolute value of a number \`x\`.

\`\`\`c
julien@ubuntu:~/0x0c. macro, structures$ cat 3-main.c
#include <stdio.h>
#include "3-function_like_macro.h"
#include "3-function_like_macro.h"

/**
* main - check the code
* * Return: Always 0.
 */
int main(void)
{
    int i;
    int j;

    i = ABS(-98) * 10;
    j = ABS(98) * 10;
    printf("%d, %d\\n", i, j);
    return (0);
}
julien@ubuntu:~/0x0c. macro, structures$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 3-main.c -o d
julien@ubuntu:~/0x0c. macro, structures$ ./d 
980, 980
\`\`\`

**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x0D-preprocessor\`
* File: \`3-function_like_macro.h\`
{{ACTION_BUTTONS}}

### 4. SUM {{mandatory}}

Score: 65.0% (Checks completed: 100.0%)

Write a function-like macro \`SUM(x, y)\` that computes the sum of the numbers \`x\` and \`y\`.

\`\`\`c 
julien@ubuntu:~/0x0c. macro, structures$ cat 4-main.c
#include <stdio.h>
#include "4-sum.h"
#include "4-sum.h"

/**
* main - check the code
* * Return: Always 0.
 */
int main(void)
{
    int s;

    s = SUM(98, 1024);
    printf("%d\\n", s);
    return (0);
}
julien@ubuntu:~/0x0c. macro, structures$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 4-main.c -o e
julien@ubuntu:~/0x0c. macro, structures$ ./e 
1122
\`\`\`

**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x0D-preprocessor\`
* File: \`4-sum.h\`
{{ACTION_BUTTONS}}
`,
//   quiz: preprocessorQuiz,
};
