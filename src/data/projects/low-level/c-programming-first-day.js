export const cProgrammingFirstDay = {

    title: "First Day of C Programming",
    status: "low-level-programming",
    content: `# First Day of C Programming
![C Programming](https://img.shields.io/badge/C-Programming-red)
👤 <PERSON> <PERSON>
📝 Beginner
⚖️ Weight: 1
📅 Start: Mar 16, 2024, 6:00 AM
📅 End: Mar 17, 2024, 6:00 AM
⏱️ Duration: 1 day

### In a nutshell...
* **Auto QA review:** 27.5/40 mandatory
* **Altogether:**
  * **Mandatory:** 68.75%
  * **Optional:** No optional tasks
* **Calculation:** 40.91% (Checks completed: 81.82%)

## Concepts
For this project, we expect you to look at these concepts:
* [C programming](/concepts/26)

## Resources
**Read or watch**:
* [Everything you need to know to start with C.pdf](/alx-assets/misc/2022/4/e0ccf91eec6b977a9e00ed384dc285df9c2772e3.pdf)
* [<PERSON>](https://en.wikipedia.org/wiki/<PERSON>)
* ["C" Programming Language: <PERSON>](https://www.youtube.com/watch?v=de2Hsvxaf8M)
* [Why C Programming Is Awesome](https://www.youtube.com/watch?v=smGalmxPVYc)

## Learning Objectives
At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:

### General
* Why C programming is awesome
* Who invented C
* Who are <PERSON> Ritchie, Brian Kernighan and Linus Torvalds
* What are the benefits of learning C programming

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements
### General
* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your files should end with a new line
* A \`README.md\` file at the root of the repo, containing a description of the repository

## More Info
You finally got here and we are all happy for you.

To get you started on the right foot, here is a few things you need to know about C programming.

C is a powerful and foundational programming language with immense importance in the world of software engineering. Here are a few reasons why C is crucial:

* **Efficiency**: C allows you to write highly efficient code, making it ideal for system-level programming and developing resource-intensive applications.
* **Portability**: C code can be easily ported to different platforms, making it suitable for creating software that can run on various devices and operating systems.
* **Foundation for other languages**: Many modern programming languages, including C++, Java, and Python, have roots in C. Learning C will provide you with a solid foundation and make it easier to grasp these languages.
* **Widely used**: C is extensively used in industries such as embedded systems, operating systems, game development, and networking. Mastering C opens up diverse career opportunities and allows you to work on cutting-edge technologies.
* **Low-level control**: C gives you direct access to memory and hardware resources, allowing you to have fine-grained control over your programs. This is crucial when performance optimization is paramount.

**Here is a simple task to help you get access to the right resources for learning C.**

## Tasks

### 0. YouTube for the Win {{mandatory}}

Score: 100.0% (Checks completed: 100.0%)

As your first task for C programming, go ahead and subscribe to these YouTube channels where you can find extensive and easy to understand explanation of various concepts in the C language.

* [Julien Barbier YouTube Channel](https://www.youtube.com/@0xJulien): Provides useful tips, videos, and live coding sessions that can help you solve problems and improve your coding skills.
* [Dr. Ehoneah Obed YouTube Channel](https://www.youtube.com/@ehoneahobed): Student-led discussions on various topics covered in the ALX SE program.

The two channels below are well known for their down to earth content on C programming:

* [Jenny’s lecture Youtube Channel](https://www.youtube.com/@JennyslecturesCSIT)
* [Neso Academy YouTube Channel](https://www.youtube.com/@nesoacademy)

{{ACTION_BUTTONS}}

### 1. On your playlist {{mandatory}}  
Score: 100.0% (Checks completed: 100.0%)

Check out the following playlists and bookmark them for future references when working on your C programming projects.

* [Programming in C - Jenny's Lecture](https://www.youtube.com/watch?v=EjavYOFoJJ0&list=PLdo5W4Nhv31a8UcMN9-35ghv8qyFWD9_S)
* [C Programming - Dr. Ehoneah Obed](https://www.youtube.com/watch?v=LM6IjCbtpZA&list=PLqEaji1b3wvRaudEAMvyt1dIClA_y6SLS)
* [CS50 2022 - Lecture 1 - C](https://www.youtube.com/live/ywg7cW0Txs4)
* [C Programming Neso Academy](https://www.youtube.com/watch?v=rLf3jnHxSmU&list=PLBlnK6fEyqRggZZgYpPMUxdY1CYkZtARR)

{{ACTION_BUTTONS}}
`,
};
