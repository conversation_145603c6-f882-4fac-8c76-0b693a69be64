// import { variadicQuiz } from "../../project-quizzes/low-level/variadic-quiz";

export const variadic = {
	title: "0x10. C - Variadic functions",
	status: "low-level-programming",
	content: `# 0x10. C - Variadic functions
![C Programming](https://img.shields.io/badge/C-Programming-blue)
👤 By <PERSON> Barbier
📝 Advanced
⚖️ Weight: 1
📅 Start: Mar 25, 2024 6:00 AM
📅 End: Mar 26, 2024 6:00 AM
⏱️ Duration: 24 hours

### In a nutshell...
* **Auto QA review:** 22.0/44 mandatory
* **Altogether:**
  * **Mandatory:** 50.0%
  * **Optional:** No optional tasks
  * **Calculation:** 22.0 + (0.0 * 0.0) == 50.0%

## Resources

**Read or watch**:
* [stdarg.h](https://en.wikipedia.org/wiki/Stdarg.h)
* [Variadic Functions](https://www.gnu.org/software/libc/manual/html_node/Variadic-Functions.html)
* [Const Keyword](https://www.youtube.com/watch?v=1W4oyuOdXv8)
* [Variadic Functions in C Programming](https://www.youtube.com/watch?v=Lh7xydr8zzU)

## Additional Resources
* [Variadic Functions in C Programming](https://www.youtube.com/watch?v=Lh7xydr8zzU)

## Learning Objectives

At the end of this project, you are expected to be able to [explain to anyone](https://fs.blog/feynman-learning-technique/), **without the help of Google**:

### General
* What are variadic functions
* How to use \`va_start\`, \`va_arg\` and \`va_end\` macros
* Why and how to use the \`const\` type qualifier

### Copyright - Plagiarism
* You are tasked to come up with solutions for the tasks below yourself to meet with the above learning objectives.
* You will not be able to meet the objectives of this or any following project by copying and pasting someone else's work.
* You are not allowed to publish any content of this project.
* Any form of plagiarism is strictly forbidden and will result in removal from the program.

## Requirements
### General

* Allowed editors: \`vi\`, \`vim\`, \`emacs\`
* All your files will be compiled on Ubuntu 20.04 LTS using \`gcc\`, using the options \`-Wall -Werror -Wextra -pedantic -std=gnu89\`
* All your files should end with a new line
* A \`README.md\` file, at the root of the folder of the project is mandatory
* Your code should use the \`Betty\` style. It will be checked using \`betty-style.pl\` and \`betty-doc.pl\`
* You are not allowed to use global variables
* No more than 5 functions per file
* The only C standard library functions allowed are \`malloc\`, \`free\` and \`exit\`. Any use of functions like \`printf\`, \`puts\`, \`calloc\`, \`realloc\` etc… is forbidden
* You are allowed to use the following macros: \`va_start\`, \`va_arg\` and \`va_end\`
* You are allowed to use \`_putchar\`
* You don’t have to push \`_putchar.c\`, we will use our file. If you do it won’t be taken into account
* In the following examples, the \`main.c\` files are shown as examples. You can use them to test your functions, but you don’t have to push them to your repo (if you do we won’t take them into account). We will use our own \`main.c\` files at compilation. Our \`main.c\` files might be different from the one shown in the examples
* The prototypes of all your functions and the prototype of the function \`_putchar\` should be included in your header file called \`variadic_functions.h\`
* Don’t forget to push your header file
* All your header files should be include guarded

## Quiz Questions

## Tasks

### 0. Beauty is variable, ugliness is constant {{mandatory}}

Write a function that returns the sum of all its parameters.
* Prototype: \`int sum_them_all(const unsigned int n, ...);\`
* If \`n == 0\`, return \`0\`

\`\`\`c
julien@ubuntu:~/0x0f. variadic functions$ cat 0-main.c
#include <stdio.h>
#include "variadic_functions.h"

/**
 * main - check the code
 *
 * Return: Always 0.
 */
int main(void)
{
    int sum;

    sum = sum_them_all(2, 98, 1024);
    printf("%d\\n", sum);
    sum = sum_them_all(4, 98, 1024, 402, -1024);
    printf("%d\\n", sum);    
    return (0);
}
julien@ubuntu:~/0x0f. variadic functions$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 0-main.c 0-sum_them_all.c -o a
julien@ubuntu:~/0x0f. variadic functions$ ./a 
1122
500
\`\`\`


**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x10-variadic_functions\`
* File: \`0-sum_them_all.c\`
{{ACTION_BUTTONS}}

### 1. To be is to be the value of a variable {{mandatory}}

Write a function that prints numbers, followed by a new line.
* Prototype: \`void print_numbers(const char *separator, const unsigned int n, ...);\`
* where \`separator\` is the string to be printed between numbers
* and \`n\` is the number of integers passed to the function
* You are allowed to use \`printf\`
* If \`separator\` is \`NULL\`, don't print it
* Print a new line at the end of your function

\`\`\`c
julien@ubuntu:~/0x0f. variadic functions$ cat 1-main.c
#include "variadic_functions.h"

/**
 * main - check the code
 *
 * Return: Always 0.
 */
int main(void)
{
    print_numbers(", ", 4, 0, 98, -1024, 402);
    return (0);
}
julien@ubuntu:~/0x0f. variadic functions$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 1-main.c 1-print_numbers.c -o b
julien@ubuntu:~/0x0f. variadic functions$ ./b
0, 98, -1024, 402
\`\`\`

**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x10-variadic_functions\`
* File: \`1-print_numbers.c\`
{{ACTION_BUTTONS}}

### 2. One woman's constant is another woman's variable {{mandatory}}

Write a function that prints strings, followed by a new line.
* Prototype: \`void print_strings(const char *separator, const unsigned int n, ...);\`
* where \`separator\` is the string to be printed between the strings
* and \`n\` is the number of strings passed to the function
* You are allowed to use \`printf\`
* If separator is NULL, don't print it
* If one of the string is NULL, print \`(nil)\` instead
* Print a new line at the end of your function

\`\`\`c
julien@ubuntu:~/0x0f. Variadic functions$ cat 2-main.c
#include "variadic_functions.h"

/**
 * main - check the code
 *
 * Return: Always 0.
 */
int main(void)
{
    print_strings(", ", 2, "Jay", "Django");
    return (0);
}
julien@ubuntu:~/0x0f. Variadic functions$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 2-main.c 2-print_strings.c -o c
julien@ubuntu:~/0x0f. Variadic functions$ ./c 
Jay, Django
\`\`\`

**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x10-variadic_functions\`
* File: \`2-print_strings.c\`
{{ACTION_BUTTONS}}

### 3. To be is a to be the value of a variable {{mandatory}}

Write a function that prints anything.
* Prototype: \`void print_all(const char * const format, ...);\`
* where \`format\` is a list of types of arguments passed to the function
  * \`c\`: char
  * \`i\`: integer
  * \`f\`: float
  * \`s\`: char * (if the string is NULL, print \`(nil)\` instead
  * any other char should be ignored
* You are not allowed to use \`for\`, \`goto\`, ternary operator, \`else\`, \`do ... while\`
* You can use a maximum of
  * 2 \`while\` loops
  * 2 \`if\`
* You can declare a maximum of 9 variables
* You are allowed to use \`printf\`
* Print a new line at the end of your function

\`\`\`c
julien@ubuntu:~/0x0f. Variadic functions$ cat 3-main.c
#include "variadic_functions.h"

/**
 * main - check the code
 *
 * Return: Always 0.
 */
int main(void)
{
    print_all("ceis", 'B', 3, "stSchool");
    return (0);
}
julien@ubuntu:~/0x0f. Variadic functions$ gcc -Wall -pedantic -Werror -Wextra -std=gnu89 3-main.c 3-print_all.c -o d
julien@ubuntu:~/0x0f. Variadic functions$ ./d 
B, 3, stSchool
\`\`\`

**Repo:**
* GitHub repository: \`alx-low_level_programming\`
* Directory: \`0x10-variadic_functions\`
* File: \`3-print_all.c\`
{{ACTION_BUTTONS}}`,
// 	quiz: variadicQuiz,
};
