#!/bin/bash

# Create backup directories if they don't exist
mkdir -p src/data/backups/onboarding-tools
mkdir -p quiz_files/tools

# Make sure all files are properly backed up
ls src/data/backups/onboarding-tools/emacs.js
ls src/data/backups/onboarding-tools/emacs-01.js
ls src/data/backups/onboarding-tools/professional-tech.js
ls src/data/backups/onboarding-tools/vi-02.js

# Verify quiz files exist
ls quiz_files/tools/emacs-quiz.md
ls quiz_files/tools/professional-tech-quiz.md

# Remove onboarding-tools directory only if previous commands succeeded
echo "Removing onboarding-tools directory..."
rm -rf src/data/projects/onboarding-tools

# Verify final structure
echo "Verifying final directory structure..."
ls -la src/data/projects/tools/
ls -la src/data/backups/onboarding-tools/
ls -la quiz_files/tools/