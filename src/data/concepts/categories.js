// Deduplicate files in a category
const deduplicateFiles = (files) => {
  return [...new Set(files)];
};

// Process categories to ensure unique files across all categories
const processCategories = (categories) => {
  const seenFiles = new Map(); // Track which category each file was first seen in
  
  // First pass: Record the first category each file appears in
  categories.forEach(category => {
    category.files.forEach(file => {
      if (!seenFiles.has(file)) {
        seenFiles.set(file, category.id);
      }
    });
  });
  
  // Second pass: Only keep files in their primary category
  return categories.map(category => ({
    ...category,
    files: category.files.filter(file => seenFiles.get(file) === category.id)
  }));
};

const rawCategories = [
  {
    id: 'programming-fundamentals',
    name: 'Programming Fundamentals',
    description: 'Core programming concepts and languages',
    icon: 'Code',
    files: [
      'C-programming.md',
      'Pointers-and_arrays.md',
      'data-structures.md',
      'C-static_libraries.md',
      'automatic_and-dynamic-allocation.md'
    ]
  },
  {
    id: 'development-tools',
    name: 'Development Tools',
    description: 'Essential tools for software development',
    icon: 'Tools',
    files: [
      'using_emacs-as-editor.md',
      'git-github_cheatsheet.md',
      'authenticating-git.md',
      'chrome.md',
      'source-code-mgt.md'
    ]
  },
  {
    id: 'web-development',
    name: 'Web Development',
    description: 'Web technologies and frameworks',
    icon: 'Globe',
    files: [
      'HTMLCSS.md',
      'javascript-in-the-browser.md',
      'rest_api.md',
      'web-server.md',
      'trinity_of-frontend-quality.md'
    ]
  },
  {
    id: 'system-engineering',
    name: 'System Engineering',
    description: 'System architecture and infrastructure',
    icon: 'Server',
    files: [
      'Shell.md',
      'coding-your-own_shell.md',
      'DNS.md',
      'load-balancer.md',
      'server.md',
      'docker.md',
      'CICD.md',
      'monitoring.md',
      'child-process.md',
      'network-basics.md'
    ]
  },
  {
    id: 'databases',
    name: 'Databases',
    description: 'Database management and operations',
    icon: 'Database',
    files: [
      'Databases.md',
      'fresh-reset_and-install_mysql.md',
      'dealing-with-data_statistically.md'
    ]
  },
  {
    id: 'python',
    name: 'Python Development',
    description: 'Python programming and packages',
    icon: 'Python',
    files: [
      'Python-programming.md',
      'python-packages.md'
    ]
  },
  {
    id: 'project-development',
    name: 'Project Development',
    description: 'Project management and development practices',
    icon: 'Project',
    files: [
      'approaching-a-project.md',
      'portfolio-project_overview.md',
      'portfolio-project_deeper-overview.md',
      'research-and_project-approval.md',
      'webstack-portfolio_criteria.md',
      'technical-writing.md',
      'right-engineering_rightdocumenting.md',
      'never-forget-a-test.md',
      'flowcharts.md',
      '3-ways-to-make-a-portfolio-great.md'
    ]
  },
  {
    id: 'collaboration',
    name: 'Team Collaboration',
    description: 'Working effectively in teams',
    icon: 'Users',
    files: [
      'group-projects.md',
      'all-about-team-projects-FAQ-pairings.md',
      'pair-programming.md',
      'peer-learning-day.md',
      'your-learning_community.md',
      'code-of-conduct-intro.md',
      'code-of-conduct.md',
      'code-of-conduct_enforcement.md',
      'slack.md',
      'ALX_SE-discord-migration.md'
    ]
  },
  {
    id: 'technical-skills',
    name: 'Technical Skills',
    description: 'Essential technical skills and practices',
    icon: 'Brain',
    files: [
      'technical_questioning.md',
      'white-boarding.md',
      'take-the_hot_seat.md',
      'essential_cheat-sheet.md',
      'ALX-SE-program_cheatsheet.md'
    ]
  },
  {
    id: 'special-projects',
    name: 'Special Projects',
    description: 'Specialized project implementations',
    icon: 'Star',
    files: [
      'airbnb_clone.md',
      'maze-project.md',
      'Printf-function_brief.md',
      'webstack-debugging.md'
    ]
  }
];

export const categoriesConfig = processCategories(rawCategories);

// Helper to get a category by ID
export const getCategoryById = (categoryId) => {
  return categoriesConfig.find(category => category.id === categoryId);
};

// Helper to get the primary category for a file
export const getCategoryForFile = (filename) => {
  return categoriesConfig.find(category => 
    category.files.includes(filename)
  );
};

// Helper to get all unique concept files
export const getAllConceptFiles = () => {
  const allFiles = new Set();
  categoriesConfig.forEach(category => {
    category.files.forEach(file => allFiles.add(file));
  });
  return Array.from(allFiles);
};