{"3-ways-to-make-a-portfolio-great.md": "_135.md", "ALX-SE-program_cheatsheet.md": "_104541.md", "ALX_SE-discord-migration.md": "_100033.md", "C-programming.md": "_26.md", "C-static_libraries.md": "_61.md", "CICD.md": "_43.md", "DNS.md": "_12.md", "Databases.md": "_37.md", "HTMLCSS.md": "_2.md", "Pointers-and_arrays.md": "_60.md", "Printf-function_brief.md": "_100034.md", "Python-programming.md": "_550.md", "README.md": null, "SE-Face_off-cup.md": "_106963.md", "SE-Face_off-starter-pack.md": "_106962.md", "Shell.md": "_9.md", "airbnb_clone.md": "_74.md", "all-about-team-projects-FAQ-pairings.md": "_100037.md", "approaching-a-project.md": "_350.md", "authenticating-git.md": "_100035.md", "automatic_and-dynamic-allocation.md": "_62.md", "child-process.md": "_110.md", "chrome.md": "_224.md", "code-of-conduct-intro.md": "_100010.md", "code-of-conduct.md": "_100011.md", "code-of-conduct_enforcement.md": "_100012.md", "coding-your-own_shell.md": "_64.md", "data-structures.md": "_120.md", "dealing-with-data_statistically.md": "_35.md", "docker.md": "_65.md", "essential_cheat-sheet.md": "_105416.md", "flowcharts.md": "_130.md", "fresh-reset_and-install_mysql.md": "_100002.md", "git-github_cheatsheet.md": "_57.md", "group-projects.md": "_111.md", "javascript-in-the-browser.md": "_3.md", "load-balancer.md": "_46.md", "maze-project.md": "_133.md", "mentor_yellow-pages.md": "_100031.md", "monitoring.md": "_13.md", "network-basics.md": "_33.md", "never-forget-a-test.md": "_47.md", "on-call.md": "_39.md", "pair-programming.md": "_121.md", "peer-learning-day.md": "_58.md", "portfolio-project_deeper-overview.md": "_102042.md", "portfolio-project_overview.md": "_137.md", "python-packages.md": "_66.md", "regular-expression.md": "_29.md", "research-and_project-approval.md": "_138.md", "rest_api.md": "_45.md", "right-engineering_rightdocumenting.md": "_6.md", "server.md": "_67.md", "slack.md": "_221.md", "source-code-mgt.md": "_22.md", "struggling-with-sandbox.md": "_100039.md", "take-the_hot_seat.md": "_106541.md", "technical-writing.md": "_225.md", "technical_questioning.md": "_100006.md", "the_framework.md": "_75.md", "trinity_of-frontend-quality.md": "_4.md", "using_emacs-as-editor.md": "_54.md", "web-server.md": "_17.md", "webstack-debugging.md": "_68.md", "webstack-portfolio_criteria.md": "_102912.md", "white-boarding.md": "_100000.md", "your-learning_community.md": "_100001.md", "job-search-resources.md": "_100.md", "database-administration.md": "_49.md", "databases.md": "_556.md", "the-framework.md": "_559.md", "se-face-off-cup-round-of-11.md": "_107041.md", "the-alx-se-tutors-program-a-guide.md": "_107371.md", "se-face-off-cup-semi-finals.md": "_107372.md", "portfolio-project-review-introducing-automated-project-review-system.md": "_107416.md", "brandu-challenge-focus-draft-elevate.md": "_108711.md", "brandu-challenge-1st-challenge-draft-your-personal-mission-statement.md": "_108713.md", "brandu-challenge-2nd-challenge-revamp-your-cv-cover-letter.md": "_108825.md", "brandu-challenge-3rd-challenge-crafting-your-professional-story.md": "_108835.md"}