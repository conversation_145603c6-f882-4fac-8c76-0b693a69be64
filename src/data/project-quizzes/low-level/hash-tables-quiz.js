export const hashTablesQuiz = [
  {
    category: "Low Level Programming",
    question: "What is a hash function?",
    options: [
      "A function that converts data of arbitrary size to fixed-size values",
      "A function that sorts data in ascending order",
      "A function that encrypts data",
      "A function that compresses data"
    ],
    correctAnswer: "A function that converts data of arbitrary size to fixed-size values"
  },
  {
    category: "Low Level Programming",
    question: "What is a collision in hash tables?",
    options: [
      "When two different keys hash to the same index",
      "When the hash table is full",
      "When a key cannot be hashed",
      "When the hash function fails"
    ],
    correctAnswer: "When two different keys hash to the same index"
  },
  {
    category: "Low Level Programming",
    question: "Which of these is a valid collision resolution technique?",
    options: [
      "Chaining",
      "Deleting the old key",
      "Ignoring new keys",
      "Expanding the table infinitely"
    ],
    correctAnswer: "Chaining"
  },
  {
    category: "Low Level Programming",
    question: "What is the average time complexity for searching in a well-implemented hash table?",
    options: [
      "O(1)",
      "O(n)",
      "O(log n)",
      "O(n^2)"
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "Low Level Programming",
    question: "What is the load factor of a hash table?",
    options: [
      "Number of stored elements divided by table size",
      "Number of collisions in the table",
      "Size of the largest chain",
      "Number of empty slots"
    ],
    correctAnswer: "Number of stored elements divided by table size"
  },
  {
    category: "Low Level Programming",
    question: "Which of these is NOT a characteristic of a good hash function?",
    options: [
      "Predictable sequence of values",
      "Uniform distribution of values",
      "Fast computation",
      "Deterministic output"
    ],
    correctAnswer: "Predictable sequence of values"
  },
  {
    category: "Low Level Programming",
    question: "What happens when you try to insert a key-value pair into a hash table where the key already exists?",
    options: [
      "The old value is updated with the new value",
      "A collision occurs",
      "The new key-value pair is ignored",
      "The hash table expands"
    ],
    correctAnswer: "The old value is updated with the new value"
  },
  {
    category: "Low Level Programming",
    question: "What is linear probing?",
    options: [
      "A collision resolution technique that checks the next slot sequentially",
      "A method to create hash functions",
      "A way to sort hash tables",
      "A technique to compress hash tables"
    ],
    correctAnswer: "A collision resolution technique that checks the next slot sequentially"
  },
  {
    category: "Low Level Programming",
    question: "What is the main advantage of chaining over open addressing?",
    options: [
      "It can handle more collisions without performance degradation",
      "It uses less memory",
      "It is easier to implement",
      "It has faster lookup times"
    ],
    correctAnswer: "It can handle more collisions without performance degradation"
  },
  {
    category: "Low Level Programming",
    question: "What is the purpose of rehashing?",
    options: [
      "To maintain performance by resizing the table when it becomes too full",
      "To fix corrupted hash values",
      "To remove deleted elements",
      "To sort the hash table"
    ],
    correctAnswer: "To maintain performance by resizing the table when it becomes too full"
  }
];