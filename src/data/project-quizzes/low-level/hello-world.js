export const hello_worldQuiz = [
  {
    category: "hello_world",
    question: "Which command can be used to compile a C source file?",
    options: [
      "c-compiler",
      "bash",
      "gcc"
    ],
    correctAnswer: "gcc"
  },
  {
    category: "hello_world",
    question: "In which category belongs the C programming language?",
    options: [
      "Interpreted language",
      "Compiled language"
    ],
    correctAnswer: "Compiled language"
  },
  {
    category: "hello_world",
    question: "What is the common extension for a C header file?",
    options: [
      ".header",
      ".h",
      ".hpp",
      ".ch"
    ],
    correctAnswer: ".h"
  },
  {
    category: "hello_world",
    question: "What is the common extension for a C source file?",
    options: [
      ".txt",
      ".cpp",
      ".c",
      ".py"
    ],
    correctAnswer: ".c"
  },
  {
    category: "hello_world",
    question: "What are the different steps to form an executable file from C source code",
    options: [
      "Compilation and linking",
      "Interpretation, compilation and assembly",
      "Interpretation, assembly and compilation",
      "Preprocessing and compilation",
      "Preprocessing, compilation, assembly, and linking"
    ],
    correctAnswer: "Preprocessing, compilation, assembly, and linking"
  },
  {
    category: "hello_world",
    question: "Which of the following are both valid comment syntaxes in ANSI C, and Betty-compliant?",
    options: [
      "```c\n# Comment\n```",
      "```c\n/* Comment */\n```",
      "```c\n/*\nComment\n*/\n```",
      "```c\n/*\n * Comment\n */\n```",
      "```c\n/* Comment /* nested */ */\n```",
      "```c\n// Comment\n```"
    ],
    correctAnswer: [
      "```c\n/* Comment */\n```",
      "```c\n/*\n * Comment\n */\n```"
    ],
    isMulti: true
  }
];
