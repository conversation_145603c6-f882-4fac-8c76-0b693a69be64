export const linked_lists_quizQuiz = [
  {
    category: "linked_lists_",
    question: "In a singly linked list, what are possible directions to traverse it? (select all possible answers)",
    options: [
      "Forward",
      "Backward",
    ],
    correctAnswer: ["Forward"],
    isMulti: true
  },
  {
    category: "linked_lists_",
    question: "What's a node? (select all possible answers)",
    options: [
      "It's an integer",
      "It's a server",
      "It's a cell in an array",
      "It's a space allocated in memory",
      "It's a structure with a pointer to the next node and value information",
    ],
    correctAnswer: ["It's a space allocated in memory", "It's a structure with a pointer to the next node and value information"],
    isMulti: true
  },
  {
    category: "linked_lists_",
    question: "What’s the \"tail\" of a linked list?",
    options: [
      "It’s the node with the pointer to the next equals to \`NULL\`",
      "It’s the node with the highest value",
      "It’s the first node",
      "It’s the node with the lowest value",
    ],
    correctAnswer: "It’s the node with the pointer to the next equals to \`NULL\`"
  },
  {
    category: "linked_lists_",
    question: "What’s the \"head\" of a linked list?",
    options: [
      "It’s the node with the lowest value",
      "It’s the last node",
      "It’s the first node",
      "It’s the node with the pointer to the next equals to NULL",
      "It’s the node with the highest value",
    ],
    correctAnswer: "It’s the first node"
  },
  {
    category: "linked_lists_",
    question: "Arrays Vs Linked Lists: select all true statements",
    options: [
      "We can easily remove an element from an Array",
      "Array can contain as value a structure",
      "We can add elements indefinitely to a linked list",
      "We can easily removed an element from a Linked list",
      "Linked list can contain as value a structure",
      "Memory is aligned for a Linked list - each elements are back to back in the memory",
      "We can add elements indefinitely to an array",
      "Memory is aligned for an Array - each elements are back to back in the memory",
    ],
    correctAnswer: ["Array can contain as value a structure", "We can add elements indefinitely to a linked list", "We can easily removed an element from a Linked list", "Linked list can contain as value a structure", "Memory is aligned for an Array - each elements are back to back in the memory"],
    isMulti: true
  }
];
