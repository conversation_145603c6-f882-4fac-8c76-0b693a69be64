export const doubly_linked_lists_quizQuiz = [
  {
    category: "doubly_linked_lists_",
    question: "In a doubly linked list, what are possible directions to traverse it? (select all possible answers)",
    options: [
      "Forward",
      "Backward",
    ],
    correctAnswer: ["Forward", "Backward"],
    isMulti: true
  },
  {
    category: "doubly_linked_lists_",
    question: "In a doubly linked list, what’s the “head” of a linked list?",
    options: [
      "It’s the node with the pointer to the next equals to NULL",
      "It’s the node with the pointer to the previous equals to NULL",
    ],
    correctAnswer: "It’s the node with the pointer to the previous equals to NULL"
  }
];
