export const recursion_quizQuiz = [
  {
    category: "recursion_",
    question: "What does this code print?\n```c\nvoid print(int nb)\n{\n    if (nb < 0) \n    {\n        return;\n    }\n    printf(\"%d\", nb);\n    nb --;\n    print(nb);\n}\n\nint main(void)\n{\n    print(4);\n    return (0);\n}\n```",
    options: [
      "01234",
      "1234",
      "43210",
      "4321",
    ],
    correctAnswer: "43210"
  },
  {
    category: "recursion_",
    question: "What does this code print?\n```c\nvoid print(int nb)\n{\n    printf(\"%d\", nb);\n    nb --;\n    if (nb > 0) \n    {\n        print(nb);\n    }\n}\n\nint main(void)\n{\n    print(2);\n    return (0);\n}\n```",
    options: [
      "012",
      "21",
      "12",
      "210",
    ],
    correctAnswer: "21"
  },
  {
    category: "recursion_",
    question: "What does this code print?\n```c\nvoid print(int nb)\n{\n    printf(\"%d\", nb);\n    nb ++;\n    if (nb < 10) \n    {\n        print(nb);\n    }\n}\n\nint main(void)\n{\n    print(4);\n    return (0);\n}\n```",
    options: [
      "987654",
      "345678910",
      "109876543",
      "456789",
    ],
    correctAnswer: "456789"
  },
  {
    category: "recursion_",
    question: "What does this code print?\n```c\nvoid print(int nb)\n{\n    printf(\"%d\", nb);\n    -- nb;\n    if (nb > 0) \n    {\n        print(nb);\n    }\n}\n\nint main(void)\n{\n    print(4);\n    return (0);\n}\n```",
    options: [
      "4321",
      "321",
      "43210",
      "3210",
    ],
    correctAnswer: "4321"
  },
  {
    category: "recursion_",
    question: "What does this code print?\n```c\nint print(int nb)\n{\n    if (nb < 0) \n    {\n        return (0);\n    }\n    printf(\"%d\", nb + print(nb - 1));\n    nb --;\n    return (nb);\n}\n\nint main(void)\n{\n    print(4);\n    return (0);\n}\n```",
    options: [
      "64200",
      "01234568",
      "00246",
    ],
    correctAnswer: "00246"
  }
];
