/* eslint-disable no-useless-escape */
export const file_io_quizQuiz = [
  {
    category: "file_io_",
    question: "What is the `oflag` used to open a file with the mode read only?",
    options: [
      "\`O_WRONLY\`",
      "\`O_RDWR\`",
      "\`O_RDONLY\`",
    ],
    correctAnswer: "\`O_RDONLY\`"
  },
  {
    category: "file_io_",
    question: "Most of the time, on a classic, modern Linux system, what will be the value of the first file descriptor you will get after opening a new file with \`open\` (if open succeeds of course):",
    options: [
      "4",
      "3",
      "0",
      "5",
      "2",
      "6",
      "1",
    ],
    correctAnswer: "3"
  },
  {
    category: "file_io_",
    question: "Which of these answers are the equivalent of \`O_RDWR\` on Ubuntu 14.04 LTS? (select all correct answers):",
    options: [
      "(O_RDONLY + O_WRONLY)",
      "3 | 2",
      "(O_RDONLY << 1)",
      "(O_RDONLY | O_WRONLY)",
      "0",
      "(O_RDONLY & O_WRONLY)",
      "3",
      "1",
      "(O_RDONLY && O_WRONLY)",
      "O_RDONLY",
      "1 << 1",
      "2",
      "3 & 2",
      "O_WRONLY",
      "(O_WRONLY << 1)"
    ],
    correctAnswer: [
      "3 & 2",
      "1 << 1",
      "2",
      "(O_WRONLY << 1)"
    ],
    isMulti: true
  },
  {
    category: "file_io_",
    question: "What happens if you try to write “Best” to the standard input on Ubuntu 14.04 LTS?",
    options: [
      "Nothing",
      "The text will be printed on the terminal but I can’t pipe it",
      "Segmentation fault",
      "The text will be printed on the terminal on the standard output",
    ],
    correctAnswer: "The text will be printed on the terminal on the standard output"
  },
  {
    category: "file_io_",
    question: "What is the \`unistd\` symbolic constant for the standard input?",
    options: [
      "\`STDIN_FILENO\`",
      "\`STDERR_FILENO\`",
      "\`STDOUT_FILENO\`",
    ],
    correctAnswer: "\`STDIN_FILENO\`"
  },
  {
    category: "file_io_",
    question: "Without context, on Ubuntu 14.04 LTS, \`write\` is a ... (please select all correct answers):",
    options: [
      "game",
      "executable",
      "library call",
      "kernel routine",
      "system call"
    ],
    correctAnswer: [
      "executable",
      "system call"
    ],
    isMulti: true
  },
  {
    category: "file_io_",
    question: "What is the \`unistd\` symbolic constant for the Standard error?",
    options: [
      "\`STDIN_FILENO\`",
      "\`STDERR_FILENO\`",
      "\`STDOUT_FILENO\`",
    ],
    correctAnswer: "\`STDERR_FILENO\`"
  },
  {
    category: "file_io_",
    question: "What is the return value of the system call \`open\` if it fails?",
    options: [
      "0",
      "98",
      "-1",
    ],
    correctAnswer: "-1"
  },
  {
    category: "file_io_",
    question: "When I am using \`O_WRONLY | O_CREAT | O_APPEND\` -> the \`|\` are bitwise operators.",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "True"
  },
  {
    category: "file_io_",
    question: "why? #AlwaysAskWhy",
    options: [
      "Because most of the time, I will already have stdin (value 0), stdout (value 1) and stderr (value 2) opened when my program starts executing.",
      "Because this will be the first opened file descriptor and in CS we start counting starting from 0",
      "Because this will be the second opened file descriptor for my process",
      "Because this will be the third opened file descriptor for my process",
      "I don’t care I never ask why, just let me access the tasks!",
      "Because this will be the first opened file descriptor and we start counting starting from 1",
    ],
    correctAnswer: "Because most of the time, I will already have stdin (value 0), stdout (value 1) and stderr (value 2) opened when my program starts executing."
  },
  {
    category: "file_io_",
    question: "What is the \`unistd\` symbolic constant for the standard output?",
    options: [
      "STDIN_FILENO",
      "STDERR_FILENO",
      "STDOUT_FILENO",
    ],
    correctAnswer: "STDOUT_FILENO"
  },
  {
    category: "file_io_",
    question: "What system call would you use to write to a file descriptor? (select all correct answers)",
    options: [
      "printf",
      "write",
      "fprintf"
    ],
    correctAnswer: ["write"],
    isMulti: true
  },
  {
    category: "file_io_",
    question: "What is the correct combination of \`oflag\`s used to open a file with the mode write only, create it if it doesn’t exist and append new content at the end if it already exists?",
    options: [
      "\`O_WRONLY\`",
      "\`O_WRONLY | O_CREAT | O_APPEND\`",
      "\`O_WRONLY | O_CREAT | O_EXCL\`",
      "\`O_RDWR | O_CREAT | O_APPEND\`",
    ],
    correctAnswer: "\`O_WRONLY | O_CREAT | O_APPEND\`"
  },
  {
    category: "file_io_",
    question: "What is the \`oflag\` used to open a file in mode read + write?",
    options: [
      "\`O_WRONLY\`",
      "\`O_RDWR\`",
      "\`O_RDONLY\`",
    ],
    correctAnswer: "O_RDWR"
  },
  {
    category: "file_io_",
    question: "Is \`open\` a function or a system call? (select all valid answers)",
    options: [
      "it's a function provided by the kernel",
      "it's a function",
      "it's a library call",
      "it's a kernel routine",
      "it's a system call"
    ],
    correctAnswer: [
      "it's a function provided by the kernel",
      "it's a function",
      "it's a system call"
    ],
    isMulti: true
  }
];
