export const moreFunctionsQuiz = [
  {
    category: "C - More functions, more nested loops",
    question: "What is the output of the following piece of code?\n\n```c\nint i;\n\ni = 0;\nwhile (i < 10)\n{\n    i++;\n    printf(\"%d\", i / 2);\n}\n```",
    options: [
      "0011223344",
      "0112233445",
      "0123456789"
    ],
    correctAnswer: "0112233445"
  },
  {
    category: "C - More functions, more nested loops",
    question: "What is the output of the following piece of code?\n\n```c\nint i;\n\nfor (i = 48; i < 58; i++)\n{\n    printf(\"%c\", i);\n}\n```",
    options: [
      "48495051525354555657",
      "School",
      "0123456789"
    ],
    correctAnswer: "0123456789"
  },
  {
    category: "C - More functions, more nested loops",
    question: "What is the return value of the following function?\n\n```c\nint some_function(void)\n{\n    int i;\n\n    for (i = 0; i < 10; i++)\n    {\n        printf(\"%d\", i);\n    }\n    return(i);\n}\n```",
    options: [
      "0",
      "10",
      "9",
      "0123456789"
    ],
    correctAnswer: "10"
  },
  {
    category: "C - More functions, more nested loops",
    question: "What is the output of the following piece of code?\n\n```c\nint i;\n\nfor (i = 0; i < 10; i++)\n{\n    printf(\"%d\", i * 2);\n}\n```",
    options: [
      "0123456789",
      "024681012141618",
      "2468101214161820"
    ],
    correctAnswer: "024681012141618"
  },
  {
    category: "C - More functions, more nested loops",
    question: "What is the output of the following piece of code?\n\n```c\nint i;\n\ni = 0;\nwhile (i < 10)\n{\n    printf(\"%d\", i % 2);\n    i++;\n}\n```",
    options: [
      "0101010101",
      "1010101010",
      "0123456789"
    ],
    correctAnswer: "0101010101"
  },
  {
    category: "C - More functions, more nested loops",
    question: "What is the return value of the following function?\n\n```c\nint some_function(void)\n{\n    printf(\"%d\", 12);\n    return (98);\n}\n```",
    options: [
      "12",
      "402",
      "98"
    ],
    correctAnswer: "98"
  },
  {
    category: "C - More functions, more nested loops",
    question: "What is the output of the following piece of code?\n\n```c\nint i;\n\ni = 9;\nwhile (i--)\n{\n    printf(\"%d\", i);\n}\n```",
    options: [
      "87654321",
      "9876543210",
      "876543210",
      "987654321"
    ],
    correctAnswer: "876543210"
  },
  {
    category: "C - More functions, more nested loops",
    question: "What is the output of the following piece of code?\n\n```c\nint i;\n\ni = -9;\nwhile (i < 0)\n{\n    printf(\"%d\", -i);\n    i++;\n}\n```",
    options: [
      "9876543210",
      "-9-8-7-6-5-4-3-2-10",
      "987654321",
      "-9-8-7-6-5-4-3-2-1"
    ],
    correctAnswer: "987654321"
  },
  {
    category: "C - More functions, more nested loops",
    question: "What is the output of the following piece of code?\n\n```c\nint i;\n\ni = 9;\nwhile (--i)\n{\n    printf(\"%d\", i);\n}\n```",
    options: [
      "87654321",
      "876543210",
      "9876543210",
      "987654321"
    ],
    correctAnswer: "87654321"
  }
];