export const argv_quizQuiz = [
  {
    category: "argv_",
    question: "What is `argv[argc]`?",
    options: [
      "The program name",
      "NULL",
      "The first command line argument",
      "The last command line argument",
      "It does not always exist",
    ],
    correctAnswer: "NULL"
  },
  {
    category: "argv_",
    question: "What is `argc`?",
    options: [
      "The number of command line arguments",
      "The size of the argv array",
      "A flag set to 1 when command line arguments are present",
      "The length of the first command line argument",
    ],
    correctAnswer: ["The number of command line arguments", "The size of the argv array"],
    isMulti: true
  },
  {
    category: "argv_",
    question: "What is `argv[0]`?",
    options: [
      "NULL",
      "The first command line argument",
      "It does not always exist",
      "The program name",
    ],
    correctAnswer: "The program name"
  },
  {
    category: "argv_",
    question: "What is `argv`?",
    options: [
      "An array containing the program compilation flags",
      "An array of size argc",
      "An array containing the program command line arguments",
    ],
    correctAnswer: ["An array of size argc", "An array containing the program command line arguments"],
    isMulti: true
  },
  {
    category: "argv_",
    question: "In the following command, what is `argv[2]`?\n```c\n$ ./argv \"My School\" \"is fun\"\n```",
    options: [
      "is",
      "School",
      "My School",
      "NULL",
      "fun",
      "My",
      "My School is fun",
      "./argv",
      "is fun",
    ],
    correctAnswer: "is fun"
  },
  {
    category: "argv_",
    question: "In the following command, what is `argv[2]`?\n```c\n$ ./argv My School is fun\n```",
    options: [
      "is",
      "School",
      "My School",
      "NULL",
      "fun",
      "My",
      "My School is fun",
      "./argv",
      "is fun",
    ],
    correctAnswer: "School"
  },
  {
    category: "argv_",
    question: "In the following command, what is `argv[2]`?\n```c\n$ ./argv \"My School is fun\"\n```",
    options: [
      "is",
      "School",
      "My School",
      "NULL",
      "fun",
      "My",
      "My School is fun",
      "./argv",
      "is fun",
    ],
    correctAnswer: "NULL"
  }
];
