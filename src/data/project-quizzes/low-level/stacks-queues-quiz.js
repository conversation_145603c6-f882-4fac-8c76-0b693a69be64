export const stacks_queues_quizQuiz = [
  {
    category: "stacks_queues_",
    question: "What’s the command used to remove a new element from a stack?",
    options: [
      "push",
      "pop",
    ],
    correctAnswer: "pop"
  },
  {
    category: "stacks_queues_",
    question: "Which of these stacks are keeping the order of insertion? (select all possible answers)",
    options: [
      "FIFO",
      "<PERSON>IF<PERSON>",
      "<PERSON>IL<PERSON>",
      "<PERSON>ILO"
    ],
    correctAnswer: ["FIFO", "LILO"],
    isMulti: true
  },
  {
    category: "stacks_queues_",
    question: "What’s the command used to add a new element to a stack?",
    options: [
      "push",
      "pop",
    ],
    correctAnswer: "push"
  },
  {
    category: "stacks_queues_",
    question: "Which of these stacks are reversing the order of insertion? (select all possible answers)",
    options: [
      "FIFO",
      "<PERSON>IFO",
      "<PERSON>IL<PERSON>",
      "<PERSON>IL<PERSON>"
    ],
    correctAnswer: ["LIFO", "FILO"],
    isMulti: true
  }
];
