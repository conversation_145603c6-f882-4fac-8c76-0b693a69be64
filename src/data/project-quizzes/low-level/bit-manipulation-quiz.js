export const bit_manipulation_quizQuiz = [
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x01 | 0x00 =\n```",
    options: [
      "0x00",
      "0x02",
      "0x01",
    ],
    correctAnswer: "0x01"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x01 << 1 =\n```",
    options: [
      "0x03",
      "0x00",
      "0x02",
      "0x10",
      "0x01",
    ],
    correctAnswer: "0x02"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n~ 0x12 =\n```",
    options: [
      "0xED",
      "0xEE",
      "0xFD",
      "0x21",
    ],
    correctAnswer: "0xED"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n~ 0x98 =\n```",
    options: [
      "0x66",
      "0x68",
      "0x67",
    ],
    correctAnswer: "0x67"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x01 & 0x01 =\n```",
    options: [
      "0x00",
      "0x02",
      "0x01",
    ],
    correctAnswer: "0x01"
  },
  {
    category: "bit_manipulation_",
    question: "What is `98` in base16?\n```c\n98\n```",
    options: [
      "0x62",
      "0x96",
      "0x98",
    ],
    correctAnswer: "0x62"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x02 >> 1 =\n```",
    options: [
      "0x02",
      "0x00",
      "0x01",
    ],
    correctAnswer: "0x01"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x01 & 0x00 =\n```",
    options: [
      "0x00",
      "0x02",
      "0x01",
    ],
    correctAnswer: "0x00"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x89 >> 3 =\n```",
    options: [
      "0x11",
      "0x89",
      "0x22",
      "0x08",
      "0x44",
    ],
    correctAnswer: "0x11"
  },
  {
    category: "bit_manipulation_",
    question: "What is `0x89` in base10?\n```c\n0x89\n```",
    options: [
      "89",
      "137",
      "135",
      "139",
    ],
    correctAnswer: "137"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x13 << 1 =\n```",
    options: [
      "0x13",
      "0x4C",
      "0x26",
      "0x98",
    ],
    correctAnswer: "0x26"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x01 | 0x01 =\n```",
    options: [
      "0x00",
      "0x02",
      "0x01",
    ],
    correctAnswer: "0x01"
  },
  {
    category: "bit_manipulation_",
    question: "What is `0x89` in base2?\n```c\n0x89\n```",
    options: [
      "0b10101001",
      "0b01101001",
      "0b10001001",
      "0b10001000",
    ],
    correctAnswer: "0b10001001"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x88 & 0x01 =\n```",
    options: [
      "0x00",
      "0x88",
      "0x01",
      "0x89",
    ],
    correctAnswer: "0x00"
  },
  {
    category: "bit_manipulation_",
    question: "What is `0b01101101` in base16?\n```c\n0b01101101\n```",
    options: [
      "0x7D",
      "0x36",
      "0x6E",
      "0xD6",
      "0x6D",
    ],
    correctAnswer: "0x6D"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x44 | 0x22 =\n```",
    options: [
      "0x22",
      "0x66",
      "0x44",
    ],
    correctAnswer: "0x66"
  },
  {
    category: "bit_manipulation_",
    question: "What is `98` in base2?\n```c\n98\n```",
    options: [
      "0b01010010",
      "0b10011000",
      "0b01100010",
    ],
    correctAnswer: "0b01100010"
  },
  {
    category: "bit_manipulation_",
    question: "What is `0b001010010` in base10?\n```c\n0b001010010\n```",
    options: [
      "81",
      "83",
      "82",
      "84",
    ],
    correctAnswer: "82"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x66 & 0x22 =\n```",
    options: [
      "0x22",
      "0x66",
      "0x44",
    ],
    correctAnswer: "0x22"
  },
  {
    category: "bit_manipulation_",
    question: "What is the result of this expression?\n```c\n0x89 & 0x01 =\n```",
    options: [
      "0x00",
      "0x88",
      "0x01",
      "0x89",
    ],
    correctAnswer: "0x01"
  }
];
