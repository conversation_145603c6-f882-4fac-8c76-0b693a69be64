export const variables_quizQuiz = [
  {
    category: "variables_",
    question: "Which of the following are valid `if` statements in ANSI C and Betty-compliant? (Considering a and b two variables of type int)",
    options: [
      "```c\nif ((((((a > b))))))\n{\n  return (a);\n}\n```",
      "```c\nif a > b\n{\n  return (a);\n}\n```",
      "```c\nif {a > b}\n(\n  return {a};\n)\n```",
      "```c\nif (a > b)\n  return (a);\n```",
      "```c\nif (a > b)\n{\n  return (a);\n}\n```"
    ],
    correctAnswer: ["```c\nif ((((((a > b))))))\n{\n  return (a);\n}\n```", "```c\nif (a > b)\n  return (a);\n```", "```c\nif (a > b)\n{\n  return (a);\n}\n```"],
    isMulti: true
  },
  {
    category: "variables_",
    question: "What is the size of the `unsigned int` data type?",
    options: [
      "1 byte",
      "4 bytes",
      "2 bytes",
      "8 bytes"
    ],
    correctAnswer: "4 bytes"
  },
  {
    category: "variables_",
    question: "What is the size of the `float` data type?",
    options: [
      "1 byte",
      "4 bytes",
      "2 bytes",
      "8 bytes"
    ],
    correctAnswer: "4 bytes"
  },
  {
    category: "variables_",
    question: "What is the size of the `char` data type?",
    options: [
      "1 byte",
      "4 bytes",
      "2 bytes",
      "8 bytes"
    ],
    correctAnswer: "1 byte"
  },
  {
    category: "variables_",
    question: "Which of the following are valid `while` or `do/while` statements in ANSI C and Betty-compliant? (Considering a and b two variables of type int)",
    options: [
      "```c\na = 0;\nwhile (a < b)\n(\n    printf(\"%d\\n\", a);\n    a++;\n)\n```",
      "```c\nwhile (a = 0; a < b; a++)\n{\n    printf(\"%d\\n\", a);\n}\n```",
      "```c\na = 0;\ndo {\n    printf(\"%d\\n\", a);\n    a++;\n} while (a < b);\n```",
      "```c\na = 0;\nwhile (a < b)\n    printf(\"%d\\n\", a++);\n```",
      "```c\na = 0;\ndo while (a < b)\n{\n    printf(\"%d\\n\", a);\n    a++;\n}\n```",
      "```c\na = 0;\nwhile (a < b)\n{\n    printf(\"%d\\n\", a);\n    a++;\n}\n```"
    ],
    correctAnswer: ["```c\na = 0;\ndo {\n    printf(\"%d\\n\", a);\n    a++;\n} while (a < b);\n```", "```c\na = 0;\nwhile (a < b)\n    printf(\"%d\\n\", a++);\n```", "```c\na = 0;\nwhile (a < b)\n{\n    printf(\"%d\\n\", a);\n    a++;\n}\n```"],
    isMulti: true
  },
  {
    category: "variables_",
    question: "Which of the following are valid `for` statements in ANSI C and Betty-compliant? (Considering a and b two variables of type int)",
    options: [
      "```c\na = 0;\nfor (a < b;;)\n{\n    printf(\"%d\\n\", a++);\n}\n```",
      "```c\nfor (a = 0; a < b; a++)\n{\n    printf(\"%d\\n\", a);\n}\n```",
      "```c\nfor (int a = 0; a < b; a++)\n{\n    printf(\"%d\\n\", a);\n}\n```",
      "```c\na = 0;\nfor (; a < b;)\n{\n    printf(\"%d\\n\", a++);\n}\n```",
      "```c\nfor (a = 0; a < b; a++)\n    printf(\"%d\\n\", a);\n```"
    ],
    correctAnswer: ["```c\nfor (a = 0; a < b; a++)\n{\n    printf(\"%d\\n\", a);\n}\n```", "```c\na = 0;\nfor (; a < b;)\n{\n    printf(\"%d\\n\", a++);\n}\n```", "```c\nfor (a = 0; a < b; a++)\n    printf(\"%d\\n\", a);\n```"],
    isMulti: true
  }
];
