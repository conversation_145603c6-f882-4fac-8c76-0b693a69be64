export const static_libraries_quizQuiz = [
  {
    category: "static_libraries_",
    question: "What is the format of a static library?",
    options: [
      "A relocatable ELF file",
      "A shared ELF file",
      "An executable ELF file",
      "An archive",
    ],
    correctAnswer: "An archive"
  },
  {
    category: "static_libraries_",
    question: "What command is used to create a static library from object files?",
    options: [
      "ar",
      "gcc",
      "ranlib",
      "nm",
      "ld",
    ],
    correctAnswer: "ar"
  },
  {
    category: "static_libraries_",
    question: "What command(s) can be used to list the symbols stored in a static library?",
    options: [
      "nm",
      "ar",
      "ranlib",
      "ld",
    ],
    correctAnswer: ["nm", "ar"],
    isMulti: true
  },
  {
    category: "static_libraries_",
    question: "What is the point of using `ranlib`?",
    options: [
      "Creating an archive",
      "Create a library from a simple archive",
      "Indexing an archive",
      "List the content of a library",
    ],
    correctAnswer: "Indexing an archive"
  }
];
