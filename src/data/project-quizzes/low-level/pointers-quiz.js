export const pointers_quizQuiz = [
  {
    category: "pointers_",
    question: "What is the identifier to print an address with `printf`?",
    options: [
      "%a",
      "%p",
      "%d",
      "%x",
    ],
    correctAnswer: "%p"
  },
  {
    category: "pointers_",
    question: "What is the value of `n` after the following code is executed?\n```c\nint n = 98;\nint *p = \u0026n;\n\n*p++;\n```",
    options: [
      "0",
      "99",
      "98",
      "402",
    ],
    correctAnswer: "98"
  },
  {
    category: "pointers_",
    question: "What is the equivalent of typing `arr[2]` with the given `int arr[5];`?",
    options: [
      "arr + 2",
      "*(arr + 2)",
      "*arr + 2",
    ],
    correctAnswer: "*(arr + 2)"
  },
  {
    category: "pointers_",
    question: "What is the size of a pointer to a `char` (on a 64-bit architecture)?",
    options: [
      "1 byte",
      "4 bytes",
      "2 bytes",
      "8 bytes",
    ],
    correctAnswer: "8 bytes"
  },
  {
    category: "pointers_",
    question: "What is the value of `n` after the following code is executed?\n```c\nint n = 98;\nint *p = \u0026n;\n\np = 402;\n```",
    options: [
      "0",
      "99",
      "98",
      "402",
    ],
    correctAnswer: "98"
  },
  {
    category: "pointers_",
    question: "If we have a variable called `var` of type `int`, how can we get its address in memory?",
    options: [
      "*var",
      "\u0026var",
      "*(var)",
    ],
    correctAnswer: "\u0026var"
  },
  {
    category: "pointers_",
    question: "What is the value of `n` after the following code is executed?\n```c\nint n = 98;\nint *p = \u0026n;\n\n*p = 402;\n```",
    options: [
      "0",
      "99",
      "98",
      "402",
    ],
    correctAnswer: "402"
  },
  {
    category: "pointers_",
    question: "What is the size in memory of the variable `int arr[5];`?",
    options: [
      "32 bytes",
      "4 bytes",
      "8 bytes",
      "10 bytes",
      "20 bytes",
      "5 bytes",
    ],
    correctAnswer: "20 bytes"
  },
  {
    category: "pointers_",
    question: "What is the size of a pointer to an `int` (on a 64-bit architecture)?",
    options: [
      "1 byte",
      "4 bytes",
      "2 bytes",
      "8 bytes",
    ],
    correctAnswer: "8 bytes"
  },
  {
    category: "pointers_",
    question: "What is the value of `n` after the following code is executed?\n```c\nint n = 98;\nint *p = \u0026n;\n```",
    options: [
      "0",
      "99",
      "98",
      "402",
    ],
    correctAnswer: "98"
  },
  {
    category: "pointers_",
    question: "Is it possible to declare a pointer to a pointer?",
    options: [
      "Yes",
      "It depends on the type the pointer is pointing to",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "pointers_",
    question: "The process of getting the value that is stored in the memory location pointed to by a pointer is called:",
    options: [
      "Pointing",
      "Dereferencing",
      "Accessing",
      "Casting",
    ],
    correctAnswer: "Dereferencing"
  },
  {
    category: "pointers_",
    question: "What happens when one tries to access an illegal memory location?",
    options: [
      "The operation is ignored",
      "The computer shuts down",
      "Segmentation fault",
      "There’s a chance for the computer to catch fire, and sometimes even explode",
    ],
    correctAnswer: "Segmentation fault"
  }
];
