export const preprocessor_quizQuiz = [
  {
    category: "preprocessor_",
    question: "The preprocessor links our code with libraries.",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "False"
  },
  {
    category: "preprocessor_",
    question: "What does the macro `TABLESIZE` expand to?\n\n```c\n#define BUFSIZE 1020\n#define TABLESIZE BUFSIZE\n#undef BUFSIZE\n#define BUFSIZE 37\n```",
    options: [
      "1020",
      "nothing",
      "37",
    ],
    correctAnswer: "37"
  },
  {
    category: "preprocessor_",
    question: "Why should we use include guards in our header files?",
    options: [
      "Because we said so, and we should never ask why.",
      "To avoid the problem of double inclusion when dealing with the include directive.",
    ],
    correctAnswer: "To avoid the problem of double inclusion when dealing with the include directive."
  },
  {
    category: "preprocessor_",
    question: "The macro `__FILE__` expands to the name of the current input file, in the form of a C string constant.",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "True"
  },
  {
    category: "preprocessor_",
    question: "What are the steps of compilation?",
    options: [
      "compiler 2. preprocessor 3. assembler 4. linker",
      "preprocessor 2.compiler 3. linker 4. assembler",
      "preprocessor 2.compiler 3. assembler 4. linker",
    ],
    correctAnswer: "preprocessor 2.compiler 3. assembler 4. linker"
  },
  {
    category: "preprocessor_",
    question: "What will be the last 5 lines of the output of the command `gcc -E` on this code?\n\n```c\n#include <stdlib.h>\n\nint main(void)\n{\n    NULL;\n    return (EXIT_SUCCESS);\n}\n```",
    options: [
      "```c\nint main(void)\n{\n 0;\n return (0);\n}\n```",
      "```c\nint main(void)\n{\n ((void *)0);\n return (0);\n}\n```",
      "```c\nint main()\n{\n 0;\n return (0);\n}\n```",
      "```c\nint main(void)\n{\n '\\0';\n return (0);\n}\n```",
    ],
    correctAnswer: "```c\nint main(void)\n{\n ((void *)0);\n return (0);\n}\n```"
  },
  {
    category: "preprocessor_",
    question: "The preprocessor generates object code",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "False"
  },
  {
    category: "preprocessor_",
    question: "This code will try to allocate 1024 bytes in the heap:\n\n```c\n#define BUFFER_SIZE 1024\nmalloc(BUFFER_SIZE)\n```",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "True"
  },
  {
    category: "preprocessor_",
    question: "What will be the output of this program? (on a standard 64 bits, Linux machine)\n\n```c\n#include <stdio.h>\n#include <stdlib.h>\n\n#define int char\n\nint main(void)\n{\n    int i;\n\n    i = 5;\n    printf (\"sizeof(i) = %lu\", sizeof(i));\n    return (EXIT_SUCCESS);\n}\n```",
    options: [
      "sizeof(i) = 4",
      "Segmentation Fault",
      "sizeof(i) = 8",
      "sizeof(i) = 5",
      "sizeof(i) = 1",
      "It does not compile",
    ],
    correctAnswer: "sizeof(i) = 1"
  },
  {
    category: "preprocessor_",
    question: "This is the correct way to define the macro `SUB`:\n\n```c\n#define SUB(a, b) a - b\n```",
    options: [
      "Yes",
      "No, it should be written this way: `#define SUB(a, b) (a) - (b)`",
      "No, it should be written this way: `#define SUB(a, b) (a - b)`",
      "No, it should be written this way: `#define SUB(a, b) ((a) - (b))`",
    ],
    correctAnswer: "No, it should be written this way: `#define SUB(a, b) ((a) - (b))`"
  },
  {
    category: "preprocessor_",
    question: "The preprocessor generates assembly code",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "False"
  },
  {
    category: "preprocessor_",
    question: "`NULL` is a macro",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "True"
  },
  {
    category: "preprocessor_",
    question: "The preprocessor removes all comments",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "True"
  },
  {
    category: "preprocessor_",
    question: "This portion of code is actually using the library stdlib:\n\n```c\n#include <stdlib.h>\n```",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "False"
  },
  {
    category: "preprocessor_",
    question: "What is the `gcc` option that runs only the preprocessor?",
    options: [
      "-E",
      "-pedantic",
      "-a",
      "-cisfun",
      "-p",
      "-preprocessor",
      "-P",
    ],
    correctAnswer: "-E"
  }
];
