export const even_more_pointers_quizQuiz = [
  {
    category: "even_more_pointers_",
    question: "In this following code, what is the value of `a[3][0]`?\n```c\nint a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};\n```",
    options: [
      "7",
      "{7, 8}",
      "8",
      "5",
    ],
    correctAnswer: "7"
  },
  {
    category: "even_more_pointers_",
    question: "What is the size of `p` in this code?\n```c\nint *p;\n```",
    options: [
      "4 bytes",
      "16 bytes",
      "8 bytes",
    ],
    correctAnswer: "8 bytes"
  },
  {
    category: "even_more_pointers_",
    question: "What is stored inside a pointer to a pointer to an int?",
    options: [
      "An address where an int is stored",
      "An address where an address is stored",
      "An int",
    ],
    correctAnswer: "An address where an address is stored"
  },
  {
    category: "even_more_pointers_",
    question: "In this following code, what is the value of `a[0][0]`?\n```c\nint a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};\n```",
    options: [
      "1",
      "3",
      "2",
      "4",
    ],
    correctAnswer: "1"
  },
  {
    category: "even_more_pointers_",
    question: "What is the size of `p` in this code?\n```c\nint **p;\n```",
    options: [
      "4 bytes",
      "16 bytes",
      "8 bytes",
    ],
    correctAnswer: "8 bytes"
  },
  {
    category: "even_more_pointers_",
    question: "What is the size of `*p` in this code?\n```c\nint *p;\n```",
    options: [
      "4 bytes",
      "16 bytes",
      "8 bytes",
    ],
    correctAnswer: "4 bytes"
  },
  {
    category: "even_more_pointers_",
    question: "In this following code, what is the value of `a[1][1]`?\n```c\nint a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};\n```",
    options: [
      "1",
      "3",
      "2",
      "4",
    ],
    correctAnswer: "4"
  },
  {
    category: "even_more_pointers_",
    question: "In this following code, what is the value of `a[3][1]`?\n```c\nint a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};\n```",
    options: [
      "7",
      "{7, 8}",
      "9",
      "8",
    ],
    correctAnswer: "8"
  },
  {
    category: "even_more_pointers_",
    question: "What is the size of `*p` in this code?\n```c\nint **p;\n```",
    options: [
      "4 bytes",
      "16 bytes",
      "8 bytes",
    ],
    correctAnswer: "8 bytes"
  }
];
