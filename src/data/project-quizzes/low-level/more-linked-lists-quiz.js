export const moreLinkedListsQuiz = [
  {
    category: "more_linked_lists_",
    question: "What is the difference between a singly linked list and a doubly linked list?",
    options: [
      "A singly linked list can only store integers, while a doubly linked list can store any data type",
      "A singly linked list has one pointer per node (next), while a doubly linked list has two (next and previous)",
      "A singly linked list can only be traversed forward, while a doubly linked list can be traversed in both directions",
      "Both B and C are correct"
    ],
    correctAnswer: "Both B and C are correct"
  },
  {
    category: "more_linked_lists_",
    question: "When deleting a node from a linked list, what must be updated?",
    options: [
      "Only the next pointer of the previous node",
      "Only the data in the node being deleted",
      "The next pointer of the previous node and free the deleted node's memory",
      "Nothing needs to be updated"
    ],
    correctAnswer: "The next pointer of the previous node and free the deleted node's memory"
  },
  {
    category: "more_linked_lists_",
    question: "What is the time complexity of inserting a node at the end of a linked list if you don't have a tail pointer?",
    options: [
      "O(1)",
      "O(n)",
      "O(log n)",
      "O(n²)"
    ],
    correctAnswer: "O(n)"
  }
];
