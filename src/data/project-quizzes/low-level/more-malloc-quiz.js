export const more_malloc_quizQuiz = [
  {
    category: "more_malloc_",
    question: "malloc returns an address",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "True"
  },
  {
    category: "more_malloc_",
    question: "To allocate enough space for an array of 10 integers (on a 64bit, Linux machine), I can use:",
    options: [
      "malloc(64 * 10)",
      "malloc(10 * sizeof(int))",
      "malloc(10 * int)",
    ],
    correctAnswer: "malloc(10 * sizeof(int))"
  },
  {
    category: "more_malloc_",
    question: "The memory space reserved when calling `malloc` is on:",
    options: [
      "The stack",
      "The heap",
    ],
    correctAnswer: "The heap"
  },
  {
    category: "more_malloc_",
    question: "malloc returns a pointer",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "True"
  },
  {
    category: "more_malloc_",
    question: "What will you see on the terminal?\n```c\nint main(void)\n{\n    int *ptr;\n\n    *ptr = 98;\n    printf(\"%d\\n\", *ptr);\n    return (0);\n}\n```",
    options: [
      "0",
      "It doesn't compile",
      "98",
      "Segmentation Fault",
    ],
    correctAnswer: "Segmentation Fault"
  },
  {
    category: "more_malloc_",
    question: "If I want to copy the string “Best School” into a new space in memory, I can use this statement to reserve enough space for it (select all valid answers):",
    options: [
      "malloc(11)",
      "malloc(12)",
      "malloc(strlen(\"Best School\"))",
      "malloc(strlen(\"Best School\") + 1)",
      "malloc(sizeof(\"Best School\"))",
      "malloc(10)"
    ],
    correctAnswer: [
      "malloc(12)",
      "malloc(strlen(\"Best School\") + 1)",
      "malloc(sizeof(\"Best School\"))"
    ],
    isMulti: true
  },
  {
    category: "more_malloc_",
    question: "You can do this:\n```c\nchar *s;\n\ns = strdup(\"Best School\");\nif (s != NULL)\n{\n    free(s);\n}\n```",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "more_malloc_",
    question: "You can do this:\n```c\nfree(\"Best School\");\n```",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "more_malloc_",
    question: "What is wrong with this code:\n```c\nint cp(void)\n{\n    char *s;\n\n    s = malloc(12);\n    strcpy(s, \"Best School\");\n    return (0);\n}\n```",
    options: [
      "You don't have enough space to store the copy of the string \"Best School\"",
      "You can't call strcpy with a string literal",
      "There is no comment",
      "malloc can fail so we should check its return value all the time before using the pointers returned by the function."
    ],
    correctAnswer: [
      "There is no comment",
      "malloc can fail so we should check its return value all the time before using the pointers returned by the function."
    ],
    isMulti: true
  },
  {
    category: "more_malloc_",
    question: "You can do this:\n```c\nchar str[] = \"Best School\";\n\nfree (str);\n```",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  }
];
