export const malloc_quizQuiz = [
  {
    category: "malloc_",
    question: "How many bytes will this statement allocate?\n```c\nmalloc(sizeof(unsigned int) * 2)\n```",
    options: [
      "2",
      "8",
      "4",
    ],
    correctAnswer: "8"
  },
  {
    category: "malloc_",
    question: "What is <PERSON>grind?",
    options: [
      "A container service",
      "It's a new step when I compile with gcc",
      "It's a program to validate memory allocation",
      "It's a program to test a C program in a specific environment",
    ],
    correctAnswer: "It's a program to validate memory allocation"
  },
  {
    category: "malloc_",
    question: "How many bytes will this statement allocate?\n```c\nmalloc(sizeof(int) * 10)\n```",
    options: [
      "40",
      "10",
      "32",
    ],
    correctAnswer: "40"
  },
  {
    category: "malloc_",
    question: "How many bytes will this statement allocate?\n```c\nmalloc(sizeof(int) * 4)\n```",
    options: [
      "4",
      "16",
      "8",
      "32",
    ],
    correctAnswer: "16"
  },
  {
    category: "malloc_",
    question: "How many bytes will this statement allocate?\n```c\nmalloc(10)\n```",
    options: [
      "2",
      "4",
      "10",
      "40",
    ],
    correctAnswer: "10"
  },
  {
    category: "malloc_",
    question: "How many bytes will this statement allocate?\n```c\nmalloc(sizeof(char) * 10)\n```",
    options: [
      "10",
      "40",
      "20",
    ],
    correctAnswer: "10"
  },
  {
    category: "malloc_",
    question: "How many bytes will this statement allocate?\n```c\nmalloc((sizeof(char) * 10) + 1)\n```",
    options: [
      "20",
      "1",
      "21",
      "11",
      "10",
    ],
    correctAnswer: "11"
  }
];
