export const printfQuiz = [
  {
    category: "Low Level Programming",
    question: "What is the return value of printf?",
    options: [
      "The number of characters printed",
      "Always 0 on success",
      "The length of the format string",
      "1 on success, 0 on failure"
    ],
    correctAnswer: "The number of characters printed"
  },
  {
    category: "Low Level Programming",
    question: "Which of these format specifiers are valid in printf? (Select all that apply)",
    options: [
      "%d",
      "%s",
      "%x",
      "%z"
    ],
    correctAnswer: ["%d", "%s", "%x"],
    isMulti: true
  },
  {
    category: "Low Level Programming",
    question: "What happens if you pass a string to %d in printf?",
    options: [
      "It prints the string as is",
      "It causes undefined behavior",
      "It prints 0",
      "It raises a compiler error"
    ],
    correctAnswer: "It causes undefined behavior"
  },
  {
    category: "Low Level Programming",
    question: "How do you print a literal % character using printf?",
    options: [
      "%",
      "%%",
      "\\%",
      "%p"
    ],
    correctAnswer: "%%"
  },
  {
    category: "Low Level Programming",
    question: "Which format specifier is used for printing unsigned integers?",
    options: [
      "%d",
      "%i",
      "%u",
      "%n"
    ],
    correctAnswer: "%u"
  },
  {
    category: "Low Level Programming",
    question: "What is the purpose of field width in printf format specifiers?",
    options: [
      "To specify the minimum number of characters to be printed",
      "To limit the maximum number of characters",
      "To add padding to the output",
      "To specify the precision of floating point numbers"
    ],
    correctAnswer: "To specify the minimum number of characters to be printed"
  },
  {
    category: "Low Level Programming",
    question: "What happens when using %s with NULL in printf?",
    options: [
      "Prints 'NULL'",
      "Prints '(null)'",
      "Causes a segmentation fault",
      "Prints an empty string"
    ],
    correctAnswer: "Causes a segmentation fault"
  },
  {
    category: "Low Level Programming",
    question: "Which of these is a valid way to print a pointer address?",
    options: [
      "%x",
      "%d",
      "%p",
      "%a"
    ],
    correctAnswer: "%p"
  },
  {
    category: "Low Level Programming",
    question: "What is the purpose of the 'precision' specifier in printf?",
    options: [
      "Only affects floating point numbers",
      "Controls minimum number of digits for integers and maximum characters for strings",
      "Only affects string length",
      "Has no effect on output"
    ],
    correctAnswer: "Controls minimum number of digits for integers and maximum characters for strings"
  },
  {
    category: "Low Level Programming",
    question: "Which of these format specifiers requires a long int argument?",
    options: [
      "%d",
      "%ld",
      "%hd",
      "%bd"
    ],
    correctAnswer: "%ld"
  },
  {
    category: "Low Level Programming",
    question: "What is the correct way to handle variable arguments in a custom printf implementation?",
    options: [
      "Using arrays",
      "Using va_list, va_start, and va_end macros",
      "Using function pointers",
      "Using global variables"
    ],
    correctAnswer: "Using va_list, va_start, and va_end macros"
  }
]; 