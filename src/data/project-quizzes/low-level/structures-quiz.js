export const structures_quizQuiz = [
  {
    category: "structures_",
    question: "Given this code, to set the member y of my variable my_point to 98, I can do (select all valid answers):\n```c\nstruct point {\n   int x;\n   int y;\n};\nstruct point my_point = { 3, 7 };\nstruct point *p = \u0026my_point;\n```",
    options: [
      "(*p).y = 98;",
      "my_point.y = 98;",
      "(*p).y = 98;",
      "p-\u003ey = 98;",
      "\u0026my_point.y = 98;",
    ],
    correctAnswer: ["my_point.y = 98;", "p-\u003ey = 98;", "(*p).y = 98;"],
    isMulti: true
  },
  {
    category: "structures_",
    question: "You should write documentation for all the structures you create",
    options: [
      "True",
      "As soon as I write my structure.",
      "I’ll do it 5 minutes before the deadline when I try Betty on my header file",
    ],
    correctAnswer: ["True", "As soon as I write my structure."],
    isMulti: true
  },
  {
    category: "structures_",
    question: "Those two codes do the same thing:\n```c\ntypedef struct point point;\nstruct point {\n   int    x;\n   int    y;\n};\npoint p = {1, 2};\n```\n\n```c\ntypedef struct point point;\nstruct point {\n   int    x;\n   int    y;\n};\npoint p = { .y = 2, .x = 1 };\n```",
    options: [
      "True",
      "False: the members of the structures will not have the same values",
      "False: the second does not compile",
    ],
    correctAnswer: "True"
  },
  {
    category: "structures_",
    question: "The general syntax for a struct declaration in C is:\n```c\nstruct tag_name {\n   type member1;\n   type member2;\n   /* declare as many members as desired, but the entire structure size must be known to the compiler. */\n};\n```",
    options: [
      "True",
      "Maybe",
      "False",
    ],
    correctAnswer: "True"
  }
];
