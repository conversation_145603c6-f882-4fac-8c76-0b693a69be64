export const more_pointers_quizQuiz = [
  {
    category: "more_pointers_",
    question: "What is wrong with the following code?\n```c\nint n = 5;\nint array[5];\nint i = 3;\n\narray[n] = i;\n```",
    options: [
      "Nothing is wrong",
      "The array array is not entirely initialized",
      "It is impossible to declare the variable array this way",
      "It is not possible to access array[n]",
    ],
    correctAnswer: "It is not possible to access array[n]"
  },
  {
    category: "more_pointers_",
    question: "What is the type of `var`?\n```c\nvar = \"Best\";\n```",
    options: [
      "string",
      "int *",
      "char *",
    ],
    correctAnswer: "char *"
  },
  {
    category: "more_pointers_",
    question: "What is wrong with the following code?\n```c\nint n = 5;\nint array[n];\nint i = 3;\n\narray[n] = i;\n```",
    options: [
      "Nothing is wrong",
      "The array array is not entirely initialized",
      "It is impossible to declare the variable array this way",
      "It is not possible to access array[n]",
    ],
    correctAnswer: "It is impossible to declare the variable array this way"
  },
  {
    category: "more_pointers_",
    question: "What is wrong with the following code?\n```c\nint n = 5;\nint array[10];\nint i = 3;\n\narray[n] = i;\n```",
    options: [
      "Nothing is wrong",
      "The array array is not entirely initialized",
      "It is impossible to declare the variable array this way",
      "It is not possible to access array[n]",
    ],
    correctAnswer: "Nothing is wrong"
  },
  {
    category: "more_pointers_",
    question: "What is/are the difference(s) between the two following variables? (Except their names)\n```c\nchar *s1 = \"\";\nchar *s2 = NULL;\n```",
    options: [
      "The first one can be dereferenced, not the second one",
      "They are the same",
      "The first one points to 0, the second one points to a 0-byte",
      "The second one can be dereferenced, not the first one",
      "The first one points to a 0-byte, the second one points to 0"
    ],
    correctAnswer: [
      "The first one can be dereferenced, not the second one",
      "The first one points to a 0-byte, the second one points to 0"
    ],
    isMulti: true
  },
  {
    category: "more_pointers_",
    question: "Why is it important to reserve enough space for an extra character when declaring/allocating a string?",
    options: [
      "In case we need one",
      "For the null byte (end of string)",
      "For memory alignment",
      "For fun",
    ],
    correctAnswer: "For the null byte (end of string)"
  },
  {
    category: "more_pointers_",
    question: "What happens when one tries to dereference a pointer to NULL?",
    options: [
      "Nothing",
      "Kernel panic",
      "Segmentation fault",
      "World War Z",
    ],
    correctAnswer: "Segmentation fault"
  }
];
