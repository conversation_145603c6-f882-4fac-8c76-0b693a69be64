export const function_pointers_quizQuiz = [
  {
    category: "function_pointers_",
    question: "This `void (*anjula[])(int, float)` is:",
    options: [
      "An array of pointers to functions that take an `int` and a `float` as parameters and returns nothing",
      "A pointer to a function that takes an `int` and a `float` as parameters and returns nothing",
      "A pointer to a function that takes an `int` and a `float` as parameters and returns an empty array",
      "A pointer to an array of functions that take an `int` and a `float` as parameters and returns nothing",
      "A pointer to a function that takes an array of `int` and `float` as a parameter and returns nothing",
    ],
    correctAnswer: "An array of pointers to functions that take an `int` and a `float` as parameters and returns nothing"
  },
  {
    category: "function_pointers_",
    question: "Which one is a pointer to a function?",
    options: [
      "`int func(int a, float b);`",
      "`int (*func)(int a, float b);`",
      "`int *func(int a, float b);`",
      "`(int *)func(int a, float b);`",
    ],
    correctAnswer: "`int (*func)(int a, float b);`"
  },
  {
    category: "function_pointers_",
    question: "If `f` is a pointer to a function that takes no parameter and returns an int, you can call the function pointed by f this way (check all correct answers if there is more than one):",
    options: [
      "`f()`",
      "`(*f)()`",
      "`f`",
      "`&f()`"
    ],
    correctAnswer: [
      "`f()`",
      "`(*f)()`"
    ],
    isMulti: true
  },
  {
    category: "function_pointers_",
    question: "To store the address of this function:\n\n```c\nvoid neyo(void);\n```\n\nto the variable `f` of type pointer to a function that does not take any argument and does not return anything, you would do (check all correct answers if there is more than one):",
    options: [
      "`f = neyo;`",
      "`*f = neyo;`",
      "`f = &neyo;`",
      "`*f = &neyo;`"
    ],
    correctAnswer: [
      "`f = neyo;`",
      "`f = &neyo;`"
    ],
    isMulti: true
  },
  {
    category: "function_pointers_",
    question: "What does a pointer to a function point to (check all correct answers if there is more than one)?",
    options: [
      "data",
      "The first character of the name of the function",
      "code",
      "The first byte of code of the function"
    ],
    correctAnswer: [
    "code",
    "The first byte of code of the function"
    ],
    isMulti: true
  }
];
