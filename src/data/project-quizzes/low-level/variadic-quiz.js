export const variadicQuiz = [
  {
    category: "Low Level Programming",
    question: "What header file must be included to use variadic functions in C?",
    options: [
      "varargs.h",
      "stdarg.h",
      "stdio.h",
      "stdlib.h"
    ],
    correctAnswer: "stdarg.h"
  },
  {
    category: "Low Level Programming",
    question: "Which macros are used with variadic functions? (Select all that apply)",
    options: [
      "va_start",
      "va_arg",
      "va_end",
      "va_list"
    ],
    correctAnswer: ["va_start", "va_arg", "va_end", "va_list"],
    isMulti: true
  },
  {
    category: "Low Level Programming",
    question: "What is the purpose of va_end macro?",
    options: [
      "To initialize the argument list",
      "To access the next argument",
      "To clean up the memory used by va_start",
      "To count the number of arguments"
    ],
    correctAnswer: "To clean up the memory used by va_start"
  }
]; 