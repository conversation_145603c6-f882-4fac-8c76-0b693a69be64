export const functionsQuiz = [
  {
    category: "functions",
    question: "What is the ASCII value of `J`?",
    options: [
      "70",
      "74",
      "72",
      "76"
    ],
    correctAnswer: "74"
  },
  {
    category: "functions",
    question: "What is the result of `89 % 7`?",
    options: [
      "0",
      "3",
      "2",
      "5"
    ],
    correctAnswer: "5"
  },
  {
    category: "functions",
    question: "Which of these loop statements don't exist?",
    options: [
      "loop_to",
      "for",
      "foreach",
      "do... while",
      "each",
      "while"
    ],
    correctAnswer: [
      "loop_to",
      "foreach",
      "each"
    ],
    isMulti: true
  },
  {
    category: "functions",
    question: "What is the result of `12 % 3`?",
    options: [
      "0",
      "2",
      "1",
      "3"
    ],
    correctAnswer: "0"
  },
  {
    category: "functions",
    question: "What is the ASCII value of `a`?",
    options: [
      "97",
      "12",
      "65",
      "1"
    ],
    correctAnswer: "97"
  },
  {
    category: "functions",
    question: "What is the result of `12 % 10`?",
    options: [
      "0",
      "2",
      "1",
      "3"
    ],
    correctAnswer: "2"
  },
  {
    category: "functions",
    question: "What is the ASCII value of `A`?",
    options: [
      "97",
      "12",
      "65",
      "1"
    ],
    correctAnswer: "65"
  },
  {
    category: "functions",
    question: "What is the result of `12 % 2`?",
    options: [
      "0",
      "2",
      "1"
    ],
    correctAnswer: "0"
  },
  {
    category: "functions",
    question: "What is the ASCII value of `-`?",
    options: [
      "45",
      "47",
      "3"
    ],
    correctAnswer: "45"
  },
  {
    category: "functions",
    question: "What is the ASCII value of `0`?",
    options: [
      "79",
      "48",
      "0"
    ],
    correctAnswer: "48"
  },
  {
    category: "functions",
    question: "What is the ASCII value of `5`?",
    options: [
      "50",
      "5",
      "53"
    ],
    correctAnswer: "53"
  }
];
