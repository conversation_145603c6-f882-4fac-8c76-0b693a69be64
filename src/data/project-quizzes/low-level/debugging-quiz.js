// Rename from debugging.js to debugging-quiz.js for consistency
export const debuggingQuiz = [
  {
    category: "C - Debugging",
    question: "This code doesn't work as intended.\n\n```c\n#include \"main.h\"\n\n/**\n * main - prints even numbers from 0 to 100\n * Return: 0\n */\nint main(void)\n{\n    int i;\n\n    for (i = 0; i < 100; i++)\n    {\n        if (i % 2 != 0)\n        {\n            continue;\n        }\n        else\n        {\n            break;\n        }\n\n        printf(\"%d\\n\", i);\n    }\n\n    return(0);\n}\n```\n\nLet's add printf statements to the code. What information do the printf statements tell us about how our code is executed?\n\n```c\n#include \"main.h\"\n\n/**\n * main - prints even numbers from 0 to 100\n * Return: 0\n */\nint main(void)\n{\n    int i;\n\n    printf(\"Before loop\\n\");\n\n    for (i = 0; i < 100; i++)\n    {\n        if (i % 2 != 0)\n        {\n            printf(\"i is not even so don't print\\n\");\n            continue;\n        }\n        else\n        {\n            printf(\"i is even, break to print\\n\");\n            break;\n        }\n\n        printf(\"Outside of if/else, still inside for loop\\n\");\n\n        printf(\"%d\\n\", i);\n    }\n\n    printf(\"For loop exited\\n\");\n\n    return(0);\n}\n```",
    options: [
      "A `printf` statement shows when the `for` loop is finished",
      "`printf` statements shows that `break` will cause \"For loop exited\" to print, indicating that the even number is never printed",
      "A `printf` statement shows exactly how many times the loop executes",
      "A `printf` statement shows that there is an infinite loop in the code"
    ],
    correctAnswer: [
      "A `printf` statement shows when the `for` loop is finished",
      "`printf` statements shows that `break` will cause \"For loop exited\" to print, indicating that the even number is never printed"
    ],
    isMulti: true
  },
  {
    category: "C - Debugging",
    question: "Look at the following code.\n\n```bash\ncarrie@ubuntu:/debugging$ cat main.c\n#include <stdio.h>\n\n/**\n * main - debugging example\n * Return: 0\n */\nint main(void)\n{\n    char *hello = \"Hello, World!\";\n\n    for (i = 0; hello[i] != '\\0'; i++)\n    {\n        printf(\"%c\", hello[i]);\n    }\n\n    printf(\"\\n\");\n\n    return (0);\n}\ncarrie@ubuntu:/debugging$\n```\n\n```bash\ncarrie@ubuntu:/debugging$ gcc -Wall -Werror -Wextra -pedantic main.c\nmain.c: In function 'main':\nmain.c:11:7: error: 'i' undeclared (first use in this function)\n  for (i = 0; hello[i] != '\\0'; i++)\n\nmain.c:11:7: note: each undeclared identifier is reported only once for each function it appears in\nmain.c:19:8: error: variable 'hello' set but not used [-Werror=unused-but-set-variable]\n    char *hello = \"Hello, World!\";\n         ^\ncc1: all warnings being treated as errors\ncarrie@ubuntu:/debugging$\n```\n\nIn the main.c file, on what line is the first error that the compiler returns?",
    options: [
      "9",
      "7",
      "11"
    ],
    correctAnswer: "11"
  },
  {
    category: "C - Debugging",
    question: "The following code gives this output.\n\n```bash\ncarrie@ubuntu:/debugging$ cat main.c\n#include <stdio.h>\n\n/**\n * main - debugging example\n * Return: 0\n */\nint main(void)\n{\n    int i;\n    int j;\n    int k;\n\n    i = 0;\n    j = 1000;\n    while (i < j)\n    {\n        k = j / 98;\n        i = i + k;\n        printf(\"%d\\n\", i);\n        j == j - 1;\n    }\n\n    return (0);\n}\ncarrie@ubuntu:/debugging$\n```\n\n```bash\ncarrie@ubuntu:/debugging$ gcc -Wall -Werror -Wextra -pedantic main.c\nmain.c: In function 'main':\nmain.c:20:3: error: statement with no effect [-Werror=unused-value]\n   j == j - 1;\n\ncc1: all warnings being treated as errors\ncarrie@ubuntu:/debugging$\n```\n\nWhat is the error?",
    options: [
      "We don't need to assign a new value to `j` because it doesn't do anything",
      "We want to compare `j` so we need an `if` statement before `j == j - 1`",
      "We want to assign `j` a new value, not compare it, so it should be `j = j - 1` instead of `j == j - 1`"
    ],
    correctAnswer: "We want to assign `j` a new value, not compare it, so it should be `j = j - 1` instead of `j == j - 1`"
  },
  {
    category: "C - Debugging",
    question: "The following code gives this incorrect output.\n\n```bash\ncarrie@ubuntu:/debugging$ cat main.c\n#include <stdio.h>\n\n/**\n * main - debugging example\n * Return: 0\n */\nint main(void)\n{\n    int i;\n    int j;\n\n    for (i = 0; i < 10; i++)\n    {\n        j = 0;\n        while (j < 10)\n        {\n            printf(\"%d\", j);\n        }\n        printf(\"\\n\");\n    }\n\n    return (0);\n}\ncarrie@ubuntu:/debugging$\n```\n\n```bash\ncarrie@ubuntu:/debugging$ gcc -Wall -Werror -Wextra -pedantic main.c\ncarrie@ubuntu:/debugging$ ./a.out\n00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000\n^C\ncarrie@ubuntu:/debugging$\n```\n\nWhich of the following statements about what is causing the error is true?",
    options: [
      "`j` never increments so it will always be less than `10`",
      "`j` never increments so it is always going to print `0`",
      "`j` is always equal to `i` so the loop will never end"
    ],
    correctAnswer: [
      "`j` never increments so it will always be less than `10`",
      "`j` never increments so it is always going to print `0`",
    ],
    isMulti: true
  }
];