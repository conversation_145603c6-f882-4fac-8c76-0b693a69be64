export const gitAdvancedQuiz = [
  {
    category: "Git",
    question: "What's the command to stage a specific file for commit?",
    options: [
      "git push",
      "git add .",
      "git add filename"
    ],
    correctAnswer: "git add filename"
  },
  {
    category: "Git",
    question: "What command shows repository status?",
    options: [
      "git init",
      "git checkout",
      "git status"
    ],
    correctAnswer: "git status"
  },
  {
    category: "Git",
    question: "How do you create a new branch and switch to it? (Select all that apply)",
    options: [
      "git checkout -b branch-name",
      "git branch branch-name && git checkout branch-name",
      "git new branch-name",
      "git switch -c branch-name"
    ],
    correctAnswer: [
      "git checkout -b branch-name",
      "git branch branch-name && git checkout branch-name",
      "git switch -c branch-name"
    ],
    isMulti: true
  },
  {
    category: "Git",
    question: "Which commands are used to undo local changes in Git? (Select all that apply)",
    options: [
      "git reset",
      "git revert",
      "git restore",
      "git remove"
    ],
    correctAnswer: [
      "git reset",
      "git revert",
      "git restore"
    ],
    isMulti: true
  }
];