export const emacsAdvancedQuiz = [
  {
    category: "Emacs Advanced",
    question: "In Emacs, what happens when you have multiple buffers open with the same file?",
    options: [
      "Only one buffer can be opened, others are automatically closed",
      "All buffers stay open but only the last one can modify the file",
      "All buffers stay open and any can modify the file, but changes in one buffer don't automatically reflect in others",
      "Emacs prevents opening the same file in multiple buffers"
    ],
    correctAnswer: "All buffers stay open and any can modify the file, but changes in one buffer don't automatically reflect in others"
  },
  {
    category: "Emacs Advanced",
    question: "When using keyboard shortcuts in Emacs, what's the difference between 'C-' and 'M-C-'?",
    options: [
      "No difference, they are interchangeable",
      "'C-' uses Ctrl key alone while 'M-C-' uses both Meta (Alt) and Ctrl keys together",
      "'C-' is for command mode while 'M-C-' is for meta mode",
      "'M-C-' is just another way to write 'C-'"
    ],
    correctAnswer: "'C-' uses Ctrl key alone while 'M-C-' uses both Meta (Alt) and Ctrl keys together"
  },
  {
    category: "Emacs Advanced",
    question: "In Emacs, what's the relationship between buffers and windows?",
    options: [
      "One buffer can only be displayed in one window",
      "Buffers and windows are the same thing",
      "The same buffer can be displayed in multiple windows simultaneously",
      "Windows can only show one buffer at a time"
    ],
    correctAnswer: "The same buffer can be displayed in multiple windows simultaneously"
  },
  {
    category: "Emacs Advanced",
    question: "Which keys can be used as the Meta key in Emacs? (Select all that apply)",
    options: [
      "Alt key",
      "Esc key (pressed and released before the next key)",
      "Windows/Command key",
      "Shift + M"
    ],
    correctAnswer: [
      "Alt key",
      "Esc key (pressed and released before the next key)"
    ],
    isMulti: true
  }
];
