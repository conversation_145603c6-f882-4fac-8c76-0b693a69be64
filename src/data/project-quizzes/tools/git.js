export const gitQuiz = [
  {
    category: "git",
    question: "You have the following files in your project directory:\n\n```\njulien@ubuntu:/tmp/git_project$ ls\n0-test  0-test~ #0-test# file1  file2\n```\n\nYou have edited `0-test` and you want to add it to your GitHub repo. What is the correct command to add **only** `0-test`?",
    options: [
      "git add .",
      "git add 0-test",
      "git add -N 0-test",
    ],
    correctAnswer: "git add 0-test",
    tips: "#### Tips:\n\nYou should learn what each of these commands would actually do if you were to execute them!"
  },
  {
    category: "git",
    question: "What command can you use to see what changes have been staged, which haven’t, and which files aren’t being tracked by Git?",
    options: [
      "git init",
      "git checkout",
      "git status",
    ],
    correctAnswer: "git status"
  }
];
