export const emacsQuiz = [
	{
		category: "emacs",
		question: "You can only have one buffer open in Emacs at a time.",
		options: ["True", "False"],
		correctAnswer: "False",
	},
	{
		category: "emacs",
		question:
			"In Emacs’ documentation, what does `C` in a shortcut command stand for?",
		options: ["Command", "Shift + C", "Ctrl"],
		correctAnswer: "Ctrl",
	},
	{
		category: "emacs",
		question: "In Emacs, a buffer is an object that a file’s text is held in.",
		options: ["True", "False"],
		correctAnswer: "True",
	},
	{
		category: "emacs",
		question:
			"In Emacs’ documentation, what does `M`  in a shortcut command stand for?",
		options: ["Alt", "Meta", "Shift + M"],
		correctAnswer: ["Alt", "Meta"],
		isMulti: true,
	},
];
