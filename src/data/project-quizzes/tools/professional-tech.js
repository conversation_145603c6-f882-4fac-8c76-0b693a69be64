export const techQuiz = [
  {
    category: "tech",
    question: "You get an email and an invitation to a mandatory meeting with a staff member. What should you do?",
    options: [
      "Delete the email",
      "<PERSON><PERSON> affirmatively or contact the staff member to reschedule the meeting.",
      "Ignore the invitation",
    ],
    correctAnswer: "<PERSON><PERSON> affirmatively or contact the staff member to reschedule the meeting."
  },
  {
    category: "tech",
    question: "In Slack,  should never be used.",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "True"
  },
  {
    category: "tech",
    question: "Which Slack channel could you use to ask a question about a project?",
    options: [
      "#general",
      "Local cohort channel",
      "#random",
    ],
    correctAnswer: "Local cohort channel"
  },
  {
    category: "tech",
    question: "When answering someone’s question in Slack channel, I should use a ____________ to answer them - without alerting all members of the channel and offering your answer to all members too.",
    options: [
      "message on the channel",
      "private message",
      "thread",
    ],
    correctAnswer: "thread"
  },
  {
    category: "tech",
    question: "You open and close the same resource multiple times while completing various projects. What should you do to streamline your workflow?",
    options: [
      "Bookmark the resource that you constantly used",
      "Never close the tab",
      "Save the resource as your homepage when you open your browser.",
    ],
    correctAnswer: "Bookmark the resource that you constantly used"
  },
  {
    category: "tech",
    question: "Google Chrome is the only browser that allows access to ALX’s Intranet.",
    options: [
      "True",
      "False",
    ],
    correctAnswer: "False"
  }
];
