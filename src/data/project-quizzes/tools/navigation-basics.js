export const navigationQuiz = [
  {
    category: "Shell Navigation",
    question: "Which command should you use to list all files of your current directory?",
    options: [
      "touch",
      "less",
      "cd",
      "ls",
      "pwd"
    ],
    correctAnswer: "ls"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to display the current path of your current directory?",
    options: [
      "less",
      "touch",
      "ls",
      "cd",
      "pwd"
    ],
    correctAnswer: "pwd"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to display the content of a file?",
    options: [
      "touch",
      "ls",
      "cd",
      "less",
      "pwd"
    ],
    correctAnswer: "less"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to rename or move a file (or directory)?",
    options: [
      "rmdir",
      "mkdir",
      "rm",
      "mv",
      "cp"
    ],
    correctAnswer: "mv"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to delete a directory?",
    options: [
      "rmdir",
      "mkdir",
      "rm",
      "mv",
      "cp"
    ],
    correctAnswer: "rmdir"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to copy a file (or directory if additional argument)?",
    options: [
      "rmdir",
      "cp",
      "mv",
      "rm",
      "mkdir"
    ],
    correctAnswer: "cp"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to change directory?",
    options: [
      "cd",
      "less",
      "pwd",
      "ls",
      "touch"
    ],
    correctAnswer: "cd"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to delete a file (or directory if additional argument)?",
    options: [
      "cp",
      "mkdir",
      "rm",
      "mv",
      "rmdir"
    ],
    correctAnswer: "rm"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to create an empty file?",
    options: [
      "ls",
      "less",
      "cd",
      "touch",
      "pwd"
    ],
    correctAnswer: "touch"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to create a directory?",
    options: [
      "mkdir",
      "cp",
      "mv",
      "rm",
      "rmdir"
    ],
    correctAnswer: "mkdir"
  }
];
