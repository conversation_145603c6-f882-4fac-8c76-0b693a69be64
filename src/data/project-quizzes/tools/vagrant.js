export const vagrantQuiz = [
  {
    category: "Vagrant",
    question: "Which is the correct way of accessing a Linux environment for coding? Select all correct answers",
    options: [
      "Use a Linux chroot",
      "Use a Linux container", 
      "Use a Linux emulator",
      "Install Linux on your computer",
      "Use a Linux debugger",
      "Use a virtual machine to run Linux",
      "Use a cloud-based Linux environment",
      "Use a Linux compiler"
    ],
    correctAnswer: [
      "Use a Linux chroot",
      "Use a Linux container",
      "Install Linux on your computer", 
      "Use a virtual machine to run Linux",
      "Use a cloud-based Linux environment"
    ],
    isMulti: true
  },
  {
    category: "Vagrant",
    question: "What is a virtual machine?",
    options: [
      "A set of servers for software development",
      "An emulation of a computer system",
      "A system for developing virtual reality"
    ],
    correctAnswer: "An emulation of a computer system"
  },
  {
    category: "Vagrant",
    question: "What is the difference between a Docker image and a Docker container?",
    options: [
      "A Docker image is immutable, meaning that it cannot be changed once it is created.",
      "A Docker image is a blueprint or template for creating a Docker container.",
      "Docker image is like a blueprint, while a Docker container is like a house built from the blueprint.",
      "A Docker container is a running instance of a Docker image.",
      "A Docker image is a snapshot of a Docker container in a specific state.",
      "A Docker image is a lightweight, standalone, executable package of software that includes everything needed to run an application",
      "A Docker image is a running instance of a Docker container.",
      "A Docker container is a runtime instance of a Docker image."
    ],
    correctAnswer: [
      "A Docker image is immutable, meaning that it cannot be changed once it is created.",
      "A Docker image is a blueprint or template for creating a Docker container.",
      "Docker image is like a blueprint, while a Docker container is like a house built from the blueprint.",
      "A Docker container is a running instance of a Docker image.",
      "A Docker image is a lightweight, standalone, executable package of software that includes everything needed to run an application",
      "A Docker container is a runtime instance of a Docker image."
    ],
    isMulti: true
  },
  {
    category: "Vagrant",
    question: "What is the correct definition of WSL? Select all correct answers",
    options: [
      "Windows Subsystem for Linux",
      "A way to run Linux distributions on Windows without the need for a virtual machine",
      "A compatibility layer for running Linux binary executables natively on Windows 10",
      "A web service that allows users to create and share online surveys"
    ],
    correctAnswer: [
      "Windows Subsystem for Linux",
      "A way to run Linux distributions on Windows without the need for a virtual machine",
      "A compatibility layer for running Linux binary executables natively on Windows 10"
    ],
    isMulti: true
  },
  {
    category: "Vagrant",
    question: "[Fun question] Are you aware that all ALX products (the Intranet, the Checker, Sandboxes, Kimba,,,) are built and developed under several docker containers?",
    options: [
      "No I didn't, this is interesting!",
      "Yes"
    ],
    correctAnswer: ["No I didn't, this is interesting!", "Yes"],
    isMulti: true
  },
  {
    category: "Vagrant",
    question: "Ubuntu is a ___ distribution.",
    options: [
      "Windows",
      "MacOS",
      "Linux"
    ],
    correctAnswer: "Linux"
  },
  {
    category: "Vagrant",
    question: "Where can you get help with WSL? Select all correct answers",
    options: [
      "The WSL GitHub repository",
      "The Microsoft Store",
      "The Microsoft Docs",
      "The WSL Stack Overflow tag",
      "The Windows Help app"
    ],
    correctAnswer: [
      "The WSL GitHub repository",
      "The Microsoft Docs",
      "The WSL Stack Overflow tag"
    ],
    isMulti: true
  },
  {
    category: "Vagrant",
    question: "How do you launch a WSL distribution?",
    options: [
      "wsl --distribution <distribution name>",
      "start wsl <distribution name>",
      "wsl run <distribution name>"
    ],
    correctAnswer: "wsl --distribution <distribution name>"
  },
  {
    category: "Vagrant",
    question: "Select all correct definitions of Docker",
    options: [
      "A containerization platform",
      "A tool that packages an application and all its dependencies together into a standardized unit for software development",
      "A software platform that uses OS-level virtualization to deliver software in packages called containers",
      "A software development kit (SDK) for building and running distributed applications"
    ],
    correctAnswer: [
      "A containerization platform",
      "A tool that packages an application and all its dependencies together into a standardized unit for software development",
      "A software platform that uses OS-level virtualization to deliver software in packages called containers"
    ],
    isMulti: true
  },
  {
    category: "Vagrant",
    question: "What is the difference between a WSL distribution and a Linux distribution? Select all correct answers?",
    options: [
      "A WSL distribution is a special type of Linux distribution that is designed to run on Windows.",
      "A WSL distribution is a regular Linux distribution that is packaged to run on Windows using the Windows Subsystem for Linux (WSL).",
      "A WSL distribution is a lightweight version of a Linux distribution that is designed to be used on resource-constrained devices."
    ],
    correctAnswer: [
      "A WSL distribution is a special type of Linux distribution that is designed to run on Windows.",
      "A WSL distribution is a regular Linux distribution that is packaged to run on Windows using the Windows Subsystem for Linux (WSL)."
    ],
    isMulti: true
  }
];
