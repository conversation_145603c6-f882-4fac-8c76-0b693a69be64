export const shellNavigationQuiz = [
  {
    category: "Shell Navigation",
    question: "Which command should you use to display the content of a file?",
    options: [
      "ls",
      "pwd",
      "cd",
      "touch",
      "less"
    ],
    correctAnswer: "less"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to display the current path of your current directory?",
    options: [
      "touch",
      "pwd",
      "ls",
      "less",
      "cd"
    ],
    correctAnswer: "pwd"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to create a directory?",
    options: [
      "cp",
      "rmdir",
      "mv",
      "mkdir",
      "rm"
    ],
    correctAnswer: "mkdir"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to change directory?",
    options: [
      "less",
      "touch",
      "pwd",
      "cd",
      "ls"
    ],
    correctAnswer: "cd"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to delete a directory?",
    options: [
      "mkdir",
      "cp",
      "rm",
      "rmdir",
      "mv"
    ],
    correctAnswer: "rmdir"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to list all files of your current directory?",
    options: [
      "less",
      "pwd",
      "cd",
      "touch",
      "ls"
    ],
    correctAnswer: "ls"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to delete a file (or directory if additional argument)?",
    options: [
      "mkdir",
      "rmdir",
      "rm",
      "cp",
      "mv"
    ],
    correctAnswer: "rm"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to copy a file (or directory if additional argument)?",
    options: [
      "cp",
      "mkdir",
      "mv",
      "rmdir",
      "rm"
    ],
    correctAnswer: "cp"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to create an empty file?",
    options: [
      "less",
      "pwd",
      "cd",
      "ls",
      "touch"
    ],
    correctAnswer: "touch"
  },
  {
    category: "Shell Navigation",
    question: "Which command should you use to rename or move a file (or directory)?",
    options: [
      "mkdir",
      "cp",
      "rm",
      "rmdir",
      "mv"
    ],
    correctAnswer: "mv"
  }
];