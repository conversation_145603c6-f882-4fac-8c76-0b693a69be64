export const viAdvancedQuiz = [
  {
    category: "Vi Advanced",
    question: "What is the correct way to quit vi?",
    options: [
      ":q<Return>",
      "q",
      "<ESC>"
    ],
    correctAnswer: ":q<Return>"
  },
  {
    category: "Vi Advanced",
    question: "Is vi case sensitive by default?",
    options: [
      "True",
      "False"
    ],
    correctAnswer: "True"
  },
  {
    category: "Vi Advanced",
    question: "Which key allows you to start inserting text?",
    options: [
      "<Insert>",
      "i",
      "<Return>"
    ],
    correctAnswer: "i"
  },
  {
    category: "Vi Advanced",
    question: "How do you return to command mode from insert mode?",
    options: [
      "Ctrl / Command + C",
      "<Return>",
      "<ESC>"
    ],
    correctAnswer: "<ESC>"
  }
];
