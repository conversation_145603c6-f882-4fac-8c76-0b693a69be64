export const sorting_quizQuiz = [
  {
    category: "sorting_",
    question: "What is the time complexity accessing the nth element in an unsorted Python 3 list?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(log(n))"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of accessing the nth element of a doubly linked list?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "Assuming you have a pointer to the node to set the value of, what is the time complexity of setting the value of the nth element in a doubly linked list?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the best case time complexity of insertion in a hash table with the implementation you used during the previous Hash Table C project (chaining)?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n^2)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of inserting at index n on an unsorted array?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of removing at index n in an unsorted array?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "Assuming you have a pointer to the node to remove, what is the time complexity of removing the nth element of a doubly linked list?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of searching for an element in a singly linked list of size n?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of searching for an element in a stack of size n?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(2^n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of searching for an element in a queue of size n if you are given a pointer to both the head and the tail of the queue?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n^2)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of searching for an element in an unsorted array of size n?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "Assuming you have a pointer to the node to insert, what is the time complexity of inserting after the nth element of a doubly linked list?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of removing at index n from an unsorted Python 3 list?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of “pushing” an element into a queue if you are given a pointer to both the head and the tail of the queue?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of accessing the nth element on an unsorted array?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of “popping” an element in a queue if you are given a pointer to both the head and the tail of the queue?",
    options: [
      "O(nlog(n))",
      "O(1)",
      "O(n!)",
      "O(log(n))",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of setting the value of the nth element in a singly linked list? (Assuming you have a pointer to the node to set the value of)",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of searching for an element - worst case - in a hash table with the implementation you used during the previous Hash Table C project (chaining)?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of searching for an element in an unsorted Python 3 list of size n?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of setting a value at index n in an unsorted array?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n^2)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of worst case deletion from a hash table with the implementation you used during the previous Hash Table C project (chaining)?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of inserting after the nth element of a singly linked list? (Assuming you have a pointer to the node to insert)",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of setting value at index n in an unsorted Python 3 list?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of searching for an element in a doubly linked list of size n?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the worst case time complexity of insertion in a hash table with the implementation you used during the previous Hash Table C project (chaining)?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of best case deletion from a hash table with the implementation you used during the previous Hash Table C project (chaining)?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of the “push” operation onto a stack?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of removing the nth element of a singly linked list? (Assuming you have a pointer to the node to remove)",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the best case time complexity searching for an element in a hash table with the implementation you used during the previous Hash Table C project (chaining)?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of inserting into an unsorted Python 3 list at index n?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of accessing the nth element of a singly linked list?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of the “pop” operation onto a stack?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(1)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(n)"
  },
  {
    category: "sorting_",
    question: "What is the time complexity of this function / algorithm?",
    options: [
      "O(log(n))",
      "O(n^2)",
      "O(1)",
      "O(nlog(n))",
      "O(n!)",
      "O(2^n)",
      "O(n)",
    ],
    correctAnswer: "O(nlog(n))"
  }
];
