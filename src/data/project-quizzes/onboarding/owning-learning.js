export const owningLearningQuiz = [
  {
    category: "Owning Learning",
    question: "which of the following is part of the recommended learning framework within the ALX program. (Select all that apply)",
    options: [
      "Read the project prerequisites, man pages, documentation, books, and articles. Try all the examples in the docs",
      "Write or draw out your problem on a whiteboard or paper. It's easier to visualize your problem when it isn't pure code.",
      "Pause, take a breath, and think of the solution. Make sure you understand all of the required concepts and algorithms before you proceed to solve tasks.",
      "Whether or not your code runs when you input it, make sure you know why it doesn't (or does) work without looking at the error message."
    ],
    correctAnswer: [
      "Read the project prerequisites, man pages, documentation, books, and articles. Try all the examples in the docs",
      "Write or draw out your problem on a whiteboard or paper. It's easier to visualize your problem when it isn't pure code.",
      "Pause, take a breath, and think of the solution. Make sure you understand all of the required concepts and algorithms before you proceed to solve tasks.",
      "Whether or not your code runs when you input it, make sure you know why it doesn't (or does) work without looking at the error message."
    ],
    isMulti: true
  },
  {
    category: "Owning Learning",
    question: "How do you ask good technical questions?",
    options: [
      "Share what you've already tried",
      "Sum up your question in a title",
      "Provide code sample",
      "Provide screenshots",
      "Format, Lint, and Document Your Code",
      "Provide context"
    ],
    correctAnswer: [
      "Share what you've already tried",
      "Sum up your question in a title",
      "Provide code sample",
      "Format, Lint, and Document Your Code",
      "Provide context"
    ],
    isMulti: true,
    tips: "#### Tips:\n\nRead the concept page [Technical Questioning](/concepts/technical_questioning)"
  },
  {
    category: "Owning Learning",
    question: "Why is it important to really understand what you're learning? (Select all that apply)",
    options: [
      "Because learning doesn't happen from skimming through a book or remembering enough to pass a test",
      "Ultimately, the point of learning is to understand the world",
      "Information is learned when you can explain it and use it in a wide variety of situations."
    ],
    correctAnswer: [
      "Because learning doesn't happen from skimming through a book or remembering enough to pass a test",
      "Ultimately, the point of learning is to understand the world",
      "Information is learned when you can explain it and use it in a wide variety of situations."
    ],
    isMulti: true
  },
  {
    category: "Owning Learning",
    question: "Which statement best describes the relationship between focused and diffuse thinking?",
    options: [
      "Focused thinking, your brain processes very specific information deeply",
      "Both are required in order to master a topic or make progress on a difficult project. When you are learning something new, you need to understand both the context for the information (diffuse) and the specifics of the subject (focused).",
      "Diffuse thinking, the brain analyses much more information at once but in less depth"
    ],
    correctAnswer: "Both are required in order to master a topic or make progress on a difficult project. When you are learning something new, you need to understand both the context for the information (diffuse) and the specifics of the subject (focused)."
  },
  {
    category: "Owning Learning",
    question: "Which statements are true about knowledge and understanding? (Select all that apply)",
    options: [
      "The person who says he knows what he thinks but cannot express it usually does not know what he thinks",
      "When you know something, the labels are unimportant, because it's not necessary to keep it in the box it came in.",
      "When you only know what something is called, you have no real sense of what it is."
    ],
    correctAnswer: [
      "The person who says he knows what he thinks but cannot express it usually does not know what he thinks",
      "When you know something, the labels are unimportant, because it's not necessary to keep it in the box it came in.",
      "When you only know what something is called, you have no real sense of what it is."
    ],
    isMulti: true
  },
  {
    category: "Owning Learning",
    question: "What are the benefits of asking questions? (Select all that apply)",
    options: [
      "You learn how to ask questions",
      "You learn by answering questions",
      "You will gain a better understanding of the problem"
    ],
    correctAnswer: [
      "You learn how to ask questions",
      "You learn by answering questions",
      "You will gain a better understanding of the problem"
    ],
    isMulti: true
  },
  {
    category: "Owning Learning",
    question: "When using the Feynman Technique, which of the following should you NOT do?",
    options: [
      "Pretend to teach a concept you want to learn about to a student in the sixth grade.",
      "Organize and simplify.",
      "Identify gaps in your explanation. Go back to the source material to better understand it.",
      "make things bigger, more complex"
    ],
    correctAnswer: "make things bigger, more complex"
  },
  {
    category: "Owning Learning",
    question: "Which statement about focused and diffused thinking is correct?",
    options: [
      "focussed thinking is like seeing a brick wall and saying it's just too difficult to run through or climb through while diffused thinking is like your mind saying \"Hey why don't we tie balloons and just float over it\"",
      "focussed thinking is like your mind saying \"Hey why don't we tie balloons and just float over it\" while diffused thinking is like seeing a brick wall and saying it's too difficult to run through or climb through."
    ],
    correctAnswer: "focussed thinking is like seeing a brick wall and saying it's just too difficult to run through or climb through while diffused thinking is like your mind saying \"Hey why don't we tie balloons and just float over it\""
  },
  {
    category: "Owning Learning",
    question: "What should you remember when asking for help? (Select all that apply)",
    options: [
      "it's your job to ask and it's your job to help",
      "Explain what the problem is and what you've tried",
      "Respect other people's time"
    ],
    correctAnswer: [
      "it's your job to ask and it's your job to help",
      "Explain what the problem is and what you've tried",
      "Respect other people's time"
    ],
    isMulti: true
  },
  {
    category: "Owning Learning",
    question: "Which practices should you follow when researching solutions? (Select all that apply)",
    options: [
      "Trust everything on the Intranet",
      "Never copy and paste something from the Internet. You need to understand everything before you implement something you read. Typing yourself - instead of cp/paste - ensures that you better understand and not miss any details.",
      "Only google something specific"
    ],
    correctAnswer: [
      "Never copy and paste something from the Internet. You need to understand everything before you implement something you read. Typing yourself - instead of cp/paste - ensures that you better understand and not miss any details.",
      "Only google something specific"
    ],
    isMulti: true
  },
  {
    category: "Owning Learning",
    question: "What are important principles when helping others learn? (Select all that apply)",
    options: [
      "Respect every question; there is nothing more demotivating than having someone laughing at your question or being asked \"what? you still don't know that?\"",
      "There are no stupid questions",
      "Do NOT give the final answer. You are here to guide, help them find the solution by themselves."
    ],
    correctAnswer: [
      "Respect every question; there is nothing more demotivating than having someone laughing at your question or being asked \"what? you still don't know that?\"",
      "There are no stupid questions",
      "Do NOT give the final answer. You are here to guide, help them find the solution by themselves."
    ],
    isMulti: true
  }
];