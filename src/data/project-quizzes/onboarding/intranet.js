export const intranetQuiz = [
  {
    category: "intranet",
    question: "What are the steps of the Feynman Learning Technique? (Select all that apply)",
    options: [
      "Step 3: Reflect, Refine, and Simplify",
      "Step 1: Choose a concept you want to learn about.",
      "Step 2: Close your eyes and think",
      "Step 2: Explain it to a 12-year-old",
      "Step 4: Organize and Review",
      "Step 1: Open a random page and start reading/watching",
    ],
    correctAnswer: [
      "Step 3: Reflect, Refine, and Simplify",
      "Step 1: Choose a concept you want to learn about.",
      "Step 2: Explain it to a 12-year-old",
      "Step 4: Organize and Review",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "How can I interact with the sandbox? (Select all that apply)",
    options: [
      "SMTP",
      "Webterminal",
      "SFTP",
      "IMAP",
      "SSH",
    ],
    correctAnswer: [
      "Webterminal",
      "SFTP",
      "SSH",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "When should I use the checker",
    options: [
      "I am dependent on the checker - this is the only way to check my code",
      "I will develop and test my code myself - the checker is there to confirm the checks I have done myself.",
    ],
    correctAnswer: "I will develop and test my code myself - the checker is there to confirm the checks I have done myself."
  },
  {
    category: "intranet",
    question: "When will the checker details be available to me? (Select all that apply)",
    options: [
      "Only in the first few projects",
      "Never",
      "Always",
      "After the deadline",
    ],
    correctAnswer: [
      "Only in the first few projects",
      "After the deadline",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "Where can I find a summary and details of my scores?",
    options: [
      "Dashboard, click on the eye icon",
      "Concepts page",
      "My Profile",
      "Slack",
    ],
    correctAnswer: "Dashboard, click on the eye icon"
  },
  {
    category: "intranet",
    question: "What is the preferred development environment?",
    options: [
      "Mac Os Big Sur",
      "Windows 10",
      "Red Hat Enterprise 8.4",
      "Chrome OS",
      "Ubuntu 20.04",
    ],
    correctAnswer: "Ubuntu 20.04"
  },
  {
    category: "intranet",
    question: "What is the purpose of the Captains Log? (Select all that apply)",
    options: [
      "To increase your network",
      "To let the team know how you are doing",
      "To apply for a job",
      "To reflect on your learning journey",
      "To flag issues if any",
    ],
    correctAnswer: [
      "To let the team know how you are doing",
      "To reflect on your learning journey",
      "To flag issues if any",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "What happens when I close the web terminal window/tab",
    options: [
      "I will be notified that I still have running processes",
      "The process will stop running",
      "The process will remain running in the background",
      "Nothing",
    ],
    correctAnswer: "The process will remain running in the background"
  },
  {
    category: "intranet",
    question: "In case of a project with 4 tasks, 3 of which are mandatory and each 10 points. and one is optional (advanced) for 10 points.",
    options: [
      "90,25%",
      "132,5%",
      "136,66%",
      "120%",
    ],
    correctAnswer: "132,5%"
  },
  {
    category: "intranet",
    question: "What is the purpose of the checker? (Select all that apply)",
    options: [
      "It is AI that will help you debug your code",
      "To check your code",
      "To score your project",
      "To extend the deadline",
    ],
    correctAnswer: [
      "To check your code",
      "To score your project",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "What is the duration of the foundations?",
    options: [
      "6 months",
      "3 months",
      "9 months",
      "12 months",
    ],
    correctAnswer: "9 months"
  },
  {
    category: "intranet",
    question: "In the intro video we import the time module and use sleep, i.e. . In a general sense, why is it important to get enough sleep and maintain good sleep hygiene? (Select all that apply)",
    options: [
      "When you sleep, new connections are made in your brain that were not there during the day and it makes you understand better",
      "Poor sleep has been shown to impair brain function.",
      "Good sleep can maximize problem-solving skills and enhance memory.",
      "Poor sleep is linked to depression, not being able to cope with all that is coming towards us.",
    ],
    correctAnswer: [
      "When you sleep, new connections are made in your brain that were not there during the day and it makes you understand better",
      "Poor sleep has been shown to impair brain function.",
      "Good sleep can maximize problem-solving skills and enhance memory.",
      "Poor sleep is linked to depression, not being able to cope with all that is coming towards us.",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "What should you do when you have time left in the PLD (Select all that apply)",
    options: [
      "Code challenges",
      "Internet projects",
      "Leave the session",
      "Look into previous concepts",
    ],
    correctAnswer: [
      "Code challenges",
      "Internet projects",
      "Look into previous concepts",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "What are the elements of a PLD? (Select all that apply)",
    options: [
      "Clarify Action Items",
      "Pair/Group Program",
      "Whiteboarding",
      "Share group progress",
    ],
    correctAnswer: [
      "Clarify Action Items",
      "Pair/Group Program",
      "Whiteboarding",
      "Share group progress",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "What is the aim of a PLD session? (Select all that apply)",
    options: [
      "To ensure each and every one of our peers understands content",
      "To provide your peers with answers and share solutions",
      "To share your thought processes",
      "To ensure everyone is collectively growing in technical, soft and professional skills",
      "To empower peers with the ability to solve problems",
    ],
    correctAnswer: [
      "To ensure each and every one of our peers understands content",
      "To share your thought processes",
      "To ensure everyone is collectively growing in technical, soft and professional skills",
      "To empower peers with the ability to solve problems",
    ],
    isMulti: true
  },
  {
    category: "intranet",
    question: "I can learn best by… (Select all that apply)",
    options: [
      "Doing",
      "Explaining it to another person",
      "Skipping the PLD",
      "Copy-pasting another person's code",
      "Having another person explain it to me",
    ],
    correctAnswer: [
      "Doing",
      "Explaining it to another person",
      "Having another person explain it to me",
    ],
    isMulti: true
  }
];
