export const learningCommunityQuiz = [
  {
    category: "Learningcommunity",
    question: "Using derogatory language about another person’s religion in a public channel is…",
    options: [
      "Unacceptable Behavior",
      "Totally Acceptable",
      "Bad Etiquette",
    ],
    correctAnswer: "Unacceptable Behavior"
  },
  {
    category: "Learningcommunity",
    question: "Posting off-topic content into a Slack channel is…",
    options: [
      "Unacceptable Behavior",
      "Totally Acceptable",
      "Bad Etiquette",
    ],
    correctAnswer: "Bad Etiquette"
  },
  {
    category: "Learningcommunity",
    question: "Which one of the following is not a reason why the ALX SE programme has put emphasis on the community?",
    options: [
      "The Network",
      "Get the job",
      "Accountability",
      "The Empowered Journey",
    ],
    correctAnswer: "Get the job"
  },
  {
    category: "Learningcommunity",
    question: "Posting a link to a TED Talk in #random channel is…",
    options: [
      "Unacceptable Behavior",
      "Totally Acceptable",
      "Bad Etiquette",
    ],
    correctAnswer: "Totally Acceptable"
  },
  {
    category: "Learningcommunity",
    question: "Do you agree to abide by the ALX SE community guidelines?",
    options: [
      "I Agree",
    ],
    correctAnswer: "I Agree"
  },
  {
    category: "Learningcommunity",
    question: "Posting someone else’s contact information in a public channel is…",
    options: [
      "Unacceptable Behavior",
      "Totally Acceptable",
      "Bad Etiquette",
    ],
    correctAnswer: "Unacceptable Behavior"
  },
  {
    category: "Learningcommunity",
    question: "Posting a link in a private message to a video that encourages violent response to a current event is…",
    options: [
      "Unacceptable Behavior",
      "Totally Acceptable",
      "Bad Etiquette",
    ],
    correctAnswer: "Unacceptable Behavior"
  },
  {
    category: "Learningcommunity",
    question: "Disparaging a person’s gender in a private message is…",
    options: [
      "Unacceptable Behavior",
      "Totally Acceptable",
      "Bad Etiquette",
    ],
    correctAnswer: "Unacceptable Behavior"
  },
  {
    category: "Learningcommunity",
    question: "Using a derogatory term about the sexual orientation of someone who is not a part of the ALX-SE Community is…",
    options: [
      "Unacceptable Behavior",
      "Totally Acceptable",
      "Bad Etiquette",
    ],
    correctAnswer: "Unacceptable Behavior"
  },
  {
    category: "Learningcommunity",
    question: "Posting a link to a video related to software engineering in your cohort channel is…",
    options: [
      "Unacceptable Behavior",
      "Totally Acceptable",
      "Bad Etiquette",
    ],
    correctAnswer: "Totally Acceptable"
  }
];
