export const test_driven_development_quizQuiz = [
  {
    category: "test_driven_development_",
    question: "Is this a standardized way to comment a function in Python?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "test_driven_development_",
    question: "Is this a standardized way to comment a function in Python?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "test_driven_development_",
    question: "Is this a standardized way to comment a function in Python?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "test_driven_development_",
    question: "Is this a standardized way to comment a function in Python?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "test_driven_development_",
    question: "Is this module correctly commented?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "test_driven_development_",
    question: "Is this module correctly commented?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "test_driven_development_",
    question: "Based on this code, what should all the test cases be? (select multiple)",
    options: [
    ],
    correctAnswer: ""
  }
];
