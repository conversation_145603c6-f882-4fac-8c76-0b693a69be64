export const pythonEverythingIsObjectQuiz = [
  {
    category: "Object Identity",
    question: "What does id() function return in Python?",
    options: [
      "Memory location of an object",
      "Object's value",
      "Object's type",
      "Object's reference count"
    ],
    correctAnswer: "Memory location of an object"
  },
  {
    category: "Mutable Objects",
    question: "Which of these is a mutable object in Python?",
    options: [
      "String",
      "Tuple",
      "List",
      "Integer"
    ],
    correctAnswer: "List"
  },
  {
    category: "References",
    question: "What happens when you assign a variable to another variable in Python?",
    options: [
      "A copy of the object is created",
      "A new reference to the same object is created",
      "The original variable is deleted",
      "The object is duplicated in memory"
    ],
    correctAnswer: "A new reference to the same object is created"
  },
  {
    category: "Object Comparison",
    question: "What does the 'is' operator compare?",
    options: [
      "Values of objects",
      "Types of objects",
      "Memory addresses of objects",
      "Size of objects"
    ],
    correctAnswer: "Memory addresses of objects"
  },
  {
    category: "Immutable Objects",
    question: "What happens when you modify a string?",
    options: [
      "The original string is modified",
      "A new string object is created",
      "The string is extended",
      "The string reference is updated"
    ],
    correctAnswer: "A new string object is created"
  },
  {
    category: "Object Lifetime",
    question: "When is an object deleted in Python?",
    options: [
      "When you call del on it",
      "When its reference count reaches zero",
      "When the program ends",
      "When garbage collection runs"
    ],
    correctAnswer: "When its reference count reaches zero"
  }
];