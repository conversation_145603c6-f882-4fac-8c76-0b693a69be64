export const network_0_quizQuiz = [
  {
    category: "network_0_",
    question: "What is the name of the HTTP response header used to define the formatting of the body? (This header gives details to the client on how to interpret the data received.)",
    options: [
      "Type",
      "Format",
      "Content-Format",
      "Content-Type",
    ],
    correctAnswer: "Content-Type"
  },
  {
    category: "network_0_",
    question: "What will be the port number requested by this URL?",
    options: [
      "8080",
      "443",
      "80",
      "22",
    ],
    correctAnswer: "80"
  },
  {
    category: "network_0_",
    question: "What is the name of the HTTP request header that defines the size (in bytes) of the message body?",
    options: [
      "Content-Length",
      "Content-Size",
      "Length",
      "Size",
    ],
    correctAnswer: "Content-Length"
  },
  {
    category: "network_0_",
    question: "What is the name of the HTTP response header used to define the status code of the response?",
    options: [
      "Status",
      "Code",
      "Status-Code",
      "Http-Status",
    ],
    correctAnswer: "Status"
  },
  {
    category: "network_0_",
    question: "In the following URL, what’s the name of the parameter in the query string?",
    options: [
      "batch",
      "89",
      "apply",
    ],
    correctAnswer: "batch"
  },
  {
    category: "network_0_",
    question: "In the following URL, how many parameters are in the query string?",
    options: [
      "4",
      "3",
      "1",
      "5",
      "2",
    ],
    correctAnswer: "3"
  },
  {
    category: "network_0_",
    question: "When you are typing  in your browser, which HTTP verb is used?",
    options: [
      "POST",
      "GET",
      "DELETE",
      "PUT",
    ],
    correctAnswer: "GET"
  },
  {
    category: "network_0_",
    question: "In the following URL, what’s the hostname?",
    options: [
      "google",
      "www.google.com",
      "google.com",
      "www.google",
    ],
    correctAnswer: "www.google.com"
  },
  {
    category: "network_0_",
    question: "What is the name of the HTTP response header used to send cookies to the client from the server?",
    options: [
      "Send-Cookies",
      "Cookie-Setter",
      "Set-Cookie",
    ],
    correctAnswer: "Set-Cookie"
  },
  {
    category: "network_0_",
    question: "What is the first digit of status codes that indicate a server error?",
    options: [
      "4xx",
      "1xx",
      "3xx",
      "5xx",
      "2xx",
    ],
    correctAnswer: "5xx"
  },
  {
    category: "network_0_",
    question: "What is the  option to set a cookie with a key-value pair?",
    options: [
      "-d",
      "-a",
      "-b",
      "-c",
    ],
    correctAnswer: "-b"
  },
  {
    category: "network_0_",
    question: "In the following URL, what’s the resource path?",
    options: [
      "assets/scripts/main.js",
      "assets/scripts",
      "main.js",
    ],
    correctAnswer: "assets/scripts/main.js"
  },
  {
    category: "network_0_",
    question: "What will be the port number requested by this URL?",
    options: [
      "80",
      "8888",
      "8080",
    ],
    correctAnswer: "8080"
  },
  {
    category: "network_0_",
    question: "In the following URL, what’s the resource path?",
    options: [
      "v1",
      "v1/auth/new",
      "v1/auth",
      "v1/auth/new/index.html",
    ],
    correctAnswer: "v1/auth/new"
  },
  {
    category: "network_0_",
    question: "In this following HTML code, which HTTP verb will be used when you will submit this form?",
    options: [
      "ENTER",
      "POST",
      "SUBMIT",
      "GET",
      "FORM",
    ],
    correctAnswer: "POST"
  },
  {
    category: "network_0_",
    question: "What is the name of the HTTP request header used to send cookies from the client?",
    options: [
      "Set-Cookie",
      "Cookie",
      "Send-Cookie",
      "Cookies",
    ],
    correctAnswer: "Cookie"
  },
  {
    category: "network_0_",
    question: "What is the name of the HTTP response header that defines a list of available HTTP methods for this URL?",
    options: [
      "Verbs",
      "Verbs-Allowed",
      "Allow",
    ],
    correctAnswer: "Allow"
  },
  {
    category: "network_0_",
    question: "What is the  option to disable the progression display?",
    options: [
      "-s",
      "-b",
      "-c",
      "-p",
    ],
    correctAnswer: "-s"
  },
  {
    category: "network_0_",
    question: "In the following URL, how many parameters are in the query string?",
    options: [
      "2",
      "1",
      "3",
    ],
    correctAnswer: "2"
  },
  {
    category: "network_0_",
    question: "What is the  option to save the body of the resulting response to a file?",
    options: [
      "-d",
      "-r",
      "-b",
      "-o",
    ],
    correctAnswer: "-o"
  },
  {
    category: "network_0_",
    question: "Which HTTP request header indicates the browser used by the client sending the request?",
    options: [
      "Origin",
      "I-Am",
      "User-Agent",
      "Browser-Name",
    ],
    correctAnswer: "User-Agent"
  },
  {
    category: "network_0_",
    question: "In the following URL, what’s the protocol?",
    options: [
      "http",
      "ftp",
      "https",
    ],
    correctAnswer: "http"
  },
  {
    category: "network_0_",
    question: "What is the  option that defines the HTTP method used?",
    options: [
      "-d",
      "-s",
      "-X",
    ],
    correctAnswer: "-X"
  },
  {
    category: "network_0_",
    question: "In this following HTML code, which HTTP verb will be used when you will submit this form?",
    options: [
      "GET",
      "POST",
      "UPDATE",
      "PUT",
    ],
    correctAnswer: "PUT"
  },
  {
    category: "network_0_",
    question: "In the following URL, what’s the resource path?",
    options: [
      "/",
      "www.google.com/index.html",
      "index.html",
    ],
    correctAnswer: "index.html"
  },
  {
    category: "network_0_",
    question: "What’s the status code number for a permanent redirection (moved permanently)?",
    options: [
      "302",
      "201",
      "301",
      "304",
      "300",
    ],
    correctAnswer: "301"
  },
  {
    category: "network_0_",
    question: "What is the  option to follow all redirects?",
    options: [
      "-s",
      "-L",
      "-X",
    ],
    correctAnswer: "-L"
  },
  {
    category: "network_0_",
    question: "What is the name of the HTTP response header used to define the size, in bytes, of the body of the response?",
    options: [
      "Body-Size",
      "Length",
      "Content-Size",
      "Content-Length",
    ],
    correctAnswer: "Content-Length"
  },
  {
    category: "network_0_",
    question: "In the following URL, what’s the sub domain?",
    options: [
      ".com",
      "api",
      "api.google",
    ],
    correctAnswer: "api"
  },
  {
    category: "network_0_",
    question: "Which  option is used to set an HTTP header to a specific value?",
    options: [
      "-H",
      "-s",
      "-X",
    ],
    correctAnswer: "-H"
  },
  {
    category: "network_0_",
    question: "What’s the status code number for an invalid HTTP request (server can’t understand it)?",
    options: [
      "500",
      "400",
      "404",
    ],
    correctAnswer: "400"
  },
  {
    category: "network_0_",
    question: "When an HTTP response indicates a redirection, which header defines the URL the client should be redirected to?",
    options: [
      "Redirect-Location",
      "Redirect-URI",
      "Next-Location",
      "Redirect",
      "Location",
    ],
    correctAnswer: "Location"
  },
  {
    category: "network_0_",
    question: "What’s the status code number for a web page that can’t be found?",
    options: [
      "404",
      "500",
      "405",
    ],
    correctAnswer: "404"
  },
  {
    category: "network_0_",
    question: "What is the  option to set a body key-value parameter?",
    options: [
      "-b",
      "-d",
      "-X",
    ],
    correctAnswer: "-d"
  },
  {
    category: "network_0_",
    question: "What will be the port number requested by this URL?",
    options: [
      "543",
      "548",
      "80",
    ],
    correctAnswer: "548"
  },
  {
    category: "network_0_",
    question: "In the following URL, what’s the sub domain?",
    options: [
      "api-dev",
      "/v1",
      "/v1/auth/new",
    ],
    correctAnswer: "api-dev"
  }
];
