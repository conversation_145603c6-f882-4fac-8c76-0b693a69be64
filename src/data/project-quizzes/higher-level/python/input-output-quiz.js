export const pythonInputOutputQuiz = [
  {
    category: "File Operations",
    question: "Which method is used to close a file in Python?",
    options: [
      "file.exit()",
      "file.close()",
      "file.end()",
      "file.stop()"
    ],
    correctAnswer: "file.close()"
  },
  {
    category: "Reading Files",
    question: "What happens when you use 'with' statement with file operations?",
    options: [
      "The file remains open until program ends",
      "The file is automatically closed after the block",
      "The file is deleted after use",
      "The file becomes read-only"
    ],
    correctAnswer: "The file is automatically closed after the block"
  },
  {
    category: "Writing Files",
    question: "Which mode opens a file for writing and truncates it first?",
    options: [
      "'r'",
      "'a'",
      "'w'",
      "'x'"
    ],
    correctAnswer: "'w'"
  },
  {
    category: "JSON",
    question: "Which module is used to work with JSON in Python?",
    options: [
      "jsonlib",
      "json",
      "jsonparser",
      "jsonutil"
    ],
    correctAnswer: "json"
  },
  {
    category: "File Modes",
    question: "What does the 'a' mode do when opening a file?",
    options: [
      "Overwrites the file",
      "Appends to the file",
      "Read the file only",
      "Creates a new file only if it doesn't exist"
    ],
    correctAnswer: "Appends to the file"
  },
  {
    category: "Error Handling",
    question: "What is the best way to handle file operations in Python?",
    options: [
      "Using try-except blocks",
      "Using if-else statements",
      "Using while loops",
      "Using for loops"
    ],
    correctAnswer: "Using try-except blocks"
  }
];