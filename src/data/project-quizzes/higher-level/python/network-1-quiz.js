export const pythonNetwork1Quiz = [
  {
    category: "HTTP Requests",
    question: "Which Python library is commonly used for making HTTP requests?",
    options: [
      "requests",
      "urllib",
      "Both requests and urllib",
      "httplib"
    ],
    correctAnswer: "Both requests and urllib"
  },
  {
    category: "API Interaction",
    question: "What format is commonly used for API responses?",
    options: [
      "JSON",
      "XML",
      "HTML",
      "Plain text"
    ],
    correctAnswer: "JSON"
  },
  {
    category: "Authentication",
    question: "How is API authentication typically handled in HTTP requests?",
    options: [
      "Using headers",
      "In the URL",
      "In the request body",
      "All of the above"
    ],
    correctAnswer: "Using headers"
  },
  {
    category: "Response Handling",
    question: "What method is used to parse JSON responses in Python?",
    options: [
      "json.loads()",
      "json.parse()",
      "json.decode()",
      "json.convert()"
    ],
    correctAnswer: "json.loads()"
  },
  {
    category: "Error Handling",
    question: "What's the best practice for handling HTTP errors in Python?",
    options: [
      "Using try-except blocks",
      "Checking response status codes",
      "Both try-except and status code checks",
      "Ignoring errors"
    ],
    correctAnswer: "Both try-except and status code checks"
  }
];