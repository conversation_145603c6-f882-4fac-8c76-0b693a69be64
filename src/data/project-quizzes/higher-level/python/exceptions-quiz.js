export const pythonExceptionsQuiz = [
  {
    category: "Exception Handling",
    question: "What is the basic syntax for handling exceptions in Python?",
    options: [
      "try/catch",
      "try/except",
      "if/else",
      "catch/throw"
    ],
    correctAnswer: "try/except"
  },
  {
    category: "Multiple Exceptions",
    question: "How do you handle multiple different exceptions?",
    options: [
      "Using multiple except blocks",
      "Using if/else statements",
      "Using a switch statement",
      "Using a single catch block"
    ],
    correctAnswer: "Using multiple except blocks"
  },
  {
    category: "Finally Clause",
    question: "What is the purpose of the 'finally' clause?",
    options: [
      "Code that executes whether an exception occurs or not",
      "Code that only executes if no exception occurs",
      "Code that only executes if an exception occurs",
      "Code that defines custom exceptions"
    ],
    correctAnswer: "Code that executes whether an exception occurs or not"
  },
  {
    category: "Raising Exceptions",
    question: "How do you raise an exception in Python?",
    options: [
      "Using the raise keyword",
      "Using the throw keyword",
      "Using the except keyword",
      "Using the error keyword"
    ],
    correctAnswer: "Using the raise keyword"
  },
  {
    category: "Custom Exceptions",
    question: "How do you create a custom exception class?",
    options: [
      "Inherit from the Exception class",
      "Use the @exception decorator",
      "Create a regular class",
      "Use the except keyword"
    ],
    correctAnswer: "Inherit from the Exception class"
  }
];