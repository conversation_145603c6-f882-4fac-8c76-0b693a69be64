import { pythonClassesObjectsQuiz } from './classes-objects-quiz';
import { pythonMoreClassesQuiz } from './more-classes-quiz';
import { pythonEverythingIsObjectQuiz } from './everything-is-object-quiz';
import { pythonInheritanceQuiz } from './inheritance-quiz';
import { pythonInputOutputQuiz } from './input-output-quiz';
import { pythonAlmostCircleQuiz } from './almost-circle-quiz';
import { pythonHelloWorldQuiz } from './hello-world-quiz';
import { pythonIfElseLoopsFunctionsQuiz } from './if-else-loops-functions-quiz';
import { pythonImportModulesQuiz } from './import-modules-quiz';
import { pythonDataStructuresQuiz } from './data-structures-quiz';
import { pythonMoreDataStructuresQuiz } from './more-data-structures-quiz';
import { pythonExceptionsQuiz } from './exceptions-quiz';
import { pythonTestDrivenDevelopmentQuiz } from './test-driven-development-quiz';
import { pythonORMQuiz } from './orm-quiz';
import { pythonNetwork0Quiz } from './network-0-quiz';
import { pythonNetwork1Quiz } from './network-1-quiz';

export const pythonQuizzes = {
  // Core Python Concepts
  helloWorldQuiz: pythonHelloWorldQuiz,
  ifElseLoopsFunctionsQuiz: pythonIfElseLoopsFunctionsQuiz,
  importModulesQuiz: pythonImportModulesQuiz,
  dataStructuresQuiz: pythonDataStructuresQuiz,
  moreDataStructuresQuiz: pythonMoreDataStructuresQuiz,
  exceptionsQuiz: pythonExceptionsQuiz,

  // Object-Oriented Programming
  classesObjectsQuiz: pythonClassesObjectsQuiz,
  moreClassesQuiz: pythonMoreClassesQuiz,
  everythingIsObjectQuiz: pythonEverythingIsObjectQuiz,
  inheritanceQuiz: pythonInheritanceQuiz,
  
  // Testing and Development
  testDrivenDevelopmentQuiz: pythonTestDrivenDevelopmentQuiz,
  
  // Input/Output and Advanced Concepts
  inputOutputQuiz: pythonInputOutputQuiz,
  almostCircleQuiz: pythonAlmostCircleQuiz,
  
  // Databases and Networking
  ormQuiz: pythonORMQuiz,
  network0Quiz: pythonNetwork0Quiz,
  network1Quiz: pythonNetwork1Quiz
};

export {
  pythonHelloWorldQuiz,
  pythonIfElseLoopsFunctionsQuiz,
  pythonImportModulesQuiz,
  pythonDataStructuresQuiz,
  pythonMoreDataStructuresQuiz,
  pythonExceptionsQuiz,
  pythonClassesObjectsQuiz,
  pythonMoreClassesQuiz,
  pythonEverythingIsObjectQuiz,
  pythonInheritanceQuiz,
  pythonTestDrivenDevelopmentQuiz,
  pythonInputOutputQuiz,
  pythonAlmostCircleQuiz,
  pythonORMQuiz,
  pythonNetwork0Quiz,
  pythonNetwork1Quiz
};