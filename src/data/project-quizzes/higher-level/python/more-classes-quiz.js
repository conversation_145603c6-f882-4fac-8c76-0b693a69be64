export const more_classes_quizQuiz = [
  {
    category: "more_classes_",
    question: "What is ?",
    options: [
      "Instance method that removes the last character of an instance",
      "Instance method called when an instance is deleted",
      "Instance method that prints the memory address of an instance",
    ],
    correctAnswer: "Instance method called when an instance is deleted"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "89"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "98"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "89"
  },
  {
    category: "more_classes_",
    question: "What is ?",
    options: [
      "A class attribute",
      "The instance method called when a new object is created",
      "A class method",
      "The instance method called when a class is called for the first time",
    ],
    correctAnswer: "The instance method called when a new object is created"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "98"
  },
  {
    category: "more_classes_",
    question: "What is ?",
    options: [
      "Instance method that prints an “official” string representation of an instance",
      "Instance method that returns the dictionary representation of an instance",
      "Instance method that returns an “official” string representation of an instance",
    ],
    correctAnswer: "Instance method that returns an “official” string representation of an instance"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "98"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "98"
  },
  {
    category: "more_classes_",
    question: "What is ?",
    options: [
      "Instance method that returns an “informal” and nicely printable string representation of an instance",
      "Instance method that prints an “informal” and nicely printable string representation of an instance",
      "Instance method that returns the dictionary representation of an instance",
    ],
    correctAnswer: "Instance method that returns an “informal” and nicely printable string representation of an instance"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "89"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "1"
  },
  {
    category: "more_classes_",
    question: "What is ?",
    options: [
      "The string documentation of an object (based on docstring)",
      "Creates man file",
      "Prints the documentation of an object",
    ],
    correctAnswer: "The string documentation of an object (based on docstring)"
  },
  {
    category: "more_classes_",
    question: "What do these lines print?",
    options: [
      "None",
      "89",
      "1",
      "98",
    ],
    correctAnswer: "1"
  }
];
