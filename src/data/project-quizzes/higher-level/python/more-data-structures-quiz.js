export const more_data_structures_quizQuiz = [
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "‘age’",
      "0",
      "Nothing",
      "89",
    ],
    correctAnswer: "0"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "0 1 2 3",
      "1 3 4 2",
      "1 2 3 4",
      "1 3 4 2 0",
    ],
    correctAnswer: "1 3 4 2"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "89",
      "id",
      "a[‘id’]",
      "John",
      "‘id’",
    ],
    correctAnswer: "89"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "1 2 3",
      "1 2 3 4",
      "0 1 2 3",
    ],
    correctAnswer: "1 2 3"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "12",
      "‘age’",
      "89",
      "Nothing",
      "Not found",
    ],
    correctAnswer: "Nothing"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "0 1 2 3",
      "1 2 3",
      "0 1 2 3 5",
      "1 2 3 4",
    ],
    correctAnswer: "1 2 3 4"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "0 1 2 3",
      "Hello Holberton School 98",
      "1 2 3 4",
    ],
    correctAnswer: "Hello Holberton School 98"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "89",
      "id",
      "a[‘id’]",
      "John",
      "‘id’",
    ],
    correctAnswer: "89"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "1 2 3",
      "0 1 2",
      "0 1 2 3",
    ],
    correctAnswer: "0 1 2"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "3",
      "4",
      "[1, 2, 3, 4]",
      "[3]",
      "[4]",
    ],
    correctAnswer: "4"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "list",
      "‘projects’",
      "[1]",
      "Nothing",
      "[1, 2, 3, 4]",
    ],
    correctAnswer: "[1, 2, 3, 4]"
  },
  {
    category: "more_data_structures_",
    question: "What do these lines print?",
    options: [
      "Bob",
      "89",
      "Amy",
      "Nothing",
      "[ { ‘id’: 82, ‘name’: “Bob” }, { ‘id’: 83, ‘name’: “Amy” } ]",
    ],
    correctAnswer: "Amy"
  }
];
