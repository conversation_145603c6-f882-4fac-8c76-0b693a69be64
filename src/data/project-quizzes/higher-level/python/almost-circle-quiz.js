export const pythonAlmostCircleQuiz = [
  {
    category: "OOP",
    question: "What is Unit Testing in Python?",
    options: [
      "A way to test individual components of a program",
      "A way to test the entire program at once",
      "A way to compile Python code",
      "A way to document code"
    ],
    correctAnswer: "A way to test individual components of a program"
  },
  {
    category: "Testing",
    question: "Which module is commonly used for unit testing in Python?",
    options: [
      "pytest",
      "unittest",
      "testcase",
      "testing"
    ],
    correctAnswer: "unittest"
  },
  {
    category: "Class Methods",
    question: "What is the purpose of __str__ method?",
    options: [
      "To create a string representation of an object for debugging",
      "To convert an object to string",
      "To return a user-friendly string representation of an object",
      "To compare strings"
    ],
    correctAnswer: "To return a user-friendly string representation of an object"
  },
  {
    category: "File Operations",
    question: "What does the json.dumps() function do?",
    options: [
      "Loads JSON from a file",
      "Converts Python object to JSON string",
      "Writes JSON to a file",
      "Reads JSON from a string"
    ],
    correctAnswer: "Converts Python object to JSON string"
  },
  {
    category: "Inheritance",
    question: "What is the benefit of using *args in a method?",
    options: [
      "It makes the code run faster",
      "It allows passing a variable number of arguments",
      "It reduces memory usage",
      "It improves code readability"
    ],
    correctAnswer: "It allows passing a variable number of arguments"
  },
  {
    category: "Architecture",
    question: "What is the purpose of a Base class?",
    options: [
      "To create objects",
      "To provide common functionality to derived classes",
      "To store data",
      "To implement interfaces"
    ],
    correctAnswer: "To provide common functionality to derived classes"
  }
];