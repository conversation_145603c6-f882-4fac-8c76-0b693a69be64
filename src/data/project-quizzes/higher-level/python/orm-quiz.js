export const pythonORMQuiz = [
  {
    category: "ORM Basics",
    question: "What is an ORM?",
    options: [
      "Object-Relational Mapping",
      "Object-Related Model",
      "Object Reference Method",
      "Object Runtime Manager"
    ],
    correctAnswer: "Object-Relational Mapping"
  },
  {
    category: "SQLAlchemy",
    question: "What is SQLAlchemy in Python?",
    options: [
      "A SQL database",
      "An ORM framework",
      "A query language",
      "A database server"
    ],
    correctAnswer: "An ORM framework"
  },
  {
    category: "Database Connection",
    question: "How do you define a database connection in SQLAlchemy?",
    options: [
      "Using create_engine()",
      "Using connect()",
      "Using db_connect()",
      "Using new_connection()"
    ],
    correctAnswer: "Using create_engine()"
  },
  {
    category: "Models",
    question: "What base class is typically used for SQLAlchemy models?",
    options: [
      "Base",
      "Model",
      "SQLModel",
      "Table"
    ],
    correctAnswer: "Base"
  },
  {
    category: "Queries",
    question: "Which method is used to execute a query in SQLAlchemy?",
    options: [
      "session.query()",
      "db.query()",
      "model.query()",
      "query.execute()"
    ],
    correctAnswer: "session.query()"
  }
];