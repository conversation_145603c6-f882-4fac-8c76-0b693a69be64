export const pythonClassesObjectsQuiz = [
  {
    category: "OOP Basics",
    question: "What is a class in Python?",
    options: [
      "A function that creates objects",
      "A blueprint for creating objects",
      "A type of variable",
      "A module import"
    ],
    correctAnswer: "A blueprint for creating objects"
  },
  {
    category: "Instance Methods",
    question: "What is the first parameter of an instance method in a class?",
    options: [
      "this",
      "self",
      "cls",
      "class"
    ],
    correctAnswer: "self"
  },
  {
    category: "Attributes",
    question: "What is the correct way to define a private attribute in Python?",
    options: [
      "private attribute",
      "_attribute",
      "__attribute",
      "private: attribute"
    ],
    correctAnswer: "__attribute"
  },
  {
    category: "Object Creation",
    question: "How do you create an instance of a class named MyClass?",
    options: [
      "new MyClass()",
      "MyClass.create()",
      "MyClass()",
      "create MyClass()"
    ],
    correctAnswer: "MyClass()"
  },
  {
    category: "Properties",
    question: "What decorator is used to define a property in Python?",
    options: [
      "@property",
      "@getter",
      "@attribute",
      "@static"
    ],
    correctAnswer: "@property"
  },
  {
    category: "Special Methods",
    question: "Which special method is called when creating a new instance of a class?",
    options: [
      "__new__",
      "__init__",
      "__create__",
      "__start__"
    ],
    correctAnswer: "__init__"
  }
];