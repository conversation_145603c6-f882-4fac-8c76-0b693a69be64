export const web_static_quizQuiz = [
  {
    category: "web_static_",
    question: "Is the following HTML markup valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "In the following code, is the text  red?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is the following HTML markup valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "In the following code, is the text  red?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "In the following code, is the text  red?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is the following HTML markup valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "In the following code, is the text  red?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "In the following code, is the text  red?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "Is the following HTML markup valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "In the following code, is the text  red?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is the following HTML markup valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "In the following code, is the text  red?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "Is following CSS syntax valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "web_static_",
    question: "Is the following HTML markup valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "web_static_",
    question: "Is the following HTML markup valid?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  }
];
