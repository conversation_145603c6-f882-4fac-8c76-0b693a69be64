export const deploy_static_quizQuiz = [
  {
    category: "deploy_static_",
    question: "What is the Fabric command to execute a shell command locally?",
    options: [
      "put",
      "run",
      "get",
      "local",
    ],
    correctAnswer: ""
  },
  {
    category: "deploy_static_",
    question: "What is the default name of a Fabric file?",
    options: [
      "Dockerfile",
      "Fabric.py",
      "fabfile.py",
      "Fabricfile",
    ],
    correctAnswer: ""
  },
  {
    category: "deploy_static_",
    question: "What is the Fabric command to upload a file (from local to remote)?",
    options: [
      "get",
      "run",
      "put",
      "local",
    ],
    correctAnswer: ""
  },
  {
    category: "deploy_static_",
    question: "What is the Fabric command to download a file (from remote to local)?",
    options: [
      "get",
      "run",
      "put",
      "local",
    ],
    correctAnswer: ""
  },
  {
    category: "deploy_static_",
    question: "What is the Fabric command for asking information to the user?",
    options: [
      "put",
      "ask",
      "prompt",
      "local",
    ],
    correctAnswer: ""
  },
  {
    category: "deploy_static_",
    question: "What is the Fabric command to execute a shell command remotely?",
    options: [
      "put",
      "run",
      "get",
      "local",
    ],
    correctAnswer: ""
  }
];
