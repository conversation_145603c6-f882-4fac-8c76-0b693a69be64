export const airbnbConsoleQuiz = [
  {
    category: "Command Interpreter",
    question: "What is a command interpreter?",
    options: [
      "A program that receives and executes commands",
      "A web browser",
      "A database",
      "A web server"
    ],
    correctAnswer: "A program that receives and executes commands"
  },
  {
    category: "Python Classes",
    question: "How do you create a new instance of a class in Python?",
    options: [
      "Using the __init__ method",
      "Using the class keyword",
      "Using the new keyword",
      "Using the create keyword"
    ],
    correctAnswer: "Using the __init__ method"
  },
  {
    category: "Storage",
    question: "What is serialization?",
    options: [
      "Converting objects to a format that can be stored or transmitted",
      "Creating new objects",
      "Deleting objects",
      "Modifying objects"
    ],
    correctAnswer: "Converting objects to a format that can be stored or transmitted"
  },
  {
    category: "File Storage",
    question: "What format is commonly used for serializing objects in Python?",
    options: [
      "JSON",
      "XML",
      "YAML",
      "CSV"
    ],
    correctAnswer: "JSON"
  },
  {
    category: "Command Line",
    question: "What method is used to parse command line arguments?",
    options: [
      "parse_args()",
      "get_args()",
      "read_args()",
      "process_args()"
    ],
    correctAnswer: "parse_args()"
  }
];