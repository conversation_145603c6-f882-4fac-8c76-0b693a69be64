export const airbnbMysqlQuiz = [
  {
    category: "Database Design",
    question: "What is the purpose of normalization in database design?",
    options: [
      "To eliminate data redundancy and ensure data integrity",
      "To make the database faster",
      "To reduce database size",
      "To create more tables"
    ],
    correctAnswer: "To eliminate data redundancy and ensure data integrity"
  },
  {
    category: "Foreign Keys",
    question: "What is a foreign key?",
    options: [
      "A field that links two tables together",
      "The main key in a table",
      "An encrypted key",
      "A backup key"
    ],
    correctAnswer: "A field that links two tables together"
  },
  {
    category: "ORM",
    question: "What is SQLAlchemy's role in the AirBnB clone?",
    options: [
      "To provide an interface between Python and MySQL",
      "To create HTML pages",
      "To handle user authentication",
      "To style web pages"
    ],
    correctAnswer: "To provide an interface between Python and MySQL"
  },
  {
    category: "Relationships",
    question: "What type of relationship exists between User and Place in AirBnB clone?",
    options: [
      "One-to-Many",
      "Many-to-Many",
      "One-to-One",
      "No relationship"
    ],
    correctAnswer: "One-to-Many"
  },
  {
    category: "Database Operations",
    question: "What operation is used to retrieve data from multiple tables?",
    options: [
      "JOIN",
      "CONNECT",
      "MERGE",
      "LINK"
    ],
    correctAnswer: "JOIN"
  }
];