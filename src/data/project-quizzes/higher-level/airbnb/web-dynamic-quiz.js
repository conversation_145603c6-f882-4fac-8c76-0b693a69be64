export const airbnbWebDynamicQuiz = [
  {
    category: "JavaScript",
    question: "What is the purpose of jQuery?",
    options: [
      "To simplify HTML DOM manipulation and AJAX calls",
      "To create databases",
      "To style web pages",
      "To handle server-side logic"
    ],
    correctAnswer: "To simplify HTML DOM manipulation and AJAX calls"
  },
  {
    category: "AJAX",
    question: "What is AJAX used for?",
    options: [
      "Making asynchronous HTTP requests",
      "Creating HTML elements",
      "Styling web pages",
      "Managing databases"
    ],
    correctAnswer: "Making asynchronous HTTP requests"
  },
  {
    category: "DOM Manipulation",
    question: "Which jQuery method is used to attach an event handler?",
    options: [
      "on()",
      "attach()",
      "event()",
      "handle()"
    ],
    correctAnswer: "on()"
  },
  {
    category: "API Integration",
    question: "What format is commonly used for API responses in AJAX?",
    options: [
      "JSON",
      "HTML",
      "XML",
      "Plain text"
    ],
    correctAnswer: "JSON"
  },
  {
    category: "Event Handling",
    question: "What is event delegation in jQuery?",
    options: [
      "Attaching event handlers to future elements",
      "Removing event handlers",
      "Creating new events",
      "Modifying event data"
    ],
    correctAnswer: "Attaching event handlers to future elements"
  }
];