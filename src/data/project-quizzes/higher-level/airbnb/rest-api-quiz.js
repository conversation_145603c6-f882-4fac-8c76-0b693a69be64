export const airbnbRestApiQuiz = [
  {
    category: "REST",
    question: "What is REST?",
    options: [
      "Architectural style for distributed systems",
      "A programming language",
      "A database system",
      "A web browser"
    ],
    correctAnswer: "Architectural style for distributed systems"
  },
  {
    category: "HTTP Methods",
    question: "Which HTTP method is used to create a new resource?",
    options: [
      "POST",
      "GET",
      "PUT",
      "DELETE"
    ],
    correctAnswer: "POST"
  },
  {
    category: "API Endpoints",
    question: "What is an API endpoint?",
    options: [
      "A URL where the API can be accessed",
      "A database table",
      "A programming function",
      "A web server"
    ],
    correctAnswer: "A URL where the API can be accessed"
  },
  {
    category: "Status Codes",
    question: "What does HTTP status code 404 indicate?",
    options: [
      "Resource not found",
      "Success",
      "Server error",
      "Bad request"
    ],
    correctAnswer: "Resource not found"
  },
  {
    category: "Authentication",
    question: "What is the purpose of JWT in REST APIs?",
    options: [
      "To securely transmit user authentication information",
      "To create database tables",
      "To style web pages",
      "To handle file uploads"
    ],
    correctAnswer: "To securely transmit user authentication information"
  }
];