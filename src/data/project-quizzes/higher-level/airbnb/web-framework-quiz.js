export const airbnbWebFrameworkQuiz = [
  {
    category: "Flask",
    question: "What is Flask in Python?",
    options: [
      "A micro web framework",
      "A database management system",
      "A testing framework",
      "An operating system"
    ],
    correctAnswer: "A micro web framework"
  },
  {
    category: "Routes",
    question: "What is a route in Flask?",
    options: [
      "URL pattern that <PERSON><PERSON><PERSON> uses to match and handle requests",
      "A database table",
      "A CSS file",
      "A JavaScript function"
    ],
    correctAnswer: "URL pattern that <PERSON>lask uses to match and handle requests"
  },
  {
    category: "Templates",
    question: "What template engine does Flask use by default?",
    options: [
      "Jinja2",
      "Django Templates",
      "Handlebars",
      "EJS"
    ],
    correctAnswer: "Jinja2"
  },
  {
    category: "Views",
    question: "What is a view function in Flask?",
    options: [
      "Function that handles requests and returns responses",
      "Function that creates database tables",
      "Function that styles web pages",
      "Function that handles user authentication"
    ],
    correctAnswer: "Function that handles requests and returns responses"
  },
  {
    category: "HTTP Methods",
    question: "Which HTTP method is typically used for form submissions?",
    options: [
      "POST",
      "GET",
      "PUT",
      "DELETE"
    ],
    correctAnswer: "POST"
  }
];