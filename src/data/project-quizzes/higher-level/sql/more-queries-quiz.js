export const more_queries_quizQuiz = [
  {
    category: "more_queries_",
    question: "Is it possible to give only read access to multiple databases and tables to a user?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "more_queries_",
    question: "What DCL means?",
    options: [
      "Document Control Language",
      "Data Concept Language",
      "Data Control Language",
      "Document Control Line",
    ],
    correctAnswer: "Data Control Language"
  },
  {
    category: "more_queries_",
    question: "Is it possible to give only read access to a table to a user?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "more_queries_",
    question: "Is it possible to give only read access to a database to a user?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "more_queries_",
    question: "Is it possible to give only insert access to a table to a user?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "more_queries_",
    question: "Is it possible to give only delete access to a table to a user?",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "more_queries_",
    question: "Which JOIN type doesn’t exist? (please select all correct answers)",
    options: [
    ],
    correctAnswer: ""
  }
];
