export const introduction_quizQuiz = [
  {
    category: "introduction_",
    question: "What does DML stand for?",
    options: [
      "Database Manipulation Language",
      "Data Manipulation Language",
      "Document Manipulation Language",
      "Document Model Language",
    ],
    correctAnswer: "Data Manipulation Language"
  },
  {
    category: "introduction_",
    question: "What does SQL stand for?",
    options: [
      "Sequences of Query Logic",
      "Solid Query Language",
      "Structured Query Language",
      "Structured Question Language",
    ],
    correctAnswer: "Structured Query Language"
  },
  {
    category: "introduction_",
    question: "How do you list all  records with age > 21 in this table?",
    options: [
      "SELECT * FROM users WHERE age < 21;",
      "SELECT * FROM users WHERE age > 21;",
      "SELECT * FROM users WHERE age IS UP TO 21;",
      "SELECT * FROM users WHERE age BETWEEN 21 AND 89;",
    ],
    correctAnswer: "SELECT * FROM users WHERE age > 21;"
  },
  {
    category: "introduction_",
    question: "What does DDL stand for?",
    options: [
      "Data Definition Language",
      "Data Document Language",
      "Database Definition Language",
      "Document Data Language",
    ],
    correctAnswer: "Data Definition Language"
  },
  {
    category: "introduction_",
    question: "What is a relational database? (please select all correct answers)",
    options: [
    ],
    correctAnswer: ""
  },
  {
    category: "introduction_",
    question: "How do you change the name of the  record with id = 89 to Holberton?",
    options: [
      "UPDATE users SET name = “Holberton” WHERE id = 89;",
      "UPDATE users SET name = “Holberton”;",
      "CHANGE users SET name = “Holberton” WHERE id = 89;",
    ],
    correctAnswer: "UPDATE users SET name = “Holberton” WHERE id = 89;"
  },
  {
    category: "introduction_",
    question: "How to you add a new record in the table ?",
    options: [
      "INSERT users (id, name, age) VALUES (2, “Betty”, 30);",
      "INSERT INTO users (id, name, age) VALUES (2, “Betty”, 30);",
      "INSERT INTO users (id, name) VALUES (2, “Betty”, 30);",
      "INSERT INTO users (id, name, age) VALUES (2, “Betty”);",
    ],
    correctAnswer: "INSERT INTO users (id, name, age) VALUES (2, “Betty”, 30);"
  },
  {
    category: "introduction_",
    question: "How do you list all  in this table?",
    options: [
      "DELETE * FROM users;",
      "SELECT ALL users;",
      "SELECT * FROM users;",
    ],
    correctAnswer: "SELECT * FROM users;"
  },
  {
    category: "introduction_",
    question: "How do you delete the  record with id = 89 in this table?",
    options: [
      "DELETE users WHERE id = 89;",
      "DELETE FROM users;",
      "DELETE FROM users WHERE id = 89;",
      "DELETE FROM users WHERE id IS EQUAL TO 89;",
    ],
    correctAnswer: "DELETE FROM users WHERE id = 89;"
  }
];
