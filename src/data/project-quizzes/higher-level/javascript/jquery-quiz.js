export const jquery_quizQuiz = [
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "jquery_",
    question: "How many HTML tags are present in the following HTML code?",
    options: [
      "4",
      "6",
      "5",
      "7",
    ],
    correctAnswer: "6"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "jquery_",
    question: "How many HTML tags are present in the following HTML code?",
    options: [
      "15",
      "18",
      "16",
      "20",
    ],
    correctAnswer: "15"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "jquery_",
    question: "How many HTML tags are present in the following HTML code?",
    options: [
      "11",
      "13",
      "12",
      "14",
    ],
    correctAnswer: "12"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "No"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  },
  {
    category: "jquery_",
    question: "In the following code snippet, does the selector called  access the HTML tag <header>:",
    options: [
      "Yes",
      "No",
    ],
    correctAnswer: "Yes"
  }
];
