export const javascriptWebScrapingQuiz = [
  {
    category: "Request",
    question: "What is the 'request' module used for in Node.js?",
    options: [
      "Making HTTP requests",
      "Creating web servers",
      "Reading files",
      "Database management"
    ],
    correctAnswer: "Making HTTP requests"
  },
  {
    category: "File System",
    question: "Which module is used to read/write files in Node.js?",
    options: [
      "fs",
      "http",
      "path",
      "stream"
    ],
    correctAnswer: "fs"
  },
  {
    category: "APIs",
    question: "What format is commonly used for API responses?",
    options: [
      "JSON",
      "XML",
      "HTML",
      "Plain text"
    ],
    correctAnswer: "JSON"
  },
  {
    category: "Promises",
    question: "What is a Promise in JavaScript?",
    options: [
      "An object representing eventual completion of an async operation",
      "A way to make HTTP requests",
      "A file reading method",
      "A type of loop"
    ],
    correctAnswer: "An object representing eventual completion of an async operation"
  },
  {
    category: "Error Handling",
    question: "How do you handle errors in async/await?",
    options: [
      "Using try/catch blocks",
      "Using if/else statements",
      "Using switch statements",
      "Using while loops"
    ],
    correctAnswer: "Using try/catch blocks"
  }
];