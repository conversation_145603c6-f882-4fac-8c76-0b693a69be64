export const apiQuiz = [
  {
    category: "API",
    question: "What's the primary purpose of using an API?",
    options: [
      "To create databases",
      "To store files",
      "To allow different software systems to communicate",
      "To manage server hardware"
    ],
    correctAnswer: "To allow different software systems to communicate"
  },
  {
    category: "API",
    question: "What Python module is recommended for making HTTP requests to an API?",
    options: [
      "http",
      "requests or urllib",
      "json",
      "sys"
    ],
    correctAnswer: "requests or urllib"
  },
  {
    category: "API",
    question: "Which data format is commonly used for API responses?",
    options: [
      "HTML",
      "Plain text",
      "JSON",
      "Binary"
    ],
    correctAnswer: "JSON"
  },
  {
    category: "API",
    question: "What's the purpose of data export functionality in APIs?",
    options: [
      "To make the API faster",
      "To store data in different formats for different use cases",
      "To compress data",
      "To encrypt data"
    ],
    correctAnswer: "To store data in different formats for different use cases"
  },
  {
    category: "API",
    question: "Why might you need to convert API data to CSV format?",
    options: [
      "Because it's faster than JSON",
      "For compatibility with spreadsheet applications",
      "Because it's more secure",
      "To reduce data size"
    ],
    correctAnswer: "For compatibility with spreadsheet applications"
  }
];