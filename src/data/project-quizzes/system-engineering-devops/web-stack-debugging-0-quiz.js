export const webStackDebugging0Quiz = [
  {
    category: "Web stack debugging",
    question: "What is the primary purpose of Docker in web stack debugging?",
    options: [
      "To create production environments",
      "To provide isolated environments for testing and debugging",
      "To speed up web applications",
      "To store web server configurations"
    ],
    correctAnswer: "To provide isolated environments for testing and debugging"
  },
  {
    category: "Web stack debugging",
    question: "Which command is used to execute a command in a running Docker container?",
    options: [
      "docker run",
      "docker exec",
      "docker start",
      "docker command"
    ],
    correctAnswer: "docker exec"
  },
  {
    category: "Web stack debugging",
    question: "What is the default port that Apache web server listens on?",
    options: [
      "8080",
      "80",
      "443",
      "3000"
    ],
    correctAnswer: "80"
  },
  {
    category: "Web stack debugging",
    question: "When port mapping in Docker using -p flag (e.g., -p 8080:80), which is the host port?",
    options: [
      "80",
      "8080",
      "Both are host ports",
      "Neither is a host port"
    ],
    correctAnswer: "8080"
  },
  {
    category: "Web stack debugging",
    question: "What command would you use to check if a web server is responding on a specific port?",
    options: [
      "ping",
      "netstat",
      "curl",
      "wget"
    ],
    correctAnswer: "curl"
  }
];