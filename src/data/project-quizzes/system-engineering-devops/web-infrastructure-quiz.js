export const web_infrastructure_quizQuiz = [
  {
    category: "web_infrastructure_",
    question: "What is a database?",
    options: [
      "Is a collection of text files that are stored so that it can be easily accessed, updated and managed by the local application.",
      "Is a collection of information that is stored and organized so that it can be easily accessed, updated and managed.",
      "Is a collection of information that is stored on a physical server and organized so that it can be easily accessed, updated and managed.",
    ],
    correctAnswer: "Is a collection of information that is stored and organized so that it can be easily accessed, updated and managed."
  },
  {
    category: "web_infrastructure_",
    question: "What is a server?",
    options: [
      "A server is a device, a virtual device or computer program or providing functionality for other programs or devices, called “clients”.",
      "A server is returning information to other computers when asked.",
      "A server is a software that serves web pages.",
    ],
    correctAnswer: "A server is a device, a virtual device or computer program or providing functionality for other programs or devices, called “clients”."
  },
  {
    category: "web_infrastructure_",
    question: "What is a codebase?",
    options: [
      "A list of software libraries.",
      "Is the most important files of a software system.",
      "Is the collection of source code that is used to build a software system.",
    ],
    correctAnswer: "Is the collection of source code that is used to build a software system."
  },
  {
    category: "web_infrastructure_",
    question: "What is a web server?",
    options: [
      "A web server is a software or physical device serving web pages over HTTP.",
      "A web server is a software that serves web pages to clients upon their request.",
      "A web server is a software that serves web pages to clients upon their request, it does this over the protocol HTTP.",
    ],
    correctAnswer: "A web server is a software that serves web pages to clients upon their request, it does this over the protocol HTTP."
  },
  {
    category: "web_infrastructure_",
    question: "What is HTTPS?",
    options: [
      "A faster version of HTTP.",
      "A version of HTTP that secure the traffic between your browser and the website by encrypting it.",
      "A version of HTTP that protect your personal information.",
    ],
    correctAnswer: "A version of HTTP that secure the traffic between your browser and the website by encrypting it."
  },
  {
    category: "web_infrastructure_",
    question: "What is a DNS?",
    options: [
      "A list of domain names.",
      "A system that contain all Internet IPs.",
      "A system to translate domain names into IP addresses.",
    ],
    correctAnswer: "A system to translate domain names into IP addresses."
  },
  {
    category: "web_infrastructure_",
    question: "What is TCP/IP?",
    options: [
      "Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on the Internet or any private network.",
      "Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on private network.",
      "Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on the Internet.",
    ],
    correctAnswer: "Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on the Internet or any private network."
  }
];
