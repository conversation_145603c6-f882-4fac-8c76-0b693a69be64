export const sshQuiz = [
  {
    category: "SSH",
    question: "What is SSH primarily used for?",
    options: [
      "To share files between computers",
      "To securely connect to and manage remote systems",
      "To browse the internet securely",
      "To encrypt local files"
    ],
    correctAnswer: "To securely connect to and manage remote systems"
  },
  {
    category: "SSH",
    question: "What are the two main components of an SSH key pair?",
    options: [
      "Username and password",
      "Public key and private key",
      "Host key and client key",
      "Access key and secret key"
    ],
    correctAnswer: "Public key and private key"
  },
  {
    category: "SSH",
    question: "Which of these is the correct command to generate an SSH key pair?",
    options: [
      "ssh-new",
      "ssh-keygen",
      "ssh-create",
      "ssh-make"
    ],
    correctAnswer: "ssh-keygen"
  },
  {
    category: "SSH",
    question: "Where are SSH keys typically stored on a Unix-like system?",
    options: [
      "In the /etc directory",
      "In the /root directory",
      "In the ~/.ssh directory",
      "In the /var/ssh directory"
    ],
    correctAnswer: "In the ~/.ssh directory"
  },
  {
    category: "SSH",
    question: "What is the advantage of using key-based authentication over password authentication?",
    options: [
      "It's faster to type",
      "It works without internet connection",
      "It's more secure and can be automated",
      "It uses less bandwidth"
    ],
    correctAnswer: "It's more secure and can be automated"
  }
];