export const shellIoRedirectionsQuiz = [
  {
    category: "Shell, I/O Redirections and filters",
    question: "Which symbol should I use to start a comment?",
    options: [
      "&",
      "//",
      "!",
      "#"
    ],
    correctAnswer: "#"
  },
  {
    category: "Shell, I/O Redirections and filters",
    question: "Which symbol should I use to redirect the standard output to a file (overwrite the file)?",
    options: [
      ">>",
      ">",
      "2>",
      "&"
    ],
    correctAnswer: ">"
  },
  {
    category: "Shell, I/O Redirections and filters",
    question: "Which symbol should I use to redirect the error output to the standard output?",
    options: [
      "2>&1",
      "1>&2",
      "2>"
    ],
    correctAnswer: "2>&1"
  },
  {
    category: "Shell, I/O Redirections and filters",
    question: "Which symbol should I use to redirect the standard output to a file (appending to the file)?",
    options: [
      ">>",
      ">",
      "2>",
      "&"
    ],
    correctAnswer: ">>"
  },
  {
    category: "Shell, I/O Redirections and filters",
    question: "Which command should I use to display the last 11 lines of a file?",
    options: [
      "head -n 11 my_file",
      "tail -n 11 my_file",
      "head 11 my_file",
      "tail 11 my_file"
    ],
    correctAnswer: "tail -n 11 my_file"
  },
  {
    category: "Shell, I/O Redirections and filters",
    question: "Which command should I use to display the entire file content?",
    options: [
      "grep",
      "head",
      "cat",
      "tail"
    ],
    correctAnswer: "cat"
  },
  {
    category: "Shell, I/O Redirections and filters",
    question: "Which symbol should I use to escape a special character?",
    options: [
      "\\",
      "$",
      "!",
      "#"
    ],
    correctAnswer: "\\"
  }
];