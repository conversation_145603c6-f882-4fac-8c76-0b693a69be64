export const shellVariablesQuiz = [
  {
    category: "Shell, init files, variables and expansions",
    question: "Which of the following correctly creates an alias?",
    options: [
      "alias push=\"git push\"",
      "alias push=git push",
      "export push=\"git push\"",
      "export push=git push"
    ],
    correctAnswer: "alias push=\"git push\""
  },
  {
    category: "Shell, init files, variables and expansions",
    question: "How can you display the content of a variable?",
    options: [
      "ls $MYVAR",
      "export $MYVAR",
      "cd $MYVAR",
      "echo $MYVAR"
    ],
    correctAnswer: "echo $MYVAR"
  },
  {
    category: "Shell, init files, variables and expansions",
    question: "How can you display the exit status of the previous command?",
    options: [
      "echo ?",
      "echo $?",
      "echo $EXITCODE",
      "echo $CODE"
    ],
    correctAnswer: "echo $?"
  },
  {
    category: "Shell, init files, variables and expansions",
    question: "What is the environment variable that contains the previous working directory?",
    options: [
      "OLDPWD",
      "OLDDIR",
      "PREVP<PERSON>",
      "PREVDIR"
    ],
    correctAnswer: "OLDPWD"
  }
];