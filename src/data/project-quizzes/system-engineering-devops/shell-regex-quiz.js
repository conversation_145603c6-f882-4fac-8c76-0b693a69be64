export const shellRegexQuiz = [
  {
    category: "Regular expressions",
    question: "Which of these strings would match a regex pattern that looks for the exact word 'school' in lowercase?",
    options: [
      "schoOl",
      "school",
      "School"
    ],
    correctAnswer: "school"
  },
  {
    category: "Regular expressions",
    question: "Which string would match a pattern that looks for 'Scho' followed by multiple 'o's and ending with 'l'?",
    options: [
      "Schoool",
      "Schoo.l",
      "Scho.l"
    ],
    correctAnswer: "Schoool"
  },
  {
    category: "Regular expressions",
    question: "Which strings would match a pattern that looks for '<PERSON><PERSON>' followed by any single character and ending with 'l'?",
    options: [
      "Scho.l, School",
      "school",
      "Schoool"
    ],
    correctAnswer: "Scho.l, School"
  },
  {
    category: "Regular expressions",
    question: "When matching phone numbers with regex, which of these would match a pattern for exactly 10 digits?",
    options: [
      "************",
      "4155049898",
      "************"
    ],
    correctAnswer: "4155049898"
  },
  {
    category: "Regular expressions",
    question: "What regex pattern would you use to match only capital letters in a string?",
    options: [
      "[A-z]",
      "[A-Z]",
      "[CAPITAL]",
      "[a-Z]"
    ],
    correctAnswer: "[A-Z]"
  }
];