export const webStackDebugging2Quiz = [
  {
    category: "Web stack debugging",
    question: "Why is it considered bad practice to run web servers as root?",
    options: [
      "Because root user is slower",
      "Because it's a security risk if an attacker gains access",
      "Because root uses more system resources",
      "Because root can't run certain web services"
    ],
    correctAnswer: "Because it's a security risk if an attacker gains access"
  },
  {
    category: "Web stack debugging",
    question: "What is the recommended user to run Nginx as?",
    options: [
      "root",
      "admin",
      "nginx",
      "www-data"
    ],
    correctAnswer: "nginx"
  },
  {
    category: "Web stack debugging",
    question: "Which command can be used to check what user a process is running as?",
    options: [
      "who",
      "ps auxff",
      "top",
      "usermod"
    ],
    correctAnswer: "ps auxff"
  },
  {
    category: "Web stack debugging",
    question: "What command is used to run a command as another user in Linux?",
    options: [
      "sudo",
      "su",
      "chown",
      "chmod"
    ],
    correctAnswer: "sudo"
  },
  {
    category: "Web stack debugging",
    question: "What does the command 'nc -z 0 8080' check for?",
    options: [
      "If port 8080 is open and accepting connections",
      "If Nginx is running",
      "If the user is root",
      "If the server is online"
    ],
    correctAnswer: "If port 8080 is open and accepting connections"
  }
];