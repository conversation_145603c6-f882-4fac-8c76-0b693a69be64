export const configManagementQuiz = [
  {
    category: "Configuration management",
    question: "What is the main purpose of Puppet in configuration management?",
    options: [
      "To provide real-time monitoring of servers",
      "To automate the deployment and configuration of servers and software",
      "To backup server configurations",
      "To test server performance"
    ],
    correctAnswer: "To automate the deployment and configuration of servers and software"
  },
  {
    category: "Configuration management",
    question: "Which command is used to validate Puppet manifest syntax?",
    options: [
      "puppet-lint",
      "puppet-validate",
      "puppet-check",
      "puppet-test"
    ],
    correctAnswer: "puppet-lint"
  },
  {
    category: "Configuration management",
    question: "What is the file extension for Puppet manifest files?",
    options: [
      ".puppet",
      ".pp",
      ".conf",
      ".manifest"
    ],
    correctAnswer: ".pp"
  },
  {
    category: "Configuration management",
    question: "In Puppet, what is the purpose of the 'file' resource type?",
    options: [
      "To create and manage file system objects like files and directories",
      "To manage system processes",
      "To install software packages",
      "To configure network settings"
    ],
    correctAnswer: "To create and manage file system objects like files and directories"
  },
  {
    category: "Configuration management",
    question: "What command is used to apply a Puppet manifest to the system?",
    options: [
      "puppet run",
      "puppet execute",
      "puppet apply",
      "puppet start"
    ],
    correctAnswer: "puppet apply"
  }
];