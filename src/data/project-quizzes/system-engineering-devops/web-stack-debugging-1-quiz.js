export const webStackDebugging1Quiz = [
  {
    category: "Web stack debugging",
    question: "Which port does Nginx typically listen on by default?",
    options: [
      "8080",
      "80",
      "443",
      "3000"
    ],
    correctAnswer: "80"
  },
  {
    category: "Web stack debugging",
    question: "What command can you use to check if Nginx is running?",
    options: [
      "nginx status",
      "service nginx status",
      "systemctl status nginx",
      "ps aux | grep nginx"
    ],
    correctAnswer: "service nginx status"
  },
  {
    category: "Web stack debugging",
    question: "Which configuration file would you typically check first when Nginx isn't listening on the correct port?",
    options: [
      "/etc/nginx/nginx.conf",
      "/etc/nginx/sites-enabled/default",
      "/var/log/nginx/error.log",
      "/etc/nginx/conf.d/default.conf"
    ],
    correctAnswer: "/etc/nginx/sites-enabled/default"
  },
  {
    category: "Web stack debugging",
    question: "What's a quick way to test if a port is accessible?",
    options: [
      "telnet localhost [port]",
      "curl 0:[port]",
      "netstat -tuln",
      "ping localhost"
    ],
    correctAnswer: "curl 0:[port]"
  },
  {
    category: "Web stack debugging",
    question: "What command would you use to restart Nginx after configuration changes?",
    options: [
      "nginx reload",
      "service nginx restart",
      "nginx -s reload",
      "All of the above"
    ],
    correctAnswer: "service nginx restart"
  }
];