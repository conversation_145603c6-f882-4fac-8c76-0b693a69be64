export const web_server_quizQuiz = [
  {
    category: "web_server_",
    question: "What was one of the most important reason for which DNS was created",
    options: [
      "because humans are not good at remembering long sequences of numbers (IP address)",
      "to index the web",
      "to connect the Internet",
    ],
    correctAnswer: "because humans are not good at remembering long sequences of numbers (IP address)"
  },
  {
    category: "web_server_",
    question: "The main role of a web server is to",
    options: [
      "serve dynamic content",
      "host files",
      "serve static content",
    ],
    correctAnswer: "serve static content"
  },
  {
    category: "web_server_",
    question: "What is TTL within the context of DNS",
    options: [
      "a time period when DNS query results are cached",
      "a time period for DNS maintenance",
      "a time period when DNS is not answering requests",
    ],
    correctAnswer: "a time period when DNS query results are cached"
  },
  {
    category: "web_server_",
    question: "The main role of DNS is to",
    options: [
      "translate domain name into IP address",
      "name websites",
      "translate domain name into port",
    ],
    correctAnswer: "translate domain name into IP address"
  },
  {
    category: "web_server_",
    question: "Why web servers usually use child processes?",
    options: [
      "That’s just a subjective technical choice from the developers who created the software",
      "To prevent memory leak",
      "So that the web server can dynamically change the number of child process to accommodate the volume of requests to be processed",
    ],
    correctAnswer: "So that the web server can dynamically change the number of child process to accommodate the volume of requests to be processed"
  },
  {
    category: "web_server_",
    question: "A web server is",
    options: [
      "a physical machine",
      "a software",
    ],
    correctAnswer: "a software"
  },
  {
    category: "web_server_",
    question: "A DNS CNAME record translates to",
    options: [
      "an IP",
      "a domain",
    ],
    correctAnswer: "a domain"
  },
  {
    category: "web_server_",
    question: "A HTTP POST request is to",
    options: [
      "request data",
      "delete data",
      "submit data",
    ],
    correctAnswer: "submit data"
  },
  {
    category: "web_server_",
    question: "A HTTP GET request is to",
    options: [
      "request data",
      "delete data",
      "submit data",
    ],
    correctAnswer: "request data"
  },
  {
    category: "web_server_",
    question: "A DNS A record translates to",
    options: [
      "an IP",
      "a domain",
    ],
    correctAnswer: "an IP"
  }
];
