export const applicationServerQuiz = [
  {
    category: "Application Server",
    question: "What's the purpose of running Gunicorn with multiple workers?",
    options: [
      "To save memory",
      "To handle more concurrent requests",
      "To use less CPU",
      "To reduce network traffic"
    ],
    correctAnswer: "To handle more concurrent requests"
  },
  {
    category: "Application Server",
    question: "Why is it important to configure Nginx as a reverse proxy?",
    options: [
      "To encrypt traffic",
      "To serve static files efficiently and forward dynamic requests to Gunicorn",
      "To reduce server load",
      "To block malicious traffic"
    ],
    correctAnswer: "To serve static files efficiently and forward dynamic requests to Gunicorn"
  },
  {
    category: "Application Server",
    question: "What's the benefit of using systemd to manage application server processes?",
    options: [
      "It makes the application faster",
      "It reduces memory usage",
      "It automatically starts processes after server reboot",
      "It improves security"
    ],
    correctAnswer: "It automatically starts processes after server reboot"
  },
  {
    category: "Application Server",
    question: "How does Gunicorn's master/workers infrastructure help with zero-downtime deployments?",
    options: [
      "By caching all requests",
      "By using less memory",
      "By gradually replacing workers with new code while maintaining service",
      "By running multiple applications simultaneously"
    ],
    correctAnswer: "By gradually replacing workers with new code while maintaining service"
  },
  {
    category: "Application Server",
    question: "What's the purpose of application server logging?",
    options: [
      "To make the application faster",
      "To save disk space",
      "To track errors and access patterns",
      "To encrypt sensitive data"
    ],
    correctAnswer: "To track errors and access patterns"
  }
];