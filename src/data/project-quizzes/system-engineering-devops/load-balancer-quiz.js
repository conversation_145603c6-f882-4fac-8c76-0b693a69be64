export const loadBalancerQuiz = [
  {
    category: "Load balancer",
    question: "What is the main purpose of a load balancer?",
    options: [
      "To block malicious traffic",
      "To distribute incoming traffic across multiple servers",
      "To cache web content",
      "To encrypt network traffic"
    ],
    correctAnswer: "To distribute incoming traffic across multiple servers"
  },
  {
    category: "Load balancer",
    question: "What is a round-robin algorithm in load balancing?",
    options: [
      "Routing traffic based on server location",
      "Distributing requests sequentially to each server in turn",
      "Sending traffic to the fastest responding server",
      "Blocking repeated requests from the same source"
    ],
    correctAnswer: "Distributing requests sequentially to each server in turn"
  },
  {
    category: "Load balancer",
    question: "What is the purpose of the X-Served-By HTTP header?",
    options: [
      "To encrypt the server response",
      "To identify which backend server handled the request",
      "To specify the content type",
      "To set the response cache time"
    ],
    correctAnswer: "To identify which backend server handled the request"
  },
  {
    category: "Load balancer",
    question: "Which software is commonly used as a load balancer in Ubuntu systems?",
    options: [
      "Apache",
      "Nginx",
      "HAProxy",
      "Tomcat"
    ],
    correctAnswer: "HAProxy"
  },
  {
    category: "Load balancer",
    question: "Why is redundancy important in web infrastructure?",
    options: [
      "To make websites load faster",
      "To save server costs",
      "To prevent single points of failure",
      "To reduce network traffic"
    ],
    correctAnswer: "To prevent single points of failure"
  }
];