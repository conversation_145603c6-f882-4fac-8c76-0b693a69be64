export const shellLoopsQuiz = [
  {
    category: "Loops, conditions and parsing",
    question: "In a shell script, which command generates an SSH key pair?",
    options: [
      "ssh-key",
      "ssh-keygen",
      "ssh-create",
      "ssh-make"
    ],
    correctAnswer: "ssh-keygen"
  },
  {
    category: "Loops, conditions and parsing",
    question: "Which loop is most appropriate when you want to execute code UNTIL a condition becomes true?",
    options: [
      "for loop",
      "while loop", 
      "until loop",
      "do loop"
    ],
    correctAnswer: "until loop"
  },
  {
    category: "Loops, conditions and parsing",
    question: "What is the correct shebang for a Bash script?",
    options: [
      "#!/bin/bash",
      "#!/usr/bin/env bash",
      "#/bin/bash",
      "#/usr/bin/bash"
    ],
    correctAnswer: "#!/usr/bin/env bash"
  },
  {
    category: "Loops, conditions and parsing",
    question: "What is the correct syntax for a basic if statement in Bash?",
    options: [
      "if [condition] then",
      "if (condition) then",
      "if [ condition ]; then",
      "if condition"
    ],
    correctAnswer: "if [ condition ]; then"
  },
  {
    category: "Loops, conditions and parsing",
    question: "In Bash, what is the IFS (Internal Field Separator) commonly used for?",
    options: [
      "To separate commands in a script",
      "To separate fields in text processing",
      "To separate files in a directory",
      "To separate users in a system"
    ],
    correctAnswer: "To separate fields in text processing"
  }
];