export const networkingBasics0Quiz = [
  {
    category: "Networking basics",
    question: "What is the OSI model?",
    options: [
      "Set of specifications that network hardware manufacturers must respect",
      "The OSI model is a conceptual model that characterizes the communication functions of a telecommunication system without regard to their underlying internal structure and technology",
      "The OSI model is a model that characterizes the communication functions of a telecommunication system with a strong regard for their underlying internal structure and technology"
    ],
    correctAnswer: "The OSI model is a conceptual model that characterizes the communication functions of a telecommunication system without regard to their underlying internal structure and technology"
  },
  {
    category: "Networking basics",
    question: "How is the OSI model organized?",
    options: [
      "Alphabetically",
      "From the lowest to the highest level",
      "Randomly"
    ],
    correctAnswer: "From the lowest to the highest level"
  },
  {
    category: "Networking basics",
    question: "What type of network is a computer in local connected to?",
    options: [
      "Internet",
      "WAN",
      "LAN"
    ],
    correctAnswer: "LAN"
  },
  {
    category: "Networking basics",
    question: "What is a MAC address?",
    options: [
      "The name of a network interface",
      "The unique identifier of a network interface",
      "A network interface"
    ],
    correctAnswer: "The unique identifier of a network interface"
  },
  {
    category: "Networking basics",
    question: "What is an IP address?",
    options: [
      "Is to devices connected to a network what postal address is to houses",
      "The unique identifier of a network interface",
      "Is a number that network devices use to connect to networks"
    ],
    correctAnswer: "Is to devices connected to a network what postal address is to houses"
  },
  {
    category: "Networking basics",
    question: "Which statement is correct for TCP?",
    options: [
      "It is a protocol that is transferring data in a slow way but surely",
      "It is a protocol that is transferring data in a fast way and might lose data along in the process"
    ],
    correctAnswer: "It is a protocol that is transferring data in a slow way but surely"
  },
  {
    category: "Networking basics",
    question: "Which statement is correct for UDP?",
    options: [
      "It is a protocol that is transferring data in a slow way but surely",
      "It is a protocol that is transferring data in a fast way and might lose data along in the process"
    ],
    correctAnswer: "It is a protocol that is transferring data in a fast way and might lose data along in the process"
  }
];