export const mysqlQuiz = [
  {
    category: "MySQL",
    question: "What are the advantages of having a replica database server?",
    options: [
      "Only for backup purposes",
      "Only for load distribution",
      "For both redundancy and load distribution",
      "Only for testing purposes"
    ],
    correctAnswer: "For both redundancy and load distribution"
  },
  {
    category: "MySQL",
    question: "What privilege is needed for a MySQL user to check replication status?",
    options: [
      "SUPER",
      "REPLICATION CLIENT",
      "SELECT",
      "ALL PRIVILEGES"
    ],
    correctAnswer: "REPLICATION CLIENT"
  },
  {
    category: "MySQL",
    question: "Which command would you use to create a backup of all MySQL databases?",
    options: [
      "mysql backup",
      "mysqldump --all-databases",
      "mysql export",
      "mysqlbackup"
    ],
    correctAnswer: "mysqldump --all-databases"
  },
  {
    category: "MySQL",
    question: "What does 'SHOW MASTER STATUS' display?",
    options: [
      "Database size",
      "Current user permissions",
      "Binary log position and file",
      "Server uptime"
    ],
    correctAnswer: "Binary log position and file"
  },
  {
    category: "MySQL",
    question: "Which of these is required for MySQL replication to work?",
    options: [
      "Both servers must have identical hardware",
      "Both servers must be in the same datacenter",
      "The replica must have access to the primary server's port (usually 3306)",
      "Both servers must run the same operating system"
    ],
    correctAnswer: "The replica must have access to the primary server's port (usually 3306)"
  }
];