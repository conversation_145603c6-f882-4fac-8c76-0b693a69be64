export const shellProcessesQuiz = [
  {
    category: "Processes and signals",
    question: "What is a PID?",
    options: [
      "Process Internal Description",
      "Process Identification Number",
      "Program Interface Description",
      "Program Identification Directory"
    ],
    correctAnswer: "Process Identification Number"
  },
  {
    category: "Processes and signals",
    question: "Which command can you use to list all processes?",
    options: [
      "ls -l",
      "ps aux",
      "top",
      "proc"
    ],
    correctAnswer: "ps aux"
  },
  {
    category: "Processes and signals",
    question: "Which command is used to terminate a process using its PID?",
    options: [
      "stop",
      "terminate",
      "kill",
      "end"
    ],
    correctAnswer: "kill"
  },
  {
    category: "Processes and signals",
    question: "What are the two signals that cannot be ignored in Linux?",
    options: [
      "SIGTERM and SIG<PERSON><PERSON><PERSON>",
      "SIGSTOP and SIGCONT",
      "SIGKILL and SIGSTOP",
      "SIGINT and SIGTERM"
    ],
    correctAnswer: "SIGKILL and SIGSTOP"
  },
  {
    category: "Processes and signals",
    question: "What happens to child processes when their parent process dies?",
    options: [
      "They continue running normally",
      "They become zombie processes",
      "They are automatically terminated",
      "They are adopted by init process"
    ],
    correctAnswer: "They are adopted by init process"
  }
];