export const webstackMonitoringQuiz = [
  {
    category: "Webstack Monitoring",
    question: "What are the two main categories of web stack monitoring?",
    options: [
      "Frontend and Backend monitoring",
      "Application and Server monitoring",
      "Network and Database monitoring",
      "Hardware and Software monitoring"
    ],
    correctAnswer: "Application and Server monitoring"
  },
  {
    category: "Webstack Monitoring",
    question: "What type of data does application monitoring focus on?",
    options: [
      "Server hardware status",
      "Network traffic",
      "Running software behavior and performance",
      "Database size"
    ],
    correctAnswer: "Running software behavior and performance"
  },
  {
    category: "Webstack Monitoring",
    question: "What are access logs used for in a web server?",
    options: [
      "To store user passwords",
      "To record who accessed the server and what they accessed",
      "To monitor CPU usage",
      "To track server uptime"
    ],
    correctAnswer: "To record who accessed the server and what they accessed"
  },
  {
    category: "Webstack Monitoring",
    question: "Which of these is NOT typically monitored in server monitoring?",
    options: [
      "CPU usage",
      "Memory usage",
      "User interface design",
      "Network load"
    ],
    correctAnswer: "User interface design"
  },
  {
    category: "Webstack Monitoring",
    question: "What's the main purpose of error logs in a web server?",
    options: [
      "To track successful requests",
      "To record server maintenance schedules",
      "To document failed requests and server errors",
      "To monitor user activity"
    ],
    correctAnswer: "To document failed requests and server errors"
  }
];