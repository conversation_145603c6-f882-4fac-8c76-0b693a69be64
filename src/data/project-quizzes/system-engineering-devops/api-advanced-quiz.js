export const apiAdvancedQuiz = [
  {
    category: "API Advanced",
    question: "What is pagination in the context of APIs?",
    options: [
      "A way to format API responses",
      "A method to split large amounts of data into smaller chunks",
      "A type of API authentication",
      "A way to compress API data"
    ],
    correctAnswer: "A method to split large amounts of data into smaller chunks"
  },
  {
    category: "API Advanced",
    question: "When working with the Reddit API, why is setting a custom User-Agent important?",
    options: [
      "To authenticate the request",
      "To prevent Too Many Requests errors",
      "To improve response speed",
      "To format the response data"
    ],
    correctAnswer: "To prevent Too Many Requests errors"
  },
  {
    category: "API Advanced",
    question: "What is the main advantage of using recursion for API calls?",
    options: [
      "It's faster than using loops",
      "It uses less memory",
      "It can handle paginated responses more elegantly",
      "It's required by the Reddit API"
    ],
    correctAnswer: "It can handle paginated responses more elegantly"
  },
  {
    category: "API Advanced",
    question: "What should your code do when encountering an invalid subreddit in the Reddit API?",
    options: [
      "Follow the redirect",
      "Return None or 0",
      "Keep trying until successful",
      "Raise an exception"
    ],
    correctAnswer: "Return None or 0"
  },
  {
    category: "API Advanced",
    question: "How should keywords be handled when counting occurrences in titles?",
    options: [
      "They should be case-sensitive",
      "They should be case-insensitive",
      "They should only count exact matches",
      "They should include partial matches"
    ],
    correctAnswer: "They should be case-insensitive"
  }
];