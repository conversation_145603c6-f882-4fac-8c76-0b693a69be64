// Onboarding quizzes
import { welcomeQuiz } from './onboarding/welcome.js';
import { intranetQuiz } from './onboarding/intranet.js';
import { mindsetQuiz } from './onboarding/mindset.js';
import { map_your_mindQuiz as mapYourMindQuiz } from './onboarding/map-your-mind.js';
import { learningCommunityQuiz } from './onboarding/learning-community.js';
import { networkQuiz } from './onboarding/network.js';
import { gritQuiz } from './onboarding/grit.js';
import { owningLearningQuiz } from './onboarding/owning-learning.js';
import { mental_healthQuiz as mentalHealthQuiz } from './onboarding/mental-health.js';
import { tweetQuiz } from './onboarding/tweet.js';

// Tools quizzes
import { shellBasicsQuiz } from './tools/shell-basics.js';
import { shellNavigationQuiz } from './tools/shell-navigation.js';
import { emacsQuiz } from './tools/emacs.js';
import { emacsAdvancedQuiz } from './tools/emacs-advanced.js';
import { viQuiz } from './tools/vi.js';
import { viAdvancedQuiz } from './tools/vi-advanced.js';
import { gitQuiz } from './tools/git.js';
import { techQuiz as professionalTechQuiz } from './tools/professional-tech.js';

// Low Level Programming quizzes
import { hello_worldQuiz as helloWorldQuiz } from './low-level/hello-world.js';
import { functionsQuiz } from './low-level/functions.js';
import { moreFunctionsQuiz } from './low-level/more_functions.js';
import { pointers_quizQuiz as pointersQuiz } from './low-level/pointers-quiz.js';
import { more_pointers_quizQuiz as morePointersQuiz } from './low-level/more-pointers-quiz.js';
import { even_more_pointers_quizQuiz as evenMorePointersQuiz } from './low-level/even-more-pointers-quiz.js';
import { recursion_quizQuiz as recursionQuiz } from './low-level/recursion-quiz.js';
import { static_libraries_quizQuiz as staticLibrariesQuiz } from './low-level/static-libraries-quiz.js';
import { argv_quizQuiz as argvQuiz } from './low-level/argv-quiz.js';
import { malloc_quizQuiz as mallocQuiz } from './low-level/malloc-quiz.js';
import { more_malloc_quizQuiz as moreMallocQuiz } from './low-level/more-malloc-quiz.js';
import { structures_quizQuiz as structuresQuiz } from './low-level/structures-quiz.js';
import { preprocessor_quizQuiz as preprocessorQuiz } from './low-level/preprocessor-quiz.js';
import { function_pointers_quizQuiz as functionPointersQuiz } from './low-level/function-pointers-quiz.js';
import { sorting_quizQuiz as sortingQuiz } from './low-level/sorting-quiz.js';
import { variadicQuiz } from './low-level/variadic-quiz.js';
// System Engineering & DevOps quizzes
import { shellBasicsQuiz as sysDevShellBasicsQuiz } from './system-engineering-devops/shell-basics-quiz.js';
import { shellPermissionsQuiz } from './system-engineering-devops/shell-permissions-quiz.js';
import { shellIoRedirectionsQuiz } from './system-engineering-devops/shell-io-redirections-quiz.js';
import { shellVariablesQuiz } from './system-engineering-devops/shell-variables-quiz.js';
import { shellLoopsQuiz } from './system-engineering-devops/shell-loops-quiz.js';
import { shellProcessesQuiz } from './system-engineering-devops/shell-processes-quiz.js';
import { shellRegexQuiz } from './system-engineering-devops/shell-regex-quiz.js';
import { networkingBasics0Quiz } from './system-engineering-devops/networking-basics-0-quiz.js';

// Higher-level programming quizzes
import { warmup_quizQuiz as javascriptWarmupQuiz } from './higher-level/javascript/warmup-quiz.js';
import { objects_quizQuiz as javascriptObjectsQuiz } from './higher-level/javascript/objects-quiz.js';
import { javascriptWebScrapingQuiz } from './higher-level/javascript/web-scraping-quiz.js';
import { jquery_quizQuiz as javascriptJqueryQuiz } from './higher-level/javascript/jquery-quiz.js';

// Python quizzes
import { hello_world_quizQuiz as pythonHelloWorldQuiz } from './higher-level/python/hello-world-quiz.js';
import { if_else_loops_functions_quizQuiz as pythonIfElseLoopsFunctionsQuiz } from './higher-level/python/if-else-loops-functions-quiz.js';
import { import_modules_quizQuiz as pythonImportModulesQuiz } from './higher-level/python/import-modules-quiz.js';
import { data_structures_quizQuiz as pythonDataStructuresQuiz } from './higher-level/python/data-structures-quiz.js';
import { more_data_structures_quizQuiz as pythonMoreDataStructuresQuiz } from './higher-level/python/more-data-structures-quiz.js';
import { pythonExceptionsQuiz } from './higher-level/python/exceptions-quiz.js';
import { pythonClassesObjectsQuiz } from './higher-level/python/classes-objects-quiz.js';
import { test_driven_development_quizQuiz as pythonTestDrivenDevelopmentQuiz } from './higher-level/python/test-driven-development-quiz.js';
import { more_classes_quizQuiz as pythonMoreClassesQuiz } from './higher-level/python/more-classes-quiz.js';
import { pythonEverythingIsObjectQuiz } from './higher-level/python/everything-is-object-quiz.js';
import { inheritance_quizQuiz as pythonInheritanceQuiz } from './higher-level/python/inheritance-quiz.js';
import { pythonInputOutputQuiz } from './higher-level/python/input-output-quiz.js';
import { pythonAlmostCircleQuiz } from './higher-level/python/almost-circle-quiz.js';
import { pythonORMQuiz } from './higher-level/python/orm-quiz.js';
import { network_0_quizQuiz as pythonNetwork0Quiz } from './higher-level/python/network-0-quiz.js';
import { pythonNetwork1Quiz } from './higher-level/python/network-1-quiz.js';

// SQL quizzes
import { introduction_quizQuiz as sqlIntroductionQuiz } from './higher-level/sql/introduction-quiz.js';
import { more_queries_quizQuiz as sqlMoreQueriesQuiz } from './higher-level/sql/more-queries-quiz.js';

// AirBnB quizzes
import { airbnbConsoleQuiz } from './higher-level/airbnb/console-quiz.js';
import { web_static_quizQuiz as airbnbWebStaticQuiz } from './higher-level/airbnb/web-static-quiz.js';
import { airbnbMysqlQuiz } from './higher-level/airbnb/mysql-quiz.js';
import { deploy_static_quizQuiz as airbnbDeployStaticQuiz } from './higher-level/airbnb/deploy-static-quiz.js';
import { airbnbWebFrameworkQuiz } from './higher-level/airbnb/web-framework-quiz.js';
import { airbnbRestApiQuiz } from './higher-level/airbnb/rest-api-quiz.js';
import { airbnbWebDynamicQuiz } from './higher-level/airbnb/web-dynamic-quiz.js';

// Additional low-level programming quizzes
import { variables_quizQuiz as variablesQuiz } from './low-level/variables-quiz.js';
import { debuggingQuiz } from './low-level/debugging-quiz.js';
import { printfQuiz } from './low-level/printf-quiz.js';
import { hashTablesQuiz } from './low-level/hash-tables-quiz.js';
import { binaryTreesQuiz } from './low-level/binary-trees-quiz.js';
import { searchQuiz } from './low-level/search-quiz.js';
import { linked_lists_quizQuiz as linkedListsQuiz } from './low-level/linked-lists-quiz.js';
import { moreLinkedListsQuiz } from './low-level/more-linked-lists-quiz.js';
import { bit_manipulation_quizQuiz as bitManipulationQuiz } from './low-level/bit-manipulation-quiz.js';
import { file_io_quizQuiz as fileIoQuiz } from './low-level/file-io-quiz.js';
import { simpleShellQuiz } from './low-level/simple-shell-quiz.js';
import { doubly_linked_lists_quizQuiz as doublyLinkedListsQuiz } from './low-level/doubly-linked-lists-quiz.js';
import { stacks_queues_quizQuiz as stacksQueuesQuiz } from './low-level/stacks-queues-quiz.js';

// Additional system engineering devops quizzes
import { networking_basics_1_quizQuiz as networkingBasics1Quiz } from './system-engineering-devops/networking-basics-1-quiz.js';
import { web_infrastructure_quizQuiz as webInfrastructureQuiz } from './system-engineering-devops/web-infrastructure-quiz.js';
import { configManagementQuiz } from './system-engineering-devops/config-management-quiz.js';
import { sshQuiz } from './system-engineering-devops/ssh-quiz.js';
import { web_server_quizQuiz as webServerQuiz } from './system-engineering-devops/web-server-quiz.js';
import { webStackDebugging0Quiz } from './system-engineering-devops/web-stack-debugging-0-quiz.js';
import { webStackDebugging1Quiz } from './system-engineering-devops/web-stack-debugging-1-quiz.js';
import { loadBalancerQuiz } from './system-engineering-devops/load-balancer-quiz.js';
import { https_ssl_quizQuiz as httpsSslQuiz } from './system-engineering-devops/https-ssl-quiz.js';
import { webStackDebugging2Quiz } from './system-engineering-devops/web-stack-debugging-2-quiz.js';
import { firewall_quizQuiz as firewallQuiz } from './system-engineering-devops/firewall-quiz.js';
import { mysqlQuiz } from './system-engineering-devops/mysql-quiz.js';
import { apiQuiz } from './system-engineering-devops/api-quiz.js';
import { apiAdvancedQuiz } from './system-engineering-devops/api-advanced-quiz.js';
import { webstackMonitoringQuiz } from './system-engineering-devops/webstack-monitoring-quiz.js';
import { applicationServerQuiz } from './system-engineering-devops/application-server-quiz.js';

// Additional tools quizzes
import { vagrantQuiz } from './tools/vagrant.js';
import { gitAdvancedQuiz } from './tools/git-advanced.js';

// Combined export object
export const projectQuizzes = {
  // Map project slugs to their corresponding quizzes
  'welcome-onboard': welcomeQuiz,
  'intranet': intranetQuiz,
  'mindset': mindsetQuiz,
  'map-your-mind': mapYourMindQuiz,
  'learning-community': learningCommunityQuiz,
  'network': networkQuiz,
  'grit-assignment': gritQuiz,
  'owning-learning': owningLearningQuiz,
  'mental-health': mentalHealthQuiz,
  'tweet-a-day': tweetQuiz,

  // Tools quizzes
  'shell-basics': shellBasicsQuiz,
  'shell-navigation': shellNavigationQuiz,
  'emacs': emacsQuiz,
  'emacs-advanced': emacsAdvancedQuiz,
  'vi': viQuiz,
  'vi-advanced': viAdvancedQuiz,
  'git': gitQuiz,
  'professional-tech': professionalTechQuiz,

  // Zero-day quizzes (reusing tool quizzes with different keys)
  'zero-day-shell': shellNavigationQuiz,
  'zero-day-emacs': emacsQuiz,
  'zero-day-vi': viQuiz,
  'zero-day-git': gitQuiz,
  'zero-day-tech': professionalTechQuiz,

  // Low Level Programming quizzes
  'hello-world': helloWorldQuiz,
  'functions': functionsQuiz,
  'more-functions': moreFunctionsQuiz,
  'pointers': pointersQuiz,
  'more-pointers': morePointersQuiz,
  'even-more-pointers': evenMorePointersQuiz,
  'recursion': recursionQuiz,
  'static-libraries': staticLibrariesQuiz,
  'argv': argvQuiz,
  'argc-argv': argvQuiz,
  'malloc': mallocQuiz,
  'more-malloc': moreMallocQuiz,
  'structures': structuresQuiz,
  'preprocessor': preprocessorQuiz,
  'function-pointers': functionPointersQuiz,
  'sorting': sortingQuiz,
  'variadic': variadicQuiz,
  // System Engineering & DevOps quizzes
  'shell-basics-devops': sysDevShellBasicsQuiz,
  'shell-permissions': shellPermissionsQuiz,
  'shell-io-redirections': shellIoRedirectionsQuiz,
  'shell-variables': shellVariablesQuiz,
  'shell-loops': shellLoopsQuiz,
  'shell-processes': shellProcessesQuiz,
  'shell-regex': shellRegexQuiz,
  'networking-basics-0': networkingBasics0Quiz,

  // Higher-level programming quizzes
  'javascript-warm-up': javascriptWarmupQuiz,
  'javascript-objects': javascriptObjectsQuiz,
  'javascript-web-scraping': javascriptWebScrapingQuiz,
  'javascript-web-jquery': javascriptJqueryQuiz,

  // Python quizzes
  'python-hello-world': pythonHelloWorldQuiz,
  'python-if-else': pythonIfElseLoopsFunctionsQuiz,
  'python-import': pythonImportModulesQuiz,
  'python-data-structures': pythonDataStructuresQuiz,
  'python-more-data': pythonMoreDataStructuresQuiz,
  'python-exceptions': pythonExceptionsQuiz,
  'python-classes': pythonClassesObjectsQuiz,
  'python-test-driven': pythonTestDrivenDevelopmentQuiz,
  'python-more-classes': pythonMoreClassesQuiz,
  'python-everything-object': pythonEverythingIsObjectQuiz,
  'python-inheritance': pythonInheritanceQuiz,
  'python-input-output': pythonInputOutputQuiz,
  'python-almost-circle': pythonAlmostCircleQuiz,
  'python-object-relational': pythonORMQuiz,
  'python-network-0': pythonNetwork0Quiz,
  'python-network-1': pythonNetwork1Quiz,

  // SQL quizzes
  'sql-introduction': sqlIntroductionQuiz,
  'sql-more-queries': sqlMoreQueriesQuiz,

  // AirBnB quizzes
  'airbnb-clone-console': airbnbConsoleQuiz,
  'airbnb-clone-web-static': airbnbWebStaticQuiz,
  'airbnb-clone-mysql': airbnbMysqlQuiz,
  'airbnb-clone-deploy': airbnbDeployStaticQuiz,
  'airbnb-clone-web-framework': airbnbWebFrameworkQuiz,
  'airbnb-clone-restful': airbnbRestApiQuiz,
  'airbnb-clone-web-dynamic': airbnbWebDynamicQuiz,

  // Additional low-level programming quizzes
  'variables': variablesQuiz,
  'debugging': debuggingQuiz,
  'printf': printfQuiz,
  'hash-tables': hashTablesQuiz,
  'binary-trees': binaryTreesQuiz,
  'search': searchQuiz,
  'linked-lists': linkedListsQuiz,
  'singly-linked-lists': linkedListsQuiz,
  'more-linked-lists': moreLinkedListsQuiz,
  'more-singly-linked-lists': moreLinkedListsQuiz,
  'bit-manipulation': bitManipulationQuiz,
  'file-io': fileIoQuiz,
  'simple-shell': simpleShellQuiz,
  'doubly-linked-lists': doublyLinkedListsQuiz,
  'stack-queues': stacksQueuesQuiz,

  // Additional system engineering devops quizzes
  'networking-basics-1': networkingBasics1Quiz,
  'web-infrastructure': webInfrastructureQuiz,
  'config-management': configManagementQuiz,
  'configuration-management': configManagementQuiz,
  'ssh': sshQuiz,
  'web-server': webServerQuiz,
  'web-stack-debugging-0': webStackDebugging0Quiz,
  'web-stack-debugging-1': webStackDebugging1Quiz,
  'load-balancer': loadBalancerQuiz,
  'https-ssl': httpsSslQuiz,
  'web-stack-debugging-2': webStackDebugging2Quiz,
  'firewall': firewallQuiz,
  'mysql': mysqlQuiz,
  'api': apiQuiz,
  'api-advanced': apiAdvancedQuiz,
  'webstack-monitoring': webstackMonitoringQuiz,
  'application-server': applicationServerQuiz,

  // Additional tools quizzes
  'vagrant': vagrantQuiz,
  'git-advanced': gitAdvancedQuiz
};
