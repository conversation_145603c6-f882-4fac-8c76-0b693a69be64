export const evaluation0Quiz = {
  id: 'evaluation-0',
  title: 'Evaluation Quiz #0',
  duration: 840, // 14 minutes in seconds
  totalQuestions: 14,
  description: "A comprehensive evaluation covering C programming, Linux commands, and system concepts.",
  rules: {
    timeLimit: "14 minutes",
    scoring: {
      correct: 1,
      incorrect: -1,
      skip: 0
    },
    resources: "No external resources allowed"
  },
  questions: [
    {
      category: "C Programming",
      question: "What shape will this code print?\n\``\`c\n#include <stdio.h>\n\n/**\n* print_shape - function to print a shape\n* /\nvoid print_shape(int num1, int num2) \n{\n    int idx1, idx2;\n    for (idx1 = 0; idx1 < num1; idx1++)\n    {\n        for (idx2 = 0; idx2 < num2; idx2++)\n        {\n            printf('#');\n        }\n        printf('\\n');\n    }\n}\n\n/** \n* main - calls print_shape()\n*\n* Return: Always 0.\n**/\n\nint main(void)\n{\n    print_shape(4, 3);\n    return (0);\n}\``\`",
      options: [
        "A rectangle with the character \`#\` with a height of 4 and width of 3",
        "A rectangle with the character \`#\` with a height of 3 and width of 4",
        "A triangle with the character \`#\` with a base of 4 and height of 3",
        "A rectangle with the character \`*\` with a height of 4 and width of 3",
        "I don't know"
      ],
      correctAnswer: "A rectangle with the character \`#\` with a height of 4 and width of 3"
    },
    {
      category: "C Programming",
      question: "What does this code print?\n\``\`c\n* print_something - function to print something\n*\n* Return: Always 0\n**/\nint print_something(int num)\n{\n    int i, j;\n    for (i = 0; i < num + 1; i++)\n    {\n        for (j = 0; j < num + 1; j++)\n        {\n            printf(\"%d\", i * j);\n            if (j < num)\n            {\n                printf(\", \");\n            }\n        }\n        printf(\"\\n\");\n    }\n    return (0);\n}\``\`",
      options: [
        "The \`n\` times table, starting with 0",
        "The \`n\` times table, excluding zero",
        "The numbers 0 to \`n\`, \`n\` times",
        "I don't know"
      ],
      correctAnswer: "The \`n\` times table, starting with 0"
    },
    {
      category: "Linux",
      question: "Which command should I use to display the exit code of the previous command?",
      options: [
        "echo ?",
        "echo $EXITCODE",
        "echo $CODE",
        "echo $?",
        "I don't know"
      ],
      correctAnswer: "echo $?"
    },
    {
      category: "C Programming",
      question: "What is the output of the following piece of code?\n\``\`c\nint i;\n\ni = 10;\nwhile (i < 20)\n{\n    printf(\"%d\", i % 2);\n    i++;\n}\``\`",
      options: [
        "0101010101",
        "0123456789",
        "1010101010",
        "I don't know"
      ],
      correctAnswer: "0101010101"
    },
    {
      category: "C Programming",
      question: "Which of these loop statements exist in C?",
      options: [
        "for",
        "while",
        "foreach",
        "do...while",
        "loop_to",
        "each",
        "I don't know"
      ],
      correctAnswers: ["for", "while", "do...while"]
    },
    {
      category: "C Programming",
      question: "What information do the \`printf\` statements tell us about how our code is executed?\n\``\`c\n#include \"school.h\"\n\n/**\n* main - prints even numbers from 0 to 100\n* Return: 0\n*/\n\nint main(void)\n{\n        int i;\n\n        printf(\"Before loop\\n\");\n\n        for (i = 0; i < 100; i++)\n        {\n                if (i % 2 != 0)\n                {\n                        printf(\"i is not even so don't print\\n\");\n                        continue;\n                }\n                else\n                {\n                        printf(\"i is even, break to print\\n\");\n                        break;\n                }\n\n                printf(\"Outside of if/else, still inside for loop\\n\");\n\n                printf(\"%d\\n\", i);\n        }\n\n        printf(\"For loop exited\\n\");\n\n        return(0);\n}\``\`",
      options: [
        "A \`printf\` statement shows when the for loop is finished",
        "A \`printf\` statement shows exactly how many times the loop executes",
        "\`printf\` statements shows that break will cause \"For loop exited\" to print, indicating that the even number is never printed",
        "A \`printf\` statement shows that there is an infinite loop in the code",
        "I don't know"
      ],
      correctAnswers: [
        "A \`printf\` statement shows when the for loop is finished",
        "A \`printf\` statement shows exactly how many times the loop executes",
        "\`printf\` statements shows that break will cause \"For loop exited\" to print, indicating that the even number is never printed"
      ]
    },
    {
      category: "C Programming",
      question: "What is the problem with the following C code?\n\``\`c\n#include <stdio.h>\n\n/**\n* main - main function \n* Return: 0\n*/\n\nint main(void)\n{\n        int i;\n\n        i = 65;\n\n        while (i < 91)\n        {\n                putchar(i);\n        }\n\n        return (0);\n}\``\`",
      options: [
        "\`i\` is not incremented in the while loop, so an infinite loop occurs",
        "\`i\` is not incremented outside the loop, so an infinite loop occurs",
        "You cannot use integers in \`putchar()\`",
        "I don't know"
      ],
      correctAnswer: "\`i\` is not incremented in the while loop, so an infinite loop occurs"
    },
    {
      category: "Linux",
      question: "Which command should I use for changing a file owner?",
      options: [
        "su",
        "chmod",
        "chown",
        "chgrp",
        "I don't know"
      ],
      correctAnswer: "chown"
    },
    {
      category: "C Programming",
      question: "What are the different steps to form an executable file from C source code?",
      options: [
        "Interpretation, compilation and assembly",
        "Preprocessing, compilation, assembly, and linking",
        "Interpretation, assembly and compilation",
        "Compilation and linking",
        "Preprocessing and compilation",
        "I don't know"
      ],
      correctAnswer: "Preprocessing, compilation, assembly, and linking"
    },
    {
      category: "Linux",
      question: "How do you change directory on Linux?",
      options: [
        "pwd",
        "cd",
        "ls",
        "which",
        "I don't know"
      ],
      correctAnswer: "cd"
    },
    {
      category: "Linux",
      question: "Which symbol should I use to redirect the error output to the standard output?",
      options: [
        "2>&1",
        "1>&2",
        "2>",
        "I don't know"
      ],
      correctAnswer: "2>&1"
    },
    {
      category: "C Programming",
      question: "What is the size of the \`float\` data type on a 64-bit machine?",
      options: [
        "1 byte",
        "2 bytes",
        "4 bytes",
        "8 bytes",
        "I don't know"
      ],
      correctAnswer: "4 bytes"
    },
    {
      category: "Linux",
      question: "What command would you use to list files on Linux?",
      options: [
        "ls",
        "which",
        "cd",
        "pwd",
        "list",
        "I don't know"
      ],
      correctAnswer: "ls"
    },
    {
      category: "Linux",
      question: "What is the numerical value for the \`r-xr--r--\` permission?",
      options: [
        "522",
        "544",
        "644",
        "411",
        "I don't know"
      ],
      correctAnswer: "544"
    }
  ]
};
