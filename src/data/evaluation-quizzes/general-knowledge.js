export const generalKnowledgeQuiz = {
  id: 'general-knowledge',
  title: 'General Knowledge',
  duration: 1800,
  totalQuestions: 66,
  description: "A broad assessment of foundational knowledge covering math, programming, and computer science concepts.",
  rules: {
    timeLimit: "30 minutes",
    scoring: {
      correct: 1,
      incorrect: -1,
      skip: 0
    },
    resources: "No external resources allowed (Internet, Google, man pages)",
  },
  questions: [
    // Math Questions (1-11)
    {
      category: "Math",
      question: "98 + 42 = ?",
      options: ["130", "120", "100", "140", "None of the above", "I don't know"],
      correctAnswer: "140"
    },
    {
      category: "Math",
      question: "98 - 42 = ?",
      options: ["56", "58", "62", "48", "None of the above", "I don't know"],
      correctAnswer: "56"
    },
    {
      category: "Math",
      question: "42 * 2 = ?",
      options: ["58", "56", "64", "84", "None of the above", "I don't know"],
      correctAnswer: "84"
    },
    {
      category: "Math",
      question: "98 / 2 = ?",
      options: ["48", "56", "58", "49", "None of the above", "I don't know"],
      correctAnswer: "49"
    },
    {
      category: "Math",
      question: "(4 * 3) = (24 / 2)",
      options: ["True", "False", "I don't know"],
      correctAnswer: "True"
    },
    {
      category: "Math",
      question: "(45 / 5) > 8",
      options: ["False", "True", "I don't know"],
      correctAnswer: "True"
    },
    {
      category: "Math",
      question: "((45 / 5) + 4) < 12",
      options: ["True", "False", "I don't know"],
      correctAnswer: "False"
    },
    {
      category: "Math",
      question: "12 / 0 = ?",
      options: ["120", "0", "12", "None of the above", "I don't know"],
      correctAnswer: "None of the above"
    },
    {
      category: "Math",
      question: "What is the result of 12 % 2?",
      options: ["2", "1", "0", "I don't know"],
      correctAnswer: "0"
    },
    {
      category: "Math",
      question: "What is the result of 12 % 3?",
      options: ["2", "1", "0", "I don't know"],
      correctAnswer: "0"
    },
    {
      category: "Math",
      question: "What is the result of 12 % 10?",
      options: ["1", "0", "2", "I don't know"],
      correctAnswer: "2"
    },

    // Binary Questions (12-16)
    {
      category: "Binary",
      question: "Binary uses what base?",
      options: ["base 1", "base 16", "base 8", "base 2", "I don't know"],
      correctAnswer: "base 2"
    },
    {
      category: "Binary",
      question: "In binary, what is each digit referred to as?",
      options: ["Bit", "Byte", "I don't know"],
      correctAnswer: "Bit"
    },
    {
      category: "Binary",
      question: "How is the decimal number 0 represented in binary?",
      options: ["00", "11", "1", "0", "I don't know"],
      correctAnswer: "0"
    },
    {
      category: "Binary",
      question: "How is the decimal number 3 represented in binary?",
      options: ["1", "0", "11", "00", "I don't know"],
      correctAnswer: "11"
    },
    {
      category: "Binary",
      question: "How is the decimal number 9 represented in binary?",
      options: ["1111", "111", "0101", "1001", "I don't know"],
      correctAnswer: "1001"
    },

    // Matrices Questions (17-18)
    {
      category: "Matrices",
      question: "What are the dimensions of this matrix?\n|1 6 4 9|\n|3 2 8 5|\n|4 0 2 7|",
      options: ["3x4", "4x3", "1x3", "I don't know"],
      correctAnswer: "3x4"
    },
    {
      category: "Matrices",
      question: "In programming, how are matrices normally stored?",
      options: ["As key value pairs", "As an array of arrays", "As a dictionary", "As a List", "I don't know"],
      correctAnswer: "As an array of arrays"
    },

    // Javascript Questions (19-24)
    {
      category: "Javascript",
      question: "Does Javascript have String as a native data type?",
      options: ["No", "Yes", "I don't know"],
      correctAnswer: "Yes"
    },
    {
      category: "Javascript",
      question: "What does let mean?\n(please check all true answers)",
      options: [
        "It's the keyword to define a variable that can be re-assign during the execution",
        "It's the keyword to define a constant variable",
        "It's the keyword to define a global variable",
        "It's the keyword to define a variable with optionally initializing it to a value",
        "It's the keyword to define a variable in the local scope",
        "I don't know"
      ],
      correctAnswers: [
        "It's the keyword to define a variable that can be re-assign during the execution",
        "It's the keyword to define a variable with optionally initializing it to a value",
        "It's the keyword to define a variable in the local scope"
      ]
    },
    {
      category: "Javascript",
      question: "What is the output of this javascript code?\n\``\`javascript\nfunction myFunction(a) {\n  console.log(a);\n}\n\nmyFunction(12);\n\``\`",
      options: ["2", "12", "1", "I don't know"],
      correctAnswer: "12"
    },
    {
      category: "Javascript",
      question: "What is the output of this javascript code?\n\``\`javascript\nconst number = 12;\nfunction myFunction(a) {\n  console.log(a);\n}\n\nmyFunction(number);\n\``\`",
      options: ["1", "12", "2", "I don't know"],
      correctAnswer: "12"
    },
    {
      category: "Javascript",
      question: "What is the output of this javascript code?\n\``\`javascript\nconst b = 79;\nfunction myFunction(a) {\n  console.log(a + b);\n}\n\nmyFunction(10);\n\``\`",
      options: ["89", "79", "10", "I don't know"],
      correctAnswer: "89"
    },
    {
      category: "Javascript",
      question: "What is the output of this javascript code?\n\``\`javascript\nlet b = 1;\nfunction myFunction(a) {\n  console.log(a + b);\n  b = a;\n}\n\nmyFunction(3);\nmyFunction(4);\n\``\`",
      options: ["3, 4", "4, 3", "4, 7", "3, 7", "I don't know"],
      correctAnswer: "4, 7"
    },

    // SQL Questions (25-31)
    {
      category: "SQL",
      question: "What does SQL stand for?",
      options: [
        "Sequences of Query Logic",
        "Solid Query Language",
        "Structured Query Language",
        "Structured Question Language",
        "I don't know"
      ],
      correctAnswer: "Structured Query Language"
    },
    {
      category: "SQL",
      question: "What is a relational database?\n(please select all correct answers)",
      options: [
        "data are organized by tables, records and columns",
        "a database",
        "a collection of data",
        "a table contains only one object representation",
        "a table contains multiple object representation",
        "married databases",
        "data are organized by tables and indexes",
        "I don't know"
      ],
      correctAnswers: [
        "data are organized by tables, records and columns",
        "a database",
        "a collection of data",
        "data are organized by tables and indexes"
      ]
    },
    {
      category: "SQL",
      question: "What does DDL stand for?",
      options: [
        "Data Definition Language",
        "Database Definition Language",
        "Document Data Language",
        "Data Document Language",
        "I don't know"
      ],
      correctAnswer: "Data Definition Language"
    },
    {
      category: "SQL",
      question: "What does DML stand for?",
      options: [
        "Document Model Language",
        "Data Manipulation Language",
        "Document Manipulation Language",
        "Database Manipulation Language",
        "I don't know"
      ],
      correctAnswer: "Data Manipulation Language"
    },
    {
      category: "SQL",
      question: "How do you list all users in this table?\nTable: users (id int, name varchar(256), age int)",
      options: [
        "DELETE * FROM users;",
        "SELECT * FROM users;",
        "SELECT ALL users;",
        "ALL users;",
        "I don't know"
      ],
      correctAnswer: "SELECT * FROM users;"
    },
    {
      category: "SQL",
      question: "How do you add a new record in the table users?\nTable: users (id int, name varchar(256), age int)",
      options: [
        "INSERT INTO users (id, name, age) VALUES (2, \"Betty\", 30);",
        "INSERT INTO users (id, name) VALUES (2, \"Betty\");",
        "INSERT INTO users (id, age) VALUES (2, 30);",
        "INSERT users (id, age) VALUES (2, \"Betty\", 30);",
        "I don't know"
      ],
      correctAnswer: "INSERT INTO users (id, name, age) VALUES (2, \"Betty\", 30);"
    },
    {
      category: "SQL",
      question: "What is an example of a relational database?\n(choose all that apply)",
      options: [
        "Redis",
        "Oracle",
        "Key-value stores",
        "Cassandra",
        "MySQL",
        "Mongo DB",
        "PostgreSQL",
        "I don't know"
      ],
      correctAnswers: ["Oracle", "MySQL", "PostgreSQL"]
    },

    // Big O Questions (32-34)
    {
      category: "Big O",
      question: "What is the time complexity of this function / algorithm?\n\``\`c\nvoid f(int n) {\n  printf(\"n = %d\\n\", n);\n}\``\`",
      options: [
        "O(n!)", "O(log(n!))", "O(n^2)", "O(log(n))", 
        "O(2^n)", "O(1)", "O(n)", "O(n log n)", "I don't know"
      ],
      correctAnswer: "O(1)"
    },
    {
      category: "Big O",
      question: "What is the time complexity of this function / algorithm?\n\``\`c\nvoid f(int n) {\n  int i;\n  for (i = 0; i < n; i++) {\n    printf(\"%d\\n\", i);\n  }\n}\``\`",
      options: [
        "O(1)", "O(n^2)", "O(log(n!))", "O(2^n)", 
        "O(log(n))", "O(n)", "O(n log n)", "O(n!)", "I don't know"
      ],
      correctAnswer: "O(n)"
    },
    {
      category: "Big O",
      question: "What is the time complexity of this function / algorithm?\n\``\`c\nvoid f(int n) {\n  int i;\n  for (i = 0; i < n; i++) {\n    for(int j = 0; j < n; j++){\n      printf(\"%d\\n\", i);\n    }\n  }\n}\``\`",
      options: [
        "O(log(n!))", "O(n^2)", "O(n)", "O(n!)", 
        "O(log(n))", "O(2^n)", "O(n log n)", "O(1)", "I don't know"
      ],
      correctAnswer: "O(n^2)"
    },

    // HTML/CSS Questions (35-45)
    {
      category: "HTML/CSS",
      question: "What does HTML stand for?",
      options: [
        "Hypertext markup language",
        "Hot test more loss",
        "Highly Textual marking list",
        "I don't know"
      ],
      correctAnswer: "Hypertext markup language"
    },
    {
      category: "HTML/CSS",
      question: "Choose the correct HTML element for the largest heading:",
      options: ["<head>", "<h6>", "<heading>", "<h1>", "I don't know"],
      correctAnswer: "<h1>"
    },
    {
      category: "HTML/CSS",
      question: "What is the correct HTML element for inserting a line break?",
      options: ["<break>", "<br>", "<lb>", "I don't know"],
      correctAnswer: "<br>"
    },
    {
      category: "HTML/CSS",
      question: "What is the correct HTML for adding a background color?",
      options: [
        "<body bg=\"yellow\">",
        "<body style=\"background-color:yellow;\">",
        "<background>yellow</background>",
        "I don't know"
      ],
      correctAnswer: "<body style=\"background-color:yellow;\">"
    },
    {
      category: "HTML/CSS",
      question: "Choose the correct HTML element to define emphasized text:",
      options: ["<i>", "<italic>", "<emphasize>", "<em>", "I don't know"],
      correctAnswer: "<em>"
    },
    {
      category: "HTML/CSS",
      question: "What is the correct HTML for creating a hyperlink?",
      options: [
        "<a:http://www.google.com></a>",
        "<a href=\"http://www.google.com\">Google</a>",
        "<a name=\"http://www.google.com\">Google</a>",
        "<a url=\"http://www.google.com\">Google</a>",
        "I don't know"
      ],
      correctAnswer: "<a href=\"http://www.google.com\">Google</a>"
    },
    {
      category: "HTML/CSS",
      question: "Which character is used to indicate an end tag?",
      options: ["*", ":", "$", "/", "I don't know"],
      correctAnswer: "/"
    },
    {
      category: "HTML/CSS",
      question: "What does CSS stand for?",
      options: [
        "Cascading style sheets",
        "Creative style sheets",
        "Colorful style sheets",
        "C style sheets",
        "I don't know"
      ],
      correctAnswer: "Cascading style sheets"
    },
    {
      category: "HTML/CSS",
      question: "What is the correct HTML for referring to an external style sheet?",
      options: [
        "<style src=\"mystyle.css\">",
        "<link rel=\"stylesheet\" type=\"text/css\" href=\"mystyle.css\">",
        "<stylesheet>mystyle.css</stylesheet>",
        "I don't know"
      ],
      correctAnswer: "<link rel=\"stylesheet\" type=\"text/css\" href=\"mystyle.css\">"
    },
    {
      category: "HTML/CSS",
      question: "When in an HTML document is the correct place to refer to an external style sheet?",
      options: ["In the <body> section", "In the <head> section", "At the end of the document", "I don't know"],
      correctAnswer: "In the <head> section"
    },
    {
      category: "HTML/CSS",
      question: "Which HTML tag is used to define an internal style sheet?",
      options: ["<css>", "<style>", "<script>", "<import>", "<link>", "I don't know"],
      correctAnswer: "<style>"
    },

    // ASCII Questions (46-48)
    {
      category: "ASCII",
      question: "What is ASCII?",
      options: [
        "Alpha source code for information exchange",
        "A short code for converting letters",
        "American standard code for information interchange",
        "Alphabetical source code for information integration",
        "I don't know"
      ],
      correctAnswer: "American standard code for information interchange"
    },
    {
      category: "ASCII",
      question: "What is the ASCII value of A?",
      options: ["65", "1", "a", "A", "I don't know"],
      correctAnswer: "65"
    },
    {
      category: "ASCII",
      question: "What is the following word 67 65 84 in ASCII?",
      options: ["GYM", "CAT", "CAR", "BAR", "I don't know"],
      correctAnswer: "CAT"
    },

    // Python Questions (49-57)
    {
      category: "Python",
      question: "Which example below is declaring an array in Python?",
      options: ["[1, 2, 3]", "Array == 1, 2, 3", "I don't know"],
      correctAnswer: "[1, 2, 3]"
    },
    {
      category: "Python",
      question: "Indents are unimportant in Python.",
      options: ["False", "True", "I don't know"],
      correctAnswer: "False"
    },
    {
      category: "Python",
      question: "Is Python interpreted or compiled?",
      options: ["Compiled", "Interpreted", "Both", "I don't know"],
      correctAnswer: "Interpreted"
    },
    {
      category: "Python",
      question: "Who created Python?",
      options: [
        "Julien Barbier",
        "Bob Dylan",
        "Guido van Rossum",
        "Yukihiro Matsumoto",
        "I don't know"
      ],
      correctAnswer: "Guido van Rossum"
    },
    {
      category: "Python",
      question: "What does this python code print?\n>>> print(\"Tech School\")",
      options: ["\"Tech School\"", "'Tech School'", "School", "Tech School", "I don't know"],
      correctAnswer: "Tech School"
    },
    {
      category: "Python",
      question: "What does this python code print?\nif 12 == 48/4:\n  print(\"Tech\")\nelse:\n  print(\"School\")",
      options: ["Tech", "School", "I don't know"],
      correctAnswer: "Tech"
    },
    {
      category: "Python",
      question: "What does this python code print?\nfor i in range(4):\n  print(i, end=\" \")",
      options: ["1 2 3 4", "0 1 2 3", "1 2 3", "0 1 2 3 4", "I don't know"],
      correctAnswer: "0 1 2 3"
    },
    {
      category: "Python",
      question: "What does this command line print?\n>>> print(\"{:d} Battery street\".format(98))",
      options: ["98 Battery street", "8 Battery street", "\"98 Battery street\"", "9 Battery street", "I don't know"],
      correctAnswer: "98 Battery street"
    },
    {
      category: "Python",
      question: "What does this command line print?\n>>> a = \"Python is cool\"\n>>> print(a[4])",
      options: ["o", "P", "n", "Python is cool Python is cool Python is cool Python is cool", "I don't know"],
      correctAnswer: "o"
    },

    // Linux Questions (58-66)
    {
      category: "Linux",
      question: "What character is used to escape special characters in Linux?",
      options: ["| (pipe)", "ESC (escape)", "\\ (backslash)", "/ (slash)", "I don't know"],
      correctAnswer: "\\ (backslash)"
    },
    {
      category: "Linux",
      question: "What is an example of a wild card in Linux?",
      options: ["echo", "{a..z}", "*", ".", "I don't know"],
      correctAnswer: "*"
    },
    {
      category: "Linux",
      question: "In Linux, what is the superuser account?",
      options: ["The computer's IP address", "Superuser", "root", "I don't know"],
      correctAnswer: "root"
    },
    {
      category: "Linux",
      question: "Is Linux case-sensitive?",
      options: ["No", "Yes", "I don't know"],
      correctAnswer: "Yes"
    },
    {
      category: "Linux",
      question: "How do you print a listing of the current directory's files and directories?",
      options: ["list", "ls", "cd", "pwd", "I don't know"],
      correctAnswer: "ls"
    },
    {
      category: "Linux",
      question: "How do you print a \"long listing\", which includes extra details such as permissions, ownership, file size, and timestamps?",
      options: ["ls -a", "ls", "ls -l", "ls -1", "I don't know"],
      correctAnswer: "ls -l"
    },
    {
      category: "Linux",
      question: "How do you change directory on Linux?",
      options: ["pwd", "which", "cd", "ls", "I don't know"],
      correctAnswer: "cd"
    },
    {
      category: "Linux",
      question: "What does RTFM stand for?",
      options: [
        "Remember The First Manipulation",
        "Read, Teach, Forget, Migrate",
        "Read The F** Manual",
        "Read The Fine Manual",
        "I don't know"
      ],
      correctAnswer: "Read The F** Manual"
    }
  ]
};
