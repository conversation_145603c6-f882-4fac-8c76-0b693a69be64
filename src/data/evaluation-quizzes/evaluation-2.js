export const evaluation2Quiz = {
  id: 'evaluation-2',
  title: 'Evaluation Quiz #2',
  duration: 2940, // 49 minutes in seconds
  totalQuestions: 16,
  description: "A comprehensive evaluation covering binary operations, Python, C programming, and system calls.",
  rules: {
    timeLimit: "49 minutes",
    scoring: {
      correct: 1,
      incorrect: -1,
      skip: 0
    },
    resources: "No external resources allowed"
  },
  questions: [
    {
      category: "Binary Operations",
      question: "\`0x13 << 1\` = ?",
      options: [
        "0x13",
        "0x12",
        "0x26",
        "0x4C",
        "I don't know"
      ],
      correctAnswer: "0x26"
    },
    {
      category: "Python",
      question: "What does this command line print?\n\``\`python\n>>> a = \"Hello, world!\"\n>>> print(a[:5])\``\`",
      options: [
        "Hello",
        "world!",
        "orld!",
        "I don't know"
      ],
      correctAnswer: "Hello"
    },
    {
      category: "Number Systems",
      question: "What is \`98\` in base 16?",
      options: [
        "0x62",
        "0x98",
        "0x96",
        "I don't know"
      ],
      correctAnswer: "0x62"
    },
    {
      category: "System Calls",
      question: "What is the \`unistd\` symbolic constant for the standard input?",
      options: [
        "STDIN_FILENO",
        "STDOUT_FILENO",
        "STDERR_FILENO",
        "I don't know"
      ],
      correctAnswer: "STDIN_FILENO"
    },
    {
      category: "System Programming",
      question: "Without context, on Ubuntu 14.04 LTS, \`write\` is a(n) …",
      options: [
        "executable",
        "system call",
        "library call",
        "game",
        "I don't know"
      ],
      correctAnswers: ["executable", "system call", "library call"]
    },
    {
      category: "Number Systems",
      question: "What is\`0b001010010\` in base10?",
      options: [
        "81",
        "82",
        "83",
        "84",
        "I don't know"
      ],
      correctAnswer: "82"
    },
    {
      category: "Python",
      question: "What does this print?\n\``\`python\n>>> print(\"My favorite line of {} is {:d}.\".format(\"The Zen of Python\", 11))\``\`",
      options: [
        "My favorite line of The Zen of Python is 11.",
        "My favorite line of T is 1.",
        "My favorite line of The Zen of Python is 1.",
        "I don't know"
      ],
      correctAnswer: "My favorite line of The Zen of Python is 11."
    },
    {
      category: "C Programming",
      question: "The following code gives this incorrect output. Which of the following statements about what is causing the error is true?\n\``\`c\nint i;\nint j;\n\nfor (i = 0; i < 10; i++)\n{\n    j = 0;\n    while (j < 10)\n    {\n        printf(\"%d\", j);\n    }\n    printf(\"\\n\");\n}\``\`",
      options: [
        "j never increments so it will always be less than 10",
        "j is always equal to i so the loop will never end",
        "j never increments so it is always going to print 0",
        "I don't know"
      ],
      correctAnswers: [
        "j never increments so it will always be less than 10",
        "j never increments so it is always going to print 0"
      ]
    },
    {
      category: "Python",
      question: "Choose the line of code to replace the comment below so the function prints a given string without a lower or uppercase \`c\`.\n\``\`python\ndef no_c_print(s):\n    new_string = ''\n    for character in s:\n        # REPLACE THIS LINE\n        new_string += character\n    print(new_string)\``\`",
      options: [
        "if character not in 'Cc':",
        "if character != 'c' and character != 'C':",
        "if character != \"cC\"",
        "I don't know"
      ],
      correctAnswers: [
        "if character not in 'Cc':",
        "if character != 'c' and character != 'C':"
      ]
    },
    {
      category: "System Programming",
      question: "When I am using \`O_WRONLY | O_CREAT | O_APPEND\` -> the \`|\` are bitwise operators.",
      options: [
        "True",
        "False",
        "I don't know"
      ],
      correctAnswer: "True"
    },
    {
      category: "Binary Operations",
      question: "\`~ 0x98 =\` ?",
      options: [
        "0x66",
        "0x67",
        "0x68",
        "I don't know"
      ],
      correctAnswer: "0x67"
    },
    {
      category: "C Programming",
      question: "In the \`main.c\` file, on what line is the first error that the compiler returns?",
      options: [
        "9",
        "11",
        "7",
        "I don't know"
      ],
      correctAnswer: "11"
    },
    {
      category: "C Programming",
      question: "What are the different steps to form an executable file from C source code?",
      options: [
        "Interpretation, compilation and assembly",
        "Preprocessing, compilation, assembly, and linking",
        "Interpretation, assembly and compilation",
        "Compilation and linking",
        "Preprocessing and compilation",
        "I don't know"
      ],
      correctAnswer: "Preprocessing, compilation, assembly, and linking"
    },
    {
      category: "Python",
      question: "Choose a statement that would complete the function that returns a string made up of \`+\` \`n\` number of times (assuming \`n\` > 0).\n\``\`python\ndef print_plus(n):\n    # REPLACE THIS LINE\``\`",
      options: [
        "return n*'+'",
        "return '+'*n",
        "return ''+n",
        "return\`+n+n+n\`",
        "I don't know"
      ],
      correctAnswers: ["return n*'+'", "return '+'*n"]
    },
    {
      category: "C Programming",
      question: "What is wrong with the following code?\n\``\`c\nint n = 0;\nint array[5];\nint i = 5;\n\narray[n] = i;\``\`",
      options: [
        "Nothing is wrong",
        "It is impossible to declare the variable \`array\` this way",
        "The array \`array\` is not entirely initialized",
        "It is not possible to access \`array[n]\`",
        "I don't know"
      ],
      correctAnswer: "Nothing is wrong"
    },
    {
      category: "System Programming",
      question: "What is the correct combination of \`oflags\` used to open a file with the mode write only, create it if it doesn't exist and append new content at the end if it already exists?",
      options: [
        "O_WRONLY",
        "O_WRONLY | O_CREAT | O_EXCL",
        "O_WRONLY | O_CREAT | O_APPEND",
        "O_RDWR | O_CREAT | O_APPEND",
        "I don't know"
      ],
      correctAnswer: "O_WRONLY | O_CREAT | O_APPEND"
    }
  ]
};
