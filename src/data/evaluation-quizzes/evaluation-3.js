export const evaluation3Quiz = {
  id: 'evaluation-3',
  title: 'Evaluation Quiz #3',
  duration: 1500, // 25 minutes in seconds
  totalQuestions: 17,
  description: "A comprehensive evaluation covering Python programming, data structures, and system concepts.",
  rules: {
    timeLimit: "25 minutes",
    scoring: {
      correct: 1,
      incorrect: -1,
      skip: 0
    },
    resources: "No external resources allowed"
  },
  questions: [
    {
      category: "System Calls",
      question: "What is the \`unistd\` symbolic constant for the standard output?",
      options: [
        "STDIN_FILENO",
        "STDOUT_FILENO",
        "STDERR_FIELNO",
        "I don't know"
      ],
      correctAnswer: "STDOUT_FILENO"
    },
    {
      category: "Python",
      question: "What do these lines print?\n\``\`python\n>>> def my_function(counter=89):\n>>>     return counter + 1\n>>> \n>>> print(my_function())\``\`",
      options: [
        "1",
        "89",
        "90",
        "891",
        "I don't know"
      ],
      correctAnswer: "90"
    },
    {
      category: "Data Structures",
      question: "In a doubly linked list, what's the \"head\" of a linked list?",
      options: [
        "It's the node with the pointer to the next node equals to \`NULL\`",
        "It's the node with the pointer to the previous node equals to \`NULL\`",
        "I don't know"
      ],
      correctAnswer: "It's the node with the pointer to the previous node equals to \`NULL\`"
    },
    {
      category: "Data Structures",
      question: "In a doubly linked list, what are possible directions to traverse it?",
      options: [
        "Forward",
        "Backward",
        "I don't know"
      ],
      correctAnswers: ["Forward", "Backward"]
    },
    {
      category: "Python",
      question: "What does this print?\n\``\`python\n>>> print(\"{:d} Mission street, {}\".format(972, \"San Francisco\"))\``\`",
      options: [
        "\"972 Mission street, San Francisco\"",
        "72 Mission street, San",
        "972 Mission street, San Francisco",
        "San Francisco Mission street, 972",
        "I don't know"
      ],
      correctAnswer: "972 Mission street, San Francisco"
    },
    {
      category: "Memory Management",
      question: "How many bytes will this statement allocate on a 64 bit machine?\n\``\`c\nmalloc(sizeof(char) * 4)\``\`",
      options: [
        "4",
        "8",
        "12",
        "16",
        "I don't know"
      ],
      correctAnswer: "4"
    },
    {
      category: "Data Structures",
      question: "You're standing in line at a grocery store, which data type best represents this situation?",
      options: [
        "Queue",
        "Array",
        "Dictionary",
        "Stack",
        "I don't know"
      ],
      correctAnswer: "Queue"
    },
    {
      category: "Python",
      question: "What do these lines print?\n\``\`python\n>>> def my_function(counter=89):\n>>>     print(\"Counter: {}\".format(counter))\n>>> \n>>> my_function(12)\``\`",
      options: [
        "Counter: 12",
        "Counter: 89",
        "Counter: 101",
        "I don't know"
      ],
      correctAnswer: "Counter: 12"
    },
    {
      category: "C Programming",
      question: "What's wrong with the following C code to get the nth node of a linked list?\n\``\`c\nlistint_t *get_nodeint_at_index(listint_t *head, unsigned int index)\n{\n        unsigned int i;\n        listint_t *ptr;\n\n        if (head == NULL)\n                return (NULL);\n\n        ptr = head;\n        i = 0;\n\n        while (i < index)\n        {\n                ptr = ptr->next;\n                i++;\n        }\n\n        return (ptr);\n}\``\`",
      options: [
        "There is no check for if \`ptr->next\` is \`NULL\` before moving \`ptr\`",
        "The function should not return \`NULL\` if \`head\` is not found.",
        "If \`index\` is out of range, the program should return \`NULL\`",
        "Nothing is wrong",
        "I don't know"
      ],
      correctAnswers: [
        "There is no check for if \`ptr->next\` is \`NULL\` before moving \`ptr\`",
        "If \`index\` is out of range, the program should return \`NULL\`"
      ]
    },
    {
      category: "Python",
      question: "What do these lines print?\n\``\`python\n>>> a = [1, 2, 3, 4]\n>>> a[2] = 10\n>>> a\``\`",
      options: [
        "[1, 2, 3, 4]",
        "[1, 10, 3, 4]",
        "[1, 2, 10, 4]",
        "[1, 2, 10, 10]",
        "I don't know"
      ],
      correctAnswer: "[1, 2, 10, 4]"
    },
    {
      category: "Python",
      question: "What do these lines print?\n\``\`python\nfor i in range(2, 10, 2):\n    print(i, end=\" \")\``\`",
      options: [
        "2 3 4 5 6 7 8 9 10",
        "2 3 4 5 6 7 8 9",
        "4 6 8 10 12 14 16 18",
        "2 4 6 8",
        "I don't know"
      ],
      correctAnswer: "2 4 6 8"
    },
    {
      category: "Python",
      question: "What does this print?\n\``\`python\n>>> a = \"Python is cool\"\n>>> print(a[7:-5])\``\`",
      options: [
        "on",
        "nohtyP",
        "Python",
        "si",
        "is",
        "I don't know"
      ],
      correctAnswer: "is"
    },
    {
      category: "Python",
      question: "What do these lines print?\n\``\`python\n>>> a = { 'id': 89, 'name': \"John\", 'projects': [1, 2, 3, 4], 'friends': [ { 'id': 82, 'name': \"Bob\" }, { 'id': 83, 'name': \"Amy\" } ] }\n>>> a.get('friends')[-1].get(\"name\")\``\`",
      options: [
        "89",
        "[{'id':82, 'name':\"Bob\"}, {'id':83, 'name': \"Amy\"}]",
        "'Amy'",
        "'Bob'",
        "Nothing",
        "I don't know"
      ],
      correctAnswer: "'Amy'"
    },
    {
      category: "Python",
      question: "What is a circular import in Python?",
      options: [
        "When two or more modules are dependant on each other.",
        "When you import a module for calculating dimensions for circles.",
        "When one module imports multiple other modules.",
        "I don't know"
      ],
      correctAnswer: "When two or more modules are dependant on each other."
    },
    {
      category: "System Programming",
      question: "Which symbol should I use to redirect the error output to the standard output?",
      options: [
        "2>&1",
        "1>&2",
        "2>",
        "I don't know"
      ],
      correctAnswer: "2>&1"
    },
    {
      category: "Python",
      question: "Which line of code will create a list of every other number from 0 to 10 in reverse in Python?",
      options: [
        "list(range(10, 0, -2))",
        "array(range(10, 0, -2))",
        "list(range(0, 10, -2))",
        "array(10, 0, 2))",
        "I don't know"
      ],
      correctAnswer: "list(range(10, 0, -2))"
    },
    {
      category: "Python",
      question: "What do these lines print?\n\``\`python\na = 12\nif a > 2:\n    if a % 2 == 0:\n        print(\"Tech\")\n    else:\n        print(\"C is fun\")\nelse:\n    print(\"School\")\``\`",
      options: [
        "Tech",
        "C is fun",
        "School",
        "I don't know"
      ],
      correctAnswer: "Tech"
    }
  ]
};
