export const evaluation1Quiz = {
  id: 'evaluation-1',
  title: 'Evaluation Quiz #1',
  duration: 1800, // 30 minutes in seconds
  totalQuestions: 15,
  description: "A comprehensive evaluation covering pointers, memory management, and C programming concepts.",
  rules: {
    timeLimit: "30 minutes",
    scoring: {
      correct: 1,
      incorrect: -1,
      skip: 0
    },
    resources: "No external resources allowed"
  },
  questions: [
    {
      category: "Memory Management",
      question: "What is the size of a pointer to an \`int\` (on a 64-bit architecture)",
      options: [
        "1 byte",
        "2 bytes",
        "4 bytes",
        "8 bytes",
        "I don't know"
      ],
      correctAnswer: "8 bytes"
    },
    {
      category: "Pointers",
      question: "What is the value of \`n\` after the following code is executed?\n\``\`c\nint n = 98;\nint *p = &n;\n\n*p++;\``\`",
      options: [
        "0",
        "98",
        "99",
        "402",
        "I don't know"
      ],
      correctAnswer: "98"
    },
    {
      category: "Memory Management",
      question: "Are there any memory leaks with the following code (on a 64-bit architecture)?\n\``\`c\n#include <stdio.h>\n#include <stdlib.h>\n\n/**\n * struct list_s - singly linked list\n * @str: string - (malloc'ed string)\n * @len: length of the string\n * @next: points to the next node\n *\n * Description: singly linked list node structure\n * for your project\n */\ntypedef struct list_s\n{\n        char *str;\n        unsigned int len;\n        struct list_s *next;\n} list_t;\n\nint main(void)\n{\n        list_t *node = NULL;\n        node = malloc(sizeof(list_t));\n\n        node->len = 3;\n\n        node->str = malloc(sizeof(char) * node->len);\n        node->str[0] = 'H';\n        node->str[1] = 'i';\n        node->str[2] = '\\0';\n\n        node->next = NULL;\n\n        free(node);\n\n        return (0);\n}\``\`",
      options: [
        "Yes, 3 bytes of memory were lost",
        "No, no memory leaks were possible",
        "Yes, 24 bytes of memory were lost",
        "Yes, 15 bytes of memory were lost",
        "I don't know"
      ],
      correctAnswer: "Yes, 3 bytes of memory were lost"
    },
    {
      category: "Memory Management",
      question: "The memory space reserved when calling \`malloc\` is on:",
      options: [
        "The heap",
        "The stack",
        "I don't know"
      ],
      correctAnswer: "The heap"
    },
    {
      category: "Libraries",
      question: "What command(s) can be used to list the symbols stored in a static library?",
      options: [
        "nm",
        "ranlib",
        "ar",
        "ld",
        "I don't know"
      ],
      correctAnswers: ["nm", "ar"]
    },
    {
      category: "Memory Management",
      question: "How many bytes will this statement allocate on a 64-bit machine?\n\``\`c\nmalloc(sizeof(int) * 4)\``\`",
      options: [
        "4",
        "8",
        "16",
        "32",
        "I don't know"
      ],
      correctAnswer: "16"
    },
    {
      category: "Pointers",
      question: "What is the size of \`*p\` in this code on a 64-bit machine?\n\``\`c\nint **p;\``\`",
      options: [
        "4 bytes",
        "8 bytes",
        "16 bytes",
        "I don't know"
      ],
      correctAnswer: "8 bytes"
    },
    {
      category: "Function Pointers",
      question: "This \`void (*anjula[])(int, float)\` is:",
      options: [
        "A pointer to a function that takes an \`int\` and a \`float\` as parameters and returns nothing",
        "A pointer to a function that takes an array of \`int\` and \`float\` as a parameter and returns nothing",
        "A pointer to a function that takes an \`int\` and a \`float\` as parameters and returns an empty array",
        "An array of pointers to functions that take an \`int\` and a \`float\` as parameters and returns nothing",
        "A pointer to an array of functions that take an \`int\` and a \`float\` as parameters and returns nothing",
        "I don't know"
      ],
      correctAnswer: "An array of pointers to functions that take an \`int\` and a \`float\` as parameters and returns nothing"
    },
    {
      category: "Arrays",
      question: "What is wrong with the following code?\n\``\`c\nint n = 5;\nint array[5];\nint i = 3;\n\narray[n] = i;\``\`",
      options: [
        "Nothing is wrong",
        "It is impossible to declare the variable \`array\` this way",
        "The array \`array\` is not entirely initialized",
        "While it is possible to access \`array[n]\`, we are not supposed to as this is not the array anymore",
        "I don't know"
      ],
      correctAnswer: "While it is possible to access \`array[n]\`, we are not supposed to as this is not the array anymore"
    },
    {
      category: "Recursion",
      question: "What does this code print?\n\``\`c\nvoid print(int nb)\n{\n    printf(\"%d\", nb);\n    -- nb;\n    if (nb > 0) \n    {\n        print(nb);\n    }\n}\n\nint main(void)\n{\n    print(4);\n    return (0);\n}\``\`",
      options: [
        "4321",
        "43210",
        "321",
        "3210",
        "I don't know"
      ],
      correctAnswer: "4321"
    },
    {
      category: "Data Structures",
      question: "How much space would you need to allocate for a list node with the following structure on a 64-bit machine?\n\``\`c\n/**\n * struct list_s - singly linked list\n * @str: string - (malloc'ed string)\n * @len: length of the string\n * @next: points to the next node\n *\n * Description: singly linked list node structure\n * for your project\n */\ntypedef struct list_s\n{\n    char *str;\n    unsigned int len;\n    struct list_s *next;\n} list_t;\``\`",
      options: [
        "20 bytes",
        "It's impossible to know without knowing what \`str\` is",
        "24 bytes",
        "32 bytes",
        "I don't know"
      ],
      correctAnswer: "20 bytes"
    },
    {
      category: "Memory Management",
      question: "How many bytes will this statement allocate on a 64-bit machine?\n\``\`c\nmalloc(sizeof(char) * 10)\``\`",
      options: [
        "10",
        "20",
        "40",
        "80",
        "I don't know"
      ],
      correctAnswer: "10"
    },
    {
      category: "Structs",
      question: "Given this code:\n\``\`c\nstruct point {\n   int x;\n   int y;\n};\nstruct point my_point = { 3, 7 };\nstruct point *p = &my_point;\``\`\nTo set the member \`y\` of my variable \`my_point\` to \`98\`, I can do (select all valid answers):",
      options: [
        "my_point.y = 98;",
        "my_point->y = 98;",
        "p.y = 98;",
        "(*p).y = 98;",
        "p->y = 98;",
        "I don't know"
      ],
      correctAnswers: ["my_point.y = 98;", "(*p).y = 98;", "p->y = 98;"]
    },
    {
      category: "Preprocessor",
      question: "What does the macro \`TABLESIZE\` expand to?\n\``\`c\n#define BUFSIZE 1020\n#define TABLESIZE BUFSIZE\n#undef BUFSIZE\n#define BUFSIZE 37\``\`",
      options: [
        "1020",
        "37",
        "nothing",
        "I don't know"
      ],
      correctAnswer: "37"
    },
    {
      category: "Operators",
      question: "What is the result of \`12 % 3\`?",
      options: [
        "0",
        "1",
        "2",
        "3",
        "4",
        "I don't know"
      ],
      correctAnswer: "0"
    }
  ]
};
