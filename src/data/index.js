import { welcomeOnboard } from './projects/onboarding/welcome-onboard';
import { mentalHealth } from './projects/onboarding/mental-health';
import { learningCommunity } from './projects/onboarding/learning-community';
import { networking } from './projects/onboarding/networking';
import { intranet } from './projects/onboarding/intranet';
import { shellNavigation as navigation } from './projects/tools/shell-navigation';
import { gitProject } from './projects/tools/git';
import { vi as viProject } from './projects/tools/vi';
import { emacs as emacsProject } from './projects/tools/emacs';
import { vagrant as vagrantProject } from './projects/tools/vagrant';
import { slackIntro } from './projects/onboarding/slack-intro';
import { mindset } from './projects/onboarding/mindset';
import { mapYourMind } from './projects/onboarding/map-your-mind';
import { tweetADay } from './projects/onboarding/tweet-a-day';
import { gritAssignment } from './projects/onboarding/grit-assignment';
import { owningLearning } from './projects/onboarding/owning-learning';
import { git } from './projects/alx-zero_day/git-1';
import { professionalTech } from './projects/tools/professional-tech';
import { realBuddies } from './projects/onboarding/real-buddies';

// Import low-level programming projects
import { helloWorld } from './projects/low-level/hello-world';
import { cProgrammingFirstDay } from './projects/low-level/c-programming-first-day';
import { variables } from './projects/low-level/variables';
import { functions } from './projects/low-level/functions';
import { debugging } from './projects/low-level/debugging';
import { moreFunctions } from './projects/low-level/more-functions';
import { pointers } from './projects/low-level/pointers';
import { morePointers } from './projects/low-level/more-pointers';
import { evenMorePointers } from './projects/low-level/even-more-pointers';
import { recursion } from './projects/low-level/recursion';
import { staticLibraries } from './projects/low-level/static-libraries';
import { argv } from './projects/low-level/argv';
import { malloc } from './projects/low-level/malloc';
import { moreMalloc } from './projects/low-level/more-malloc';
import { preprocessor } from './projects/low-level/preprocessor';
import { structures } from './projects/low-level/structures';
import { functionPointers } from './projects/low-level/function-pointers';
import { variadic } from './projects/low-level/variadic';
import { printf } from './projects/low-level/printf';
import { hashTables } from './projects/low-level/hash-tables';
import { sorting } from './projects/low-level/sorting';
import { makefiles } from './projects/low-level/makefiles';
import { binaryTrees } from './projects/low-level/binary-trees';
import { search } from './projects/low-level/search';
import { linkedLists } from './projects/low-level/linked-lists';
import { moreLinkedLists } from './projects/low-level/more-linked-lists';
import { bitManipulation } from './projects/low-level/bit-manipulation';
import { fileIo } from './projects/low-level/file-io';
import { simpleShell } from './projects/low-level/simple-shell';
import { dynamicLibraries } from './projects/low-level/dynamic-libraries';
import { doublyLinkedLists } from './projects/low-level/doubly-linked-lists';
import { stackQueues } from './projects/low-level/stack-queues';

// Import system engineering devops projects
import { systemEngineeringDevops } from './projects/system-engineering-devops';

// Import higher-level programming projects
import { pythonProjects } from './projects/higher-level/python';
import { airbnbProjects } from './projects/higher-level/airbnb';
import { sqlProjects } from './projects/higher-level/sql';
import { javascriptProjects } from './projects/higher-level/javascript';

// Import more projects
import { moreProjects } from './projects/more';

// Import portfolio projects
import { portfolioProjects } from './projects/portfolio';

/**
 * Project content supports two formats:
 * 
 * 1. Simple string format (legacy):
 *    export const project = \`# Title\ncontent...\`
 * 
 * 2. Object format (new):
 *    export const project = {
 *      title: "Project Title",
 *      status: "project-status",
 *      content: \`# Title\ncontent...\`
 *    }
 */
export const projectContent = {
  'real-buddies': realBuddies,
  'professional-tech': professionalTech,
  'welcome-onboard': welcomeOnboard,
  'shell-navigation': navigation,
  'git': gitProject,
  'vi': viProject,
  'emacs': emacsProject,
  'vagrant': vagrantProject,
  'slack-intro': slackIntro,
  'mindset': mindset,
  'map-your-mind': mapYourMind,
  'grit-assignment': gritAssignment,
  'owning-learning': owningLearning,
  'git-03': git,
  'tweet-a-day': tweetADay,
  'mental-health': mentalHealth,
  'learning-community': learningCommunity,
  'networking': networking,
  'intranet': intranet,

  // Low-level programming projects
  'hello-world': helloWorld,
  'c-programming-first-day': cProgrammingFirstDay,
  'variables': variables,
  'functions': functions,
  'debugging': debugging,
  'more-functions': moreFunctions,
  'pointers': pointers,
  'even-more-pointers': evenMorePointers,
  'more-pointers': morePointers,
  'recursion': recursion,
  'static-libraries': staticLibraries,
  'argv': argv,
  'argc-argv': argv,
  'malloc': malloc,
  'more-malloc': moreMalloc,
  'preprocessor': preprocessor,
  'structures': structures,
  'function-pointers': functionPointers,
  'variadic': variadic,
  'printf': printf,
  'hash-tables': hashTables,
  'sorting': sorting,
  'makefiles': makefiles,
  'binary-trees': binaryTrees,
  'search': search,
  'linked-lists': linkedLists,
  'singly-linked-lists': linkedLists,
  'more-linked-lists': moreLinkedLists,
  'more-singly-linked-lists': moreLinkedLists,
  'bit-manipulation': bitManipulation,
  'file-io': fileIo,
  'simple-shell': simpleShell,
  'doubly-linked-lists': doublyLinkedLists,
'dynamic-libraries': dynamicLibraries,
  'stack-queues': stackQueues,

  // System Engineering & DevOps projects
  'shell-basics': systemEngineeringDevops[0],
  'shell-permissions': systemEngineeringDevops[1],
  'shell-io-redirections': systemEngineeringDevops[2],
  'shell-variables': systemEngineeringDevops[3],
  'shell-loops': systemEngineeringDevops[4],
  'loops-conditions': systemEngineeringDevops[4],
  'shell-processes': systemEngineeringDevops[5],
  'processes-signals': systemEngineeringDevops[5],
  'shell-regex': systemEngineeringDevops[6],
  'regular-expressions': systemEngineeringDevops[6],
  'networking-basics-0': systemEngineeringDevops[7],
  'networking-basics-1': systemEngineeringDevops[8],
  'web-infrastructure': systemEngineeringDevops[9],
  'config-management': systemEngineeringDevops[10],
  'configuration-management': systemEngineeringDevops[10],
  'ssh': systemEngineeringDevops[11],
  'web-server': systemEngineeringDevops[12],
  'web-stack-debugging-0': systemEngineeringDevops[13],
  'web-stack-debugging-1': systemEngineeringDevops[14],
  'load-balancer': systemEngineeringDevops[15],
  'https-ssl': systemEngineeringDevops[16],
  'web-stack-debugging-2': systemEngineeringDevops[17],
  'firewall': systemEngineeringDevops[20],
  'mysql': systemEngineeringDevops[21],
  'api': systemEngineeringDevops[22],
  'api-advanced': systemEngineeringDevops[23],
  'webstack-monitoring': systemEngineeringDevops[24],
  'application-server': systemEngineeringDevops[25],
  'web-stack-debugging-3': systemEngineeringDevops[18],
  'web-stack-debugging-4': systemEngineeringDevops[19],
  'what-happens-when': systemEngineeringDevops[26],
  'what-happens-url': systemEngineeringDevops[26],
  'postmortem': systemEngineeringDevops[27],

  // Higher-level programming projects
  'python-hello-world': pythonProjects.helloWorld,
  'python-if-else': pythonProjects.ifElseLoopsFunctions,
  'python-import': pythonProjects.importModules,
  'python-data-structures': pythonProjects.dataStructures,
  'python-more-data': pythonProjects.moreDataStructures,
  'python-exceptions': pythonProjects.exceptions,
  'python-classes': pythonProjects.classesObjects,
  'python-test-driven': pythonProjects.testDrivenDevelopment,
  'python-more-classes': pythonProjects.moreClasses,
  'python-everything-object': pythonProjects.everythingIsObject,
  'python-inheritance': pythonProjects.inheritance,
  'python-input-output': pythonProjects.inputOutput,
  'python-almost-circle': pythonProjects.almostCircle,
  'python-object-relational': pythonProjects.orm,
  'python-network-0': pythonProjects.network0,
  'python-network-1': pythonProjects.network1,

  // AirBnB Clone projects
  'airbnb-clone-console': airbnbProjects.console,
  'airbnb-clone-web-static': airbnbProjects.webStatic,
  'airbnb-clone-mysql': airbnbProjects.mysql,
  'airbnb-clone-deploy': airbnbProjects.deployStatic,
  'airbnb-clone-web-framework': airbnbProjects.webFramework,
  'airbnb-clone-restful': airbnbProjects.restApi,
  'airbnb-clone-web-dynamic': airbnbProjects.webDynamic,

  // SQL projects
  'sql-introduction': sqlProjects.introduction,
  'sql-more-queries': sqlProjects.moreQueries,

  // JavaScript projects
  'javascript-warm-up': javascriptProjects.warmup,
  'javascript-objects': javascriptProjects.objects,
  'javascript-web-scraping': javascriptProjects.webScraping,
  'javascript-web-jquery': javascriptProjects.jquery,

  // More! projects
  'rsa-factoring': moreProjects.rsaFactoring,
  'command-line-win': moreProjects.commandLineWin,
  'fix-my-code-0': moreProjects.fixMyCode0,
  'attack-is-best-defense': moreProjects.attackIsBestDefense,
  'fix-my-code-1': moreProjects.fixMyCode1,

  // Portfolio projects
  'portfolio-research-1': portfolioProjects.research1,
  'portfolio-research-2': portfolioProjects.research2,
  'portfolio-research-3': portfolioProjects.research3,
  'portfolio-week-1': portfolioProjects.week1,
  'portfolio-week-2': portfolioProjects.week2,
  'portfolio-landing-page': portfolioProjects.landingPage,
  'portfolio-presentation': portfolioProjects.presentation,
  'portfolio-cleanup': portfolioProjects.cleanup,
  'portfolio-blog-post': portfolioProjects.blogPost
};
