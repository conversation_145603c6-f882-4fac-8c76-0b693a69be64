export const julyProjects = {
  // Week 1
  "1": [
    { id: "python-import", title: "0x02. Python - import & modules" }
  ],
  "2": [
    { id: "python-import", title: "0x02. Python - import & modules" }
  ],
  "3": [
    { id: "python-import", title: "0x02. Python - import & modules" }
  ],
  "4": [
    { id: "python-import", title: "0x02. Python - import & modules" }
  ],
  // Week 2
  "7": [
    { id: "python-data-structures", title: "0x03. Python - Data Structures: Lists, Tuples" }
  ],
  "8": [
    { id: "python-data-structures", title: "0x03. Python - Data Structures: Lists, Tuples" }
  ],
  "9": [
    { id: "python-data-structures", title: "0x03. Python - Data Structures: Lists, Tuples" }
  ],
  "10": [
    { id: "python-data-structures", title: "0x03. Python - Data Structures: Lists, Tuples" }
  ],
  "11": [
    { id: "python-data-structures", title: "0x03. Python - Data Structures: Lists, Tuples" }
  ],
  // Week 3
  "14": [
    { id: "python-more-data-structures", title: "0x04. Python - More Data Structures: Set, Dictionary" }
  ],
  "15": [
    { id: "python-more-data-structures", title: "0x04. Python - More Data Structures: Set, Dictionary" }
  ],
  "16": [
    { id: "python-more-data-structures", title: "0x04. Python - More Data Structures: Set, Dictionary" }
  ],
  "17": [
    { id: "python-more-data-structures", title: "0x04. Python - More Data Structures: Set, Dictionary" }
  ],
  "18": [
    { id: "python-more-data-structures", title: "0x04. Python - More Data Structures: Set, Dictionary" }
  ],
  // Week 4
  "21": [
    { id: "python-exceptions", title: "0x05. Python - Exceptions" }
  ],
  "22": [
    { id: "python-exceptions", title: "0x05. Python - Exceptions" }
  ],
  "23": [
    { id: "python-exceptions", title: "0x05. Python - Exceptions" }
  ],
  "24": [
    { id: "python-exceptions", title: "0x05. Python - Exceptions" }
  ],
  "25": [
    { id: "python-exceptions", title: "0x05. Python - Exceptions" }
  ],
  // Week 5
  "28": [
    { id: "python-classes", title: "0x06. Python - Classes and Objects" }
  ],
  "29": [
    { id: "python-classes", title: "0x06. Python - Classes and Objects" }
  ],
  "30": [
    { id: "python-classes", title: "0x06. Python - Classes and Objects" }
  ],
  "31": [
    { id: "python-classes", title: "0x06. Python - Classes and Objects" }
  ]
}; 