export const augustProjects = {
  // Week 1
  "1": [
    { id: "python-classes", title: "0x06. Python - Classes and Objects" }
  ],
  // Week 2
  "4": [
    { id: "python-test-driven", title: "0x07. Python - Test-driven development" }
  ],
  "5": [
    { id: "python-test-driven", title: "0x07. Python - Test-driven development" }
  ],
  "6": [
    { id: "python-test-driven", title: "0x07. Python - Test-driven development" }
  ],
  "7": [
    { id: "python-test-driven", title: "0x07. Python - Test-driven development" }
  ],
  "8": [
    { id: "python-test-driven", title: "0x07. Python - Test-driven development" }
  ],
  // Week 3
  "11": [
    { id: "python-more-classes", title: "0x08. Python - More Classes and Objects" }
  ],
  "12": [
    { id: "python-more-classes", title: "0x08. Python - More Classes and Objects" }
  ],
  "13": [
    { id: "python-more-classes", title: "0x08. Python - More Classes and Objects" }
  ],
  "14": [
    { id: "python-more-classes", title: "0x08. Python - More Classes and Objects" }
  ],
  "15": [
    { id: "python-more-classes", title: "0x08. Python - More Classes and Objects" }
  ],
  // Week 4
  "18": [
    { id: "python-everything", title: "0x09. Python - Everything is object" }
  ],
  "19": [
    { id: "python-everything", title: "0x09. Python - Everything is object" }
  ],
  "20": [
    { id: "python-everything", title: "0x09. Python - Everything is object" }
  ],
  "21": [
    { id: "python-everything", title: "0x09. Python - Everything is object" }
  ],
  "22": [
    { id: "python-everything", title: "0x09. Python - Everything is object" }
  ],
  // Week 5
  "25": [
    { id: "python-inheritance", title: "0x0A. Python - Inheritance" }
  ],
  "26": [
    { id: "python-inheritance", title: "0x0A. Python - Inheritance" }
  ],
  "27": [
    { id: "python-inheritance", title: "0x0A. Python - Inheritance" }
  ],
  "28": [
    { id: "python-inheritance", title: "0x0A. Python - Inheritance" }
  ],
  "29": [
    { id: "python-inheritance", title: "0x0A. Python - Inheritance" }
  ]
}; 