export const juneProjects = {
  // Week 1
  "2": [
    { id: "binary-trees", title: "0x1D. C - Binary trees" }
  ],
  "3": [
    { id: "binary-trees", title: "0x1D. C - Binary trees" }
  ],
  "4": [
    { id: "binary-trees", title: "0x1D. C - Binary trees" }
  ],
  "5": [
    { id: "binary-trees", title: "0x1D. C - Binary trees" }
  ],
  "6": [
    { id: "binary-trees", title: "0x1D. C - Binary trees" }
  ],
  // Week 2
  "9": [
    { id: "search-algorithms", title: "0x1E. C - Search Algorithms" }
  ],
  "10": [
    { id: "search-algorithms", title: "0x1E. C - Search Algorithms" }
  ],
  "11": [
    { id: "search-algorithms", title: "0x1E. C - Search Algorithms" }
  ],
  "12": [
    { id: "search-algorithms", title: "0x1E. C - Search Algorithms" }
  ],
  "13": [
    { id: "search-algorithms", title: "0x1E. C - Search Algorithms" }
  ],
  // Week 3
  "16": [
    { id: "makefiles", title: "0x1C. C - Makefiles" }
  ],
  "17": [
    { id: "makefiles", title: "0x1C. C - Makefiles" }
  ],
  "18": [
    { id: "makefiles", title: "0x1C. C - Makefiles" }
  ],
  "19": [
    { id: "makefiles", title: "0x1C. C - Makefiles" }
  ],
  "20": [
    { id: "makefiles", title: "0x1C. C - Makefiles" }
  ],
  // Week 4
  "23": [
    { id: "python-hello", title: "0x00. Python - Hello, World" }
  ],
  "24": [
    { id: "python-hello", title: "0x00. Python - Hello, World" }
  ],
  "25": [
    { id: "python-hello", title: "0x00. Python - Hello, World" },
    { id: "python-if-else", title: "0x01. Python - if/else, loops, functions" }
  ],
  "26": [
    { id: "python-if-else", title: "0x01. Python - if/else, loops, functions" }
  ],
  "27": [
    { id: "python-if-else", title: "0x01. Python - if/else, loops, functions" }
  ],
  // Week 5
  "30": [
    { id: "python-if-else", title: "0x01. Python - if/else, loops, functions" }
  ]
}; 