export const aprilProjects = {
  "1": [
    { id: "even-more-pointers", title: "0x07. C - Even more pointers, arrays and strings" }
  ],
  "2": [
    { id: "recursion", title: "0x08. C - Recursion" }
  ],
  "3": [
    { id: "recursion", title: "0x08. C - Recursion" }
  ],
  "7": [
    { id: "static-libraries", title: "0x09. C - Static libraries" }
  ],
  "8": [
    { id: "static-libraries", title: "0x09. C - Static libraries" },
    { id: "argc-argv", title: "0x0A. C - argc, argv" }
  ],
  "9": [
    { id: "static-libraries", title: "0x09. C - Static libraries" },
    { id: "argc-argv", title: "0x0A. C - argc, argv" }
  ],
  "10": [
    { id: "static-libraries", title: "0x09. C - Static libraries" },
    { id: "argc-argv", title: "0x0A. C - argc, argv" }
  ],
  "11": [
    { id: "argc-argv", title: "0x0A. C - argc, argv" }
  ],
  "14": [
    { id: "malloc", title: "0x0B. C - malloc, free" },
    { id: "more-malloc", title: "0x0C. C - More malloc, free" }
  ],
  "15": [
    { id: "malloc", title: "0x0B. C - malloc, free" },
    { id: "more-malloc", title: "0x0C. C - More malloc, free" }
  ],
  "16": [
    { id: "malloc", title: "0x0B. C - malloc, free" }
  ],
  "17": [
    { id: "preprocessor", title: "0x0D. C - Preprocessor" }
  ],
  "18": [
    { id: "preprocessor", title: "0x0D. C - Preprocessor" }
  ],
  "21": [
    { id: "structures", title: "0x0E. C - Structures, typedef" }
  ],
  "22": [
    { id: "structures", title: "0x0E. C - Structures, typedef" },
    { id: "function-pointers", title: "0x0F. C - Function pointers" }
  ],
  "23": [
    { id: "function-pointers", title: "0x0F. C - Function pointers" }
  ],
  "28": [
    { id: "variadic", title: "0x10. C - Variadic functions" }
  ],
  "29": [
    { id: "variadic", title: "0x10. C - Variadic functions" }
  ],
  "30": [
    { id: "printf", title: "0x11. C - printf" }
  ]
}; 