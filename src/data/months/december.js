export const decemberProjects = {
  // Week 1
  "1": [
    { id: "dynamic-libraries", title: "0x18. Dynamic libraries" }
  ],
  "2": [
    { id: "dynamic-libraries", title: "0x18. Dynamic libraries" }
  ],
  "3": [
    { id: "dynamic-libraries", title: "0x18. Dynamic libraries" }
  ],
  "4": [
    { id: "dynamic-libraries", title: "0x18. Dynamic libraries" }
  ],
  "5": [
    { id: "dynamic-libraries", title: "0x18. Dynamic libraries" }
  ],
  // Week 2
  "8": [
    { id: "stacks-queues", title: "0x19. C - Stacks, Queues - LIFO, FIFO" }
  ],
  "9": [
    { id: "stacks-queues", title: "0x19. C - Stacks, Queues - LIFO, FIFO" }
  ],
  "10": [
    { id: "stacks-queues", title: "0x19. C - Stacks, Queues - LIFO, FIFO" }
  ],
  "11": [
    { id: "stacks-queues", title: "0x19. C - Stacks, Queues - LIF<PERSON>, FIFO" }
  ],
  "12": [
    { id: "stacks-queues", title: "0x19. C - Stacks, Queues - LIFO, FIFO" }
  ],
  // Week 3
  "15": [
    { id: "hash-tables", title: "0x1A. Hash tables" }
  ],
  "16": [
    { id: "hash-tables", title: "0x1A. Hash tables" }
  ],
  "17": [
    { id: "hash-tables", title: "0x1A. Hash tables" }
  ],
  "18": [
    { id: "hash-tables", title: "0x1A. Hash tables" }
  ],
  "19": [
    { id: "hash-tables", title: "0x1A. Hash tables" }
  ],
  // Week 4
  "22": [
    { id: "sorting-algorithms", title: "0x1B. C - Sorting algorithms & Big O" }
  ],
  "23": [
    { id: "sorting-algorithms", title: "0x1B. C - Sorting algorithms & Big O" }
  ],
  "24": [
    { id: "sorting-algorithms", title: "0x1B. C - Sorting algorithms & Big O" }
  ],
  "25": [
    { id: "sorting-algorithms", title: "0x1B. C - Sorting algorithms & Big O" }
  ],
  "26": [
    { id: "sorting-algorithms", title: "0x1B. C - Sorting algorithms & Big O" }
  ]
}; 