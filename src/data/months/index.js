import { februaryProjects } from './february';
import { marchProjects } from './march';
import { aprilProjects } from './april';
import { mayProjects } from './may';
import { juneProjects } from './june';
import { julyProjects } from './july';
import { augustProjects } from './august';
import { septemberProjects } from './september';
import { octoberProjects } from './october';
import { novemberProjects } from './november';
import { decemberProjects } from './december';

export const monthsData = {
  "2": februaryProjects,  // February
  "3": marchProjects,     // March
  "4": aprilProjects,     // April
  "5": mayProjects,       // May
  "6": juneProjects,
  "7": julyProjects,
  "8": augustProjects,
  "9": septemberProjects,
  "10": octoberProjects,
  "11": novemberProjects,
  "12": decemberProjects
};

// Helper function to get projects for a specific date
export const getProjectsForDate = (month, day) => {
  const monthProjects = monthsData[month];
  if (!monthProjects) return [];
  
  return monthProjects[day] || [];
};

// Helper function to get today's projects
export const getTodayProjects = () => {
  const today = new Date();
  const month = (today.getMonth() + 1).toString();
  const day = today.getDate().toString();
  
  return getProjectsForDate(month, day);
};

// Helper function to get all projects for a month
export const getMonthProjects = (month) => {
  return monthsData[month] || {};
};

// Helper function to format date for display
export const formatDate = (month, day) => {
  const date = new Date(2025, month - 1, day);
  return date.toLocaleDateString('en-US', { 
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}; 