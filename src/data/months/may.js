export const mayProjects = {
  // Week 1
  "1": [
    { id: "printf", title: "0x11. C - printf" },
    { id: "singly-linked-lists", title: "0x12. C - Singly linked lists" }
  ],
  "2": [
    { id: "printf", title: "0x11. C - printf" },
    { id: "singly-linked-lists", title: "0x12. C - Singly linked lists" }
  ],
  // Week 2
  "5": [
    { id: "printf", title: "0x11. C - printf" },
    { id: "more-singly-linked-lists", title: "0x13. C - More singly linked lists" }
  ],
  "6": [
    { id: "more-singly-linked-lists", title: "0x13. C - More singly linked lists" }
  ],
  "7": [
    { id: "more-singly-linked-lists", title: "0x13. C - More singly linked lists" }
  ],
  "8": [
    { id: "more-singly-linked-lists", title: "0x13. C - More singly linked lists" }
  ],
  "9": [
    { id: "more-singly-linked-lists", title: "0x13. C - More singly linked lists" }
  ],
  // Week 3
  "12": [
    { id: "file-io", title: "0x15. C - File I/O" }
  ],
  "13": [
    { id: "file-io", title: "0x15. C - File I/O" },
    { id: "simple-shell", title: "0x16. C - Simple Shell" }
  ],
  "14": [
    { id: "simple-shell", title: "0x16. C - Simple Shell" }
  ],
  "15": [
    { id: "simple-shell", title: "0x16. C - Simple Shell" }
  ],
  "16": [
    { id: "simple-shell", title: "0x16. C - Simple Shell" }
  ],
  // Week 4
  "19": [
    { id: "doubly-linked-lists", title: "0x17. C - Doubly linked lists" },
    { id: "simple-shell", title: "0x16. C - Simple Shell" }
  ],
  "20": [
    { id: "doubly-linked-lists", title: "0x17. C - Doubly linked lists" },
    { id: "simple-shell", title: "0x16. C - Simple Shell" }
  ],
  "21": [
    { id: "sql-introduction", title: "0x0D. SQL - Introduction" },
    { id: "simple-shell", title: "0x16. C - Simple Shell" }
  ],
  "22": [
    { id: "sql-introduction", title: "0x0D. SQL - Introduction" },
    { id: "sql-more-queries", title: "0x0E. SQL - More queries" }
  ],
  "23": [
    { id: "sql-more-queries", title: "0x0E. SQL - More queries" }
  ],
  // Week 5
  "26": [
    { id: "hash-tables", title: "0x1A. C - Hash tables" }
  ],
  "27": [
    { id: "hash-tables", title: "0x1A. C - Hash tables" },
    { id: "sorting", title: "0x1B. C - Sorting algorithms & Big O" }
  ],
  "28": [
    { id: "hash-tables", title: "0x1A. C - Hash tables" },
    { id: "sorting", title: "0x1B. C - Sorting algorithms & Big O" }
  ],
  "29": [
    { id: "hash-tables", title: "0x1A. C - Hash tables" },
    { id: "sorting", title: "0x1B. C - Sorting algorithms & Big O" }
  ],
  "30": [
    { id: "hash-tables", title: "0x1A. C - Hash tables" },
    { id: "sorting", title: "0x1B. C - Sorting algorithms & Big O" }
  ]
}; 