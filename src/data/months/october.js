export const octoberProjects = {
  // Week 1
  "1": [
    { id: "javascript-warm-up", title: "0x12. JavaScript - Warm up" }
  ],
  "2": [
    { id: "javascript-warm-up", title: "0x12. JavaScript - Warm up" }
  ],
  "3": [
    { id: "javascript-warm-up", title: "0x12. JavaScript - Warm up" }
  ],
  // Week 2
  "6": [
    { id: "javascript-objects", title: "0x13. JavaScript - Objects, Scopes and Closures" }
  ],
  "7": [
    { id: "javascript-objects", title: "0x13. JavaScript - Objects, Scopes and Closures" }
  ],
  "8": [
    { id: "javascript-objects", title: "0x13. JavaScript - Objects, Scopes and Closures" }
  ],
  "9": [
    { id: "javascript-objects", title: "0x13. JavaScript - Objects, Scopes and Closures" }
  ],
  "10": [
    { id: "javascript-objects", title: "0x13. JavaScript - Objects, Scopes and Closures" }
  ],
  // Week 3
  "13": [
    { id: "javascript-web-scraping", title: "0x14. JavaScript - Web scraping" }
  ],
  "14": [
    { id: "javascript-web-scraping", title: "0x14. JavaScript - Web scraping" }
  ],
  "15": [
    { id: "javascript-web-scraping", title: "0x14. JavaScript - Web scraping" }
  ],
  "16": [
    { id: "javascript-web-scraping", title: "0x14. JavaScript - Web scraping" }
  ],
  "17": [
    { id: "javascript-web-scraping", title: "0x14. JavaScript - Web scraping" }
  ],
  // Week 4
  "20": [
    { id: "python-object-relational-mapping", title: "0x0F. Python - Object-relational mapping" }
  ],
  "21": [
    { id: "python-object-relational-mapping", title: "0x0F. Python - Object-relational mapping" }
  ],
  "22": [
    { id: "python-object-relational-mapping", title: "0x0F. Python - Object-relational mapping" }
  ],
  "23": [
    { id: "python-object-relational-mapping", title: "0x0F. Python - Object-relational mapping" }
  ],
  "24": [
    { id: "python-object-relational-mapping", title: "0x0F. Python - Object-relational mapping" }
  ],
  // Week 5
  "27": [
    { id: "python-network", title: "0x10. Python - Network #0" }
  ],
  "28": [
    { id: "python-network", title: "0x10. Python - Network #0" }
  ],
  "29": [
    { id: "python-network", title: "0x10. Python - Network #0" }
  ],
  "30": [
    { id: "python-network", title: "0x10. Python - Network #0" }
  ],
  "31": [
    { id: "python-network", title: "0x10. Python - Network #0" }
  ]
}; 