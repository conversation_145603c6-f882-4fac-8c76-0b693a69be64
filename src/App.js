import React, { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { ConceptProvider } from './contexts/ConceptContext';
import './App.css';
import Sidebar from './components/Sidebar';
import Month0 from './components/Month0';
import Month1 from './components/Month1';
import Month2 from './components/Month2';
import Month3 from './components/Month3';
import Month4 from './components/Month4';
import Month5 from './components/Month5';
import Month6 from './components/Month6';
import Month7 from './components/Month7';
import Month8 from './components/Month8';
import Month9 from './components/Month9';
import Month10 from './components/Month10';
import ProjectPage from './components/ProjectPage';
import ProjectDetails from './components/ProjectDetails';
import ComingSoon from './components/ComingSoon';
import NotFound from './components/NotFound';
import ErrorBoundary from './components/ErrorBoundary';
import ThemeToggle from './components/ThemeToggle';
import ScrollToTopButton from './components/ScrollToTopButton';
import QuizList from './components/quiz/QuizList';
import QuizPage from './components/quiz/QuizPage';

// Import concept components
import ConceptsPage from './components/concepts/ConceptsPage';
import ConceptDetail from './components/concepts/ConceptDetail';
import ConceptGrid from './components/concepts/ConceptGrid';
import CategoryDetail from './components/concepts/CategoryDetail';
import DirectConceptDetail from './components/concepts/DirectConceptDetail';

function App() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [currentMonth, setCurrentMonth] = useState(0); // 0 for February through 10 for December

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const navigateMonth = (direction) => {
    if (direction === 'prev' && currentMonth > 0) {
      setCurrentMonth(currentMonth - 1);
    } else if (direction === 'next' && currentMonth < 10) {
      setCurrentMonth(currentMonth + 1);
    }
  };

  const resetToCurrentMonth = () => {
    const today = new Date();
    const currentRealMonth = today.getMonth(); // 0 for Jan, 1 for Feb, ..., 11 for Dec
    // The app's currentMonth state: 0 for February, 1 for March, ..., 10 for December
    // So, we need to map the actual month to this state.
    // e.g., May (js month 4) -> app state 3
    // February (js month 1) -> app state 0
    let targetIndex = currentRealMonth - 1;

    // Handle January (js month 0) - default to February (app state 0) as it's the first available month
    if (currentRealMonth === 0) {
      targetIndex = 0;
    }
    
    // Ensure targetIndex is within the valid range [0, 10]
    if (targetIndex < 0) {
      targetIndex = 0; // Default to February if somehow out of lower bound
    } else if (targetIndex > 10) {
      targetIndex = 10; // Default to December if somehow out of upper bound
    }

    setCurrentMonth(targetIndex);
  };

  const renderCurrentMonth = () => {
    const monthComponents = [
      <Month0
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month1
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month2
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month3
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month4
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month5
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month6
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month7
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month8
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month9
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />,
      <Month10
        onPrevMonth={() => navigateMonth('prev')}
        onNextMonth={() => navigateMonth('next')}
        onResetMonth={resetToCurrentMonth}
        isFirstMonth={currentMonth === 0}
        isLastMonth={currentMonth === 10}
      />
    ];

    return monthComponents[currentMonth];
  };

  return (
    <ThemeProvider>
      <ConceptProvider>
        <ErrorBoundary>
        <div className="flex min-h-screen bg-background text-text relative overflow-x-hidden transition-colors duration-300 ease-in-out">
          <Sidebar isOpen={isSidebarOpen} onToggle={handleSidebarToggle} />
          <main className={`flex-grow w-full min-h-screen relative flex flex-col overflow-hidden bg-background transition-all duration-300 ease-in-out p-1 md:p-2 lg:p-3 ${isSidebarOpen ? "opacity-50 pointer-events-none md:ml-48 md:opacity-100 md:pointer-events-auto" : "ml-12"}`}>
            <ThemeToggle />
            <ScrollToTopButton />
            <ErrorBoundary>
                <Routes>
                  <Route path="/" element={<Navigate to="/planning" replace />} />
                  <Route
                    path="/planning"
                    element={renderCurrentMonth()}
                  />
                  <Route path="/projects" element={<ProjectPage />} />
                  <Route path="/projects/:id" element={<ProjectDetails />} />
                  <Route
                    path="/dashboard"
                    element={<ComingSoon title="Dashboard" />}
                  />
                  <Route
                    path="/curriculums"
                    element={<ComingSoon title="Curriculums" />}
                  />
                  <Route
                    path="/peers"
                    element={<ComingSoon title="Peer Learning" />}
                  />

                  {/* Concepts Routes */}
                  <Route path="/concepts" element={<ConceptsPage />}>
                    <Route index element={<ConceptGrid />} />
                    <Route path=":categoryId" element={<CategoryDetail />} />
                    <Route path=":categoryId/:conceptId" element={<ConceptDetail />} />
                  </Route>

                  {/* Direct Concept Access Route */}
                  <Route path="/concept/:conceptId" element={<DirectConceptDetail />} />

                  {/* Quiz Routes */}
                  <Route path="/evaluation-quizzes" element={<QuizList />} />
                  <Route path="/evaluation-quizzes/:id" element={<QuizPage />} />
                  <Route path="/evaluation-quizzes/:id/latest" element={<QuizPage />} />

                  <Route path="*" element={<NotFound />} />
                </Routes>
            </ErrorBoundary>
          </main>
        </div>
        </ErrorBoundary>
      </ConceptProvider>
    </ThemeProvider>
  );
}

export default App;
