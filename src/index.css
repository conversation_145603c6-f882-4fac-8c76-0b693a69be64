@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: rgb(255, 255, 255);  /* Pure white background */
    --text: rgb(15, 23, 42);  /* Darker text for better contrast */
    --border: rgb(226, 232, 240);  /* Softer border color */
    --sidebar-text: rgb(15, 23, 42);  /* Dark text for sidebar */
    --card-bg: rgb(255, 255, 255);  /* Pure white for cards */
    --hover-bg: rgb(241, 245, 249);  /* Lighter hover state */
    --muted-text: rgb(100, 116, 139);  /* More visible muted text */
    --taskHeader-bg: rgb(241, 245, 249);  /* Light gray for light mode */
    
    /* Light theme code block colors */
    --code-bg: rgb(40, 40, 40);  /* Gruvbox dark background for code blocks */
    --code-text: rgb(30, 41, 59);   /* Dark text for contrast */
    --code-keyword: rgb(147, 51, 234);
    --code-function: rgb(37, 99, 235);
    --code-string: rgb(22, 163, 74);
    --code-comment: rgb(100, 116, 139);
  }

  :root.dark {
    --background: rgb(15, 23, 42);  /* Dark blue background */
    --text: rgb(248, 250, 252);  /* Light text */
    --border: rgb(51, 65, 85);  /* Dark blue border */
    --sidebar-text: rgb(248, 250, 252);  /* Light text for sidebar */
    --card-bg: rgb(30, 41, 59);  /* Slightly lighter than background */
    --hover-bg: rgb(51, 65, 85);  /* Even lighter for hover */
    --muted-text: rgb(148, 163, 184);  /* Muted blue-gray */
    --taskHeader-bg: rgb(30, 41, 59);  /* Same as card background */
    
    /* One Dark Pro Code Block Colors - consistent in dark theme */
    --code-bg: rgb(40, 44, 52);
    --code-text: rgb(171, 178, 191);
    --code-keyword: rgb(198, 120, 221);
    --code-function: rgb(97, 175, 239);
    --code-string: rgb(152, 195, 121);
    --code-comment: rgb(92, 99, 112);
  }


  pre code {
    font-family: 'Fira Code', 'Fira Mono', Menlo, Consolas, 'DejaVu Sans Mono', monospace;
    font-size: 0.875rem; /* Equivalent to text-sm */
    line-height: 1.5; /* Equivalent to leading-relaxed */
    color: var(--code-text);
    display: block;
    background: transparent;
  }

  /* Video iframe responsive container styles */
  /* Removed .relative > iframe rule to allow video to fill container */

  /* @media (max-width: 768px) {
    .relative > iframe {
      max-width: 100%;
    }
  } */
}

/* Syntax highlighting styles */
.token.keyword,
.token.builtin { 
  color: var(--code-keyword) !important; 
}

.token.function { 
  color: var(--code-function) !important; 
}

.token.string,
.token.attr-value { 
  color: var(--code-string) !important; 
}

.token.comment { 
  color: var(--code-comment) !important;
  font-style: italic;
}

/* Custom override for specific badge text visibility */
span.bg-muted.text-muted-foreground {
  color: #FFFFFF !important;
}

html.dark span.dark\:bg-dark-muted.dark\:text-dark-muted-foreground {
  color: #FFFFFF !important;
}

/* Font loading for code blocks */
@font-face {
  font-family: 'Fira Code';
  src: url('https://cdn.jsdelivr.net/npm/firacode@6.2.0/distr/woff2/FiraCode-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

/* Force transparent background for shield badges and their container */
.badge-container,
.badge-container img,
.inline-image {
  background-color: transparent !important;
}
