import { categoriesConfig, getAllConceptFiles } from '../data/concepts/categories';
import { getConceptFilename } from './conceptMapping';

export const processConceptFiles = async () => {
  const concepts = {};
  const categories = [...categoriesConfig];
  const conceptsByFile = new Map(); // Track concepts by filename

  try {
    // Get all unique concept files
    const allFiles = getAllConceptFiles();

    // First pass: Process all unique files
    for (const filename of allFiles) {
      try {
        // Extract the concept ID from the filename if possible
        const conceptIdMatch = filename.match(/.*?(\d+).*?/);
        const conceptId = conceptIdMatch ? conceptIdMatch[1] : null;

        // Get the corresponding numeric ID file
        const numericIdFile = conceptId ? getConceptFilename(conceptId) : null;

        // Add debugging
        console.log(`Processing file: ${filename}`);
        console.log(`  - Extracted concept ID: ${conceptId}`);
        console.log(`  - Mapped to numeric file: ${numericIdFile}`);

        const content = await fetchConceptContent(numericIdFile || filename);
        const category = categories.find(cat => cat.files.includes(filename));
        const processed = await processConceptContent(content, filename, category?.id);

        // Store the processed concept
        concepts[processed.id] = processed;
        conceptsByFile.set(filename, processed);
      } catch (error) {
        console.error(`Error processing file ${filename}:`, error);
        continue;
      }
    }

    // Second pass: Populate categories with their concepts
    for (const category of categories) {
      category.concepts = category.files
        .map(filename => {
          const concept = conceptsByFile.get(filename);
          if (!concept) return null;
          return {
            id: concept.id,
            title: concept.title,
            summary: concept.summary
          };
        })
        .filter(Boolean); // Remove null entries
    }

    return { categories, concepts };
  } catch (error) {
    console.error('Error processing concept files:', error);
    throw error;
  }
};

const fetchConceptContent = async (filename) => {
  try {
    // Add debugging
    console.log(`Fetching concept file: ${filename}`);

    // Fetch the concept file from the concept_files directory
    const response = await fetch(`/concept_files/${filename}`);
    if (!response.ok) {
      // Try the old location as fallback
      console.log(`Trying fallback location for: ${filename}`);
      const oldResponse = await fetch(`/alx-concepts_page-main/${filename}`);
      if (!oldResponse.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      console.warn(`WARNING: File ${filename} was loaded from old location!`);
      return await oldResponse.text();
    }

    const content = await response.text();
    console.log(`Successfully loaded: ${filename}`);

    if (!content.trim()) {
      return `# ${filename.replace('.md', '')}
Content is being prepared...`;
    }
    return content;
  } catch (error) {
    console.error(`Error fetching ${filename}:`, error);
    // Return a basic content on error
    return `# ${filename.replace('.md', '')}
Content is being prepared...`;
  }
};

const processConceptContent = async (content, filename, categoryId) => {
  // Extract title from first heading
  const titleMatch = content.match(/^#\s+(.+)$/m);
  const title = titleMatch ? titleMatch[1] : filename.replace('.md', '');

  // Generate ID from filename
  const id = filename.replace('.md', '').toLowerCase();

  // Extract summary from first paragraph after title
  const summaryMatch = content.match(/^#.*?\n\n(.*?)(\n\n|$)/s);
  const summary = summaryMatch
    ? summaryMatch[1].replace(/\[|\]|\(|\)/g, '').trim()
    : '';

  // Process image paths
  const processedContent = processImagePaths(content);

  // Generate table of contents
  const toc = generateTableOfContents(content);

  return {
    id,
    title,
    summary,
    content: processedContent,
    toc,
    categoryId,
    metadata: {
      filename,
      lastUpdated: new Date().toISOString()
    }
  };
};

const processImagePaths = (content) => {
  // Replace relative image paths with public URL paths
  return content.replace(
    /!\[([^\]]*)\]\((?!http)([^)]+)\)/g,
    (_, alt, path) => {
      const publicPath = `/concept-images/${path.replace(/^\.\//, '')}`;
      return `![${alt}](${publicPath})`;
    }
  );
};

const generateTableOfContents = (content) => {
  const headings = [];
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  let match;

  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length;
    const text = match[2];
    const id = text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');

    headings.push({
      level,
      text,
      id
    });
  }

  return headings;
};
