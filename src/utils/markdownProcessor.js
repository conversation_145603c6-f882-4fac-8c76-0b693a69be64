import React from 'react';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { gruvboxDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import rehypeSlug from 'rehype-slug';
import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkRehype from 'remark-rehype';

// Function to decode HTML entities
const decodeHtmlEntities = (str) => {
  return str
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");
};

// Process the markdown content to ensure consistent formatting
export const processMarkdown = (content) => {
  if (!content) return '';

  let processedContent = content;

  // First, protect code blocks by replacing them with placeholders
  const codeBlocks = [];
  processedContent = processedContent.replace(/```[\s\S]*?```/g, (match) => {
    codeBlocks.push(match);
    return `CODE_BLOCK_${codeBlocks.length - 1}`;
  });

  // Process command line blocks ($ commands and their output)
  processedContent = processedContent.replace(
    /^\$ .*(?:\n(?!\$).*)*/gm,
    (match) => {
      codeBlocks.push('```bash\n' + match + '\n```');
      return `CODE_BLOCK_${codeBlocks.length - 1}`;
    }
  );

  // Process ubuntu@ip... style command blocks
  processedContent = processedContent.replace(
    /^ubuntu@.*(?:\n(?!ubuntu@).*)*/gm,
    (match) => {
      codeBlocks.push('```bash\n' + match + '\n```');
      return `CODE_BLOCK_${codeBlocks.length - 1}`;
    }
  );

  // Add proper spacing after headings
  processedContent = processedContent.replace(/^(#{1,6} .+)$/gm, '$1\n');

  // Add proper list formatting
  processedContent = processedContent.replace(/^([*-] .+)$/gm, '$1\n');

  // Add spacing around blockquotes
  processedContent = processedContent.replace(/^(> .+)$/gm, '\n$1\n');

  // Clean up multiple newlines
  processedContent = processedContent.replace(/\n{3,}/g, '\n\n');

  // Restore code blocks and decode HTML entities within them
  processedContent = processedContent.replace(/CODE_BLOCK_(\d+)/g, (_, index) => {
    let block = codeBlocks[index];
    if (block.startsWith('```')) {
      const lines = block.split('\n');
      const firstLine = lines[0];
      const content = lines.slice(1, -1).join('\n');
      return firstLine + '\n' + decodeHtmlEntities(content) + '\n```';
    }
    return block;
  });

  return processedContent;
};

// Configuration for ReactMarkdown
export const markdownConfig = {
  remarkPlugins: [remarkGfm],
  rehypePlugins: [
    rehypeSlug,
    [rehypeAutolinkHeadings, { behavior: 'wrap' }]
  ],
  components: {
    a: ({ node, children, href, ...props }) => (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="text-red-800 dark:text-red-400 hover:underline"
        {...props}
      >
        {children}
      </a>
    ),
    strong: ({ node, children, ...props }) => (
      <strong className="font-bold" {...props}>
        {children}
      </strong>
    ),
    h1: ({ children, ...props }) => (
      <h1 className="text-3xl font-bold mt-8 mb-4" {...props}>
        {children}
      </h1>
    ),
    h2: ({ children, ...props }) => (
      <h2 className="text-2xl font-semibold mt-6 mb-3" {...props}>
        {children}
      </h2>
    ),
    h3: ({ children, ...props }) => (
      <h3 className="text-xl font-medium mt-4 mb-2" {...props}>
        {children}
      </h3>
    ),
    p: ({ children, ...props }) => (
      <p className="my-4 leading-relaxed" {...props}>
        {children}
      </p>
    ),
    ul: ({ children, ...props }) => (
      <ul className="list-disc pl-6 my-4 space-y-2" {...props}>
        {children}
      </ul>
    ),
    ol: ({ children, ...props }) => (
      <ol className="list-decimal pl-6 my-4 space-y-2" {...props}>
        {children}
      </ol>
    ),
    li: ({ children, ...props }) => (
      <li className="pl-2" {...props}>
        {children}
      </li>
    ),
    blockquote: ({ children, ...props }) => (
      <blockquote className="border-l-4 border-primary pl-4 my-4 italic" {...props}>
        {children}
      </blockquote>
    ),
    code: ({ node, inline, className, children, ...props }) => {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <SyntaxHighlighter
          style={gruvboxDark}
          language={match[1]}
          PreTag="div"
          customStyle={{
            margin: 'rem 0',
            padding: '0rem',
            backgroundColor: '#2d3748',
            borderRadius: '0rem',
      
            wordBreak: 'break-word',
            whiteSpace: 'pre-wrap',
            overflowWrap: 'break-word'
          }}
          showLineNumbers={true}
          wrapLines={true}
          wrapLongLines={true}
          {...props}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      ) : (
        <code className="inline-code" {...props}>
          {children}
        </code>
      );
    },
    img: ({ src, alt, ...props }) => (
      <img
        src={src}
        alt={alt}
        className="rounded-lg border border-border my-4"
        loading="lazy"
        onError={(e) => {
          e.target.onerror = null;
          e.target.src = '/placeholder-image.png';
        }}
        {...props}
      />
    ),
    table: ({ children, ...props }) => (
      <div className="my-4 overflow-x-auto">
        <table className="min-w-full divide-y divide-border" {...props}>
          {children}
        </table>
      </div>
    ),
    th: ({ children, ...props }) => (
      <th className="px-4 py-2 bg-muted font-medium" {...props}>
        {children}
      </th>
    ),
    td: ({ children, ...props }) => (
      <td className="px-4 py-2 border-t border-border" {...props}>
        {children}
      </td>
    )
  }
};
