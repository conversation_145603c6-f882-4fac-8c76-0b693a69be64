// This file provides mapping between concept IDs and their filenames
// Generated automatically from project files

// Map from concept ID to filename
export const conceptIdToFilename = {
  '135': '3-ways-to-make-a-portfolio-great.md',
  '104541': 'ALX-SE-program_cheatsheet.md',
  '100033': 'ALX_SE-discord-migration.md',
  '26': 'C-programming.md',
  '61': 'C-static_libraries.md',
  '43': 'CICD.md',
  '12': 'DNS.md',
  '37': 'Databases.md',
  '2': 'HTMLCSS.md',
  '60': 'Pointers-and_arrays.md',
  '100034': 'Printf-function_brief.md',
  '550': 'Python-programming.md',
  '106963': 'SE-Face_off-cup.md',
  '106962': 'SE-Face_off-starter-pack.md',
  '9': 'Shell.md',
  '74': 'airbnb_clone.md',
  '100037': 'all-about-team-projects-FAQ-pairings.md',
  '350': 'approaching-a-project.md',
  '100035': 'authenticating-git.md',
  '62': 'automatic_and-dynamic-allocation.md',
  '110': 'child-process.md',
  '224': 'chrome.md',
  '100010': 'code-of-conduct-intro.md',
  '100011': 'code-of-conduct.md',
  '100012': 'code-of-conduct_enforcement.md',
  '64': 'coding-your-own_shell.md',
  '120': 'data-structures.md',
  '35': 'dealing-with-data_statistically.md',
  '65': 'docker.md',
  '105416': 'essential_cheat-sheet.md',
  '130': 'flowcharts.md',
  '100002': 'fresh-reset_and-install_mysql.md',
  '57': 'git-github_cheatsheet.md',
  '111': 'group-projects.md',
  '3': 'javascript-in-the-browser.md',
  '46': 'load-balancer.md',
  '133': 'maze-project.md',
  '100031': 'mentor_yellow-pages.md',
  '13': 'monitoring.md',
  '33': 'network-basics.md',
  '47': 'never-forget-a-test.md',
  '39': 'on-call.md',
  '121': 'pair-programming.md',
  '58': 'peer-learning-day.md',
  '102042': 'portfolio-project_deeper-overview.md',
  '137': 'portfolio-project_overview.md',
  '66': 'python-packages.md',
  '29': 'regular-expression.md',
  '138': 'research-and_project-approval.md',
  '45': 'rest_api.md',
  '6': 'right-engineering_rightdocumenting.md',
  '67': 'server.md',
  '221': 'slack.md',
  '22': 'source-code-mgt.md',
  '100039': 'struggling-with-sandbox.md',
  '106541': 'take-the_hot_seat.md',
  '225': 'technical-writing.md',
  '100006': 'technical_questioning.md',
  '75': 'the_framework.md',
  '4': 'trinity_of-frontend-quality.md',
  '54': 'using_emacs-as-editor.md',
  '17': 'web-server.md',
  '68': 'webstack-debugging.md',
  '102912': 'webstack-portfolio_criteria.md',
  '100000': 'white-boarding.md',
  '100001': 'your-learning_community.md',
  '100': 'job-search-resources.md',
  '49': 'database-administration.md',
  '556': 'databases.md',
  '559': 'the-framework.md',
  '107041': 'se-face-off-cup-round-of-11.md',
  '107371': 'the-alx-se-tutors-program-a-guide.md',
  '107372': 'se-face-off-cup-semi-finals.md',
  '107416': 'portfolio-project-review-introducing-automated-project-review-system.md',
  '108711': 'brandu-challenge-focus-draft-elevate.md',
  '108713': 'brandu-challenge-1st-challenge-draft-your-personal-mission-statement.md',
  '108825': 'brandu-challenge-2nd-challenge-revamp-your-cv-cover-letter.md',
  '108835': 'brandu-challenge-3rd-challenge-crafting-your-professional-story.md',
};

// Function to get the filename for a concept ID
export const getConceptFilename = (conceptId) => {
  return conceptIdToFilename[conceptId] || null;
};

// Function to get the file path for a concept ID
export const getConceptFilePath = (conceptId) => {
  const filename = getConceptFilename(conceptId);
  return filename ? `concept_files/${filename}` : null;
};
