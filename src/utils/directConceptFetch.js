import { getConceptFilename } from './conceptMapping';

/**
 * This utility provides functions to directly fetch concept files from the concept_files directory
 * based on concept IDs from URLs.
 */

/**
 * Fetch a concept file directly from the concept_files directory
 * @param {string} conceptId - The concept ID from the URL (e.g., "60" for Pointers and arrays)
 * @returns {Promise<string>} - The content of the concept file
 */
export const fetchConceptById = async (conceptId) => {
  try {
    console.log(`Attempting to fetch concept with ID: ${conceptId} using mapping.`);
    
    const filename = getConceptFilename(conceptId);
    
    if (!filename) {
      console.error(`No filename mapping found for concept ID: ${conceptId}`);
      throw new Error(`Filename mapping not found for concept ID: ${conceptId}`);
    }
    
    console.log(`Mapped concept ID ${conceptId} to filename: ${filename}`);
    
    // Fetch the file from the concept_files directory
    // Ensure the path is correct, assuming conceptMapping.js provides just the filename.
    const response = await fetch(`/concept_files/${filename}`);
    
    if (!response.ok) {
      console.warn(`Concept file not found in public/concept_files/: ${filename} (ID: ${conceptId})`);
      throw new Error(`Failed to fetch concept file: ${filename} (HTTP ${response.status})`);
    }
    
    const content = await response.text();
    console.log(`Successfully loaded concept: ${filename} (ID: ${conceptId})`);
    
    return content;
  } catch (error) {
    console.error(`Error fetching concept ID ${conceptId}: ${error.message}`);
    throw error; // Re-throw the error to be caught by the calling component
  }
};

/**
 * Process the content of a concept file
 * @param {string} content - The content of the concept file
 * @param {string} conceptId - The concept ID
 * @returns {Object} - The processed concept
 */
export const processConceptContent = (content, conceptId) => {
  // Extract title from first heading
  const titleMatch = content.match(/^#\s+(.+)$/m);
  const title = titleMatch ? titleMatch[1] : `Concept ${conceptId}`;
  
  // Extract summary from first paragraph after title
  const summaryMatch = content.match(/^#.*?\n\n(.*?)(\n\n|$)/s);
  const summary = summaryMatch 
    ? summaryMatch[1].replace(/\[|\]|\(|\)/g, '').trim()
    : '';

  // Process image paths
  const processedContent = content.replace(
    /!\[([^\]]*)\]\((?!http)([^)]+)\)/g,
    (_, alt, path) => {
      const publicPath = `/concept-images/${path.replace(/^\.\//, '')}`;
      return `![${alt}](${publicPath})`;
    }
  );
  
  // Generate table of contents
  const headings = [];
  const headingRegex = /^(#{1,6})\s+(.+)$/gm;
  let match;

  while ((match = headingRegex.exec(content)) !== null) {
    const level = match[1].length;
    const text = match[2];
    const id = text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-');

    headings.push({
      level,
      text,
      id
    });
  }

  return {
    id: conceptId,
    title,
    summary,
    content: processedContent,
    toc: headings,
    metadata: {
      filename: getConceptFilename(conceptId) || `_${conceptId}.md`, // Use mapped filename if available
      lastUpdated: new Date().toISOString()
    }
  };
};
