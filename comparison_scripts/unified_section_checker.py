#!/usr/bin/env python3
"""
Unified Section Checker for ALX Course Tracker

This script checks if sections in JavaScript project files match their corresponding Markdown files.
It performs semantic comparison of various sections such as Resources, Learning Objectives, etc.
and generates comprehensive reports on the similarities between files.

Usage examples (run from the project root directory):
  # Check a specific project
  python3 comparison_scripts/unified_section_checker.py --project rest-api

  # Check all projects
  python3 comparison_scripts/unified_section_checker.py --all

  # Check a specific project and show verbose output
  python3 comparison_scripts/unified_section_checker.py --project rest-api --verbose

  # Just update the summary report based on existing results
  python3 comparison_scripts/unified_section_checker.py --update-summary

  # List all available projects
  python3 comparison_scripts/unified_section_checker.py --list-projects
  
  # View status of a specific project
  python3 comparison_scripts/unified_section_checker.py --status rest-api
"""

import os
import sys
import re
import argparse
import subprocess
from pathlib import Path
import json
from difflib import SequenceMatcher
import time
import textwrap
import datetime

# ANSI color codes for prettier output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Project paths
PROJECT_DIRS = {
    'low-level': 'src/data/projects/low-level',
    'system-engineering-devops': 'src/data/projects/system-engineering-devops',
    'higher-level': {
        'base': 'src/data/projects/higher-level',
        'subdirs': {
            'python': 'python',
            'javascript': 'javascript',
            'sql': 'sql',
            'airbnb': 'airbnb'
        }
    }
}
MD_DIR = "markdown_files"
REPORTS_DIR = "section_comparison_reports"
SUMMARY_FILE = f"{REPORTS_DIR}/section_comparison_summary.txt"

# List of sections to check
SECTIONS = ["Resources", "Additional Resources", "Learning Objectives", "Requirements"]

# Create base report directories
for project_type, path in PROJECT_DIRS.items():
    if isinstance(path, str):
        os.makedirs(f"{REPORTS_DIR}/{project_type}", exist_ok=True)
    elif isinstance(path, dict) and 'subdirs' in path:
        # Create directories for higher-level subdirs
        for subdir in path['subdirs'].keys():
            os.makedirs(f"{REPORTS_DIR}/{project_type}_{subdir}", exist_ok=True)

def get_directory_pairs(project_type, js_dir):
    """
    Find matching pairs of JS and MD files for a specific directory.
    Args:
        project_type: Type of project (e.g., 'low-level', 'higher-level/python')
        js_dir: Directory containing JS files
    Returns:
        List of dictionaries containing project information
    """
    pairs = []
    # Get all JS files for this project type
    try:
        js_files = [f for f in os.listdir(js_dir) if f.endswith('.js') and f != 'index.js']
    except OSError:
        print(f"{Colors.YELLOW}Warning: Could not read directory: {js_dir}{Colors.ENDC}")
        return pairs
        
    # Map project names to their corresponding files
    for js_file in js_files:
        project_name = js_file.replace('.js', '')
        md_file = get_markdown_filename(project_name, project_type)
        md_path = f"{MD_DIR}/{md_file}.md"
        
        # Check if MD file exists
        if os.path.exists(md_path):
            pairs.append({
                'project_name': project_name,
                'project_type': project_type,
                'js_file': f"{js_dir}/{js_file}",
                'md_file': md_path
            })
        else:
            print(f"{Colors.YELLOW}Warning: No matching MD file found for {js_file} in {project_type}{Colors.ENDC}")
    
    return pairs

def get_project_pairs():
    """
    Find matching pairs of JS and MD files for all project types.
    Returns a list of dictionaries containing project information.
    """
    pairs = []
    
    # Iterate through each project type
    for project_type, path_info in PROJECT_DIRS.items():
        if isinstance(path_info, str):
            # Handle simple directory paths (low-level, system-engineering-devops)
            if not os.path.exists(path_info):
                print(f"{Colors.YELLOW}Warning: Directory not found: {path_info}{Colors.ENDC}")
                continue
            pairs.extend(get_directory_pairs(project_type, path_info))
        elif isinstance(path_info, dict) and 'base' in path_info and 'subdirs' in path_info:
            # Handle higher-level nested structure
            base_dir = path_info['base']
            for subdir_name, subdir_path in path_info['subdirs'].items():
                full_path = f"{base_dir}/{subdir_path}"
                if not os.path.exists(full_path):
                    print(f"{Colors.YELLOW}Warning: Directory not found: {full_path}{Colors.ENDC}")
                    continue
                pairs.extend(get_directory_pairs(f"{project_type}/{subdir_name}", full_path))
    
    return pairs

def get_markdown_filename(project_name, project_type):
    """Convert project name to expected markdown filename format based on project type"""
    # Map of project names to their markdown file names
    low_level_cases = {
        'hello-world': '0x00__C_-_Hello__World',
        'variables': '0x01__C_-_Variables__if__else__while',
        'functions': '0x02__C_-_Functions__nested_loops',
        'debugging': '0x03__C_-_Debugging',
        'more-functions': '0x04__C_-_More_functions__more_nested_loops',
        'pointers': '0x05__C_-_Pointers__arrays_and_strings',
        'more-pointers': '0x06__C_-_More_pointers__arrays_and_strings',
        'even-more-pointers': '0x07__C_-_Even_more_pointers__arrays_and_strings',
        'recursion': '0x08__C_-_Recursion',
        'static-libraries': '0x09__C_-_Static_libraries',
        'argc-argv': '0x0A__C_-_argc__argv',
        'argv': '0x0A__C_-_argc__argv',
        'malloc': '0x0B__C_-_malloc__free',
        'more-malloc': '0x0C__C_-_More_malloc__free',
        'preprocessor': '0x0D__C_-_Preprocessor',
        'structures': '0x0E__C_-_Structures__typedef',
        'function-pointers': '0x0F__C_-_Function_pointers',
        'variadic': '0x10__C_-_Variadic_functions',
        'printf': '0x11__C_-_printf',
        'linked-lists': '0x12__C_-_Singly_linked_lists',
        'more-linked-lists': '0x13__C_-_More_singly_linked_lists',
        'bit-manipulation': '0x14__C_-_Bit_manipulation',
        'file-io': '0x15__C_-_File_I_O',
        'simple-shell': '0x16__C_-_Simple_Shell',
        'doubly-linked-lists': '0x17__C_-_Doubly_linked_lists',
        'dynamic-libraries': '0x18__C_-_Dynamic_libraries',
        'stack-queues': '0x19__C_-_Stacks__Queues_-_LIFO__FIFO',
        'hash-tables': '0x1A__C_-_Hash_tables',
        'sorting': '0x1B__C_-_Sorting_algorithms___Big_O',
        'makefiles': '0x1C__C_-_Makefiles',
        'binary-trees': '0x1D__C_-_Binary_trees',
        'search': '0x1E__C_-_Search_Algorithms'
    }
    
    sysdevops_cases = {
        # Shell basics and fundamentals
        'shell': '0x00__Shell__basics',
        'shell-basics': '0x00__Shell__basics',
        'shell-permissions': '0x01__Shell__permissions',
        'shell-redirections': '0x02__Shell__I_O_Redirections_and_filters',
        'shell-io-redirections': '0x02__Shell__I_O_Redirections_and_filters',
        'shell-variables': '0x03__Shell__init_files__variables_and_expansions',
        'shell-loops': '0x04__Loops__conditions_and_parsing',
        'shell-processes': '0x05__Processes_and_signals',
        'shell-regex': '0x06__Regular_expression',
        
        # Networking
        'networking-basics-0': '0x07__Networking_basics__0',
        'networking-basics-1': '0x08__Networking_basics__1',
        'web-infrastructure': '0x09__Web_infrastructure_design',
        
        # Configuration and Management
        'config-management': '0x0A__Configuration_management',
        'ssh': '0x0B__SSH',
        'web-server': '0x0C__Web_server',
        
        # Web Stack Debugging Series
        'web-stack-debugging-0': '0x0D__Web_stack_debugging__0',
        'web-stack-debugging-1': '0x0E__Web_stack_debugging__1',
        'web-stack-debugging-2': '0x12__Web_stack_debugging__2',
        'web-stack-debugging-3': '0x17__Web_stack_debugging__3',
        
        # Web Services and APIs
        'load-balancer': '0x0F__Load_balancer',
        'https-ssl': '0x10__HTTPS_SSL',
        'api': '0x15__API',
        'api-advanced': '0x16__API_advanced',
        
        # Monitoring and Infrastructure
        'firewall': '0x13__Firewall',
        'mysql': '0x14__MySQL',
        'web-stack-monitoring': '0x18__Webstack_monitoring',
        'webstack-monitoring': '0x18__Webstack_monitoring',
        'application-server': '0x1A__Application_server'
    }
    
    # Initialize high level cases dictionaries
    python_cases = {
        'hello-world': '0x00__Python_-_Hello__World',
        'if-else-loops-functions': '0x01__Python_-_if_else__loops__functions',
        'import-modules': '0x02__Python_-_import___modules',
        'data-structures': '0x03__Python_-_Data_Structures__Lists__Tuples',
        'more-data-structures': '0x04__Python_-_More_Data_Structures__Set__Dictionary',
        'exceptions': '0x05__Python_-_Exceptions',
        'classes-objects': '0x06__Python_-_Classes_and_Objects',
        'test-driven-development': '0x07__Python_-_Test-driven_development',
        'more-classes': '0x08__Python_-_More_Classes_and_Objects',
        'everything-is-object': '0x09__Python_-_Everything_is_object',
        'inheritance': '0x0A__Python_-_Inheritance',
        'input-output': '0x0B__Python_-_Input_Output',
        'almost-circle': '0x0C__Python_-_Almost_a_circle',
        'orm': '0x0F__Python_-_Object-relational_mapping',
        'network-0': '0x10__Python_-_Network__0',
        'network-1': '0x11__Python_-_Network__1'
    }

    javascript_cases = {
        'warmup': '0x12__JavaScript_-_Warm_up',
        'objects': '0x13__JavaScript_-_Objects__Scopes_and_Closures',
        'web-scraping': '0x14__JavaScript_-_Web_scraping',
        'jquery': '0x15__JavaScript_-_Web_jQuery'
    }

    sql_cases = {
        'introduction': '0x0D__SQL_-_Introduction',
        'more-queries': '0x0E__SQL_-_More_queries'
    }

    airbnb_cases = {
        'console': '0x00__AirBnB_clone_-_The_console',
        'web-static': '0x01__AirBnB_clone_-_Web_static',
        'mysql': '0x02__AirBnB_clone_-_MySQL',
        'deploy-static': '0x03__AirBnB_clone_-_Deploy_static',
        'web-framework': '0x04__AirBnB_clone_-_Web_framework',
        'rest-api': '0x05__AirBnB_clone_-_RESTful_API',
        'web-dynamic': '0x06__AirBnB_clone_-_Web_dynamic'
    }

    # Select the appropriate mapping based on project type
    if project_type == 'low-level':
        special_cases = low_level_cases
    elif project_type == 'system-engineering-devops':
        special_cases = sysdevops_cases
    elif project_type.startswith('higher-level/'):
        # Extract the subdirectory name from project type
        _, subdir = project_type.split('/', 1)
        if subdir == 'python':
            special_cases = python_cases
        elif subdir == 'javascript':
            special_cases = javascript_cases
        elif subdir == 'sql':
            special_cases = sql_cases
        elif subdir == 'airbnb':
            special_cases = airbnb_cases
        else:
            special_cases = {}
    else:
        special_cases = {}
    
    return special_cases.get(project_name, f"Unknown_{project_name}")

# Functions for extracting and comparing sections
def get_section_between(content, start_marker, end_markers):
    """Extract text between a start marker and any of the specified end markers"""
    if not content or not start_marker:
        return ""
    
    # Find the start of the section
    start_index = content.find(start_marker)
    if start_index == -1:
        return ""
    
    # Move to the end of the start marker
    start_index += len(start_marker)
    
    # Find the earliest end marker
    end_index = len(content)
    for end_marker in end_markers:
        pos = content.find(end_marker, start_index)
        if pos != -1 and pos < end_index:
            end_index = pos
    
    return content[start_index:end_index].strip()

def extract_section(content, section_name):
    """Extract a specific section from the content based on section name"""
    if not content:
        return ""
    
    # Define markers based on section name - for both JS and Markdown
    if section_name == "Resources":
        start_markers = ["## Resources", "\\## Resources"]
        end_markers = ["## Learning Objectives", "\\## Learning Objectives", 
                       "## Requirements", "\\## Requirements", 
                       "## More Info", "\\## More Info", 
                       "## Tasks", "\\## Tasks", 
                       "### General", "\\### General"]
    elif section_name == "Additional Resources":
        start_markers = ["## Additional Resources", "\\## Additional Resources"]
        end_markers = ["## Learning Objectives", "\\## Learning Objectives", 
                       "## Requirements", "\\## Requirements", 
                       "## More Info", "\\## More Info", 
                       "## Tasks", "\\## Tasks", 
                       "### General", "\\### General"]
    elif section_name == "Learning Objectives":
        start_markers = ["## Learning Objectives", "\\## Learning Objectives"]
        end_markers = ["## Requirements", "\\## Requirements", 
                       "## More Info", "\\## More Info", 
                       "## Tasks", "\\## Tasks", 
                       "## Resources", "\\## Resources", 
                       "### Python Scripts", "\\### Python Scripts"]
    elif section_name == "Requirements":
        start_markers = ["## Requirements", "\\## Requirements"]
        end_markers = ["## More Info", "\\## More Info", 
                       "## Tasks", "\\## Tasks", 
                       "## Resources", "\\## Resources", 
                       "## Learning Objectives", "\\## Learning Objectives", 
                       "### GitHub", "\\### GitHub", 
                       "### Quiz Questions", "\\### Quiz Questions"]
    elif section_name == "More Info":
        start_markers = ["## More Info", "\\## More Info"]
        end_markers = ["## Tasks", "\\## Tasks", 
                       "## Resources", "\\## Resources", 
                       "## Learning Objectives", "\\## Learning Objectives", 
                       "## Requirements", "\\## Requirements"]
    else:
        return ""
    
    # Try each start marker
    section = ""
    for start_marker in start_markers:
        if start_marker in content:
            # We found a matching start marker, now extract content until end marker
            section = get_section_between(content, start_marker, end_markers)
            if section:
                break
    
    # If we couldn't find the section, try with variations in formatting
    if not section:
        # Try with different heading levels
        for level in range(1, 6):
            for marker_text in [section_name, section_name.upper()]:
                alt_marker = "#" * level + " " + marker_text
                alt_marker_escaped = "\\" + alt_marker
                for marker in [alt_marker, alt_marker_escaped]:
                    section = get_section_between(content, marker, end_markers)
                    if section:
                        break
                if section:
                    break
            if section:
                break
    
    # If we still couldn't find the section, try with common section content
    if not section and section_name == "Learning Objectives":
        # Try to find the typical start of Learning Objectives
        typical_starts = [
            "At the end of this project, you are expected to be able to",
            "At the end of this project, you are expected to be able to explain to anyone"
        ]
        for typical_start in typical_starts:
            if typical_start in content:
                start_idx = content.find(typical_start)
                # Find the end by looking for the next section header
                end_idx = content.find("##", start_idx)
                if end_idx == -1:
                    end_idx = len(content)
                section = content[start_idx:end_idx].strip()
                break
    
    # Special handling for Resources section - it might just be a list without a header
    if not section and (section_name == "Resources" or section_name == "Additional Resources"):
        # Look for patterns like "* [Resource](link)" or "- [Resource](link)"
        link_patterns = ["* [", "- [", "\\* [", "\\- ["]
        for pattern in link_patterns:
            if pattern in content:
                # Find the start of the first link
                start_idx = content.find(pattern)
                # Find where the links section might end (look for next section or end of content)
                end_idx = len(content)
                for end_marker in end_markers:
                    pos = content.find(end_marker, start_idx)
                    if pos != -1 and pos < end_idx:
                        end_idx = pos
                
                # Extract the links section
                links_section = content[start_idx:end_idx].strip()
                
                # Verify we have actual links
                if "[" in links_section and "](" in links_section:
                    section = links_section
                    break
    
    return section

def normalize_section(content, section_name):
    """Normalize section content for comparison by removing formatting"""
    if not content:
        return ""
    
    # Make a working copy
    normalized = content
    
    # Handle escaped characters in JS template literals vs Markdown
    normalized = normalized.replace("\\`", "`")  # Convert escaped backticks
    normalized = normalized.replace("\\\\", "\\")  # Convert escaped backslashes
    normalized = normalized.replace("\\_", "_")  # Convert escaped underscores
    
    # Handle specific section types
    if section_name == "Resources" or section_name == "Additional Resources":
        # Extract and normalize links
        links = re.findall(r'[\*\-]?\s*\[(.*?)\]\((.*?)\)', normalized)
        formatted_links = []
        for desc, url in links:
            # Clean up the description and URL
            desc = desc.strip()
            url = url.strip()
            formatted_links.append(f"{desc} - {url}")
        
        if formatted_links:
            normalized = "\n".join(formatted_links)
        else:
            # If link extraction failed, fallback to text comparison
            # Remove markdown formatting for links
            normalized = re.sub(r'\[(.*?)\]\((.*?)\)', r'\1 \2', normalized)
            # Remove bullet points
            normalized = re.sub(r'^\s*[\*\-]\s+', '', normalized, flags=re.MULTILINE)
    
    elif section_name == "Learning Objectives":
        # First, remove the intro text which may vary in formatting
        if "expected to be able to" in normalized:
            # Don't split and remove the introduction text as it's part of the section
            # normalized = normalized.split("expected to be able to")[1]
            # Keep the introduction but remove "explain to anyone" and "without the help of Google" which may be formatted differently
            normalized = re.sub(r'\[explain to anyone\].*?without the help of Google', '', normalized, flags=re.DOTALL)
        
        # Don't remove the subheadings as they are part of the section
        # normalized = re.sub(r'###\s+General.*?(?=###|\Z)', '', normalized, flags=re.DOTALL)
        # normalized = re.sub(r'###\s+Copyright - Plagiarism.*?(?=###|\Z)', '', normalized, flags=re.DOTALL)
        
        # Extract bullet points, focusing on the actual learning objectives
        bullet_points = re.findall(r'[\*\-]\s+(.*?)(?:\n|$)', normalized)
        if bullet_points:
            # Join bullet points with a separator for better comparison
            normalized = " | ".join([point.strip() for point in bullet_points])
        else:
            # If bullet point extraction fails, remove all formatting but preserve key content
            normalized = re.sub(r'[\*\-\#\`]', '', normalized)  # Remove common formatting chars
            normalized = re.sub(r'\s+', ' ', normalized)  # Normalize whitespace
    
    elif section_name == "Requirements":
        # Handle subsections which might be formatted differently
        # First try to identify subsections by headers
        subsections = re.split(r'###\s+|\*\*([^*]+)\*\*', normalized)
        cleaned_subsections = []
        
        for subsection in subsections:
            if subsection and subsection.strip():
                # Clean subsection text
                cleaned = re.sub(r'[\*\-\#\`]', '', subsection)  # Remove formatting
                cleaned = re.sub(r'\s+', ' ', cleaned)  # Normalize whitespace
                if cleaned.strip():
                    cleaned_subsections.append(cleaned.strip())
        
        if cleaned_subsections:
            normalized = " | ".join(cleaned_subsections)
        else:
            # If subsection extraction fails, remove all formatting but preserve key content
            normalized = re.sub(r'[\*\-\#\`]', '', normalized)  # Remove common formatting chars
            normalized = re.sub(r'\s+', ' ', normalized)  # Normalize whitespace
    
    else:
        # For other sections, standard normalization
        # Remove markdown links but keep text and URL
        normalized = re.sub(r'\[(.*?)\]\((.*?)\)', r'\1 \2', normalized)
        # Remove formatting characters
        normalized = re.sub(r'[\*\-\#\`]', '', normalized)
        # Normalize whitespace
        normalized = re.sub(r'\s+', ' ', normalized)
    
    # Final cleanup for all sections
    # Remove quotes
    normalized = normalized.replace('"', '').replace("'", '')
    # Remove common punctuation
    for char in [',', ';', ':', '(', ')', '{', '}', '[', ']']:
        normalized = normalized.replace(char, ' ')
    # Normalize whitespace again
    normalized = re.sub(r'\s+', ' ', normalized)
    # Lowercase for case-insensitive comparison
    normalized = normalized.lower()
    
    return normalized.strip()

def extract_links(content):
    """Extract links from markdown content, keeping URL and description"""
    if not content:
        return ""
    
    # Extract links and their descriptions
    links = re.findall(r'\[([^\]]+)\]\(([^)]+)\)', content)
    
    # Format links for comparison
    formatted_links = []
    for desc, url in links:
        formatted_links.append(f"{desc.strip()} ({url.strip()})")
    
    return ' '.join(formatted_links)

def compare_resources(js_content, md_content):
    """Compare resources section specially to handle links"""
    # Normalize both contents
    js_normalized = normalize_section(js_content, "Resources")
    md_normalized = normalize_section(md_content, "Resources")
    
    # If either is empty, return 0
    if not js_normalized or not md_normalized:
        return 0.0
    
    # Calculate similarity score
    similarity = get_similarity(js_normalized, md_normalized)
    
    return similarity

def get_similarity(text1, text2):
    """
    Calculate similarity between two text strings
    Returns a float between 0 and 1 where 1 is perfect match
    """
    if not text1 or not text2:
        return 0.0
    
    # Simple character-based similarity
    s1 = text1.lower()
    s2 = text2.lower()
    
    # Calculate Jaccard similarity for character trigrams
    def get_ngrams(s, n=3):
        return [s[i:i+n] for i in range(len(s)-n+1)]
    
    trigrams1 = set(get_ngrams(s1))
    trigrams2 = set(get_ngrams(s2))
    
    if not trigrams1 or not trigrams2:
        return 0.0
    
    intersection = trigrams1.intersection(trigrams2)
    union = trigrams1.union(trigrams2)
    
    similarity = len(intersection) / len(union) if union else 0.0
    return similarity

def get_section_from_js(js_content, section_name):
    """Extract and normalize a section from JS content."""
    if not js_content or not section_name:
        return "", ""
    
    # Extract the raw section from the JS file
    raw_section = extract_section(js_content, section_name)
    
    # Normalize the section for comparison
    normalized = normalize_section(raw_section, section_name)
    
    return raw_section, normalized

def get_section_from_md(md_content, section_name):
    """Extract and normalize a section from Markdown content."""
    if not md_content or not section_name:
        return "", ""
    
    # Extract the raw section from the Markdown file
    raw_section = extract_section(md_content, section_name)
    
    # Normalize the section for comparison
    normalized = normalize_section(raw_section, section_name)
    
    return raw_section, normalized

def compare_section(js_file, md_file, section_name, verbose=False):
    """
    Compare a specific section between JS and MD files.
    
    Args:
        js_file: Path to the JavaScript file
        md_file: Path to the Markdown file
        section_name: Name of the section to compare
        verbose: Whether to print detailed output
        
    Returns:
        Dictionary with comparison results
    """
    result = {
        'section': section_name,
        'js_file': js_file,
        'md_file': md_file,
        'status': 'error',
        'similarity': 0.0,
        'raw_js': '',
        'raw_md': '',
        'normalized_js': '',
        'normalized_md': '',
        'error': None
    }
    
    try:
        # Read file contents
        with open(js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        with open(md_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # Extract and normalize sections
        raw_js, normalized_js = get_section_from_js(js_content, section_name)
        raw_md, normalized_md = get_section_from_md(md_content, section_name)
        
        # Store raw and normalized content
        result['raw_js'] = raw_js
        result['raw_md'] = raw_md
        result['normalized_js'] = normalized_js
        result['normalized_md'] = normalized_md
        
        # Calculate similarity
        if section_name in ["Resources", "Additional Resources"]:
            similarity = compare_resources(raw_js, raw_md)
        else:
            similarity = get_similarity(normalized_js, normalized_md)
        
        result['similarity'] = similarity
        
        # Set uniform threshold of 0.85 for all sections
        threshold = 0.85
        
        if similarity >= threshold:
            result['status'] = 'pass'
        else:
            result['status'] = 'fail'
        
        if verbose:
            print(f"\n{Colors.BOLD}Comparing {section_name}{Colors.ENDC}")
            print(f"JS File: {js_file}")
            print(f"MD File: {md_file}")
            print(f"Similarity: {similarity:.2f} (Threshold: {threshold:.2f})")
            print(f"Status: {result['status'].upper()}")
            
            if normalized_js and normalized_md:
                print(f"\n{Colors.BLUE}Section found in both files{Colors.ENDC}")
                if similarity < threshold or verbose:
                    print(f"\n{Colors.YELLOW}Normalized JS Section:{Colors.ENDC}")
                    print(normalized_js[:300] + "..." if len(normalized_js) > 300 else normalized_js)
                    print(f"\n{Colors.YELLOW}Normalized MD Section:{Colors.ENDC}")
                    print(normalized_md[:300] + "..." if len(normalized_md) > 300 else normalized_md)
                    
                    # Show even more debug info when very verbose
                    if verbose and similarity < threshold:
                        print(f"\n{Colors.BOLD}Raw JS Section:{Colors.ENDC}")
                        print(raw_js[:300] + "..." if len(raw_js) > 300 else raw_js)
                        print(f"\n{Colors.BOLD}Raw MD Section:{Colors.ENDC}")
                        print(raw_md[:300] + "..." if len(raw_md) > 300 else raw_md)
            elif not normalized_js and not normalized_md:
                print(f"\n{Colors.RED}Section not found in either file{Colors.ENDC}")
            elif not normalized_js:
                print(f"\n{Colors.RED}Section not found in JS file{Colors.ENDC}")
                print(f"\n{Colors.YELLOW}MD Section found:{Colors.ENDC}")
                print(raw_md[:300] + "..." if len(raw_md) > 300 else raw_md)
            elif not normalized_md:
                print(f"\n{Colors.RED}Section not found in MD file{Colors.ENDC}")
                print(f"\n{Colors.YELLOW}JS Section found:{Colors.ENDC}")
                print(raw_js[:300] + "..." if len(raw_js) > 300 else raw_js)
    
    except Exception as e:
        result['status'] = 'error'
        result['error'] = str(e)
        if verbose:
            print(f"{Colors.RED}Error comparing {section_name}: {e}{Colors.ENDC}")
    
    return result

def check_sections(project_name=None, project_type=None, sections=None, verbose=False):
    """
    Check sections in JS and MD files for one or all projects.
    Returns a list of section comparison results.
    """
    all_results = []
    project_results = {}
    
    # Default sections to check if not specified
    if sections is None:
        sections = ["Resources", "Learning Objectives", "Requirements", "Additional Resources"]
    
    # Get all project pairs
    all_pairs = get_project_pairs()
    
    # Filter projects based on name and type if specified
    filtered_pairs = []
    if project_name:
        filtered_pairs = [pair for pair in all_pairs if pair['project_name'] == project_name]
        if not filtered_pairs:
            print(f"{Colors.RED}No projects found matching the criteria.{Colors.ENDC}")
            print(f"Project '{project_name}' not found.")
            return {}
    elif project_type:
        filtered_pairs = [pair for pair in all_pairs if pair['project_type'] == project_type]
        if not filtered_pairs:
            print(f"{Colors.RED}No projects found matching the criteria.{Colors.ENDC}")
            print(f"Project type '{project_type}' not found.")
            return {}
    else:
        filtered_pairs = all_pairs
    
    print(f"Checking {len(filtered_pairs)} projects for {len(sections)} sections each.")
    
    # Check sections for each project
    for pair in filtered_pairs:
        print(f"Checking sections for {pair['project_name']} ({pair['project_type']})")
        
        # Create project results structure
        project_key = f"{pair['project_name']}_{pair['project_type']}"
        project_results[project_key] = {
            'project_name': pair['project_name'],
            'project_type': pair['project_type'],
            'js_file': pair['js_file'],
            'md_file': pair['md_file'],
            'total_sections': 0,
            'passed_sections': 0,
            'failed_sections': 0,
            'error_sections': 0,
            'section_results': []
        }
        
        for section in sections:
            print(f"  Checking section: {section}")
            result = compare_section(pair['js_file'], pair['md_file'], section, verbose)
            threshold = 0.85  # Uniform threshold for all sections
            if result['status'] == 'pass':
                print(f"  {section}: {Colors.GREEN}PASS{Colors.ENDC} ({result['similarity']:.2f}) (threshold: {threshold:.2f})")
                project_results[project_key]['passed_sections'] += 1
            elif result['status'] == 'fail':
                print(f"  {section}: {Colors.RED}FAIL{Colors.ENDC} ({result['similarity']:.2f}) (threshold: {threshold:.2f})")
                project_results[project_key]['failed_sections'] += 1
                if not verbose:
                    # Print a brief summary of the content if not verbose mode
                    if result['raw_js'] and result['raw_md']:
                        print(f"    Raw JS content: {result['raw_js'][:300]}...")
                        print(f"    Raw MD content: {result['raw_md'][:300]}...")
                    elif not result['raw_js']:
                        print(f"    JS section not found")
                    elif not result['raw_md']:
                        print(f"    MD section not found")
            else:
                print(f"  {section}: {Colors.YELLOW}ERROR{Colors.ENDC} ({result['error']})")
                project_results[project_key]['error_sections'] += 1
            
            result['project_name'] = pair['project_name']
            result['project_type'] = pair['project_type']
            all_results.append(result)
            project_results[project_key]['section_results'].append(result)
            project_results[project_key]['total_sections'] += 1
        
        # Save individual project report
        save_project_report(project_results[project_key])
    
    return project_results

def save_project_report(project_data):
    """
    Save the comparison results for a single project to the reports directory
    """
    project_name = project_data['project_name']
    project_type = project_data['project_type']
    
    # Create the appropriate directory structure based on project type
    if '/' in project_type:
        # Convert higher-level/subdir to higher-level_subdir for directory name
        main_type, subdir = project_type.split('/')
        report_dir = f"{REPORTS_DIR}/{main_type}_{subdir}"
    else:
        report_dir = f"{REPORTS_DIR}/{project_type}"
    
    os.makedirs(report_dir, exist_ok=True)
    
    # Prepare the reports
    json_report = {
        'project_name': project_name,
        'project_type': project_type,
        'js_file': project_data['js_file'],
        'md_file': project_data['md_file'],
        'total_sections': project_data['total_sections'],
        'passed_sections': project_data['passed_sections'],
        'failed_sections': project_data['failed_sections'],
        'error_sections': project_data['error_sections'],
        'section_results': project_data['section_results']
    }
    
    # Create a text report for human-readable output
    text_report = f"Section Comparison Report for {project_name} ({project_type})\n"
    text_report += f"Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    text_report += f"JS File: {project_data['js_file']}\n"
    text_report += f"MD File: {project_data['md_file']}\n\n"
    text_report += "Summary:\n"
    text_report += f"Total sections: {project_data['total_sections']}\n"
    text_report += f"Passed sections: {project_data['passed_sections']}\n"
    text_report += f"Failed sections: {project_data['failed_sections']}\n"
    text_report += f"Error sections: {project_data['error_sections']}\n\n"
    text_report += "Section Results:\n\n"
    
    for result in project_data['section_results']:
        section_name = result['section']
        status = result['status'].upper()
        similarity = result.get('similarity', 0.0)
        
        if status == 'PASS':
            text_report += f"{section_name}: {status} (Similarity: {similarity:.2f})\n\n"
        else:
            text_report += f"{section_name}: {status} (Similarity: {similarity:.2f})\n"
            text_report += "  Differences between files:\n"
            
            if similarity < 0.85 and status == 'FAIL':
                text_report += "  - Similarity score below threshold (0.85)\n\n"
            
            if 'raw_js' in result and result['raw_js']:
                text_report += f"  JS Section (first 300 chars):\n"
                text_report += f"  {result['normalized_js'][:300]}...\n\n"
            
            if 'raw_md' in result and result['raw_md']:
                text_report += f"  MD Section (first 300 chars):\n"
                text_report += f"  {result['normalized_md'][:300]}...\n\n"
            
            if result.get('error'):
                text_report += f"  Error: {result['error']}\n\n"
    
    # Save the reports
    json_path = f"{report_dir}/{project_name}_section_comparison.json"
    text_path = f"{report_dir}/{project_name}_section_comparison.txt"
    
    with open(json_path, 'w') as f:
        json.dump(json_report, f, indent=2)
    
    with open(text_path, 'w') as f:
        f.write(text_report)
    
    print(f"  Report saved to {text_path}")
    return text_path

def update_summary_report(results=None, verbose=False):
    """
    Generate or update the summary report based on individual project reports
    """
    if verbose:
        print("\nUpdating summary report...")
    
    # If no results provided, scan the directories for existing reports
    if results is None:
        results = []
        # Scan the report directories for all project reports
        for project_type, path_info in PROJECT_DIRS.items():
            if isinstance(path_info, str):
                report_dir = f"{REPORTS_DIR}/{project_type}"
                if os.path.exists(report_dir):
                    scan_report_directory(report_dir, results)
            elif isinstance(path_info, dict) and 'subdirs' in path_info:
                for subdir in path_info['subdirs'].keys():
                    report_dir = f"{REPORTS_DIR}/{project_type}_{subdir}"
                    if os.path.exists(report_dir):
                        scan_report_directory(report_dir, results)
    
    # Group projects by type
    projects_by_type = {}
    for result in results:
        project_type = result.get('project_type', 'unknown')
        if project_type not in projects_by_type:
            projects_by_type[project_type] = []
        projects_by_type[project_type].append(result)
    
    # Count failing projects
    failing_projects = []
    for project_type, projects in projects_by_type.items():
        for project in projects:
            if project.get('failed_sections', 0) > 0:
                failing_projects.append(project)
    
    # Generate the summary report
    with open(SUMMARY_FILE, 'w') as f:
        f.write("# Section Comparison Summary\n")
        f.write(f"Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Write failing projects section
        f.write("## Projects with Failed Sections\n\n")
        if failing_projects:
            for project in failing_projects:
                project_name = project.get('project_name', 'unknown')
                project_type = project.get('project_type', 'unknown')
                passed = project.get('passed_sections', 0)
                total = project.get('total_sections', 0)
                
                # Get the failed section names
                failed_sections = []
                for section in project.get('section_results', []):
                    if section.get('status') == 'fail':
                        failed_sections.append(section.get('section'))
                
                f.write(f"- **{project_name}** ({project_type}): Passed {passed}/{total} sections. "
                       f"Failed sections: {', '.join(failed_sections)}\n\n")
        else:
            f.write("No failing projects. All sections pass!\n\n")
        
        # Write summary statistics
        f.write("## Summary Statistics\n\n")
        f.write(f"- Total Projects: {len(results)}\n")
        f.write(f"- Passing Projects: {len(results) - len(failing_projects)}\n")
        f.write(f"- Failing Projects: {len(failing_projects)}\n")
        
        # Write type-specific sections
        for project_type, projects in sorted(projects_by_type.items()):
            # Skip types with no projects
            if not projects:
                continue
                
            # Get a prettier title for the section
            if '/' in project_type:
                main_type, subdir = project_type.split('/')
                section_title = f"{main_type.replace('-', ' ').title()} - {subdir.title()}"
            else:
                section_title = project_type.replace('-', ' ').title()
                
            f.write(f"\n## {section_title}\n\n")
            
            # Group by passing/failing
            passing = [p for p in projects if p.get('failed_sections', 0) == 0]
            failing = [p for p in projects if p.get('failed_sections', 0) > 0]
            
            # Write passing projects
            if passing:
                f.write("### Passing Projects\n\n")
                for project in sorted(passing, key=lambda x: x.get('project_name', '')):
                    project_name = project.get('project_name', 'unknown')
                    passed = project.get('passed_sections', 0)
                    total = project.get('total_sections', 0)
                    f.write(f"- **{project_name}**: {passed}/{total} sections passed\n")
                f.write("\n")
            
            # Write failing projects
            if failing:
                f.write("### Failing Projects\n\n")
                for project in sorted(failing, key=lambda x: x.get('project_name', '')):
                    project_name = project.get('project_name', 'unknown')
                    passed = project.get('passed_sections', 0)
                    total = project.get('total_sections', 0)
                    
                    # Get the failed section names
                    failed_sections = []
                    for section in project.get('section_results', []):
                        if section.get('status') == 'fail':
                            failed_sections.append(section.get('section'))
                    
                    f.write(f"- **{project_name}**: {passed}/{total} sections passed. Failed: {', '.join(failed_sections)}\n")
                f.write("\n")
    
    if verbose:
        print(f"Summary report updated at {SUMMARY_FILE}")
        print(f"Passing projects: {len(results) - len(failing_projects)}/{len(results)}")
    
    return SUMMARY_FILE

def scan_report_directory(report_dir, results):
    """Scan a report directory for project reports and add them to results list"""
    for filename in os.listdir(report_dir):
        if filename.endswith('_section_comparison.json'):
            json_path = os.path.join(report_dir, filename)
            try:
                with open(json_path, 'r') as f:
                    project_data = json.load(f)
                    results.append(project_data)
            except (json.JSONDecodeError, FileNotFoundError):
                print(f"Warning: Could not read report file {json_path}")

def list_available_projects():
    """List all available projects that can be checked"""
    pairs = get_project_pairs()
    
    print(f"\n{Colors.HEADER}Available Projects:{Colors.ENDC}")
    print("-" * 50)
    print(f"{'Project Name':<25} {'Project Type':<30} {'JS File':<40}")
    print("-" * 100)
    
    for pair in pairs:
        js_file = os.path.basename(pair['js_file'])
        print(f"{pair['project_name']:<25} {pair['project_type']:<30} {js_file:<40}")
    
    print("\nUse the project name with --project argument to check a specific project.")

def display_project_status(project_name=None, verbose=False):
    """
    Display the status of a specific project or all projects.
    
    Args:
        project_name: Name of project to display, or None for all
        verbose: Whether to show detailed output
    """
    if project_name:
        # Try to find the project report
        found = False
        for root, dirs, files in os.walk(REPORTS_DIR):
            for file in files:
                if file == f"{project_name}_section_comparison.txt":
                    found = True
                    with open(os.path.join(root, file), 'r') as f:
                        print(f.read())
                        
        if not found:
            print(f"{Colors.YELLOW}No report found for project '{project_name}'.{Colors.ENDC}")
            print("Run with --project flag first or check project name with --list-projects.")
    else:
        # Display the overall summary
        if os.path.exists(SUMMARY_FILE):
            with open(SUMMARY_FILE, 'r') as f:
                summary_content = f.read()
                print(summary_content)
        else:
            print(f"{Colors.YELLOW}Summary file doesn't exist. Run with --update-summary first.{Colors.ENDC}")

def main():
    """
    Main function to run the unified section checker
    """
    parser = argparse.ArgumentParser(
        description="Unified Section Checker for ALX Course Tracker",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # Command-line arguments
    project_group = parser.add_mutually_exclusive_group()
    project_group.add_argument('--project', help='Project to check')
    project_group.add_argument('--all', action='store_true', help='Check all projects')
    project_group.add_argument('--update-summary', action='store_true', help='Update the summary report')
    project_group.add_argument('--list-projects', action='store_true', help='List all available projects')
    project_group.add_argument('--status', nargs='?', const='all', help='Show project status')
    
    parser.add_argument('--verbose', action='store_true', help='Show detailed output')
    parser.add_argument('--no-summary', action='store_true', help='Skip summary update')
    parser.add_argument('--sections', help='Comma-separated list of sections to check')
    
    args = parser.parse_args()
    
    if not args.project and not args.all and not args.update_summary and not args.list_projects and not args.status:
        parser.print_help()
        return 1
    
    # List available projects
    if args.list_projects:
        list_available_projects()
        return 0
    
    # Show project status
    if args.status:
        display_project_status(args.status if args.status != 'all' else None, args.verbose)
        return 0
    
    # Update summary report
    if args.update_summary:
        update_summary_report(verbose=args.verbose)
        return 0
    
    # Parse sections to check
    sections_to_check = None
    if args.sections:
        sections_to_check = [s.strip() for s in args.sections.split(',')]
    
    # Check projects
    if args.project:
        # Check a single project
        results = check_sections(args.project, None, sections_to_check, args.verbose)
    elif args.all:
        # Check all projects
        results = check_sections(None, None, sections_to_check, args.verbose)
    
    # Update summary unless specified not to
    if not args.no_summary:
        update_summary_report(verbose=args.verbose)
    
    # Display final status
    if (args.all or args.project) and results:
        print("\nFinal Results:")
        total_sections = sum(r['total_sections'] for r in results.values())
        passed_sections = sum(r['passed_sections'] for r in results.values())
        failed_sections = sum(r['failed_sections'] for r in results.values())
        
        if total_sections > 0:
            print(f"Total sections checked: {total_sections}")
            print(f"Passed: {passed_sections} ({passed_sections/total_sections*100:.1f}%)")
            print(f"Failed: {failed_sections} ({failed_sections/total_sections*100:.1f}%)")
            
            if failed_sections > 0:
                print("\nSome sections failed. See reports for details.")
                return 1
            else:
                print("\nAll sections passed!")
                return 0
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
