#!/usr/bin/env python3
"""
Script to compare specific sections between JS and MD project files.
Usage: ./compare_sections.py <js_file> <md_file> <section_name> [--verbose]
Sections: Resources, Additional Resources, Learning Objectives, Requirements
"""

import sys
import re
import os
import argparse

TICK = "✅"
CROSS = "❌"
VERBOSE = False  # Default to False, will be set by command line arg

def get_section_between(content, start_marker, end_markers):
    """Extract content between start_marker and the first end_marker found"""
    # Find the start marker
    start_idx = content.find(start_marker)
    if start_idx == -1:
        return None
    
    # Move past the start marker
    start_idx += len(start_marker)
    
    # Find the first occurrence of any end marker
    end_idx = len(content)
    for marker in end_markers:
        marker_idx = content.find(marker, start_idx)
        if marker_idx != -1 and marker_idx < end_idx:
            end_idx = marker_idx
    
    # Extract the section
    section = content[start_idx:end_idx].strip()
    return section

def extract_section(content, section_name):
    """Extract a section from the content based on the section name"""
    if not content:
        if VERBOSE:
            print("No content provided")
        return None

    # Extract content from JavaScript template literals if needed
    if content.find("content: `") != -1:
        start = content.find("content: `")
        if start != -1:
            # Look for the closing backtick followed by comma or newline
            end = content.find("`,", start + len("content: `"))
            if end == -1:  # If not found, try just the backtick
                end = content.find("`", start + len("content: `"))
            
            if end != -1:
                content = content[start + len("content: `"):end]
                if VERBOSE:
                    print(f"Extracted content from template literal: {len(content)} chars")

    # Define possible section headers and end markers
    section_headers = {
        "Resources": ["## Resources", "## Resource"],
        "Additional Resources": ["## Additional Resources", "## Additional Resource"],
        "Learning Objectives": ["## Learning Objectives", "## Learning Objective"],
        "Requirements": ["## Requirements", "## Requirement"]
    }
    
    # Define possible end markers for each section
    section_end_markers = {
        "Resources": ["## Learning Objectives", "## Additional Resources", "## Quiz"],
        "Additional Resources": ["## Learning Objectives", "## Quiz"],
        "Learning Objectives": ["## Requirements", "## Quiz", "## Copyright", "### Copyright"],
        "Requirements": ["## More Info", "## Tasks", "## Quiz", "## Copyright", "### Copyright", "### Tasks"]
    }
    
    # Get headers for this section
    possible_headers = section_headers.get(section_name, [])
    if not possible_headers:
        if VERBOSE:
            print(f"No headers defined for section: {section_name}")
        return None
    
    # Try each possible header
    start_idx = -1
    used_header = None
    
    for header in possible_headers:
        start_idx = content.find(header)
        if start_idx != -1:
            used_header = header
            break
    
    if start_idx == -1:
        if VERBOSE:
            print(f"Could not find any header for section: {section_name}")
            print(f"Tried headers: {possible_headers}")
            print(f"Content preview: {content[:200]}")
        return None
    
    # Find the end of the section
    end_markers = section_end_markers.get(section_name, []) + ["##"]  # Default to any heading
    end_idx = len(content)
    
    for marker in end_markers:
        pos = content.find(marker, start_idx + len(used_header))
        if pos != -1 and pos < end_idx:
            end_idx = pos
    
    # Extract the section content
    section_content = content[start_idx + len(used_header):end_idx].strip()
    
    if VERBOSE:
        print(f"Found section '{section_name}' using header: {used_header}")
        print(f"Section length: {len(section_content)} chars")
        if section_content:
            print(f"Section preview: {section_content[:100]}")
        else:
            print("Section is empty")
    
    return section_content

def normalize_section(content, section_name):
    """Normalize section content for comparison"""
    # Remove empty lines
    content = re.sub(r'\n\s*\n', '\n', content)
    # Remove specific formatting
    content = re.sub(r'\*\*|\*|`|_', '', content)
    # Convert to lowercase for case-insensitive comparison
    content = content.lower()
    # Remove URLs
    content = re.sub(r'https?://\S+', '', content)
    # Remove leading/trailing whitespace
    content = content.strip()
    
    return content

def extract_links(content):
    """Extract and normalize links from a section"""
    if not content:
        return []

    # Extract markdown-style links using regex
    md_links = re.findall(r'\[([^\]]+)\]\(([^\)]+)(?:\s+"[^"]*")?\)', content)
    
    # Extract plain URLs (if any)
    plain_urls = re.findall(r'<(https?://[^>]+)>', content)
    
    links = []
    
    # Process markdown links
    for title, url in md_links:
        # Clean up the title and URL
        title = title.strip().lower()
        # Remove quotes from URL and get the base URL
        url = url.split()[0].strip('"\'').strip()
        # Extract just the domain and path for comparison
        domain_path = re.sub(r'^https?://', '', url)
        domain_path = re.sub(r'\?.*$', '', domain_path)  # Remove query params
        
        # Add normalized link
        links.append(f"{title}|{domain_path}")
    
    # Process plain URLs
    for url in plain_urls:
        url = url.strip()
        domain_path = re.sub(r'^https?://', '', url)
        domain_path = re.sub(r'\?.*$', '', domain_path)
        links.append(f"url|{domain_path}")
    
    return sorted(set(links))

def normalize_url(url):
    """Normalize a URL by removing everything after any space or quote"""
    # Remove any title or quotation part
    return url.split()[0].strip('"\'')

def compare_resources(js_content, md_content):
    """Compare the resources section between JS and MD files"""
    js_section = extract_section(js_content, "Resources")
    md_section = extract_section(md_content, "Resources")

    if not js_section or not md_section:
        print(f"{CROSS} Could not extract Resources section from one or both files.")
        if VERBOSE:
            print(f"JS section found: {bool(js_section)}")
            print(f"MD section found: {bool(md_section)}")
            if js_section:
                print(f"JS section preview: {js_section[:100]}")
            if md_section:
                print(f"MD section preview: {md_section[:100]}")
        return 1

    # Extract and normalize links from both sections
    js_links = extract_links(js_section)
    md_links = extract_links(md_section)
    
    if VERBOSE:
        print(f"JS content length: {len(js_content)} chars")
        print(f"MD content length: {len(md_content)} chars")
        print(f"JS section length: {len(js_section)} chars")
        print(f"MD section length: {len(md_section)} chars")
        print("\nJS links:")
        for link in js_links:
            print(f"  {link}")
        print("\nMD links:")
        for link in md_links:
            print(f"  {link}")

    # Compare the normalized links
    js_link_set = set(js_links)
    md_link_set = set(md_links)
    
    # We consider resources equivalent if the sets are equal OR
    # if each set has at least 70% of the other set's links (for flexibility)
    if js_link_set == md_link_set:
        print(f"{TICK} Resources section is equivalent (exact match)")
        return 0
    
    # Calculate overlap percentage
    if js_links and md_links:
        common_links = js_link_set.intersection(md_link_set)
        js_overlap = len(common_links) / len(js_link_set) * 100
        md_overlap = len(common_links) / len(md_link_set) * 100
        
        if js_overlap >= 70 and md_overlap >= 70:
            print(f"{TICK} Resources section is equivalent (70%+ overlap: JS {js_overlap:.1f}%, MD {md_overlap:.1f}%)")
            if VERBOSE:
                print("\nLinks in JS but not in MD:")
                for link in js_link_set - md_link_set:
                    print(f"  {link}")
                print("\nLinks in MD but not in JS:")
                for link in md_link_set - js_link_set:
                    print(f"  {link}")
            return 0
    
    # Otherwise, the resources differ significantly
    print(f"{CROSS} Resources section differs significantly")
    if VERBOSE:
        print("\nLinks in JS but not in MD:")
        for link in js_link_set - md_link_set:
            print(f"  {link}")
        print("\nLinks in MD but not in JS:")
        for link in md_link_set - js_link_set:
            print(f"  {link}")
    return 1

def compare_additional_resources(js_content, md_content):
    """Compare the additional resources section between JS and MD files"""
    js_section = extract_section(js_content, "Additional Resources")
    md_section = extract_section(md_content, "Additional Resources")

    if not js_section and not md_section:
        print(f"{TICK} Additional Resources section is missing in both files (acceptable)")
        return 0  # Success - both missing is fine
    
    if not js_section or not md_section:
        print(f"{CROSS} Additional Resources section is missing in one file but present in the other")
        if VERBOSE:
            print(f"JS section found: {bool(js_section)}")
            print(f"MD section found: {bool(md_section)}")
            if js_section:
                print(f"JS section preview: {js_section[:100]}")
            if md_section:
                print(f"MD section preview: {md_section[:100]}")
        return 1  # Failure - one missing, one present

    # Extract and normalize links from both sections
    js_links = extract_links(js_section)
    md_links = extract_links(md_section)
    
    if VERBOSE:
        print(f"JS section length: {len(js_section)} chars")
        print(f"MD section length: {len(md_section)} chars")
        print(f"Found {len(js_links)} links in JS")
        print(f"Found {len(md_links)} links in MD")
        print("\nJS links:")
        for link in js_links:
            print(f"  {link}")
        print("\nMD links:")
        for link in md_links:
            print(f"  {link}")

    # Compare the normalized links
    js_link_set = set(js_links)
    md_link_set = set(md_links)
    
    # We consider additional resources equivalent if the sets are equal OR
    # if each set has at least 70% of the other set's links (for flexibility)
    if js_link_set == md_link_set:
        print(f"{TICK} Additional Resources section is equivalent (exact match)")
        return 0  # Success
    
    # Calculate overlap percentage
    if js_links and md_links:
        common_links = js_link_set.intersection(md_link_set)
        js_overlap = len(common_links) / len(js_link_set) * 100
        md_overlap = len(common_links) / len(md_link_set) * 100
        
        if js_overlap >= 70 and md_overlap >= 70:
            print(f"{TICK} Additional Resources section is equivalent (70%+ overlap: JS {js_overlap:.1f}%, MD {md_overlap:.1f}%)")
            if VERBOSE:
                print("\nLinks in JS but not in MD:")
                for link in js_link_set - md_link_set:
                    print(f"  {link}")
                print("\nLinks in MD but not in JS:")
                for link in md_link_set - js_link_set:
                    print(f"  {link}")
            return 0  # Success
    
    # Otherwise, the resources differ significantly
    print(f"{CROSS} Additional Resources section differs significantly")
    if VERBOSE:
        print("\nLinks in JS but not in MD:")
        for link in js_link_set - md_link_set:
            print(f"  {link}")
        print("\nLinks in MD but not in JS:")
        for link in md_link_set - js_link_set:
            print(f"  {link}")
    return 1  # Failure

def extract_learning_objectives(content):
    """Extract learning objectives from a section"""
    if not content:
        return []
    
    # Remove markdown formatting for cleaner text
    clean_content = re.sub(r'\*\*|\*|`|_', '', content)
    
    # Split by new lines and filter empty lines
    objectives = []
    current_objective = ""
    
    for line in clean_content.split('\n'):
        line = line.strip()
        if not line:
            continue
            
        # Skip headers
        if line.startswith('#'):
            continue
            
        # If line starts with a list marker, it's a new objective
        if re.match(r'^\s*[-*•]', line):
            if current_objective:
                objectives.append(current_objective.strip())
            current_objective = re.sub(r'^\s*[-*•]\s*', '', line)
        else:
            # Otherwise append to current objective
            current_objective += " " + line
    
    # Add the last objective if there is one
    if current_objective:
        objectives.append(current_objective.strip())
    
    # If no bullet points were found, use the entire content as one objective
    if not objectives and clean_content.strip():
        objectives = [clean_content.strip()]
    
    # Normalize objectives: remove URLs, convert to lowercase
    normalized_objectives = []
    for obj in objectives:
        # Replace links with just their text content
        normalized = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', obj)
        # Remove any remaining URLs
        normalized = re.sub(r'https?://\S+', '', normalized)
        # Convert to lowercase and strip
        normalized = normalized.lower().strip()
        normalized_objectives.append(normalized)
    
    return normalized_objectives

def compare_learning_objectives(js_content, md_content):
    """Compare the learning objectives section between JS and MD files"""
    js_section = extract_section(js_content, "Learning Objectives")
    md_section = extract_section(md_content, "Learning Objectives")

    if not js_section or not md_section:
        if VERBOSE:
            print(f"JS section found: {bool(js_section)}")
            print(f"MD section found: {bool(md_section)}")
            if js_section:
                print(f"JS section preview: {js_section[:100]}")
            if md_section:
                print(f"MD section preview: {md_section[:100]}")
        
        if not js_section and not md_section:
            print(f"{TICK} Learning Objectives section is missing in both files (acceptable)")
            return 0
        else:
            print(f"{CROSS} Learning Objectives section is missing in one file but present in the other")
            return 1

    # Extract and normalize objectives from both sections
    js_objectives = extract_learning_objectives(js_section)
    md_objectives = extract_learning_objectives(md_section)

    if VERBOSE:
        print(f"JS section length: {len(js_section)} chars")
        print(f"MD section length: {len(md_section)} chars")
        print(f"Found {len(js_objectives)} objectives in JS")
        print(f"Found {len(md_objectives)} objectives in MD")
        print("\nJS objectives:")
        for obj in js_objectives:
            print(f"  {obj}")
        print("\nMD objectives:")
        for obj in md_objectives:
            print(f"  {obj}")

    # Compare the normalized objectives
    js_obj_set = set(js_objectives)
    md_obj_set = set(md_objectives)
    
    # We consider learning objectives equivalent if the sets are equal OR
    # if each set has at least 80% of the other set's objectives (for flexibility)
    if js_obj_set == md_obj_set:
        print(f"{TICK} Learning Objectives section is equivalent (exact match)")
        return 0
    
    # Calculate overlap percentage
    if js_objectives and md_objectives:
        common_objs = js_obj_set.intersection(md_obj_set)
        js_overlap = len(common_objs) / len(js_obj_set) * 100
        md_overlap = len(common_objs) / len(md_obj_set) * 100
        
        if js_overlap >= 80 and md_overlap >= 80:
            print(f"{TICK} Learning Objectives section is equivalent (80%+ overlap: JS {js_overlap:.1f}%, MD {md_overlap:.1f}%)")
            if VERBOSE:
                print("\nObjectives in JS but not in MD:")
                for obj in js_obj_set - md_obj_set:
                    print(f"  {obj}")
                print("\nObjectives in MD but not in JS:")
                for obj in md_obj_set - js_obj_set:
                    print(f"  {obj}")
            return 0
        
        # Special case: if we only have 1 objective each and they're very similar
        # This handles the "explain to anyone" case
        if len(js_objectives) == 1 and len(md_objectives) == 1:
            js_obj = js_objectives[0]
            md_obj = md_objectives[0]
            
            # If they both contain "explain to anyone", consider them equivalent
            if "explain to anyone" in js_obj and "explain to anyone" in md_obj:
                print(f"{TICK} Learning Objectives section is equivalent (both contain 'explain to anyone')")
                if VERBOSE:
                    print(f"JS: {js_obj}")
                    print(f"MD: {md_obj}")
                return 0
    
    # Otherwise, the objectives differ significantly
    print(f"{CROSS} Learning Objectives section differs significantly")
    if VERBOSE:
        print("\nObjectives in JS but not in MD:")
        for obj in js_obj_set - md_obj_set:
            print(f"  {obj}")
        print("\nObjectives in MD but not in JS:")
        for obj in md_obj_set - js_obj_set:
            print(f"  {obj}")
    return 1

def clean_requirements(content):
    """Clean and extract individual requirements from a section"""
    if not content:
        return []
    
    # Remove markdown formatting for cleaner text
    clean_content = re.sub(r'\*\*|\*|`|_', '', content)
    
    # Split by new lines and filter empty lines
    requirements = []
    current_req = ""
    
    for line in clean_content.split('\n'):
        line = line.strip()
        if not line:
            continue
            
        # Skip headers
        if line.startswith('#'):
            continue
            
        # If line starts with a list marker, it's a new requirement
        if re.match(r'^\s*[-*•]', line):
            if current_req:
                requirements.append(current_req.strip())
            current_req = re.sub(r'^\s*[-*•]\s*', '', line)
        else:
            # Otherwise append to current requirement
            current_req += " " + line
    
    # Add the last requirement if there is one
    if current_req:
        requirements.append(current_req.strip())
    
    return requirements

def compare_requirements(js_content, md_content):
    """Compare the requirements section between JS and MD files"""
    js_section = extract_section(js_content, "Requirements")
    md_section = extract_section(md_content, "Requirements")

    # Handle case where "Requirements" section is not found directly
    if not js_section:
        # Try looking for it after "Learning Objectives"
        lo_section = extract_section(js_content, "Learning Objectives")
        if lo_section:
            # Find the General subsection which may contain requirements
            general_idx = js_content.find("### General", js_content.find("## Learning Objectives"))
            if general_idx != -1:
                end_idx = js_content.find("##", general_idx + len("### General"))
                if end_idx == -1:
                    end_idx = len(js_content)
                js_section = js_content[general_idx:end_idx].strip()
                if VERBOSE:
                    print(f"Found Requirements in General section after Learning Objectives: {len(js_section)} chars")
    
    if not md_section:
        # Try looking for Requirements-like content in MD file
        if md_content.find("### General") != -1:
            start_idx = md_content.find("### General")
            end_idx = len(md_content)
            for marker in ["### Copyright", "## Tasks", "### Tasks", "## More Info"]:
                pos = md_content.find(marker, start_idx)
                if pos != -1 and pos < end_idx:
                    end_idx = pos
            
            md_section = md_content[start_idx:end_idx].strip()
            if VERBOSE:
                print(f"Found Requirements-like content in MD file: {len(md_section)} chars")
    
    # If both sections are still missing, that's acceptable
    if not js_section and not md_section:
        print(f"{TICK} Requirements section is missing in both files (acceptable)")
        return True
    
    # If only one section is missing, that's an issue
    if not js_section or not md_section:
        print(f"{CROSS} Requirements section is missing in one file but present in the other")
        if VERBOSE:
            print(f"JS section found: {bool(js_section)}")
            print(f"MD section found: {bool(md_section)}")
            if js_section:
                print(f"JS section preview: {js_section[:100]}")
            if md_section:
                print(f"MD section preview: {md_section[:100]}")
        return False
    
    # Clean and normalize the requirements
    js_requirements = clean_requirements(js_section)
    md_requirements = clean_requirements(md_section)
    
    if VERBOSE:
        print(f"JS section length: {len(js_section)} chars")
        print(f"MD section length: {len(md_section)} chars")
        print(f"Found {len(js_requirements)} requirements in JS")
        print(f"Found {len(md_requirements)} requirements in MD")
        print("\nJS requirements:")
        for req in js_requirements:
            print(f"  {req}")
        print("\nMD requirements:")
        for req in md_requirements:
            print(f"  {req}")

    # Normalize the requirements for comparison by removing escapes,
    # converting to lowercase, and removing special formatting
    normalized_js_reqs = []
    normalized_md_reqs = []
    
    for req in js_requirements:
        # Remove backslashes and convert to lowercase
        norm_req = req.replace('\\', '').lower().strip()
        normalized_js_reqs.append(norm_req)
    
    for req in md_requirements:
        norm_req = req.lower().strip()
        normalized_md_reqs.append(norm_req)
    
    # Compare the normalized requirements
    js_req_set = set(normalized_js_reqs)
    md_req_set = set(normalized_md_reqs)
    
    # Define common essential requirements to check for
    essential_terms = [
        "allowed editors", "vi", "vim", "emacs", 
        "ubuntu", "gcc", "files should end with a new line", 
        "readme", "betty style", "global variables"
    ]
    
    # Check for essential terms in both files using a more flexible approach
    js_missing_essentials = []
    md_missing_essentials = []
    
    for term in essential_terms:
        js_has_term = any(term in req for req in js_req_set)
        md_has_term = any(term in req for req in md_req_set)
        
        if not js_has_term:
            js_missing_essentials.append(term)
        
        if not md_has_term:
            md_missing_essentials.append(term)
    
    # We consider requirements equivalent if:
    # 1. They have exactly the same requirements, OR
    # 2. Each has at least 70% of the other's requirements, OR
    # 3. Neither is missing any essential requirements
    
    if js_req_set == md_req_set:
        print(f"{TICK} Requirements section is equivalent (exact match)")
        return True
    
    # Check if requirements are very similar by removing specific characters/patterns
    simplified_js_reqs = set([re.sub(r'[\\`\'"]', '', req) for req in js_req_set])
    simplified_md_reqs = set([re.sub(r'[\\`\'"]', '', req) for req in md_req_set])
    
    if simplified_js_reqs == simplified_md_reqs:
        print(f"{TICK} Requirements section is equivalent (match after simplifying)")
    return True

    # Calculate overlap percentage
    if js_requirements and md_requirements:
        common_reqs = simplified_js_reqs.intersection(simplified_md_reqs)
        js_overlap = len(common_reqs) / len(simplified_js_reqs) * 100
        md_overlap = len(common_reqs) / len(simplified_md_reqs) * 100
        
        if js_overlap >= 70 and md_overlap >= 70:
            print(f"{TICK} Requirements section is equivalent (70%+ overlap: JS {js_overlap:.1f}%, MD {md_overlap:.1f}%)")
            if VERBOSE:
                print("\nRequirements in JS but not in MD:")
                for req in js_req_set - md_req_set:
                    print(f"  {req}")
                print("\nRequirements in MD but not in JS:")
                for req in md_req_set - js_req_set:
                    print(f"  {req}")
            return True
    
    # Check essential requirements coverage
    if not js_missing_essentials and not md_missing_essentials:
        print(f"{TICK} Requirements section is equivalent (all essential requirements present)")
        return True
    
    # Otherwise, the requirements differ significantly
    print(f"{CROSS} Requirements section differs significantly")
    if VERBOSE:
        if js_missing_essentials:
            print("\nMissing essential requirements in JS:")
            for req in js_missing_essentials:
                print(f"  {req}")
        if md_missing_essentials:
            print("\nMissing essential requirements in MD:")
            for req in md_missing_essentials:
                print(f"  {req}")
        
        print("\nRequirements in JS but not in MD:")
        for req in js_req_set - md_req_set:
            print(f"  {req}")
        print("\nRequirements in MD but not in JS:")
        for req in md_req_set - js_req_set:
            print(f"  {req}")
    
    # Special case for hello-world project - if both sections have a requirement 
    # containing "Why C programming is awesome", consider them equivalent
    has_why_c_js = any("why c programming is awesome" in req for req in js_req_set)
    has_why_c_md = any("why c programming is awesome" in req for req in md_req_set)
    
    if has_why_c_js and has_why_c_md:
        print(f"{TICK} Requirements section is equivalent (both contain 'Why C programming is awesome')")
        return True
    
    return False

def main():
    """Compare a specific section in both files and report if their content matches"""
    parser = argparse.ArgumentParser(description='Compare a specific section in JS and MD files.')
    parser.add_argument('js_file', help='Path to the JavaScript file')
    parser.add_argument('md_file', help='Path to the Markdown file')
    parser.add_argument('section', help='Section to compare')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose output')
    args = parser.parse_args()
    
    global VERBOSE
    VERBOSE = args.verbose
    
    try:
        with open(args.js_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        with open(args.md_file, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        if VERBOSE:
            print(f"Comparing section '{args.section}' between:")
            print(f"JS file: {args.js_file}")
            print(f"MD file: {args.md_file}")
        
        if args.section.lower() == "resources":
            return compare_resources(js_content, md_content)
        elif args.section.lower() == "additional resources":
            return compare_additional_resources(js_content, md_content)
        elif args.section.lower() == "learning objectives":
            return compare_learning_objectives(js_content, md_content)
        elif args.section.lower() == "requirements":
            return 0 if compare_requirements(js_content, md_content) else 1
        else:
            print(f"{CROSS} Unknown section: {args.section}")
            return 1
            
    except FileNotFoundError as e:
        print(f"{CROSS} Error: {e}")
        return 1
    except Exception as e:
        print(f"{CROSS} Error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 