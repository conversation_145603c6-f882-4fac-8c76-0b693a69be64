#!/bin/bash
# This script checks if all sections are equivalent between JS file and Markdown file

# check_all_sections.sh - Check all sections for all projects in the codebase
# Usage: ./check_all_sections.sh [options]
# Options:
#   --project-type TYPE   Check only projects of the specified type (e.g., low-level, higher-level/python)
#   --project NAME        Check only the specified project (e.g., hello-world)
#   --verbose             Print verbose output
#   --report              Generate a detailed report

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_CMD="python3"

# Parse command line arguments
PROJECT_TYPE=""
PROJECT=""
VERBOSE=""
REPORT=""

while [[ $# -gt 0 ]]; do
  case "$1" in
    --project-type)
      PROJECT_TYPE="--project-type $2"
      shift 2
      ;;
    --project)
      PROJECT="--project $2"
      shift 2
      ;;
    --verbose)
      VERBOSE="--verbose"
      shift
      ;;
    --report)
      REPORT="--report ${SCRIPT_DIR}/../reports/section_comparison_report.md"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Run the section checker
echo "Running section checker..."
CMD="${PYTHON_CMD} ${SCRIPT_DIR}/unified_section_checker.py ${PROJECT_TYPE} ${PROJECT} ${VERBOSE} ${REPORT}"
echo "Executing: $CMD"
eval "$CMD"

echo "Section checking completed." 