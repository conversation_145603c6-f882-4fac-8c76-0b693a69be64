# ALX Course Tracker Comparison Scripts

This directory contains scripts for comparing JavaScript and Markdown versions of ALX course materials to ensure consistency.

## Unified Project Checker

The main script to use is `unified_project_checker.py`, which provides a simplified interface for comparing projects and automatically updating summaries.

### Features

- Single command to check one or all projects
- Automatic summary updates
- Colored output for better readability
- Detailed failure reporting
- JSON and text-based reports for compatibility
- Interactive project status viewing
- Intelligent handling of formatting differences

### Installation

No special installation is required beyond the standard Python libraries. The script is compatible with Python 3.6+.

### Basic Usage

The script should be run from the parent directory (the root of the project) using the python3 command:

```bash
# Check a specific project
python3 comparison_scripts/unified_project_checker.py --project functions

# Check all projects
python3 comparison_scripts/unified_project_checker.py --all

# Show verbose output
python3 comparison_scripts/unified_project_checker.py --project debugging --verbose

# List all available projects
python3 comparison_scripts/unified_project_checker.py --list-projects

# Check project status
python3 comparison_scripts/unified_project_checker.py --status functions

# Just update the summary report
python3 comparison_scripts/unified_project_checker.py --update-summary
```

### Command Line Arguments

| Argument | Short | Description |
|----------|-------|-------------|
| `--project PROJECT` | `-p` | Check a specific project by name |
| `--all` | `-a` | Check all projects |
| `--update-summary` | `-u` | Update the summary report based on existing results |
| `--list-projects` | `-l` | List all available projects that can be checked |
| `--status [PROJECT]` | `-s` | Show status of all projects or a specific project |
| `--verbose` | `-v` | Show detailed output during checks |
| `--no-summary` | - | Skip summary update after project check |

### Common Workflows

#### Fixing a specific project

1. List available projects:
   ```bash
   python3 comparison_scripts/unified_project_checker.py --list-projects
   ```

2. Check the specific project with verbose output:
   ```bash
   python3 comparison_scripts/unified_project_checker.py --project functions --verbose
   ```

3. After fixing any issues in the files, run the check again:
   ```bash
   python3 comparison_scripts/unified_project_checker.py --project functions --verbose
   ```

#### Weekly maintenance check

Run a full check of all projects and update the summary:
```bash
python3 comparison_scripts/unified_project_checker.py --all --verbose
```

### Understanding the Output

The script outputs several types of information:

1. **Progress information**: Shows which project and task is being checked
2. **Task status**: Indicates whether each task passed or failed
3. **Failure reasons**: Lists specific differences that caused a task to fail
4. **Summary statistics**: Shows how many projects and tasks passed/failed

### Reports

The script generates reports in two formats:

1. **JSON reports**: Detailed machine-readable reports at `comparison_reports/{project}/task_comparison.json`
2. **Text reports**: Human-readable reports at `comparison_reports/{project}/task_comparison.txt`
3. **Summary report**: Overview of all projects at `comparison_reports/task_comparison_summary.txt`

## Legacy Scripts

The following scripts are maintained for backward compatibility:

- `check_all_tasks.sh`: Checks all tasks for a specific project
- `check_all_sections.sh`: Checks sections for a specific project
- `check_failing_tasks.sh`: Generates the summary report
- `compare_all_projects.py`: Checks all projects and sections
- `compare_tasks.py`: Core task comparison logic
- `compare_sections.py`: Core section comparison logic

These scripts are no longer needed for regular operation as `unified_project_checker.py` replaces them with a more efficient workflow.

## Best Practices

1. **Always check your changes**: After modifying files, always run the checker to ensure no regressions.
2. **Use verbose mode for debugging**: When issues arise, use the `--verbose` flag to get detailed output.
3. **Check the summary report**: The summary gives a good overview of the project status.
4. **Address one project at a time**: Focus on fixing one project completely before moving to the next.

## Comparison Methodology

The script uses several strategies to compare tasks:

1. **Content normalization**: Strips away formatting differences, normalizing bullet points and whitespace
2. **Similarity thresholds**: Allows for minor differences using similarity ratios (>0.85 is considered a match)
3. **Essential content focus**: Only compares the important parts of tasks, ignoring presentation differences
4. **Task structure analysis**: Identifies tasks by headers and sections, handling variations in formatting

## Troubleshooting

- **"Project not found"**: Use `--list-projects` to see all available projects
- **Task failures**: Use `--verbose` to see detailed reasons for task failures
- **JSON errors**: If a JSON report is corrupted, delete it and re-run the check
- **Summary not updating**: Run `--update-summary` to force a summary update

## Contributing

When modifying the comparison scripts:

1. Update `unified_project_checker.py` instead of the legacy scripts
2. Add clear documentation for any new features
3. Ensure backward compatibility with existing report formats
4. Test changes thoroughly with a variety of projects 