#!/usr/bin/env python3
"""
Debug script to examine sections in both files.
"""

import re

def read_file(file_path):
    """Read the content of a file"""
    with open(file_path, 'r') as f:
        return f.read()

def find_section_headers(content):
    """Find all section headers in the content"""
    # Pattern for Markdown section headers
    pattern = r'^(#+)\s+(.*?)$'
    
    headers = []
    for line in content.split('\n'):
        match = re.match(pattern, line)
        if match:
            level = len(match.group(1))
            title = match.group(2).strip()
            headers.append((level, title))
    
    return headers

def dump_section(title, content, max_chars=500):
    """Print a section with a title and specified max characters"""
    print(f"\n===== {title} =====")
    print(content[:max_chars])
    if len(content) > max_chars:
        print("... (truncated)")
    print("=" * (len(title) + 12))

def extract_requirements_section(content, is_js=False):
    """Extract requirements section with detailed debugging"""
    source = "JS" if is_js else "MD"
    
    # Print the content around the Requirements section for debugging
    req_index = content.find("## Requirements")
    if req_index == -1:
        print(f"{source} file: '## Requirements' not found")
        return None
    
    # Extract the content from ## Requirements to the next ## section
    start_idx = req_index
    content_after = content[start_idx:]
    
    # Find the next ## section
    next_section_match = re.search(r'\n##\s+[^#]', content_after[15:])
    if next_section_match:
        end_idx = 15 + next_section_match.start()
        section_content = content_after[:end_idx].strip()
    else:
        section_content = content_after.strip()
    
    print(f"\n{source} file: Requirements section found, length: {len(section_content)} chars")
    dump_section(f"{source} Requirements Section", section_content)
    
    # Look for General subsection in JS file or Ubuntu 20.04 in MD file
    if is_js:
        subsection_pattern = r'### General\s*(.*?)(?:###|$)'
    else:
        subsection_pattern = r'### Ubuntu 20\.04\s*(.*?)(?:###|$)'
    
    subsection_match = re.search(subsection_pattern, section_content, re.DOTALL)
    
    if subsection_match:
        subsection_content = subsection_match.group(1).strip()
        subsection_name = "General" if is_js else "Ubuntu 20.04"
        print(f"{source} file: {subsection_name} subsection found, length: {len(subsection_content)} chars")
        dump_section(f"{source} {subsection_name} Subsection", subsection_content)
        return subsection_content
    else:
        subsection_name = "General" if is_js else "Ubuntu 20.04"
        print(f"{source} file: {subsection_name} subsection NOT found")
        
        # Check if there are other subsections
        subsections = re.findall(r'### (.*?)\n', section_content)
        if subsections:
            print(f"{source} file subsections found:", subsections)
    
    return section_content

def search_key_requirements(content, requirements_list):
    """Search for key requirements in content"""
    found = []
    not_found = []
    
    for req in requirements_list:
        if req.lower() in content.lower():
            found.append(req)
        else:
            not_found.append(req)
    
    return found, not_found

def main():
    # File paths
    js_file = "src/data/projects/low-level/argv.js"
    md_file = "markdown_files/0x0A__C_-_argc__argv.md"
    
    # Read file contents
    js_content = read_file(js_file)
    md_content = read_file(md_file)
    
    # Find section headers
    js_headers = find_section_headers(js_content)
    md_headers = find_section_headers(md_content)
    
    # Print section headers
    print("JavaScript File Section Headers:")
    for level, title in js_headers:
        print(f"{'#' * level} {title}")
    
    print("\nMarkdown File Section Headers:")
    for level, title in md_headers:
        print(f"{'#' * level} {title}")
    
    # Extract requirements sections
    print("\n--- Requirements Section Extraction ---")
    js_requirements = extract_requirements_section(js_content, is_js=True)
    md_requirements = extract_requirements_section(md_content)
    
    # Define key requirements to search for
    key_requirements = [
        "Allowed editors:",
        "All your files will be compiled",
        "Your code should use the Betty style",
        "You are not allowed to use global variables",
        "No more than 5 functions per file",
        "You are allowed to use the standard library"
    ]
    
    # Search for key requirements
    print("\n--- Key Requirements Search Results ---")
    if js_requirements:
        js_found, js_not_found = search_key_requirements(js_requirements, key_requirements)
        print("\nJS file key requirements found:", js_found)
        print("JS file key requirements NOT found:", js_not_found)
    
    if md_requirements:
        md_found, md_not_found = search_key_requirements(md_requirements, key_requirements)
        print("\nMD file key requirements found:", md_found)
        print("MD file key requirements NOT found:", md_not_found)

if __name__ == "__main__":
    main() 