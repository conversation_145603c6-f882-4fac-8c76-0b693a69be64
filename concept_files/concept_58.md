# Peer Learning Day - How To Get Started

![Image](assets/image_2_90981f1e9228241788e7272cb019ce8c833c8d04.PNG)

## Requirements

Peer learning days (PLD) are mandatory days

You must collaborate with your assigned group.

If you are regrouped in the same room: Make sure all task have been whiteboarded/understood by peers before going to work out the tasks in code.

## Introduction

Peer Learning Days are when we ensure that each and every one of our peers understands content, and that everyone is collectively growing in technical, soft, and professional skills.

PLDs are when you will collaboratively review prior projects with an intranet-assigned group of cohort peers.

With your group, you will review these recent concepts and tasks by defining your objectives and clarifying action items for success.

It’s very important to emphasize that Peer Learning Days are not about sharing solutions.

Why? That does not empower peers with the ability to solve problems themselves!

Peer Learning is when you share your thought process - whether through conversation, whiteboarding, debugging or live coding.

Examples:

- “How did you come to that conclusion?”

- “What had you tried?”

- “Did the man page give you a lead?”

- “Did you remember a fundamental concept that shed light on your challenge”

► Modeling this form of thinking for one another is invaluable, and will strengthen your entire cohort.

► Your ability to articulate your knowledge (aka help teach others) will be required to succeed at a technical interview and a career in software engineering

## 0. Define Objectives

As a group, start your time together by revisiting the project learning objectives.

With those in mind, vote on what concepts and tasks need review starting at lowest numbered question from the projects.

Even if it receives only one vote as an “easier” task, it is where the group must start to ensure a strong foundation is had by all.

► Do your group objectives ensure that even those struggling are brought back into understanding & reflect the most recent project/s? If not, redefine.

## 1. Clarify Action Items

Clarify how you will make the most of your time starting with whiteboarding; this will anchor your peer learning.

► You don’t have a lot of time, so ensure you’re utilizing it optimally.

## 2. Whiteboard

Whiteboard through all of the project tasks. *

Take turns, so everyone gets practice whiteboarding, articulating, and asking effective questions.

The student who is presenting should always …

- Clarify if anyone has question

- Open the floor to other students with alternate thought processes or code

- Ensure that everyone understands

Those who are observing should always …

- Participate because it is a two way conversation

- If it’s needed, jump up to the board to help them out; it’s a conversation

- Try to make the presenter feel at ease

► It is hard to explain a concept in front of everyone. Some people are naturally comfortable speaking in front of a crowd while others are not. Help the presenter with questions if it is not clear enough. Try to make them at ease.

► Remember, your ability to help another understand also strengthens your skills to become an engineer.

* Sometimes there will be too many projects to cover during PLDs, and your group won’t be able to cover everything. Start with the first task on which at least one student didn’t get all the points because peer learning benefits everyone!

## 3. Pair/Group Program

Once you’ve worked through all the problem sets via whiteboarding, you can start to code, and a great way to do this is pair programming!

Before moving forward, stop to read the How-To on Pair Programming concept page.

By this point, after a day of whiteboarding, the solutions should make sense. That said, you may still have days where you get stuck on syntax, details, etc.



> Even if you don’t get stuck, pair programming is a necessary skill for your future that you’ll be pleased to have when your problems get increasingly difficult.

Even if you don’t get stuck, pair programming is a necessary skill for your future that you’ll be pleased to have when your problems get increasingly difficult.

Regardless of if you think you understand something, pair programming is an invaluable industry skill that you need to practice

## 4. Share Group Progress

Both before standup and before the end of the day, you will share your objectives, action items, progress, and bottlenecks with either staff or TAs.

This encourages both accountability, encouragement, and accessibility since you’ll be able to see what other groups’ strengths/weaknesses are.

►Remember, we go farther when we go together!

► Use this time to offer additional help to cohort peers or to seek out those that understood your groups’ pain points well - Peer Learning doesnt’ end at 3p ?

## Important:

► We ask that you do not take pictures or notes during whiteboarding sessions, so that the focus is on asking questions until solutions are found.

► We recommend that you chat with students from the previous cohorts to see how they self organized.

► If you have time remaining in PLD, ask yourselves questions about what you have learned so far + do code challenges.

Examples :

- It can be anything that you can find in the documentations, man, and projects done so far

- Code challenges

- Take either a random (ie. an algorithm from the net or a book) or specific task from any of the projects done so far and try to solve it together

- Optional intranet projects

- You can also prepare your own code challenges!

► Whenever possible try to use whiteboards vs computers.

► These recommendations and requirements were created by tech industry veterans that want you to succeed! If you follow them, we are confident you will enter the workplace with the foundation to excel.