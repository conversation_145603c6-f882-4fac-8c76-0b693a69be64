# Portfolio Project Overview

## What’s a Portfolio Project?

The Portfolio Project will be a project you will showcase on your resumé, online, and during interviews. A completed Portfolio Project will consist of:

- A landing page describing your project

- A comprehensive README.md

- A demo of the project

- A presentation

- A blog post

## Why build a Portfolio project?

The Portfolio Project serves several different purposes:

- Highlights a Student’s unique interests and background when networking, or interviewing with potential employers.

- Provides a realistic workplace scenario where engineers create a solution to fit loosely defined requirements, break this down into concrete tasks and implement on a deadline.

- Allows for self-directed learning to explore a new technical topic, deepen understanding of a curriculum-covered topic, or use technology to bring an idea to life.

## Timeline and checkpoints

Projects can be developed alone, or in teams of up to 3 students. The technologies and project concept are defined by the student teams.

Research & project approval (Part 1-3)

- Week 1:
    * Project proposal (staff review, approval required)

- Week 2: 
    * MVP (Minimum Viable Product) proposal (staff review, approval required)

- Week 3: 
    *  Trello board (staff review, approval required)

Build Portfolio Project (Part 1-3)

- Week 4: 
    * Making Progress (status update, staff review)

- Week 5: 
    * MVP completed (staff review, approval required)

- Week 6: 
    * Landing page deployed
    * Final presentation preparation & delivery (staff review, approval required)

Week 6: 
    * Landing page deployed
    * Final presentation preparation & delivery (staff review, approval required)

- Week 7: 
    * Blog post reflection (peer review)
    * github cleanup

Week 7: 
    * Blog post reflection (peer review)
    * github cleanup

End of Foundations Year

# Resources

Check out these software tools that are available to you:

- Github Education Pack (available through the “Tools” section of your left-hand menu)

- Microsoft Applications