# SE Face-off Cup : Qualifications 💻🏆





Qualification Phase Challenge

## ⏬Read below before filling the form⏬

## Teams Format

Filling out this form only will NOT automatically qualify for the Cup!

- Who<PERSON><PERSON> is filling this Form is considered the Team leader of the Team

- Team Leaders and teammates can be part of only ONE group

- Every team need to be composed by  6 learners with a fair distribution across different Cohorts(ex. 5 C18 + 1 C2blended will not be accepted)

- Should your team be selected for the next stage of the Cup,all of its members should be available to actively engage - or risk disqualification for the entire team

## The Qualification Challenge

### Challenge Name

Web Tic Tac Toe

### Description

Competitors will have to create a browser-based game called Web Tic Tac Toe

- Use basic web development technologies like HTML, CSS, JavaScript.

- The use of complex frameworks will be considered favorably in the evaluation phase.

- Playability: the game should be playable with the computer and with other people.

### Minimal Requirements

- User data persistence.

- Usage of SOCKET.IO is necessary (a basic implementation is enough), since multiplayer mode is required.

### Evaluation Methodology

Evaluation will be based on the following criteria:

- The project’s usage of front-end scaffolding frameworks like Vite, Webpack, Parcel, ESBuild, etc.

- The project’s usage of back-end frameworks like Flask, FastAPI, ExpressJS, etc.

- Utilization of DBMS for storing various user data like account information, wins, losses, etc.

- Mobile-friendly approach to the front-end.

- Cleanliness of directory structure.

- Professionalism in using version control systems.

### Submission Requirement

- Submit the link to your GitHub repository with a well-written README on how to set it up locally.

- Record and submit a video where you go through the project - no editing needed.

- Bonus: Provide a link to the deployed website.

## 👏Congratulations for reading till here!

Centered Text
    
        .centered-text {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 5vh; /* Full viewport height */
            text-align: center;
            font-family: Poppins;
            font-weight: bold;
        }

## ▶️ FILL THE FORM AND QUALIFY◀️

