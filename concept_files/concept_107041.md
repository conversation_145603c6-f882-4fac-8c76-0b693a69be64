# SE Face-Off Cup: Round of 11 💻🏆





Qualification Phase Challenge

## ⏬Read below before filling the form⏬

NB. Team Leaders (who submitted the qualification form) are responsible for submitting this one! Only ONE submission per team is possible

### Challenge Name

API Innovation Challenge

### Description

The API Innovation Challenge aims to encourage you to leverage public APIs to create innovative and useful applications.

You and your team will build a project that utilizes at least two of the provided APIs to solve a real-world problem or create a unique application

### Minimal Requirements

Back-end

- Programming Language: Python, JavaScript, or another suitable language.

- Web Framework: Flask, Django, Express.js, or a similar framework.

- Database: A relational database (e.g., PostgreSQL, MySQL) or a NoSQL database (e.g., MongoDB, Firebase).

- API Integration: Effective integration of at least two of the following APIs:

- OpenWeatherMap

- TheMovieDB

- NASA API

- Google Gemini API

- OpenAI GPT

- IBM Watson

- Intrinio

- Alpha Vantage

- Google Maps API

- OMDB API

Front-end

- HTML: Structure the content of the application.

- CSS: Style the appearance of the application using bootstrap, tailwind css or any other framework.

- JavaScript:Add interactivity and dynamic elements to the application

- Front-end Framework:A framework like React, or Vue.js is a plus

### Evaluation Criteria

Functionality: The project’s ability to solve a problem or provide a useful service.

Creativity: The originality and innovation of the project.

Technical Implementation: The quality of the code, API usage, and overall technical implementation.

User Experience: The ease of use and user-friendliness of the project.

Documentation: The clarity and completeness of the project documentation.

### Submission Requirement

▶️ Submit the link to your GitHub or GitLab Public repository with the project code and a detailed project report or README explaining the project’s functionality, API usage, and technical details.



▶️ Demo Video: A short video demonstrating the project’s features and functionality.



## 👏Congratulations for reading till here!

Centered Text
    
        .centered-text {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 5vh; /* Full viewport height */
            text-align: center;
            font-family: Poppins;
            font-weight: bold;
        }

## ▶️ FILL THE FORM!◀️

