# The Essential Cheat Sheet for Open Source Projects #ALXLevelUp

# Open Source Projects: A Beginner’s Guide

## What is Open Source?

Open source software is a software with source code that anyone can inspect, modify, and enhance. Anyone who has Internet access can benefit from Open Source Software.

## Basics of Open Source Projects

Popular Open Source Platforms 
    -  GitHub
    -  GitLab
    -  Bitbucket

Key words when starting with Open Source -  (e.g., forking, cloning, committing, pushing, opening pull requests)

- Open-Source Communities: groups of developers and non-coders who collaborate on open source projects

- Source Code: The original code which can be viewed, modified, and distributed under an open-source license

- License: Terms under which Open Source Softwares can be used, modified, and distributed

- Contribute: it can acquire the form of debugging, fixing codes, making corrections to project’s documents. Anyone can contribute to Open Source Project, regardless of their coding skills

- Forking: it is a copy of a repository owned by an organization or community or user: it allows the user to experiment, make changes in the forked repository and then make a pull request to the original project for your changes to be included

- Cloning: create a copy of a repository from a remote server to a local one

- Tag: key words through which to look for projects

- Key components of Open Source Projects (e.g., maintainers, contributors, source code repository, project license)

## Exploring Open Source Projects

Finding Projects:

Choose a project based on your skill level and interests: you can search them by programming language, category, or tags:

List of categories to begin looking for your next project:

> Programming Languages,
Frameworks and Libraries,
Data Engineering,
DevOps and Cloud,
Web Development,
Mobile Development,
Game Development,
Security,
Miscellaneous

Programming Languages,
Frameworks and Libraries,
Data Engineering,
DevOps and Cloud,
Web Development,
Mobile Development,
Game Development,
Security,
Miscellaneous

List of tags you can use to look to exercise:

> ‘good-first-issue’
‘beginner-friendly’
‘first-timers-only’
‘easy-pick’
‘starter-project’
‘newbie-friendly’
‘intermediate’
‘medium-difficulty’
‘improvement-needed’
‘enhancement’
‘feature-request’
‘bug-fix’
‘up-for-grabs’
‘help-wanted’

‘good-first-issue’
‘beginner-friendly’
‘first-timers-only’
‘easy-pick’
‘starter-project’
‘newbie-friendly’
‘intermediate’
‘medium-difficulty’
‘improvement-needed’
‘enhancement’
‘feature-request’
‘bug-fix’
‘up-for-grabs’
‘help-wanted’

Explore curated lists

- CodeTriage

- First timers only

- Awesome for beginners

- First Contributions

- Up for Grabs

- Code Tribute

- Ovio

- awesome-oss

## Getting Started

- Creating a GitHub account

- Familiarizing with Git and version control

Getting Involved with Open Source

You can either:

- Creating your own Open Source Project

- Contributing to existing Open Source Project

As soon as you land to an Open Source Project..
Make sure to check out:

- The README file : it contains all you need to know about the project, and where to find the informations

- The Code of Conduct : it includes all the rules that the Community will abide by

- The CONTRIBUTING.md : all you need to know before you start contributing in a project. In this document you normally find out if the project accepts contributions, how project’s  maintainers prefers Commit messages and Pull Requests to be formulated

- LICENSE

Now, you should be clear on the project’s goals and components, teams, and rules of behaviour and contribution.

Where to go next?

Land on “Issues” and browse through them to understand where you can help the project with - issues that need immediate help are usually tagged with the tag help-wanted . Once you find the issue you are interested in solving, you can start interacting with the Maintainer to take up the issue, as well as asking all the questions you need.

Explore some of the most common types of Contributions:

- Reporting bugs and issues

- Writing documentation

- Fixing bugs

- Adding features

- Leading development efforts

- Documentation and translation

- Design and user interface

- Organizing and event planning

- Code review and testing

## How to make a contribution?

Follow these steps

![Image](assets/image_61_860cb6d88434bf5d8e7ef0a3ec0d205886452b30.png)

Image from skillcrush.com

What can you do to keep a respectful and inclusive environment?

- Read and abide by project’s documentation, coding standards, and contribution process. Respect the existing community’s norms and etiquettes, including how to report, respond to comments, making commits and pulling requests

- Open Source is about collaborating with others: Be polite, constructive, and responsive to feedback from maintainers and other contributors

- Start small - keep doing it - think big: Start by tackling small tasks and making small pull requests, then make incremental changes. 
Keep your documentations clear, preferably by using markdowns

## Benefits of Contributing to Open Source

- Enhance your coding skills and learn from experienced developers by:


Understanding of Git and GitHub
Getting familiar with other version control systems (e.g., SVN, Mercurial)
Understanding of software development processes and methodologies
Increase knowledge of common programming concepts and data structures

- Understanding of Git and GitHub

- Getting familiar with other version control systems (e.g., SVN, Mercurial)

- Understanding of software development processes and methodologies

- Increase knowledge of common programming concepts and data structures

- Improve your communication skills and problem solving

- Get confident in working in teams and collaborate with others, possibly through digital collaboration tools (e.g., Slack, Trello, Asana)

- Understanding of project management and issue tracking

- Build a strong portfolio and showcase your work to potential employers

- Gain valuable experience in collaboration and communication

- Become part of a global developer community

- Give back to the software development world