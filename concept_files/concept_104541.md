# ALX Software Engineer Program Cheatsheet

### Welcome to the ALX Software Engineer Program Cheatsheet!

👉🏽 This page is your go-to resource once you attended the Let’s Talk Session or watched the recording.

Here, you’ll find practical tips, engaging tutorials, and reliable links to help you navigate through your journey with ease, in addition to what discussed during the call

Last update: 30.4.2024

##### 🟢 Time Management

- Read about effective time management strategies in this insightful article: Time Management Article

- Watch curated playlists for time management tips: Time Management Playlist

##### 🟢 Task Prioritization and Project Efficiency

- Learn how to prioritize tasks and approach projects efficiently: Project and Coding Efficiency Tutorial

- Decide which are the prioritary tasks for your day (a realistic number, ex. 3) and concentrate on completing those

##### 🟢 Navigating Projects

- Navigate your projects effectively with these steps:


Check Learning Objectives.
Review foundational concepts.
Utilize available resources.
Explore YouTube tutorials for visual learning.

- Check Learning Objectives.

- Review foundational concepts.

- Utilize available resources.

- Explore YouTube tutorials for visual learning.

##### 🟢 Actively participating in PLDs and find peers to work with

Here some suggetions from your colleagues:
1. Take the initiative and organize a PLD yourself - you just need to share an online meeting link, with suggested time, date and topic and invite your peers to join
2. Reach out to a peer in another Cohort who already did the project, to ask them if they want to participate in the PLDs
3. Engage on your Discord Cohort channel (ex. #cohort19 ) by asking or answering a question, or sharing your knowledge or experience - Find friends before colleagues!

##### 🟢 Fuelling motivation

If it’s true that Motivation comes from within, it is your daily job to nurture it! Fortunately, you do not have to do it alone! You can benefit from a thriving Community of people! 
Hereunder you can find a selection of interviews and session recordings that can help you fuel your motivation and carry on:

Graduates unplugged: the story of Peter and Rose who speak about how they face a problem they haven’t encountered before, how the research and handle anxiety - watch their interview here

From Doubt to Determination: Yassir was a C13 learner who nearly gave up on graduating because he was feeling hopeless. The help of some of his colleagues helped him to find strenght and continue his journey: he finally graduated with flying colours. Read their interview here

If you sometimes wonder if is it all worthy, Terry’s story will clear your sight on where the Program can lead you to: thanks to her ability in the Portfolio Project and in showcasing her skills, she found her first entry-level job as a back-end development days after graduation as part of Cohort 12. Watch her interview here

#### 💻Technical Issues💻

##### 🔵 Sandbox Troubleshooting: Solve Sandbox issues with these tutorials

Tutorial 1, Tutorial 2, Windows Sandbox Setup

##### 🔵 SSH and Servers: Understand SSH and server concepts

SSH and Servers Explanation

##### 🔵 Checker Explained: Decode the Checker in under a minute

Checker Explanation

##### 🔵 C Navigation: Clear doubts in C navigation with this 7-minute tutorial

C Navigation Explanation

##### 🔵 Ubuntu Setup: Set up Ubuntu 20.04 Windows Subsystem for Linux easily

Ubuntu Setup Tutorial

##### 🔵 Git SSH Authentication: Learn about Git SSH authentication with this demo

Git SSH Authentication Demo

#### 🔌Offline Learning and Coding🔌

No stable internet access? No problem! Follow these steps:

##### 🔵 Download project materials for offline access

Click “ctrl + P” or the right tab of the mouse and select “print”, then select “PDF” as destination

##### 🔵 Set up your local coding environment when accessing the sandbox (for windows or mac OS)

You can set up a working environment using the following setup to have a similar environment (Ubuntu 20.04) like the Sandbox on your local machine. Then you can install your favorite editor vi, vim, emacs on the docker or wsl and also all the libraries, programming languages you need to work on the project. Then you can work on the projects offline. Follow the steps highlighted in this Project page

##### 🔵 Access offline documentation for your programming languages and frameworks

All languages, frameworks, and libraries can be used offline. Here you can explore Python, C++, Ruby, PHP, Swift, React, Angular, and Django offline documentation:
- Python Documentation
- C++ Reference
- Ruby Documentation
- PHP Manual
- Swift Documentation
- React Documentation
- Angular Documentation
- Django Documentation

##### 🔵 Stable Internet Access Alternatives

If you lack stable internet, consider these options:
- Seek places with free Wi-Fi, like bars, libraries, coworking spaces, or university study rooms.
- Utilize mobile hotspot or tethering from your smartphone.
- Invest in a travel Wi-Fi router for reliable internet access on the go.

##### 🔵 Collaboration Tools and Backup Solutions

Work on group projects efficiently using Git for version control. Plan ahead, prioritize tasks, and back up your work regularly to avoid data loss during internet outages.

##### 🔵 Alternative Devices

Utilize tablets or cloud services like DigitalOcean if you lack a PC and follow Youssef’s advises (on Discord as @youssef.charif.hamidi_070222): I downloaded a working version of termux on Frandroid because it’s no longer available on the Play store, so I started to leverage the most power of shell scripting to gain time and energy (because I should mention that I type with only one finger),I also studied well Vim shortcuts which also have been a game changer.
The problem I usually encounter is some technologies like MySQL server or Docker aren’t supported in termux (as far as I know) , so for now, I am profiting of DigitalOcean 200$ free creative on our Github Student Pack and work on a rented Ubuntu machine when I don’t have what I need locally on termux (reliable internet connection is a must have in this case)

Use your phone, as Abdullah Mohammed Hussain (on Discord as @abdulmohusain) suggests: download termux and connect to your web terminal using ssh.

#### ✍🏽Admin issues✍🏽

##### 🟠 Understanding Warning Emails

Received a warning email? Don’t panic! It signifies a need for improvement but doesn’t mean withdrawal. Ensure a monthly average score of at least 70% in the last month of Foundations (Month 8th) and last month of Specialization (Month 2), with the Specialization Portfolio Project validated by a Mentor.

##### 🟠 Do you find it difficult to engage on Discord?

Dive into this Youtube Playlist  for Discord navigation tips.

##### 🟠 I don’t have access to my Discord account anymore, how do I do?

If you have access to the Intranet, follow the following steps:

Step 1: go to my profile
Step 2: unlink discord account
Step 3: link new account

##### 🟠 Deferment option

If you need to defer, follow these steps:
Go to Intranet -> On the bottom left of the page you will see your Profile -> Scroll down and select deferment.

##### 🟠 I am in Cohort 1 Blended and I want to defer

If you’re from Cohort 1 and want to defer, fill in this form

##### 🟠 Do you want to withdraw

Write an <NAME_EMAIL> and it will redirect you to the assistance of the Support team. Waiting time of approximately 24 hours to respond during working hours and working days, unless exceptions.

##### 🟠 How to Download your transcript

Easy - follow the steps highlighted in this video

##### 🟠 You have enrolled in AICE and unable to see your SE Curriculum

Unfortunately it is not possible to be in two Courses at the same time on Savanna (Intranet). We are working to solve the situation and we have taken note of all the affected learners who contacted us - we will follow up once we have a solution. Apologies for any inconvenience

#### How to contact support or chat with the support co-pilot

You have different ways you can contact get the support you need:
Email to our SE email (<EMAIL>) to access support;
Reach out to a mentor on Discord, clearly explaining your problem and ideally attaching a screenshot. The mentor will follow up;
Speak with your support AI-powered Co-pilot, LEA;
Open a ticket

Please note that the support team may take up to 24 hours to respond during working hours and working days

#### 📣ALX SE Community Townhall - April edition📣

#### Questions and answers

18:39 Graduation Criteria and what happens in January 2025

25:02 Can I still access the Intranet after Graduation?

25:46 I did not make it to 70% minimum threshold, what will happen?

26:22 What if I get 70% at the end of foundations but I didn’t do some of the projects, for example Simple Shell. Will that disqualify me from graduating?

27:36 If I get 70% final but I missed the mark during some months, can I still get a certificate?
31:07 What happens if I plagirize the Specialization Portfolio Project?

32:46 Would failed projects, due to no manual peer review, count as incomplete curriculum? If so, how can one get an auto-failed project scored before January 2025?

35:03 Kindly clarify if we have until Jan 2025 to finalize the portfolio project before moving on to specializations, or if they’ll happen simultaneously?

36:40 Can we request for letter of recommendation before graduation and how do we request it?

40:40 In order to meet the requirement, should I aim for a score of 70% specifically in the last month or is it necessary to achieve an overall score of 70%?

42:55 Is it possible to graduate with only Foundation?

45:18 Will all cohorts have access to the intranet after Jan 2025 or does this only apply to cohort 22?

45:33 Is one-on-one follow-up support available?

47:02 I haven’t been active for a long while and I would like to defer to month 0: is it possible?

48:54 What are the options for a C14 learner who is unable to complete their portfolio project within the given time limit due to personal reasons?

49:51 About Graduations: is there a graduation ceremony or will we just get our Certificates?

51:13 How do we get in touch with the technical mentor or ALX_SE staff after Graduation?

51:42 When is cohort 2 blended starting?

52:03 I didn’t reach 70 in one of the months. Will this affect my ability to get to Specialization?

53:36 I’ve been absent from the program, can I still defer and get a Certificate in the end?

55:00 Can you tell me more about the Portoflio Project?

55:55 What were learners able to do with their Portfolio Projects?

1:00:44 I cheated on some projects and feel very remorseful. How do I correct my mistake and develop myself?

1:02:52 How do presentations go? Are we going to talk about the project in technical detail or just as if we were selling a product?

1:05:00 I’m having trouble with the GitHub Student Developer Pack from ALX. My email is rejected && validation for the Microsoft Azure offer fails. How can I fix this?

1:06:41 I am now in specialization, can I go back and try to fix one of my foundations monthly thresholds?