# Group Projects

![Image](assets/image_2_ab0138760e80bc0ac3d1.jpg)

## Teamwork Makes the Dream Work

As you well know, collaboration is incredibly important - at school, in tech, and life. Even though we know it’s important, it can be easy to find our own, individual rhythm of productivity at the expense of our partner’s (or partners’) participation.

Inversely, you may encounter a project where your partner wants to take a backseat, and would prefer for the work to be done without their contributions.

These modes of project management (along with their derivatives) disservice your understanding and invaluable life skill of bringing another up to speed.

In the spirit of wanting to hold you accountable, your group projects will include a heavily weighted expectation of equal contribution.

## “What does that mean?”

It means that both partners need to have equal as possible git commits to the master branch as possible; the farther from “equal”, the overall score of the project will decrease.

Equal as possible == no more than a 40/60 contribution from the respective partner’s user name (this will be different in the off chance of a third member)

## Note

We realize this may prove a challenge at times, and that’s ok! It’s an education in and of itself that will prepare you for your future careers. Remember to follow the Framework, and it’ll all work out.

Happy Coding!