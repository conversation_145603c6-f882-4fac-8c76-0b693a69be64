# Load balancer

Ever wonder how Facebook, Linkedin, Twitter and other web giants are handling such huge amounts of traffic? They don’t have just one server, but tens of thousands of them. In order to achieve this, web traffic needs to be distributed to these servers, and that is the role of a load-balancer.

![Image](assets/image_3_6cefdd14b2f8c36789cba132bd5a10d42d88a177.png)

### Readme:

- Load-balancing

- Load-balancing algorithms