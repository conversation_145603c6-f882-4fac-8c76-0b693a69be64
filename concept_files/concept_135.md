# 3 Ways to Make a Portfolio Project Great

This concept is written with many examples of internet applications because URLs are easiest to share, but these suggestions are helpful to keep in mind for all types of software you might develop for your Portfolio Project.

### 1. The Logged Out Experience

Anyone you show your project to will likely have only a few minutes to take a look. The worst way to spend these few minutes is to throw up a log in screen. Clearly a log in is helpful for tailoring an experience and providing a persistent experience. Let take a quick tour to see how other websites behave.

### Logged out experience examples

Many of the most heavily trafficked web sites greet strangers with a locked-out landing page. If you want to experience this, open an anonymous browser and check out Facebook, Pinterest, and Github. These power brands can rely on users flocking to their site with the intention of signing up because they already know they want to use the product being offered.

Other sites are not as well known, and must rely on passive traffic where users come to the site to explore functionality and evaluate whether the experience is for them. Some have a logged out experience, but more clearly demonstrate how their product can be used. <PERSON><PERSON> and Trello are examples of this. Both landing pages have a demo and helpful information to inform browsers how their productivity might be positively impacted.

![Image](assets/image_8_0254b5ad94ac07a84269.png)

Another path is for websites to completely expose their functionality with no requirement to login. Some sites do this because the experience would be no different whether the user was logged in or out, and the author is not interested in the inherent risk of storing credentials. Others do this because the anonymous experience is core to their functionality (like Craigslist!)

##### The “Quake Tracker” App is a great logged out example:

![Image](assets/image_12_fe651c2bdc7af270640d.png)

##### The “Savings” App is another great example:

![Image](assets/image_15_19f3bed0f4c8d972c39e.png)

Think about ways you can expose the core experience (the “fun”) without requiring a login.

### 2. A Data-Rich Experience

We want to avoid the experience where an employer or ally might stumble upon your final project, take the time to login and find that they are greeted with emptiness. Take the time to think through how you might populate some presets into your software, or ingest data to populate your experience. Many apps go to great lengths to ensure the logged-in experience feels warm and inviting.

##### Example: Airtable

![Image](assets/image_21_533600168a5efdaddbda.png)

This might be a strategy you want to use for your app. You may want to create some default data, or a tutorial to bring your app to life upon logging in.

##### Example: Findacar

![Image](assets/image_25_41f95c992d17c94f1d2c.png)

##### Example: Moro

![Image](assets/image_28_277bbc8d0d3025e9bde6.gif)

### 3. Showcase yourself

It’s not enough that your Portfolio Project is an interesting and engaging experience. It’s necessary that the viewer of your app understands you created this app as a demo of your amazing skillset. Somewhere on your app’s page, you’ll want to link to the source code for this final project’s Github, or your personal web site. Here are some examples:

##### Little corner banner

![Image](assets/image_33_a41c197063bd9645d983.png)

##### Subtle footer mention

![Image](assets/image_36_2b648511e751885d8f29.png)

##### Call out in a section, with a photo!

![Image](assets/image_39_72f65360b2aa3854944b.png)