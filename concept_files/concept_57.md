# Git and Github cheat sheet - Everything in less than 30 seconds

## Create and setup a new repository in 15 seconds

Go to Github, log-in and find the green button to create a new repository. Click on it.

You should see this:

![Image](assets/image_5_78fc06c07aaa5fef06ddc142ded7149f863b0e29.png)

Fill in the repository `name`, and the `description` and click on the green button “Create repository”.
Do not initialize this repository with a README.

You should see this now:

![Image](assets/image_9_d05563cc0467300f97e53775907d8b53da2d297d.png)

Now in your computer’s terminal, create the directory that will contain your code and `cd` into it:

![Image](assets/image_12_549b2cd81aa4fbd7583fd300254a9e63a850cb42.png)

Now let’s setup our repository, and `push` a very simple README file:

![Image](assets/image_15_5e68ba434fc2cd208980bee4884d4f75a7af7325.png)

Now if you refresh your repository page on GitHub, you should see the `README.md` file there:

![Image](assets/image_18_2f5a734d7c3d9af7c9b28d97fabfe27845b4d3a3.png)

On your computer, you should have a `.git` directory inside your directory.

![Image](assets/image_21_3a8aeef03faf1741596150155f506619d02a5751.png)

All done.

## Add new (versions of) files and commit in 5 seconds

After working on your project, you can add, commit and push new and modified files to your GitHub, with 3 commands:

```c
git add <file(s) to add>
git commit -m <A meaningful commit message>
git push
```

Here’s an example. Let’s say we created a `main.c` file, and we want to push it:

![Image](assets/image_28_30f676598d06b673957e2ca75ce74d6159b8cee3.png)

Now on GitHub, we can see that the file was correctly pushed:

![Image](assets/image_31_f21bd74f58c474c7dd10d5fb8425b90bddcaff96.png)