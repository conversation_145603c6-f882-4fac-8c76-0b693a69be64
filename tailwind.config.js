/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    "./public/index.html",
    "./src/**/*.{js,jsx,ts,tsx,vue}", // Added vue just in case, and public/index.html
  ],
  theme: {
    extend: {
      fontFamily: {
        aptos: ['Aptos', 'sans-serif'],
      },
      colors: {
        // Existing primary colors
        primary: {
          DEFAULT: '#4F46E5', // Indigo color
          light: '#818CF8',
          dark: '#3730A3',
        },
        // Colors from src/index.css
        background: 'var(--background)',
        text: 'var(--text)', // Direct mapping for --text
        foreground: 'var(--text)', // Common Tailwind naming for text
        border: 'var(--border)',
        card: 'var(--card-bg)', // For use like bg-card
        'card-bg': 'var(--card-bg)', // Direct mapping
        'card-foreground': 'var(--text)',
        'hover-bg': 'var(--hover-bg)',
        muted: 'var(--muted-text)', // For use like bg-muted
        'muted-text': 'var(--muted-text)', // Direct mapping
        'muted-foreground': 'var(--muted-text)',
        'task-header': 'var(--taskHeader-bg)', // For use like bg-task-header
        'taskHeader-bg': 'var(--taskHeader-bg)', // Direct mapping
        'sidebar-text': 'var(--sidebar-text)', // New mapping for sidebar text
        'code-bg': 'var(--code-bg)', // Top-level direct mapping
        'code-text': 'var(--code-text)', // Top-level direct mapping
        code: { // Namespaced code colors
          DEFAULT: 'var(--code-bg)',
          text: 'var(--code-text)',
          keyword: 'var(--code-keyword)',
          function: 'var(--code-function)',
          string: 'var(--code-string)',
          comment: 'var(--code-comment)',
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
};
