import { promises as fs } from 'fs';
import path from 'path';
import { glob } from 'glob';

const PROJECTS_DIR = 'src/data/projects';
const IMAGES_DIR = 'public/images';
const IMAGE_URL_REGEX = /!\[.*?\]\(((?:https:\/\/intranet\.alxswe\.com\/images\/contents\/|https:\/\/s3\.amazonaws\.com\/intranet-projects-files\/holbertonschool-sysadmin_devops\/|https:\/\/s3\.amazonaws\.com\/alx-intranet\.hbtn\.io\/uploads\/medias\/).*?)\)/g;
const PROJECT_ID_REGEX = /\* `(\d+)` \[/g;

/**
 * Parse markdown content to build a map of project IDs
 * @param {string} markdownContent - Content of the markdown file
 * @returns {Set<string>} Set of project IDs
 */
function parseProjectIds(markdownContent) {
  const projectIds = new Set();
  let match;
  
  while ((match = PROJECT_ID_REGEX.exec(markdownContent)) !== null) {
    projectIds.add(match[1]);
  }
  
  return projectIds;
}

/**
 * Build a map of image filenames to their project IDs
 * @returns {Promise<Map<string, string>>} Map where keys are image filenames and values are project IDs
 */
async function buildImageToProjectIdMap() {
  const markdownContent = await fs.readFile('markdown_files/.md', 'utf8');
  const projectIds = parseProjectIds(markdownContent);
  const imageToProjectId = new Map();

  // For each project ID, check if it has any images
  for (const projectId of projectIds) {
    try {
      const assetsPath = path.join(IMAGES_DIR, projectId, 'assets');
      const files = await fs.readdir(assetsPath).catch(() => []);
      
      // Map each image in this project's assets to the project ID
      for (const file of files) {
        imageToProjectId.set(file, projectId);
      }
    } catch (error) {
      // Skip if the project directory doesn't exist
      continue;
    }
  }

  return imageToProjectId;
}

/**
 * Process a single JavaScript file to update image URLs
 * @param {string} filePath - Path to the JavaScript file
 * @param {Map<string, string>} imageToProjectId - Map of image filenames to project IDs
 * @returns {Promise<number>} Number of URLs updated
 */
async function processFile(filePath, imageToProjectId) {
  try {
    // Read file content
    const content = await fs.readFile(filePath, 'utf8');
    let updatedContent = content;
    let updateCount = 0;
    let match;

    // Process all matches in the file
    while ((match = IMAGE_URL_REGEX.exec(content)) !== null) {
      const [fullMatch, originalUrlWithQuery] = match;
      // Remove query parameters if present
      const originalUrl = originalUrlWithQuery.split('?')[0];
      // Extract filename from cleaned URL
      const filename = path.basename(originalUrl);

      // Look up the project ID from our map
      const projectId = imageToProjectId.get(filename);

      if (projectId) {
        // Construct new relative URL
        const newUrl = `/images/${projectId}/assets/${filename}`;
        // Replace just the URL part while preserving the rest of the markdown syntax
        const newText = fullMatch.replace(originalUrl, newUrl);
        updatedContent = updatedContent.replace(fullMatch, newText);
        updateCount++;
      } else {
        console.warn(`⚠️  No project mapping found for image: ${filename} in ${filePath}`);
      }
    }

    // Only write back if changes were made
    if (updateCount > 0) {
      await fs.writeFile(filePath, updatedContent, 'utf8');
      console.log(`✅ Updated ${updateCount} URL${updateCount > 1 ? 's' : ''} in ${filePath}`);
    } else {
      console.log(`ℹ️  No updates needed in ${filePath}`);
    }

    return updateCount;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error);
    return 0;
  }
}

/**
 * Main function to process all project files
 */
async function main() {
  try {
    console.log('🔍 Starting image URL update process...\n');
    
    // Build the image-to-project ID map first
    console.log('Building image to project ID map...');
    const imageToProjectId = await buildImageToProjectIdMap();
    console.log(`Found ${imageToProjectId.size} images mapped to projects.\n`);

    // Find all .js files recursively in the projects directory
    const files = await glob(`${PROJECTS_DIR}/**/*.js`);

    if (files.length === 0) {
      console.log('No JavaScript files found in the projects directory.');
      return;
    }

    // Process all files and collect results
    const results = await Promise.all(files.map(file => processFile(file, imageToProjectId)));

    // Calculate total updates
    const totalUpdates = results.reduce((sum, count) => sum + count, 0);
    const totalFiles = files.length;
    const filesUpdated = results.filter(count => count > 0).length;

    // Print summary
    console.log('\n📊 Summary:');
    console.log(`Total files processed: ${totalFiles}`);
    console.log(`Files updated: ${filesUpdated}`);
    console.log(`Total URL updates: ${totalUpdates}`);

    if (totalUpdates > 0) {
      console.log('\n⚠️  IMPORTANT: Please review the changes and commit them to version control.');
    }
  } catch (error) {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  }
}

// Execute the script
main().catch(console.error);
