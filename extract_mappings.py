#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to extract concept mappings from project files.
This script analyzes JavaScript files in the src/data/projects directory
to find mappings between concept names and their numeric IDs.
"""

import os
import re
from collections import defaultdict

# Helper functions
def normalize_filename(concept_name):
    """Convert concept name to normalized filename"""
    # Convert to lowercase
    name = concept_name.lower()
    # Replace special characters (including underscore, colon, slash) and whitespace with hyphens
    name = re.sub(r'[_/:,\s]+', '-', name)
    # Remove any characters that are not alphanumeric or hyphen
    name = re.sub(r'[^\w\-]+', '', name)
    # Remove leading/trailing hyphens
    name = name.strip('-')
    return f"{name}.md"

def get_filename_variations(concept_name):
    """Generate possible filename variations"""
    variations = []
    name_lower = concept_name.lower()

    # 1. Fully normalized version (aggressive hyphenation)
    # "Web Stack: Portfolio Project Criteria" -> "web-stack-portfolio-project-criteria.md"
    variations.append(normalize_filename(concept_name))

    # 2. Spaces to hyphens, keep other chars as is (then clean non-alphanum except hyphen/dot)
    # "Web Stack: Portfolio Project Criteria" -> "web-stack:-portfolio-project-criteria.md" -> "web-stack-portfolio-project-criteria.md"
    var2 = re.sub(r'\s+', '-', name_lower)
    var2 = re.sub(r'[^\w\-\.]+', '', var2) # Keep dots for potential .md extension
    var2 = var2.strip('-')
    if var2 and not var2.endswith('.md'):
        variations.append(f"{var2}.md")
    elif var2 and var2.endswith('.md'):
        variations.append(var2)
    
    # 3. Spaces to underscores, keep other chars as is (then clean non-alphanum except underscore/dot)
    # "Web Stack: Portfolio Project Criteria" -> "web_stack:_portfolio_project_criteria.md" -> "web_stack_portfolio_project_criteria.md"
    var3 = re.sub(r'\s+', '_', name_lower)
    var3 = re.sub(r'[^\w_\.]+', '', var3) # Keep dots
    var3 = var3.strip('_')
    if var3 and not var3.endswith('.md'):
        variations.append(f"{var3}.md")
    elif var3 and var3.endswith('.md'):
         variations.append(var3)

    # 4. Minimal changes: just lowercase and ensure .md. Useful if name is already a filename.
    # "webstack-portfolio_criteria.md" -> "webstack-portfolio_criteria.md"
    # "Web Stack Portfolio Project Criteria" -> "web stack portfolio project criteria.md" (less likely to match but covers edge cases)
    var4 = name_lower
    if not var4.endswith('.md'):
        variations.append(f"{var4}.md")
    else:
        variations.append(var4)
        
    # 5. Specific case for "webstack-portfolio_criteria.md" like structures
    # Try to match names that are already close to the desired filename format by replacing common separators with hyphens or underscores
    # "Web Stack Portfolio Project Criteria"
    # -> "webstack-portfolio-project-criteria.md" (covered by normalize_filename)
    # -> "webstack_portfolio_project_criteria.md"
    var5_hyphen = re.sub(r'[\s_:]+', '-', name_lower) # Spaces, underscores, colons to hyphens
    var5_hyphen = re.sub(r'[^\w\-\.]+', '', var5_hyphen).strip('-') # Clean non-alphanum (keep dots, hyphens)
    if var5_hyphen and not var5_hyphen.endswith('.md'):
        variations.append(f"{var5_hyphen}.md")
    elif var5_hyphen and var5_hyphen.endswith('.md'):
        variations.append(var5_hyphen)

    var5_underscore = re.sub(r'[\s\-:]+', '_', name_lower) # Spaces, hyphens, colons to underscores
    var5_underscore = re.sub(r'[^\w_\.]+', '', var5_underscore).strip('_') # Clean non-alphanum (keep dots, underscores)
    if var5_underscore and not var5_underscore.endswith('.md'):
        variations.append(f"{var5_underscore}.md")
    elif var5_underscore and var5_underscore.endswith('.md'):
        variations.append(var5_underscore)

    # 6. Preserve existing underscores, convert spaces/colons to hyphens
    # "Web Stack Portfolio_Project Criteria" -> "web-stack-portfolio_project-criteria.md"
    var6 = name_lower
    var6 = re.sub(r'[\s:]+', '-', var6) # Convert spaces and colons to hyphens
    var6 = re.sub(r'[^\w\-_.]+', '', var6) # Clean, keeping alphanum, hyphens, underscores, dots
    var6 = var6.strip('-_')
    if var6 and not var6.endswith('.md'):
        variations.append(f"{var6}.md")
    elif var6 and var6.endswith('.md'):
        variations.append(var6)

    # 7. Preserve existing hyphens, convert spaces/colons to underscores
    # "Web Stack Portfolio-Project Criteria" -> "web_stack_portfolio-project_criteria.md"
    var7 = name_lower
    var7 = re.sub(r'[\s:]+', '_', var7) # Convert spaces and colons to underscores
    var7 = re.sub(r'[^\w\-_.]+', '', var7) # Clean, keeping alphanum, hyphens, underscores, dots
    var7 = var7.strip('-_')
    if var7 and not var7.endswith('.md'):
        variations.append(f"{var7}.md")
    elif var7 and var7.endswith('.md'):
        variations.append(var7)
        
    # Add the exact concept name if it ends with .md (case-insensitive)
    if concept_name.lower().endswith(".md"):
        variations.append(concept_name.lower())

    # Specific override for "Web Stack Portfolio Project Criteria"
    if "Web Stack Portfolio Project Criteria" in concept_name: # Check original name
        variations.append("webstack-portfolio_criteria.md")
        
    # Add variation: spaces to hyphens, keep existing underscores
    var8 = name_lower
    var8 = re.sub(r'[\s:]+', '-', var8) # Convert spaces/colons to hyphens
    var8 = re.sub(r'[^\w\-_.]+', '', var8) # Clean, keeping alphanum, -, _, .
    var8 = var8.strip('-_')
    if var8 and not var8.endswith('.md'):
        variations.append(f"{var8}.md")
    elif var8 and var8.endswith('.md'):
         variations.append(var8)

    # Add variation: spaces to underscores, keep existing hyphens
    var9 = name_lower
    var9 = re.sub(r'[\s:]+', '_', var9) # Convert spaces/colons to underscores
    var9 = re.sub(r'[^\w\-_.]+', '', var9) # Clean, keeping alphanum, -, _, .
    var9 = var9.strip('-_')
    if var9 and not var9.endswith('.md'):
        variations.append(f"{var9}.md")
    elif var9 and var9.endswith('.md'):
         variations.append(var9)

    # Remove duplicates and filter out any empty strings that might have resulted from aggressive stripping
    return list(filter(None, sorted(list(set(variations)))))

def find_matching_file(concept_name, concept_files_dir):
    """Find matching file from possible variations"""
    variations = get_filename_variations(concept_name)
    # print(f"Debug: Variations for '{concept_name}': {variations}") # Optional debug
    for filename_variation in variations:
        if not filename_variation: # Skip empty variations
            continue
        full_path = os.path.join(concept_files_dir, filename_variation)
        if os.path.exists(full_path):
            return filename_variation
    return None

# --- New Helper ---
def get_normalized_basename(text):
    """Normalizes text (filename or concept name) for comparison."""
    # Lowercase, replace separators with space, remove non-alphanum/space
    name = text.lower()
    if name.endswith('.md'):
        name = name[:-3] # Remove .md extension
    name = re.sub(r'[\-_:]+', ' ', name) # Replace separators with space
    name = re.sub(r'[^\w\s]+', '', name) # Remove non-alphanum/space
    name = ' '.join(name.split()) # Normalize whitespace
    return name.strip()

# --- Refactored Function ---
def extract_concept_mappings():
    """Extract concept mappings by matching generated variations against actual files."""
    concept_id_to_name = defaultdict(set)
    
    # Step 1: Scan project files for Concept ID -> Concept Name mappings
    concept_pattern = r'\[([^\]]+)\]\(/concepts/(\d+)\)'
    projects_dir = 'src/data/projects'
    print(f"\nScanning {projects_dir} for concept links...")
    for root, _, files in os.walk(projects_dir):
        for file in files:
            if file.endswith('.js'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        matches = re.findall(concept_pattern, content)
                        for name, c_id in matches:
                            cleaned_name = re.sub(r'[#💻🏆]+', '', name.strip()).strip()
                            if cleaned_name:
                                concept_id_to_name[c_id].add(cleaned_name)
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")

    print(f"Found {len(concept_id_to_name)} unique concept IDs mentioned in project files.")

    # Step 2: Get the list of actual .md files from public/concept_files/
    concept_files_dir = 'public/concept_files/'
    actual_files_on_disk = set()
    try:
        print(f"\nListing actual files in {concept_files_dir}...")
        for filename in os.listdir(concept_files_dir):
            if filename.endswith('.md') and os.path.isfile(os.path.join(concept_files_dir, filename)):
                actual_files_on_disk.add(filename)
        print(f"Found {len(actual_files_on_disk)} actual .md files on disk.")
    except FileNotFoundError:
        print(f"Error: Directory not found - {concept_files_dir}")
        return {}, {}

    # Step 3: Attempt to map Concept IDs to actual filenames
    id_to_filename = {}
    unmapped_ids_report = set(concept_id_to_name.keys()) # Start with all IDs as unmapped

    print("\nAttempting to map concept IDs to actual filenames...")
    for concept_id, names_set in sorted(concept_id_to_name.items()):
        match_found_for_this_id = False
        for concept_name in sorted(list(names_set)): # Iterate through all names associated with an ID
            filename_variations = get_filename_variations(concept_name)
            for variation in filename_variations:
                if variation in actual_files_on_disk:
                    id_to_filename[concept_id] = variation
                    unmapped_ids_report.discard(concept_id)
                    print(f"  Mapped: ID {concept_id} -> '{variation}' (Matched variation from name: '{concept_name}')")
                    match_found_for_this_id = True
                    break # Found a match for this concept_name's variation
            if match_found_for_this_id:
                break # Found a match for this concept_id

        # If no variation matched, try the _<id>.md fallback
        if not match_found_for_this_id:
            fallback_filename = f"_{concept_id}.md"
            if fallback_filename in actual_files_on_disk:
                id_to_filename[concept_id] = fallback_filename
                unmapped_ids_report.discard(concept_id)
                print(f"  Mapped: ID {concept_id} -> '{fallback_filename}' (Fallback `_<id>.md` Match)")
            # else: # If still no match, it remains in unmapped_ids_report
                # print(f"  Info: ID {concept_id} ({', '.join(names_set)}) - No file found through variations or fallback.")


    # Step 4: Report unmapped IDs (those for which no file was found)
    if unmapped_ids_report:
        print("\n--- Warning: Unmapped Concept IDs ---")
        print("The following concept IDs found in project files could not be mapped to any existing file in 'public/concept_files/' (either via name variations or `_<id>.md`):")
        for c_id in sorted(list(unmapped_ids_report)):
             names = concept_id_to_name.get(c_id, {"Unknown Name"})
             print(f"  - ID: {c_id}, Original Names: {', '.join(names)}")
        print("--- End Warning ---\n")
    
    return id_to_filename, concept_id_to_name

def main():
    # Extract mappings
    id_to_filename, concept_id_to_name = extract_concept_mappings()
    
    # Generate JavaScript code for the mappings
    js_code = """// This file provides mapping between concept IDs and their filenames
// Generated automatically from project files

// Map from concept ID to filename
export const conceptIdToFilename = {
"""
    
    # Add each mapping
    for concept_id, filename in sorted(id_to_filename.items()):
        names = list(concept_id_to_name[concept_id])
        name_str = ", ".join(f'"{name}"' for name in names)
        js_code += f"  '{concept_id}': '{filename}', // {name_str}\n"
    
    js_code += """};

// Function to get the filename for a concept ID
export const getConceptFilename = (conceptId) => {
  return conceptIdToFilename[conceptId] || null;
};

// Function to get the file path for a concept ID
export const getConceptFilePath = (conceptId) => {
  const filename = getConceptFilename(conceptId);
  return filename ? `concept_files/${filename}` : null;
};
"""
    
    # Write to file
    with open('src/utils/conceptMapping.js', 'w', encoding='utf-8') as f:
        f.write(js_code)
    
    print(f"Generated mappings for {len(id_to_filename)} concepts")
    print("Saved to src/utils/conceptMapping.js")

if __name__ == "__main__":
    main()
