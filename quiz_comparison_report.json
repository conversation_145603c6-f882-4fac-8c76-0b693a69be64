{"summary": {"total_js_files": 112, "total_md_files": 125, "matched_pairs": 96, "files_with_discrepancies": 84}, "discrepancies": [{"js_file": "./src/data/project-quizzes/sorting-quiz.js", "md_file": "./quiz_files/0x1B__C_-_Sorting_algorithms___Big_O_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 2, <PERSON> has 43"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the time complexity of Bubble Sort in the worst case?", "md_text": "What is the time complexity accessing the nth element in an unsorted Python 3 list?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 7"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "O(n)", "md_option": "O(log(n))"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "O(n log n)", "md_option": "O(n^2)"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "O(n^2)", "md_option": "O(1)"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "O(1)", "md_option": "O(nlog(n))"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["O(n^2)"], "md_correct": ["O(1)"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Which of the following is a stable sorting algorithm?", "md_text": "What is the time complexity of this function / algorithm?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 7"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "Quick Sort", "md_option": "O(log(n))"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "<PERSON>ap Sort", "md_option": "O(n^2)"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "Bubble Sort", "md_option": "O(1)"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "Selection Sort", "md_option": "O(nlog(n))"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["Bubble Sort"], "md_correct": ["O(log(n))"]}, {"type": "missing_js_question", "question_number": 2, "message": "Question 2 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 3, "message": "Question 3 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 4, "message": "Question 4 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 10, "message": "Question 10 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 11, "message": "Question 11 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 12, "message": "Question 12 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 13, "message": "Question 13 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 14, "message": "Question 14 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 15, "message": "Question 15 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 16, "message": "Question 16 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 17, "message": "Question 17 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 18, "message": "Question 18 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 19, "message": "Question 19 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 20, "message": "Question 20 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 21, "message": "Question 21 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 22, "message": "Question 22 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 23, "message": "Question 23 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 24, "message": "Question 24 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 25, "message": "Question 25 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 26, "message": "Question 26 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 27, "message": "Question 27 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 28, "message": "Question 28 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 29, "message": "Question 29 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 30, "message": "Question 30 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 31, "message": "Question 31 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 32, "message": "Question 32 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 33, "message": "Question 33 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 34, "message": "Question 34 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 35, "message": "Question 35 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 36, "message": "Question 36 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 37, "message": "Question 37 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 38, "message": "Question 38 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 39, "message": "Question 39 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 40, "message": "Question 40 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 41, "message": "Question 41 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 42, "message": "Question 42 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/onboarding/map-your-mind.js", "md_file": "./quiz_files/Map_your_mind_quiz.md", "discrepancies": [{"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "It", "md_option": "It's simply an implementation of an algorithm in the form of annotations and informative text written in plain English"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": ",\n      ", "md_option": "it acts as a bridge between the program and the algorithm\""}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": ",\n      ", "md_option": "pseudocode improves the readability of any approach"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["It", ",\n      ", ",\n      "], "md_correct": ["It's simply an implementation of an algorithm in the form of annotations and informative text written in plain English", "it acts as a bridge between the program and the algorithm\"", "pseudocode improves the readability of any approach"]}]}, {"js_file": "./src/data/project-quizzes/onboarding/tweet.js", "md_file": "./quiz_files/A_tweet_a_day_keeps_the__julienbarbier42_far_away_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 2, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/onboarding/mental-health.js", "md_file": "./quiz_files/Preserving_your_mental_health___Conquering_imposter_syndrome_quiz.md", "discrepancies": [{"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "A nagging doubt that you haven", "md_option": "A nagging doubt that you haven't really earned your accomplishment"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": ",\n      ", "md_option": "feeling of fraudulence"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": ",\n      ", "md_option": "our ideas and skills are not worthy of other's attention"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["A nagging doubt that you haven", ",\n      ", ",\n      ", "s attention"], "md_correct": ["A nagging doubt that you haven't really earned your accomplishment", "feeling of fraudulence", "our ideas and skills are not worthy of other's attention"]}]}, {"js_file": "./src/data/project-quizzes/onboarding/network.js", "md_file": "./quiz_files/0x11__Python_-_Network__1_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 1, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/onboarding/intranet.js", "md_file": "./quiz_files/Deep_dive_into_the_Intranet_quiz.md", "discrepancies": [{"type": "option_text_mismatch", "question_number": 15, "option_number": 3, "message": "Question 15, Option 3 text differs", "js_option": "Copy-pasting another person", "md_option": "Copy-pasting another person's code"}, {"type": "option_text_mismatch", "question_number": 15, "option_number": 4, "message": "Question 15, Option 4 text differs", "js_option": ",\n      ", "md_option": "Having another person explain it to me"}]}, {"js_file": "./src/data/project-quizzes/onboarding/grit.js", "md_file": "./quiz_files/Grit_Assignment_Part_2_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 3, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/api-quiz.js", "md_file": "./quiz_files/0x14__JavaScript_-_Web_scraping_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/api-advanced-quiz.js", "md_file": "./quiz_files/0x16__API_advanced_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/webstack-monitoring-quiz.js", "md_file": "./quiz_files/0x18__Webstack_monitoring_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/web-server-quiz.js", "md_file": "./quiz_files/0x0C__Web_server_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 8, <PERSON> has 10"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "Why do we need DNS?", "md_text": "What was one of the most important reason for which DNS was created"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the main role of a web server?", "md_text": "The main role of a web server is to"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is TTL in DNS?", "md_text": "What is TTL within the context of DNS"}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the main role of DNS?", "md_text": "The main role of DNS is to"}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Why do web servers use multiple child processes?", "md_text": "Why web servers usually use child processes?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "That", "md_option": "That’s just a subjective technical choice from the developers who created the software"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": ",\n      ", "md_option": "To prevent memory leak"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": ",\n      ", "md_option": "So that the web server can dynamically change the number of child process to accommodate the volume of requests to be processed"}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "What is a web server?", "md_text": "A web server is"}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "What is the purpose of an HTTP POST request?", "md_text": "A DNS CNAME record translates to"}, {"type": "options_count_mismatch", "question_number": 6, "message": "Question 6 options count differs: JS has 3, MD has 2"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 0, "message": "Question 6, Option 0 text differs", "js_option": "Request data", "md_option": "an IP"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 1, "message": "Question 6, Option 1 text differs", "js_option": "Delete data", "md_option": "a domain"}, {"type": "correct_answers_mismatch", "question_number": 6, "message": "Question 6 correct answers differ", "js_correct": ["Submit data"], "md_correct": ["a domain"]}, {"type": "question_text_mismatch", "question_number": 7, "message": "Question 7 text differs", "js_text": "What is the purpose of an HTTP GET request?", "md_text": "A HTTP POST request is to"}, {"type": "correct_answers_mismatch", "question_number": 7, "message": "Question 7 correct answers differ", "js_correct": ["Request data"], "md_correct": ["submit data"]}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/web-stack-debugging-2-quiz.js", "md_file": "./quiz_files/0x12__Web_stack_debugging__2_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/load-balancer-quiz.js", "md_file": "./quiz_files/0x0F__Load_balancer_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/firewall-quiz.js", "md_file": "./quiz_files/0x13__Firewall_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 6"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the main purpose of a firewall?", "md_text": "What is a firewall?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "To filter incoming and outgoing network traffic", "md_option": "A hardware security system"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "To filter outgoing traffic", "md_option": "A software security system"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "To filter incoming and outgoing TCP traffic", "md_option": "A hardware or software security system"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["To filter incoming and outgoing network traffic"], "md_correct": ["A hardware or software security system"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What are the main types of firewalls?", "md_text": "What is the main function of a firewall?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "Soft and hard firewall", "md_option": "To filter incoming and outgoing network traffic"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "Network and host-based firewall", "md_option": "To filter outgoing traffic"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "Incoming and outgoing firewall", "md_option": "To filter incoming and outgoing TCP traffic"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["Network and host-based firewall"], "md_correct": ["To filter incoming and outgoing network traffic"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "When using UFW, what important port must you be careful not to block?", "md_text": "What are the 2 types of firewall?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "Port 80 (HTTP)", "md_option": "Soft and hard firewall"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "Port 443 (HTTPS)", "md_option": "Network and host-based firewall"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "Port 22 (SSH)", "md_option": "Incoming and outgoing firewall"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Port 22 (SSH)"], "md_correct": ["Network and host-based firewall"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is port forwarding in the context of firewalls?", "md_text": "What is the main function of a firewall?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Blocking all ports", "md_option": "To filter incoming and outgoing network traffic"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Redirecting traffic from one port to another", "md_option": "To filter outgoing traffic"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "Opening all ports", "md_option": "To filter incoming and outgoing TCP traffic"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Redirecting traffic from one port to another"], "md_correct": ["To filter incoming and outgoing network traffic"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/application-server-quiz.js", "md_file": "./quiz_files/0x1A__Application_server_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/networking-basics-0-quiz.js", "md_file": "./quiz_files/0x07__Networking_basics__0_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 7, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 5, "message": "Question 5 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 6, "message": "Question 6 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/ssh-quiz.js", "md_file": "./quiz_files/0x0B__SSH_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/networking-basics-1-quiz.js", "md_file": "./quiz_files/0x08__Networking_basics__1_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, <PERSON> has 2"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is localhost?", "md_text": "What is ?"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What does 0.0.0.0 represent in networking?", "md_text": "What is ?"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/web-infrastructure-quiz.js", "md_file": "./quiz_files/0x09__Web_infrastructure_design_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 6, <PERSON> has 7"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "A server is a device, a virtual device or computer program or providing functionality for other programs or devices, called \\", "md_option": "A server is a device, a virtual device or computer program or providing functionality for other programs or devices, called “clients”."}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "A server is returning information to other computers when asked", "md_option": "A server is a software that serves web pages."}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["A server is a device, a virtual device or computer program or providing functionality for other programs or devices, called \\"], "md_correct": ["A server is a device, a virtual device or computer program or providing functionality for other programs or devices, called “clients”."]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is a web server?", "md_text": "What is a codebase?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "A web server is a software or physical device serving web pages over HTTP", "md_option": "A list of software libraries."}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "A web server is a software that serves web pages to clients upon their request", "md_option": "Is the most important files of a software system."}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "A web server is a software that serves web pages to clients upon their request, it does this over the protocol HTTP", "md_option": "Is the collection of source code that is used to build a software system."}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["A web server is a software that serves web pages to clients upon their request, it does this over the protocol HTTP"], "md_correct": ["Is the collection of source code that is used to build a software system."]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is HTTPS?", "md_text": "What is a web server?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "A faster version of HTTP", "md_option": "A web server is a software or physical device serving web pages over HTTP."}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "A version of HTTP that secure the traffic between your browser and the website by encrypting it", "md_option": "A web server is a software that serves web pages to clients upon their request."}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "A version of HTTP that protect your personal information", "md_option": "A web server is a software that serves web pages to clients upon their request, it does this over the protocol HTTP."}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["A version of HTTP that secure the traffic between your browser and the website by encrypting it"], "md_correct": ["A web server is a software that serves web pages to clients upon their request, it does this over the protocol HTTP."]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is DNS?", "md_text": "What is HTTPS?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "A list of domain names", "md_option": "A faster version of HTTP."}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "A system that contain all Internet IPs", "md_option": "A version of HTTP that secure the traffic between your browser and the website by encrypting it."}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "A system to translate domain names into IP addresses", "md_option": "A version of HTTP that protect your personal information."}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["A system to translate domain names into IP addresses"], "md_correct": ["A version of HTTP that secure the traffic between your browser and the website by encrypting it."]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "What is TCP/IP?", "md_text": "What is a DNS?"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on the Internet or any private network", "md_option": "A list of domain names."}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on private network", "md_option": "A system that contain all Internet IPs."}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 2, "message": "Question 5, Option 2 text differs", "js_option": "Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on the Internet", "md_option": "A system to translate domain names into IP addresses."}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on the Internet or any private network"], "md_correct": ["A system to translate domain names into IP addresses."]}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/web-stack-debugging-0-quiz.js", "md_file": "./quiz_files/0x0D__Web_stack_debugging__0_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/mysql-quiz.js", "md_file": "./quiz_files/0x14__MySQL_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/https-ssl-quiz.js", "md_file": "./quiz_files/0x10__HTTPS_SSL_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, <PERSON> has 3"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Where should SSL certificates be hosted?", "md_text": "You want to setup HTTPS on your website, where shall you place the certificate?"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is the main purpose of HTTPS?", "md_text": "Why do you need HTTPS?"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/system-engineering-devops/web-stack-debugging-1-quiz.js", "md_file": "./quiz_files/0x0E__Web_stack_debugging__1_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/low-level/even-more-pointers-quiz.js", "md_file": "./quiz_files/0x07__C_-_Even_more_pointers__arrays_and_strings_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "In this following code, what is the value of a[3][0]?\\n```c\\nint a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};\\n```", "md_text": "In this following code, what is the value of ?"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the size of p in this code?\\n```c\\nint *p;\\n```", "md_text": "What is the size of  in this code?"}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "In this following code, what is the value of a[0][0]?\\n```c\\nint a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};\\n```", "md_text": "In this following code, what is the value of ?"}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the size of p in this code?\\n```c\\nint **p;\\n```", "md_text": "What is the size of  in this code?"}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "What is the size of *p in this code?\\n```c\\nint *p;\\n```", "md_text": "What is the size of  in this code?"}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "In this following code, what is the value of a[1][1]?\\n```c\\nint a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};\\n```", "md_text": "In this following code, what is the value of ?"}, {"type": "question_text_mismatch", "question_number": 7, "message": "Question 7 text differs", "js_text": "In this following code, what is the value of a[3][1]?\\n```c\\nint a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};\\n```", "md_text": "In this following code, what is the value of ?"}, {"type": "question_text_mismatch", "question_number": 8, "message": "Question 8 text differs", "js_text": "What is the size of *p in this code?\\n```c\\nint **p;\\n```", "md_text": "What is the size of  in this code?"}]}, {"js_file": "./src/data/project-quizzes/low-level/preprocessor-quiz.js", "md_file": "./quiz_files/0x0D__C_-_Preprocessor_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 13, <PERSON> has 15"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What does the macro TABLESIZE expand to?\\n```c\\n#define BUFSIZE 1020\\n#define TABLESIZE BUFSIZE\\n#undef BUFSIZE\\n#define BUFSIZE 37\\n```", "md_text": "What does the macro  expand to?"}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "The macro __FILE__ expands to the name of the current input file, in the form of a C string constant.", "md_text": "The macro  expands to the name of the current input file, in the form of a C string constant."}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "The preprocessor generates object code", "md_text": "What will be the last 5 lines of the output of the command  on this code?"}, {"type": "options_count_mismatch", "question_number": 5, "message": "Question 5 options count differs: JS has 2, MD has 4"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "True", "md_option": "int main(void)"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "False", "md_option": "int main(void)"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["False"], "md_correct": ["int main(void)"]}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "This code will try to allocate 1024 bytes in the heap:\\n```c\\n#define BUFFER_SIZE 1024\\nmalloc(BUFFER_SIZE)\\n```", "md_text": "The preprocessor generates object code"}, {"type": "correct_answers_mismatch", "question_number": 6, "message": "Question 6 correct answers differ", "js_correct": ["True"], "md_correct": ["False"]}, {"type": "question_text_mismatch", "question_number": 7, "message": "Question 7 text differs", "js_text": "What will be the output of this program? (on a standard 64 bits, Linux machine)\\n```c\\n#include <stdio.h>\\n#include <stdlib.h>\\n\\n#define int char\\n\\nint main(void)\\n{\\n    int i;\\n\\n    i = 5;\\n    printf (\\", "md_text": "This code will try to allocate 1024 bytes in the heap:"}, {"type": "options_count_mismatch", "question_number": 7, "message": "Question 7 options count differs: JS has 6, MD has 2"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 0, "message": "Question 7, Option 0 text differs", "js_option": "sizeof(i) = 4", "md_option": "True"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 1, "message": "Question 7, Option 1 text differs", "js_option": "Segmentation Fault", "md_option": "False"}, {"type": "correct_answers_mismatch", "question_number": 7, "message": "Question 7 correct answers differ", "js_correct": ["sizeof(i) = 1"], "md_correct": ["True"]}, {"type": "question_text_mismatch", "question_number": 8, "message": "Question 8 text differs", "js_text": "The preprocessor generates assembly code", "md_text": "What will be the output of this program? (on a standard 64 bits, Linux machine)"}, {"type": "options_count_mismatch", "question_number": 8, "message": "Question 8 options count differs: JS has 2, MD has 6"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 0, "message": "Question 8, Option 0 text differs", "js_option": "True", "md_option": "sizeof(i) = 4"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 1, "message": "Question 8, Option 1 text differs", "js_option": "False", "md_option": "Segmentation Fault"}, {"type": "correct_answers_mismatch", "question_number": 8, "message": "Question 8 correct answers differ", "js_correct": ["False"], "md_correct": ["sizeof(i) = 1"]}, {"type": "question_text_mismatch", "question_number": 9, "message": "Question 9 text differs", "js_text": "NULL is a macro", "md_text": "This is the correct way to define the macro :"}, {"type": "options_count_mismatch", "question_number": 9, "message": "Question 9 options count differs: JS has 2, MD has 4"}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 0, "message": "Question 9, Option 0 text differs", "js_option": "True", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 1, "message": "Question 9, Option 1 text differs", "js_option": "False", "md_option": "No, it should be written this way:"}, {"type": "correct_answers_mismatch", "question_number": 9, "message": "Question 9 correct answers differ", "js_correct": ["True"], "md_correct": ["No, it should be written this way:"]}, {"type": "question_text_mismatch", "question_number": 10, "message": "Question 10 text differs", "js_text": "The preprocessor removes all comments", "md_text": "The preprocessor generates assembly code"}, {"type": "correct_answers_mismatch", "question_number": 10, "message": "Question 10 correct answers differ", "js_correct": ["True"], "md_correct": ["False"]}, {"type": "question_text_mismatch", "question_number": 11, "message": "Question 11 text differs", "js_text": "This portion of code is actually using the library stdlib.\\n```c\\n#include <stdlib.h>\\n```", "md_text": "is a macro"}, {"type": "correct_answers_mismatch", "question_number": 11, "message": "Question 11 correct answers differ", "js_correct": ["False"], "md_correct": ["True"]}, {"type": "question_text_mismatch", "question_number": 12, "message": "Question 12 text differs", "js_text": "What is the gcc option that runs only the preprocessor?", "md_text": "The preprocessor removes all comments"}, {"type": "options_count_mismatch", "question_number": 12, "message": "Question 12 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 12, "option_number": 0, "message": "Question 12, Option 0 text differs", "js_option": "-E", "md_option": "True"}, {"type": "option_text_mismatch", "question_number": 12, "option_number": 1, "message": "Question 12, Option 1 text differs", "js_option": "-pedantic", "md_option": "False"}, {"type": "correct_answers_mismatch", "question_number": 12, "message": "Question 12 correct answers differ", "js_correct": ["-E"], "md_correct": ["True"]}, {"type": "missing_js_question", "question_number": 13, "message": "Question 13 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 14, "message": "Question 14 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/search-quiz.js", "md_file": "./quiz_files/Research___Project_approval__Part_3__quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 3, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/low-level/stacks-queues-quiz.js", "md_file": "./quiz_files/0x19__C_-_Stacks__Queues_-_LIFO__FIFO_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 0, MD has 4"}, {"type": "missing_js_question", "question_number": 0, "message": "Question 0 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 1, "message": "Question 1 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 2, "message": "Question 2 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 3, "message": "Question 3 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/hello-world.js", "md_file": "./quiz_files/0x00__Python_-_Hello__World_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 6, <PERSON> has 10"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "Which command can be used to compile a C source file?", "md_text": "What does this command line print?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 3, MD has 4"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "c-compiler", "md_option": "“98 Battery street, San Francisco”"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "bash", "md_option": "98 Battery street, San Francisco"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "gcc", "md_option": "8 Battery street, San"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["gcc"], "md_correct": ["98 Battery street, San Francisco"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "In which category belongs the C programming language?", "md_text": "Who created Python?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 2, MD has 3"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "Interpreted language", "md_option": "<PERSON>"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "Compiled language", "md_option": "<PERSON>"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["Compiled language"], "md_correct": ["<PERSON>"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is the common extension for a C header file?", "md_text": "What does this command line print?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 5"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": ".header", "md_option": "si"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": ".h", "md_option": "on"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": ".hpp", "md_option": "Python"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": ".ch", "md_option": "is"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": [".h"], "md_correct": ["is"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the common extension for a C source file?", "md_text": "What does this command line print?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": ".txt", "md_option": "98 Battery street"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": ".cpp", "md_option": "9 Battery street"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": ".c", "md_option": "f\"98 Battery street\""}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": ".py", "md_option": "8 Battery street"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": [".c"], "md_correct": ["98 Battery street"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What are the different steps to form an executable file from C source code", "md_text": "What does this command line print?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 5, MD has 4"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Compilation and linking", "md_option": "ol"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Interpretation, compilation and assembly", "md_option": "o"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "Interpretation, assembly and compilation", "md_option": "l"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 3, "message": "Question 4, Option 3 text differs", "js_option": "Preprocessing and compilation", "md_option": "Nothing"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Preprocessing, compilation, assembly, and linking"], "md_correct": ["o"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "Which of the following are both valid comment syntaxes in ANSI C, and Betty-compliant?", "md_text": "What does this command line print?"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "# Comment", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "/* Comment */", "md_option": "Holberton school"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 2, "message": "Question 5, Option 2 text differs", "js_option": "/* Comment /* nested */ */", "md_option": "“Holberton school”"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 3, "message": "Question 5, Option 3 text differs", "js_option": "// Comment", "md_option": "‘Holberton school’"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["/* Comment */"], "md_correct": ["Holberton school"]}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/functions.js", "md_file": "./quiz_files/0x01__Python_-_if_else__loops__functions_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 11, <PERSON> has 9"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the ASCII value of ", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "70", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "74", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["74"], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the result of 89 % 7?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "0", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "3", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["5"], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which of these loop statements don", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 6, MD has 4"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "loop_to", "md_option": "2 3 4 5 6 7 8 9 10"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "for", "md_option": "4 6 8 10 12 14 16 18"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "foreach", "md_option": "2 3 4 5 6 7 8 9"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "do... while", "md_option": "2 4 6 8"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["loop_to", "foreach", "each"], "md_correct": ["2 4 6 8"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the result of 12 % 3?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "0", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "2", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["0"], "md_correct": ["School"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the ASCII value of ", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "97", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "12", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["97"], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "What is the result of 12 % 10?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "0", "md_option": "2 4"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "2", "md_option": "2 3 4"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 2, "message": "Question 5, Option 2 text differs", "js_option": "1", "md_option": "2 3"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 3, "message": "Question 5, Option 3 text differs", "js_option": "3", "md_option": "3 4"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["2"], "md_correct": ["2 3"]}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "What is the ASCII value of ", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 6, "message": "Question 6 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 0, "message": "Question 6, Option 0 text differs", "js_option": "97", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 1, "message": "Question 6, Option 1 text differs", "js_option": "12", "md_option": "School"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 2, "message": "Question 6, Option 2 text differs", "js_option": "65", "md_option": "C is fun"}, {"type": "correct_answers_mismatch", "question_number": 6, "message": "Question 6 correct answers differ", "js_correct": ["65"], "md_correct": ["C is fun"]}, {"type": "question_text_mismatch", "question_number": 7, "message": "Question 7 text differs", "js_text": "What is the result of 12 % 2?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 0, "message": "Question 7, Option 0 text differs", "js_option": "0", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 1, "message": "Question 7, Option 1 text differs", "js_option": "2", "md_option": "School"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 2, "message": "Question 7, Option 2 text differs", "js_option": "1", "md_option": "C is fun"}, {"type": "correct_answers_mismatch", "question_number": 7, "message": "Question 7 correct answers differ", "js_correct": ["0"], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "question_text_mismatch", "question_number": 8, "message": "Question 8 text differs", "js_text": "What is the ASCII value of ", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 8, "message": "Question 8 options count differs: JS has 3, MD has 4"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 0, "message": "Question 8, Option 0 text differs", "js_option": "45", "md_option": "1 2 3 4"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 1, "message": "Question 8, Option 1 text differs", "js_option": "47", "md_option": "0 1 2 3"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 2, "message": "Question 8, Option 2 text differs", "js_option": "3", "md_option": "1 2 3"}, {"type": "correct_answers_mismatch", "question_number": 8, "message": "Question 8 correct answers differ", "js_correct": ["45"], "md_correct": ["0 1 2 3"]}, {"type": "missing_md_question", "question_number": 9, "message": "Question 9 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 10, "message": "Question 10 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/low-level/static-libraries-quiz.js", "md_file": "./quiz_files/0x09__C_-_Static_libraries_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 3, <PERSON> has 4"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is the point of using ranlib?", "md_text": "What command(s) can be used to list the symbols stored in a static library?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["Indexing an archive"], "md_correct": []}, {"type": "missing_js_question", "question_number": 3, "message": "Question 3 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/argv-quiz.js", "md_file": "./quiz_files/0x0A__C_-_argc__argv_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 7"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is argv[argc]?", "md_text": "What is ?"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is argv[0]?", "md_text": "What is ?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["The program name"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "In the following command, what is argv[2]?\\n\\``\\`c\\n$ ./argv \\", "md_text": "What is"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 9, MD has 4"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "is", "md_option": "NULL"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "School", "md_option": "The first command line argument"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "My School", "md_option": "It does not always exist"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "NULL", "md_option": "The program name"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["is fun"], "md_correct": ["The program name"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "In the following command, what is argv[2]?\\n\\``\\`c\\n$ ./argv My School is fun\\n\\``\\`", "md_text": "What is ?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 9, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["School"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "In the following command, what is argv[2]?\\n\\``\\`c\\n$ ./argv \\", "md_text": "In the following command, what is ?"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["NULL"], "md_correct": ["is fun"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/printf-quiz.js", "md_file": "./quiz_files/0x11__C_-_printf_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 12, <PERSON> has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 5, "message": "Question 5 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 6, "message": "Question 6 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 7, "message": "Question 7 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 8, "message": "Question 8 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 9, "message": "Question 9 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 10, "message": "Question 10 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 11, "message": "Question 11 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/low-level/file-io-quiz.js", "md_file": "./quiz_files/0x15__C_-_File_I_O_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 12, <PERSON> has 15"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the oflag used to open a file with the mode read only?", "md_text": "What is the  used to open a file with the mode read only?"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Most of the time, on a classic, modern Linux system, what will be the value of the first file descriptor you will get after opening a new file with open (if open succeeds of course):", "md_text": "Most of the time, on a classic, modern Linux system, what will be the value of the first file descriptor you will get after ing a new file with open (if open succeeds of course):"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What happens if you try to write \\", "md_text": "Which of these answers are the equivalent of  on Ubuntu 14.04 LTS? (select all correct answers):"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["The text will be printed on the terminal on the standard output"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the unistd symbolic constant for the standard input?", "md_text": "What happens if you try to write “Best” to the standard input on Ubuntu 14.04 LTS?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 3, MD has 4"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "STDIN_FILENO", "md_option": "Nothing"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "STDERR_FILENO", "md_option": "The text will be printed on the terminal but I can’t pipe it"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "STDOUT_FILENO", "md_option": "Segmentation fault"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["STDIN_FILENO"], "md_correct": ["The text will be printed on the terminal on the standard output"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the unistd symbolic constant for the Standard error?", "md_text": "What is the  symbolic constant for the standard input?"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["STDERR_FILENO"], "md_correct": ["STDIN_FILENO"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "What is the return value of the system call open if it fails?", "md_text": "Without context, on Ubuntu 14.04 LTS,  is a … (please select all correct answers):"}, {"type": "options_count_mismatch", "question_number": 5, "message": "Question 5 options count differs: JS has 3, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["-1"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "When I am using O_WRONLY | O_CREAT | O_APPEND -> the | are bitwise operators.", "md_text": "What is the  symbolic constant for the Standard error?"}, {"type": "options_count_mismatch", "question_number": 6, "message": "Question 6 options count differs: JS has 2, MD has 3"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 0, "message": "Question 6, Option 0 text differs", "js_option": "True", "md_option": "STDIN_FILENO"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 1, "message": "Question 6, Option 1 text differs", "js_option": "False", "md_option": "STDERR_FILENO"}, {"type": "correct_answers_mismatch", "question_number": 6, "message": "Question 6 correct answers differ", "js_correct": ["True"], "md_correct": ["STDERR_FILENO"]}, {"type": "question_text_mismatch", "question_number": 7, "message": "Question 7 text differs", "js_text": "Why will 3 most likely be the first file descriptor received after opening a new file?", "md_text": "What is the return value of the system call  if it fails?"}, {"type": "options_count_mismatch", "question_number": 7, "message": "Question 7 options count differs: JS has 6, MD has 3"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 0, "message": "Question 7, Option 0 text differs", "js_option": "Because most of the time, I will already have stdin (value 0), stdout (value 1) and stderr (value 2) opened when my program starts executing.", "md_option": "0"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 1, "message": "Question 7, Option 1 text differs", "js_option": "Because this will be the first opened file descriptor and in CS we start counting starting from 0", "md_option": "98"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 2, "message": "Question 7, Option 2 text differs", "js_option": "Because this will be the second opened file descriptor for my process", "md_option": "-1"}, {"type": "correct_answers_mismatch", "question_number": 7, "message": "Question 7 correct answers differ", "js_correct": ["Because most of the time, I will already have stdin (value 0), stdout (value 1) and stderr (value 2) opened when my program starts executing."], "md_correct": ["-1"]}, {"type": "question_text_mismatch", "question_number": 8, "message": "Question 8 text differs", "js_text": "What is the unistd symbolic constant for the standard output?", "md_text": "When I am using  -> the | are bitwise operators."}, {"type": "options_count_mismatch", "question_number": 8, "message": "Question 8 options count differs: JS has 3, MD has 2"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 0, "message": "Question 8, Option 0 text differs", "js_option": "STDIN_FILENO", "md_option": "True"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 1, "message": "Question 8, Option 1 text differs", "js_option": "STDERR_FILENO", "md_option": "False"}, {"type": "correct_answers_mismatch", "question_number": 8, "message": "Question 8 correct answers differ", "js_correct": ["STDOUT_FILENO"], "md_correct": ["True"]}, {"type": "question_text_mismatch", "question_number": 9, "message": "Question 9 text differs", "js_text": "What system call would you use to write to a file descriptor?", "md_text": "why? #<PERSON>Ask<PERSON><PERSON>"}, {"type": "options_count_mismatch", "question_number": 9, "message": "Question 9 options count differs: JS has 3, MD has 6"}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 0, "message": "Question 9, Option 0 text differs", "js_option": "printf", "md_option": "Because most of the time, I will already have stdin (value 0), stdout (value 1) and stderr (value 2) opened when my program starts executing."}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 1, "message": "Question 9, Option 1 text differs", "js_option": "write", "md_option": "Because this will be the first opened file descriptor and in CS we start counting starting from 0"}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 2, "message": "Question 9, Option 2 text differs", "js_option": "fprintf", "md_option": "Because this will be the second opened file descriptor for my process"}, {"type": "correct_answers_mismatch", "question_number": 9, "message": "Question 9 correct answers differ", "js_correct": ["write"], "md_correct": ["Because most of the time, I will already have stdin (value 0), stdout (value 1) and stderr (value 2) opened when my program starts executing."]}, {"type": "question_text_mismatch", "question_number": 10, "message": "Question 10 text differs", "js_text": "What is the correct combination of oflags used to open a file with the mode write only, create it if it doesn", "md_text": "What is the  symbolic constant for the standard output?"}, {"type": "options_count_mismatch", "question_number": 10, "message": "Question 10 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 10, "option_number": 0, "message": "Question 10, Option 0 text differs", "js_option": "O_WRONLY", "md_option": "STDIN_FILENO"}, {"type": "option_text_mismatch", "question_number": 10, "option_number": 1, "message": "Question 10, Option 1 text differs", "js_option": "O_WRONLY | O_CREAT | O_APPEND", "md_option": "STDERR_FILENO"}, {"type": "option_text_mismatch", "question_number": 10, "option_number": 2, "message": "Question 10, Option 2 text differs", "js_option": "O_WRONLY | O_CREAT | O_EXCL", "md_option": "STDOUT_FILENO"}, {"type": "correct_answers_mismatch", "question_number": 10, "message": "Question 10 correct answers differ", "js_correct": ["O_WRONLY | O_CREAT | O_APPEND"], "md_correct": ["STDOUT_FILENO"]}, {"type": "question_text_mismatch", "question_number": 11, "message": "Question 11 text differs", "js_text": "What is the oflag used to open a file in mode read + write?", "md_text": "What system call would you use to write to a file descriptor? (select all correct answers)"}, {"type": "option_text_mismatch", "question_number": 11, "option_number": 0, "message": "Question 11, Option 0 text differs", "js_option": "O_WRONLY", "md_option": "printf"}, {"type": "option_text_mismatch", "question_number": 11, "option_number": 1, "message": "Question 11, Option 1 text differs", "js_option": "O_RDWR", "md_option": "write"}, {"type": "option_text_mismatch", "question_number": 11, "option_number": 2, "message": "Question 11, Option 2 text differs", "js_option": "O_RDONLY", "md_option": "fprintf"}, {"type": "correct_answers_mismatch", "question_number": 11, "message": "Question 11 correct answers differ", "js_correct": ["O_RDWR"], "md_correct": ["write"]}, {"type": "missing_js_question", "question_number": 12, "message": "Question 12 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 13, "message": "Question 13 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 14, "message": "Question 14 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/more-malloc-quiz.js", "md_file": "./quiz_files/0x0C__C_-_More_malloc__free_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 8, <PERSON> has 10"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "The memory space reserved when calling malloc is on:", "md_text": "The memory space reserved when calling  is on:"}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What will you see on the terminal?\\n\\``\\`c\\nint main(void)\\n{\\n    int *ptr;\\n\\n    *ptr = 98;\\n    printf(\\", "md_text": "What will you see on the terminal?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "It doesn", "md_option": "It doesn’t compile"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": ",\n      ", "md_option": "98"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 3, "message": "Question 4, Option 3 text differs", "js_option": ",\n      ", "md_option": "Segmentation Fault"}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "You can do this:\\n\\``\\`c\\nchar *s;\\n\\ns = strdup(\\", "md_text": "If I want to copy the string “Best School” into a new space in memory, I can use this statement to reserve enough space for it (select all valid answers):"}, {"type": "options_count_mismatch", "question_number": 5, "message": "Question 5 options count differs: JS has 2, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["Yes"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "You can do this:\\n\\``\\`c\\nfree(\\", "md_text": "You can do this:"}, {"type": "correct_answers_mismatch", "question_number": 6, "message": "Question 6 correct answers differ", "js_correct": ["No"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 7, "message": "Question 7 text differs", "js_text": "You can do this:\\n\\``\\`c\\nchar str[] = \\", "md_text": "You can do this:"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/recursion-quiz.js", "md_file": "./quiz_files/0x08__C_-_Recursion_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What does this code print?\\n\\``\\`c\\nvoid print(int nb)\\n{\\n    if (nb < 0) \\n    {\\n        return;\\n    }\\n    printf(\\", "md_text": "What does this code print?"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What does this code print?\\n\\``\\`c\\nvoid print(int nb)\\n{\\n    printf(\\", "md_text": "What does this code print?"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What does this code print?\\n\\``\\`c\\nvoid print(int nb)\\n{\\n    printf(\\", "md_text": "What does this code print?"}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What does this code print?\\n\\``\\`c\\nvoid print(int nb)\\n{\\n    printf(\\", "md_text": "What does this code print?"}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What does this code print?\\n\\``\\`c\\nint print(int nb)\\n{\\n    if (nb < 0) \\n    {\\n        return (0);\\n    }\\n    printf(\\", "md_text": "What does this code print?"}]}, {"js_file": "./src/data/project-quizzes/low-level/doubly-linked-lists-quiz.js", "md_file": "./quiz_files/0x17__C_-_Doubly_linked_lists_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 0, MD has 2"}, {"type": "missing_js_question", "question_number": 0, "message": "Question 0 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 1, "message": "Question 1 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/variadic-quiz.js", "md_file": "./quiz_files/0x10__C_-_Variadic_functions_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 3, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/low-level/malloc-quiz.js", "md_file": "./quiz_files/0x0B__C_-_malloc__free_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "How many bytes will this statement allocate?\\n\\``\\`c\\nmalloc(sizeof(unsigned int) * 2)\\n\\``\\`", "md_text": "How many bytes will this statement allocate?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 5, MD has 4"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "It", "md_option": "It’s a new step when I compile with gcc"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": ",\n      ", "md_option": "It’s a program to validate memory allocation"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "s a program to validate memory allocation", "md_option": "It’s a program to test a C program in a specific environment"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["It"], "md_correct": ["It’s a program to validate memory allocation"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "How many bytes will this statement allocate?\\n\\``\\`c\\nmalloc(sizeof(int) * 10)\\n\\``\\`", "md_text": "How many bytes will this statement allocate?"}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "How many bytes will this statement allocate?\\n\\``\\`c\\nmalloc(sizeof(int) * 4)\\n\\``\\`", "md_text": "How many bytes will this statement allocate?"}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "How many bytes will this statement allocate?\\n\\``\\`c\\nmalloc(10)\\n\\``\\`", "md_text": "How many bytes will this statement allocate?"}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "How many bytes will this statement allocate?\\n\\``\\`c\\nmalloc(sizeof(char) * 10)\\n\\``\\`", "md_text": "How many bytes will this statement allocate?"}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "How many bytes will this statement allocate?\\n\\``\\`c\\nmalloc((sizeof(char) * 10) + 1)\\n\\``\\`", "md_text": "How many bytes will this statement allocate?"}]}, {"js_file": "./src/data/project-quizzes/low-level/structures-quiz.js", "md_file": "./quiz_files/0x03__Python_-_Data_Structures__Lists__Tuples_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 0, MD has 10"}, {"type": "missing_js_question", "question_number": 0, "message": "Question 0 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 1, "message": "Question 1 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 2, "message": "Question 2 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 3, "message": "Question 3 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 4, "message": "Question 4 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/sorting-quiz.js", "md_file": "./quiz_files/0x1B__C_-_Sorting_algorithms___Big_O_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 15, <PERSON> has 43"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the time complexity of this function / algorithm?\\n\\``\\`c\\nvoid f(unsigned int n)\\n{\\n    int i;\\n\\n    for (i = 1; i < n; i = i * 2)\\n    {\\n        printf(\\", "md_text": "What is the time complexity of this function / algorithm?"}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "What is the time complexity of this function / algorithm?\\n\\``\\`c\\nvoid f(int n)\\n{\\n     int i;\\n     int j;\\n\\n     for (i = 0; i < n; i++)\\n     {\\n          for (j = i + 1; j < n; j++)\\n          {\\n               printf(\\", "md_text": "What is the time complexity of this function / algorithm?"}, {"type": "question_text_mismatch", "question_number": 8, "message": "Question 8 text differs", "js_text": "What is the time complexity of this function / algorithm?\\n\\``\\`c\\nvoid f(int n)\\n{\\n    printf(\\", "md_text": "What is the time complexity of this function / algorithm?"}, {"type": "question_text_mismatch", "question_number": 12, "message": "Question 12 text differs", "js_text": "What is the time complexity of this function / algorithm?\\n\\``\\`c\\nint <PERSON>(int number)\\n{\\n    if (number <= 1) return number;\\n\\n    return <PERSON><PERSON><PERSON><PERSON>(number - 2) + <PERSON><PERSON><PERSON><PERSON>(number - 1);\\n}\\n\\``\\`", "md_text": "What is the time complexity of this function / algorithm?"}, {"type": "question_text_mismatch", "question_number": 14, "message": "Question 14 text differs", "js_text": "What is the time complexity of this function / algorithm?\\n\\``\\`c\\nvoid f(int n)\\n{\\n    int i;\\n\\n    for (i = 0; i < n; i += 98)\\n    {\\n        printf(\\", "md_text": "What is the time complexity of this function / algorithm?"}, {"type": "missing_js_question", "question_number": 15, "message": "Question 15 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 16, "message": "Question 16 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 17, "message": "Question 17 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 18, "message": "Question 18 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 19, "message": "Question 19 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 20, "message": "Question 20 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 21, "message": "Question 21 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 22, "message": "Question 22 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 23, "message": "Question 23 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 24, "message": "Question 24 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 25, "message": "Question 25 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 26, "message": "Question 26 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 27, "message": "Question 27 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 28, "message": "Question 28 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 29, "message": "Question 29 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 30, "message": "Question 30 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 31, "message": "Question 31 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 32, "message": "Question 32 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 33, "message": "Question 33 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 34, "message": "Question 34 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 35, "message": "Question 35 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 36, "message": "Question 36 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 37, "message": "Question 37 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 38, "message": "Question 38 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 39, "message": "Question 39 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 40, "message": "Question 40 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 41, "message": "Question 41 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 42, "message": "Question 42 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/variables-quiz.js", "md_file": "./quiz_files/0x03__Shell__init_files__variables_and_expansions_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 6, <PERSON> has 4"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "Which of the following are valid if statements in ANSI C and Betty-compliant? (Considering a and b two variables of type int)", "md_text": "Which command should I use to define a new command  for pushing in Github?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 0, MD has 4"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": [], "md_correct": ["alias push=\"git push\""]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the size of the unsigned int data type?", "md_text": "Which command should I use to display a variable?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "1 byte", "md_option": "ls $MYVAR"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "4 bytes", "md_option": "export $MYVAR"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "2 bytes", "md_option": "cd $MYVAR"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "8 bytes", "md_option": "echo $MYVAR"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["4 bytes"], "md_correct": ["echo $MYVAR"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is the size of the float data type?", "md_text": "Which command should I use to display the exit code of the previous command?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "1 byte", "md_option": "echo ?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "4 bytes", "md_option": "echo $?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "2 bytes", "md_option": "echo $EXITCODE"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "8 bytes", "md_option": "echo $CODE"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["4 bytes"], "md_correct": ["echo $?"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the size of the char data type?", "md_text": "What is the variable name who contains the previous working directory path?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "1 byte", "md_option": "OLDPWD"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "4 bytes", "md_option": "OLDDIR"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "2 bytes", "md_option": "PREVPWD"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": "8 bytes", "md_option": "PREVDIR"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["1 byte"], "md_correct": ["OLDPWD"]}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 5, "message": "Question 5 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/low-level/more-pointers-quiz.js", "md_file": "./quiz_files/0x06__C_-_More_pointers__arrays_and_strings_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 0, MD has 7"}, {"type": "missing_js_question", "question_number": 0, "message": "Question 0 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 1, "message": "Question 1 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 2, "message": "Question 2 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 3, "message": "Question 3 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 4, "message": "Question 4 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/bit-manipulation-quiz.js", "md_file": "./quiz_files/0x14__C_-_Bit_manipulation_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 12, <PERSON> has 20"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the result of 0x01 | 0x00?", "md_text": "?"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the result of 0x01 << 1?", "md_text": "?"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is the result of ~ 0x12?", "md_text": "?"}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the result of 0x01 & 0x00?", "md_text": "?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "0x00", "md_option": "0x66"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "0x01", "md_option": "0x68"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "0x02", "md_option": "0x67"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["0x00"], "md_correct": ["0x67"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the result of 0x89 >> 3?", "md_text": "?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "0x11", "md_option": "0x00"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "0x44", "md_option": "0x02"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "0x22", "md_option": "0x01"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["0x11"], "md_correct": ["0x01"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "What is the result of 0x01 ^ 0x01?", "md_text": "What is  in base16?"}, {"type": "options_count_mismatch", "question_number": 5, "message": "Question 5 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "0x00", "md_option": "0x62"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "0x01", "md_option": "0x96"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 2, "message": "Question 5, Option 2 text differs", "js_option": "0x02", "md_option": "0x98"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["0x00"], "md_correct": ["0x62"]}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "What is the result of 0xFF & 0x0F?", "md_text": "?"}, {"type": "options_count_mismatch", "question_number": 6, "message": "Question 6 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 0, "message": "Question 6, Option 0 text differs", "js_option": "0xFF", "md_option": "0x02"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 1, "message": "Question 6, Option 1 text differs", "js_option": "0x0F", "md_option": "0x00"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 2, "message": "Question 6, Option 2 text differs", "js_option": "0xF0", "md_option": "0x01"}, {"type": "correct_answers_mismatch", "question_number": 6, "message": "Question 6 correct answers differ", "js_correct": ["0x0F"], "md_correct": ["0x01"]}, {"type": "question_text_mismatch", "question_number": 7, "message": "Question 7 text differs", "js_text": "What is the result of 0x01 << 4?", "md_text": "?"}, {"type": "options_count_mismatch", "question_number": 7, "message": "Question 7 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 0, "message": "Question 7, Option 0 text differs", "js_option": "0x10", "md_option": "0x00"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 1, "message": "Question 7, Option 1 text differs", "js_option": "0x04", "md_option": "0x02"}, {"type": "option_text_mismatch", "question_number": 7, "option_number": 2, "message": "Question 7, Option 2 text differs", "js_option": "0x08", "md_option": "0x01"}, {"type": "correct_answers_mismatch", "question_number": 7, "message": "Question 7 correct answers differ", "js_correct": ["0x10"], "md_correct": ["0x00"]}, {"type": "question_text_mismatch", "question_number": 8, "message": "Question 8 text differs", "js_text": "What is the result of 0x01 | 0x03?", "md_text": "?"}, {"type": "options_count_mismatch", "question_number": 8, "message": "Question 8 options count differs: JS has 4, MD has 5"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 0, "message": "Question 8, Option 0 text differs", "js_option": "0x00", "md_option": "0x11"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 1, "message": "Question 8, Option 1 text differs", "js_option": "0x01", "md_option": "0x89"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 2, "message": "Question 8, Option 2 text differs", "js_option": "0x02", "md_option": "0x22"}, {"type": "option_text_mismatch", "question_number": 8, "option_number": 3, "message": "Question 8, Option 3 text differs", "js_option": "0x03", "md_option": "0x08"}, {"type": "correct_answers_mismatch", "question_number": 8, "message": "Question 8 correct answers differ", "js_correct": ["0x03"], "md_correct": ["0x11"]}, {"type": "question_text_mismatch", "question_number": 9, "message": "Question 9 text differs", "js_text": "What is the result of ~(0x01 & 0x00)?", "md_text": "What is  in base10?"}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 0, "message": "Question 9, Option 0 text differs", "js_option": "0x00", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 1, "message": "Question 9, Option 1 text differs", "js_option": "0x01", "md_option": "137"}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 2, "message": "Question 9, Option 2 text differs", "js_option": "0xFF", "md_option": "135"}, {"type": "option_text_mismatch", "question_number": 9, "option_number": 3, "message": "Question 9, Option 3 text differs", "js_option": "0xFE", "md_option": "139"}, {"type": "correct_answers_mismatch", "question_number": 9, "message": "Question 9 correct answers differ", "js_correct": ["0xFF"], "md_correct": ["137"]}, {"type": "question_text_mismatch", "question_number": 10, "message": "Question 10 text differs", "js_text": "What is the result of 0x55 ^ 0xAA?", "md_text": "?"}, {"type": "option_text_mismatch", "question_number": 10, "option_number": 0, "message": "Question 10, Option 0 text differs", "js_option": "0x00", "md_option": "0x13"}, {"type": "option_text_mismatch", "question_number": 10, "option_number": 1, "message": "Question 10, Option 1 text differs", "js_option": "0xFF", "md_option": "0x4C"}, {"type": "option_text_mismatch", "question_number": 10, "option_number": 2, "message": "Question 10, Option 2 text differs", "js_option": "0x55", "md_option": "0x26"}, {"type": "option_text_mismatch", "question_number": 10, "option_number": 3, "message": "Question 10, Option 3 text differs", "js_option": "0xAA", "md_option": "0x98"}, {"type": "correct_answers_mismatch", "question_number": 10, "message": "Question 10 correct answers differ", "js_correct": ["0xFF"], "md_correct": ["0x26"]}, {"type": "question_text_mismatch", "question_number": 11, "message": "Question 11 text differs", "js_text": "What is the result of 0x88 >> 2?", "md_text": "?"}, {"type": "options_count_mismatch", "question_number": 11, "message": "Question 11 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 11, "option_number": 0, "message": "Question 11, Option 0 text differs", "js_option": "0x22", "md_option": "0x00"}, {"type": "option_text_mismatch", "question_number": 11, "option_number": 1, "message": "Question 11, Option 1 text differs", "js_option": "0x44", "md_option": "0x02"}, {"type": "option_text_mismatch", "question_number": 11, "option_number": 2, "message": "Question 11, Option 2 text differs", "js_option": "0x11", "md_option": "0x01"}, {"type": "correct_answers_mismatch", "question_number": 11, "message": "Question 11 correct answers differ", "js_correct": ["0x22"], "md_correct": ["0x01"]}, {"type": "missing_js_question", "question_number": 12, "message": "Question 12 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 13, "message": "Question 13 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 14, "message": "Question 14 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 15, "message": "Question 15 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 16, "message": "Question 16 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 17, "message": "Question 17 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 18, "message": "Question 18 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 19, "message": "Question 19 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/linked-lists-quiz.js", "md_file": "./quiz_files/0x17__C_-_Doubly_linked_lists_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 0, MD has 2"}, {"type": "missing_js_question", "question_number": 0, "message": "Question 0 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 1, "message": "Question 1 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/debugging-quiz.js", "md_file": "./quiz_files/0x17__Web_stack_debugging__3_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 4, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/low-level/pointers-quiz.js", "md_file": "./quiz_files/0x06__C_-_More_pointers__arrays_and_strings_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 13, <PERSON> has 7"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the identifier to print an address with printf?", "md_text": "What is wrong with the following code?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "%a", "md_option": "Nothing is wrong"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "%p", "md_option": "The array array is not entirely initialized"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "%d", "md_option": "It is impossible to declare the variable array this way"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "%x", "md_option": "It is not possible to access array[n]"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["%p"], "md_correct": ["It is not possible to access array[n]"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the value of n after the following code is executed?", "md_text": "What is the type of ?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "0", "md_option": "string"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "99", "md_option": "int *"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "98", "md_option": "char *"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["98"], "md_correct": ["char *"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Given int arr[5], what is the equivalent of arr[2]?", "md_text": "What is wrong with the following code?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 3, MD has 4"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "arr + 2", "md_option": "Nothing is wrong"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "*(arr + 2)", "md_option": "The array array is not entirely initialized"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "*arr + 2", "md_option": "It is impossible to declare the variable array this way"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["*(arr + 2)"], "md_correct": ["It is impossible to declare the variable array this way"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the size of a pointer to a char (on a 64-bit architecture)?", "md_text": "What is wrong with the following code?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "1 byte", "md_option": "Nothing is wrong"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "4 bytes", "md_option": "The array array is not entirely initialized"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "2 bytes", "md_option": "It is impossible to declare the variable array this way"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": "8 bytes", "md_option": "It is not possible to access array[n]"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["8 bytes"], "md_correct": ["Nothing is wrong"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the value of n after the following code is executed?", "md_text": "What is/are the difference(s) between the two following variables? (Except their names)"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["98"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "If we have a variable called var of type int, how can we get its address in memory?", "md_text": "Why is it important to reserve enough space for an extra character when declaring/allocating a string?"}, {"type": "options_count_mismatch", "question_number": 5, "message": "Question 5 options count differs: JS has 3, MD has 4"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "*var", "md_option": "In case we need one"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "&var", "md_option": "For the null byte (end of string)"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 2, "message": "Question 5, Option 2 text differs", "js_option": "*(var)", "md_option": "For memory alignment"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["&var"], "md_correct": ["For the null byte (end of string)"]}, {"type": "question_text_mismatch", "question_number": 6, "message": "Question 6 text differs", "js_text": "What is the value of n after the following code is executed?", "md_text": "What happens when one tries to dereference a pointer to NULL?"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 0, "message": "Question 6, Option 0 text differs", "js_option": "0", "md_option": "Nothing"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 1, "message": "Question 6, Option 1 text differs", "js_option": "99", "md_option": "Kernel panic"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 2, "message": "Question 6, Option 2 text differs", "js_option": "98", "md_option": "Segmentation fault"}, {"type": "option_text_mismatch", "question_number": 6, "option_number": 3, "message": "Question 6, Option 3 text differs", "js_option": "402", "md_option": "World War Z"}, {"type": "correct_answers_mismatch", "question_number": 6, "message": "Question 6 correct answers differ", "js_correct": ["402"], "md_correct": ["Segmentation fault"]}, {"type": "missing_md_question", "question_number": 7, "message": "Question 7 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 8, "message": "Question 8 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 9, "message": "Question 9 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 10, "message": "Question 10 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 11, "message": "Question 11 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 12, "message": "Question 12 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/low-level/function-pointers-quiz.js", "md_file": "./quiz_files/0x0F__C_-_Function_pointers_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 2, <PERSON> has 5"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "This is:\\n\\``\\`c\\nvoid (*anjula[])(int, float)\\n\\``\\`", "md_text": "This  is:"}, {"type": "missing_js_question", "question_number": 2, "message": "Question 2 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 3, "message": "Question 3 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 4, "message": "Question 4 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/low-level/hash-tables-quiz.js", "md_file": "./quiz_files/0x1A__C_-_Hash_tables_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 10, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 5, "message": "Question 5 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 6, "message": "Question 6 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 7, "message": "Question 7 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 8, "message": "Question 8 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 9, "message": "Question 9 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/tools/vi.js", "md_file": "./quiz_files/0x03__vi_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the correct way to quit vi?", "md_text": "How do you quit Vi?"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Is vi case sensitive by default?", "md_text": "Vi is included with almost every Linux distribution."}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which key allows you to start inserting text?", "md_text": "How do you enter Insert Mode in Vi?"}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "How do you return to command mode from insert mode?", "md_text": "How do you enter Command Mode in Vi?"}]}, {"js_file": "./src/data/project-quizzes/tools/professional-tech.js", "md_file": "./quiz_files/0x04__Professional_Technologies_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "When answering someone", "md_text": "You get an email and an invitation to a mandatory meeting with a staff member. What should you do?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "private message", "md_option": "Delete the email"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "thread", "md_option": "RSVP affirmatively or contact the staff member to reschedule the meeting."}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "message on the channel", "md_option": "Ignore the invitation"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["thread"], "md_correct": ["RSVP affirmatively or contact the staff member to reschedule the meeting."]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "In Slack, \\`@channel\\` should never be used.", "md_text": "In Slack,  should never be used."}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["False"], "md_correct": ["True"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "You get an email and an invitation to a mandatory meeting with a staff member. What should you do?", "md_text": "Which Slack channel could you use to ask a question about a project?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "RSVP affirmatively or contact the staff member to reschedule the meeting", "md_option": "#general"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "Ignore the invitation", "md_option": "Local cohort channel"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "Delete the email", "md_option": "#random"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["RSVP affirmatively or contact the staff member to reschedule the meeting"], "md_correct": ["Local cohort channel"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "You open and close the same resource multiple times while completing various projects. What should you do to streamline your workflow?", "md_text": "When answering someone’s question in Slack channel, I should use a ____________ to answer them - without alerting all members of the channel and offering your answer to all members too."}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "Never close the tab", "md_option": "message on the channel"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "Save the resource as your homepage when you open your browser", "md_option": "private message"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "Bookmark the resource that you constantly used", "md_option": "thread"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Bookmark the resource that you constantly used"], "md_correct": ["thread"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Which Slack channel could you use to ask a question about a project? Select all valid answers", "md_text": "You open and close the same resource multiple times while completing various projects. What should you do to streamline your workflow?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Local cohort channel", "md_option": "Bookmark the resource that you constantly used"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "#random", "md_option": "Never close the tab"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "#general", "md_option": "Save the resource as your homepage when you open your browser."}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Local cohort channel"], "md_correct": ["Bookmark the resource that you constantly used"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "Google Chrome is the only browser that allows access to ALX", "md_text": "Google Chrome is the only browser that allows access to ALX’s Intranet."}]}, {"js_file": "./src/data/project-quizzes/tools/git.js", "md_file": "./quiz_files/0x03__Git_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "You have the following files in your project directory:\\n\\njulien@ubuntu:/tmp/git_project$ ls\\n0-test  0-test~ #0-test# file1  file2\\n\\nYou", "md_text": "You have the following files in your project directory:"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What command can you use to see what changes have been staged, which haven", "md_text": "What command can you use to see what changes have been staged, which haven’t, and which files aren’t being tracked by Git?"}]}, {"js_file": "./src/data/project-quizzes/tools/emacs.js", "md_file": "./quiz_files/0x02__Emacs_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "In Emacs", "md_text": "In Emacs’ documentation, what does  in a shortcut command stand for?"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "In Emacs, a buffer is an object that a file", "md_text": "In Emacs, a buffer is an object that a file’s text is held in."}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "In Emacs", "md_text": "In Emacs’ documentation, what does  in a shortcut command stand for?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 3, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Alt", "Meta"], "md_correct": []}]}, {"js_file": "./src/data/project-quizzes/zero-day/vi.js", "md_file": "./quiz_files/0x03__vi_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the correct way to quit vi?", "md_text": "How do you quit Vi?"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Is vi case sensitive by default?", "md_text": "Vi is included with almost every Linux distribution."}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which key allows you to start inserting text?", "md_text": "How do you enter Insert Mode in Vi?"}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "How do you return to command mode from insert mode?", "md_text": "How do you enter Command Mode in Vi?"}]}, {"js_file": "./src/data/project-quizzes/zero-day/shell.js", "md_file": "./quiz_files/0x16__C_-_Simple_Shell_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 10, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 5, "message": "Question 5 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 6, "message": "Question 6 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 7, "message": "Question 7 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 8, "message": "Question 8 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 9, "message": "Question 9 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/zero-day/tech.js", "md_file": "./quiz_files/0x04__Professional_Technologies_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "When answering someone", "md_text": "You get an email and an invitation to a mandatory meeting with a staff member. What should you do?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "private message", "md_option": "Delete the email"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "thread", "md_option": "RSVP affirmatively or contact the staff member to reschedule the meeting."}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "message on the channel", "md_option": "Ignore the invitation"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["thread"], "md_correct": ["RSVP affirmatively or contact the staff member to reschedule the meeting."]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "In Slack, \\`@channel\\` should never be used.", "md_text": "In Slack,  should never be used."}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["False"], "md_correct": ["True"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "You get an email and an invitation to a mandatory meeting with a staff member. What should you do?", "md_text": "Which Slack channel could you use to ask a question about a project?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "RSVP affirmatively or contact the staff member to reschedule the meeting", "md_option": "#general"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "Ignore the invitation", "md_option": "Local cohort channel"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "Delete the email", "md_option": "#random"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["RSVP affirmatively or contact the staff member to reschedule the meeting"], "md_correct": ["Local cohort channel"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "You open and close the same resource multiple times while completing various projects. What should you do to streamline your workflow?", "md_text": "When answering someone’s question in Slack channel, I should use a ____________ to answer them - without alerting all members of the channel and offering your answer to all members too."}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "Never close the tab", "md_option": "message on the channel"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "Save the resource as your homepage when you open your browser", "md_option": "private message"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "Bookmark the resource that you constantly used", "md_option": "thread"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Bookmark the resource that you constantly used"], "md_correct": ["thread"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Which Slack channel could you use to ask a question about a project? Select all valid answers", "md_text": "You open and close the same resource multiple times while completing various projects. What should you do to streamline your workflow?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Local cohort channel", "md_option": "Bookmark the resource that you constantly used"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "#random", "md_option": "Never close the tab"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "#general", "md_option": "Save the resource as your homepage when you open your browser."}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Local cohort channel"], "md_correct": ["Bookmark the resource that you constantly used"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "Google Chrome is the only browser that allows access to ALX", "md_text": "Google Chrome is the only browser that allows access to ALX’s Intranet."}]}, {"js_file": "./src/data/project-quizzes/zero-day/git.js", "md_file": "./quiz_files/0x03__Git_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "You have the following files in your project directory:\\n\\njulien@ubuntu:/tmp/git_project$ ls\\n0-test  0-test~ #0-test# file1  file2\\n\\nYou", "md_text": "You have the following files in your project directory:"}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What command can you use to see what changes have been staged, which haven", "md_text": "What command can you use to see what changes have been staged, which haven’t, and which files aren’t being tracked by Git?"}]}, {"js_file": "./src/data/project-quizzes/zero-day/emacs.js", "md_file": "./quiz_files/0x02__Emacs_quiz.md", "discrepancies": [{"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "In Emacs", "md_text": "In Emacs’ documentation, what does  in a shortcut command stand for?"}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "In Emacs, a buffer is an object that a file", "md_text": "In Emacs, a buffer is an object that a file’s text is held in."}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "In Emacs", "md_text": "In Emacs’ documentation, what does  in a shortcut command stand for?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 3, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Alt", "Meta"], "md_correct": []}]}, {"js_file": "./src/data/project-quizzes/higher-level/airbnb/web-dynamic-quiz.js", "md_file": "./quiz_files/0x06__AirBnB_clone_-_Web_dynamic_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/airbnb/web-static-quiz.js", "md_file": "./quiz_files/0x01__AirBnB_clone_-_Web_static_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, <PERSON> has 24"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the purpose of semantic HTML?", "md_text": "Is the following HTML markup valid?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "To provide meaning to web content", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "To style web pages", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["To provide meaning to web content"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is a CSS selector?", "md_text": "In the following code, is the text  red?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "A pattern that matches HTML elements", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "A JavaScript function", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["A pattern that matches HTML elements"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is Flexbox used for?", "md_text": "Is following CSS syntax valid?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "Creating flexible page layouts", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "Adding images to a page", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["Creating flexible page layouts"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What does the ", "md_text": "Is following CSS syntax valid?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "Positions element relative to its normal position", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "Makes element invisible", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Positions element relative to its normal position"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the box model in CSS?", "md_text": "Is following CSS syntax valid?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Content, padding, border, and margin", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Only content and border", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Content, padding, border, and margin"], "md_correct": ["Yes"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 10, "message": "Question 10 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 11, "message": "Question 11 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 12, "message": "Question 12 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 13, "message": "Question 13 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 14, "message": "Question 14 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 15, "message": "Question 15 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 16, "message": "Question 16 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 17, "message": "Question 17 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 18, "message": "Question 18 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 19, "message": "Question 19 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 20, "message": "Question 20 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 21, "message": "Question 21 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 22, "message": "Question 22 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 23, "message": "Question 23 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/airbnb/web-framework-quiz.js", "md_file": "./quiz_files/0x04__AirBnB_clone_-_Web_framework_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/airbnb/deploy-static-quiz.js", "md_file": "./quiz_files/0x03__AirBnB_clone_-_Deploy_static_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 6"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is <PERSON><PERSON><PERSON>?", "md_text": "What is the Fabric command to execute a shell command locally?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "A Python tool for deployment automation", "md_option": "put"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "A CSS framework", "md_option": "run"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "A JavaScript library", "md_option": "get"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "A database management system", "md_option": "local"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["A Python tool for deployment automation"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Which web server is commonly used to serve static files?", "md_text": "What is the default name of a Fabric file?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "<PERSON><PERSON><PERSON>", "md_option": "Dockerfile"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "MySQL", "md_option": "Fabric.py"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "Python", "md_option": "fabfile.py"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "Git", "md_option": "Fabricfile"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["<PERSON><PERSON><PERSON>"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What protocol is commonly used to transfer files to a server?", "md_text": "What is the Fabric command to upload a file (from local to remote)?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "SFTP", "md_option": "get"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "HTTP", "md_option": "run"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "SMTP", "md_option": "put"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "IRC", "md_option": "local"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["SFTP"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the purpose of the sites-available directory in Nginx?", "md_text": "What is the Fabric command to download a file (from remote to local)?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "To store server configuration files", "md_option": "get"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "To store web pages", "md_option": "run"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "To store log files", "md_option": "put"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": "To store user data", "md_option": "local"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["To store server configuration files"], "md_correct": []}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the purpose of an A record in DNS?", "md_text": "What is the Fabric command for asking information to the user?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Maps a domain name to an IP address", "md_option": "put"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Maps a domain to another domain", "md_option": "ask"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "Stores email server information", "md_option": "prompt"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 3, "message": "Question 4, Option 3 text differs", "js_option": "Defines server aliases", "md_option": "local"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Maps a domain name to an IP address"], "md_correct": []}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/airbnb/mysql-quiz.js", "md_file": "./quiz_files/0x14__MySQL_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/airbnb/console-quiz.js", "md_file": "./quiz_files/0x00__AirBnB_clone_-_The_console_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/javascript/jquery-quiz.js", "md_file": "./quiz_files/0x15__JavaScript_-_Web_jQuery_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 14"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is j<PERSON><PERSON>y?", "md_text": "In the following code snippet, does the selector called  access the HTML tag <header>:"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "A JavaScript library designed to simplify DOM manipulation", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "A programming language", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["A JavaScript library designed to simplify DOM manipulation"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Which symbol is used to select elements in jQuery?", "md_text": "In the following code snippet, does the selector called  access the HTML tag <header>:"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "$", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "#", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["$"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is the correct syntax for document ready event in jQuery?", "md_text": "In the following code snippet, does the selector called  access the HTML tag <header>:"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "$(document).ready(function(){})", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "document.onready(function(){})", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["$(document).ready(function(){})"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "Which jQuery method is used to add content at the end of an element?", "md_text": "In the following code snippet, does the selector called  access the HTML tag <header>:"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "append()", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "add()", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["append()"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the purpose of $.ajax() in jQuery?", "md_text": "How many HTML tags are present in the following HTML code?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "To perform asynchronous HTTP requests", "md_option": "4"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "To animate elements", "md_option": "6"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "To create HTML elements", "md_option": "5"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 3, "message": "Question 4, Option 3 text differs", "js_option": "To handle events", "md_option": "7"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["To perform asynchronous HTTP requests"], "md_correct": ["6"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 10, "message": "Question 10 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 11, "message": "Question 11 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 12, "message": "Question 12 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 13, "message": "Question 13 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/javascript/warmup-quiz.js", "md_file": "./quiz_files/0x12__JavaScript_-_Warm_up_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 6"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is JavaScript?", "md_text": "Does Javascript have  as a native datatype?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "A high-level, interpreted programming language", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "A database management system", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["A high-level, interpreted programming language"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Which keyword is used to declare a variable that can be reassigned?", "md_text": "Does Javascript have  as a native datatype?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "let", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "const", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["let"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "How do you declare a function in JavaScript?", "md_text": "Does Javascript have  as a native datatype?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "function myFunction()", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "def myFunction()", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["function myFunction()"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "Which is NOT a primitive data type in JavaScript?", "md_text": "Does Javascript have  as a native datatype?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "array", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "string", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["array"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the correct syntax for an if statement in JavaScript?", "md_text": "What does  mean? (please check all true answers)"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["if (condition) { }"], "md_correct": []}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/javascript/web-scraping-quiz.js", "md_file": "./quiz_files/0x14__JavaScript_-_Web_scraping_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/javascript/objects-quiz.js", "md_file": "./quiz_files/0x13__JavaScript_-_Objects__Scopes_and_Closures_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, <PERSON> has 8"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is an object in JavaScript?", "md_text": "What is the output of this code?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "A collection of related data and/or functionality", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "A primitive data type", "md_option": "2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "A function", "md_option": "12"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "A loop construct", "md_option": "89"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["A collection of related data and/or functionality"], "md_correct": ["89"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "How do you define a class in JavaScript?", "md_text": "What is the output of this code?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "Using the class keyword", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "Using the function keyword", "md_option": "2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "Using the object keyword", "md_option": "12"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["Using the class keyword"], "md_correct": ["12"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is lexical scope?", "md_text": "What is the output of this code?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "The ability of inner functions to access variables in their outer scope", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "The global scope of a program", "md_option": "2"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "The scope within a function", "md_option": "12"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["The ability of inner functions to access variables in their outer scope"], "md_correct": ["12"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is a closure in JavaScript?", "md_text": "What is the output of this code?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "A function that has access to variables in its outer scope", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "A way to close a function", "md_option": "2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "A method to end a program", "md_option": "12"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["A function that has access to variables in its outer scope"], "md_correct": ["12"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "How do you implement inheritance in JavaScript?", "md_text": "What is the output of this code?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Using the extends keyword", "md_option": "3, 4"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Using the inherit keyword", "md_option": "3, 7"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "Using the implements keyword", "md_option": "4, 7"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 3, "message": "Question 4, Option 3 text differs", "js_option": "Using the include keyword", "md_option": "4, 3"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Using the extends keyword"], "md_correct": ["4, 7"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/test-driven-quiz.js", "md_file": "./quiz_files/0x07__Python_-_Test-driven_development_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 6, <PERSON> has 7"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the Python module used for unit testing?", "md_text": "Is this a standardized way to comment a function in Python?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "pytest", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "unittest", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["unittest"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "How do you write a doctest in Python?", "md_text": "Is this a standardized way to comment a function in Python?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 7, MD has 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "// Test: >>> function()", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "/* Test: >>> function() */", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["\\"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What should a test case verify?", "md_text": "Is this a standardized way to comment a function in Python?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "Only successful scenarios", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "Only error scenarios", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["Both successful and error scenarios"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the first step in Test-Driven Development?", "md_text": "Is this a standardized way to comment a function in Python?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "Write the implementation", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "Write documentation", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Write failing tests"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "How do you run all tests in the current directory using unittest?", "md_text": "Is this module correctly commented?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "unittest run", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "python -m unittest", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["python -m unittest"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "Which assertion should be used to verify that two values are equal?", "md_text": "Is this module correctly commented?"}, {"type": "options_count_mismatch", "question_number": 5, "message": "Question 5 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "assert_equal()", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "assertEqual()", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["assertEqual()"], "md_correct": ["Yes"]}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/if-else-loops-functions-quiz.js", "md_file": "./quiz_files/0x01__Python_-_if_else__loops__functions_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 9"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "Which keyword is used for a conditional statement in Python?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "if", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "when", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["if"], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is the correct syntax for a ", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "for (i=0; i<n; i++)", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "for i in range(n)", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["for i in range(n)"], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "How do you define a function in Python?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "function myFunction():", "md_option": "2 3 4 5 6 7 8 9 10"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "def myFunction():", "md_option": "4 6 8 10 12 14 16 18"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "define myFunction():", "md_option": "2 3 4 5 6 7 8 9"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "func myFunction():", "md_option": "2 4 6 8"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["def myFunction():"], "md_correct": ["2 4 6 8"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is the modulo operator in Python?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "%", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "mod", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["%"], "md_correct": ["School"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Which is the correct way to write ", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "&&", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "and", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["and"], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/everything-is-object-quiz.js", "md_file": "./quiz_files/0x09__Python_-_Everything_is_object_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 6, <PERSON> has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 5, "message": "Question 5 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/data-structures-quiz.js", "md_file": "./quiz_files/0x03__Python_-_Data_Structures__Lists__Tuples_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 10"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "How do you create an empty list in Python?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 1, MD has 4"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "list()", "md_option": "2"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["Both list() and []"], "md_correct": ["4"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "Which is a key difference between lists and tuples in Python?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 5"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "Tuples are immutable, lists are mutable", "md_option": "[1, 2]"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "Tuples can only store strings", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "Lists can only store numbers", "md_option": "[1]"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "Tuples are faster to sort", "md_option": "[1, 2, 3, 4]"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["Tuples are immutable, lists are mutable"], "md_correct": ["1"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which method adds an element to the end of a list?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 5"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "append()", "md_option": "a"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "add()", "md_option": "[1]"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "insert()", "md_option": "[1, 2, 3, 4]"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "extend()", "md_option": "b"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["append()"], "md_correct": ["[1, 2, 10, 4]"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What does list[1:3] do?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "Returns elements at index 1 and 3", "md_option": "-3"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "Returns elements from index 1 to 2", "md_option": "2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "Returns elements from index 1 to 3", "md_option": "[4, 3]"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Returns elements from index 1 to 2"], "md_correct": ["2"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is the correct syntax for list comprehension?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 0, MD has 5"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["[x for x in range(10)]"], "md_correct": ["[1, 2, 10, 4]"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/more-data-structures-quiz.js", "md_file": "./quiz_files/0x04__Python_-_More_Data_Structures__Set__Dictionary_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 12"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is a set in Python?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "An unordered collection with no duplicate elements", "md_option": "‘age’"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "An ordered collection with duplicate elements", "md_option": "0"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "A key-value pair collection", "md_option": "Nothing"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "A sequential collection of elements", "md_option": "89"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["An unordered collection with no duplicate elements"], "md_correct": ["0"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "How do you create an empty dictionary?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "dict()", "md_option": "0 1 2 3"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "{}", "md_option": "1 3 4 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "Both dict() and {}", "md_option": "1 2 3 4"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "new Dictionary()", "md_option": "1 3 4 2 0"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["Both dict() and {}"], "md_correct": ["1 3 4 2"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "How do you access a value in a dictionary?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 5"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "Using square brackets with the key", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "Using the get() method", "md_option": "id"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "Both are correct", "md_option": "a[‘id’]"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "Using index numbers", "md_option": "<PERSON>"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["Both are correct"], "md_correct": ["89"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is a lambda function in Python?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "An anonymous function", "md_option": "1 2 3"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "A named function", "md_option": "1 2 3 4"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "A built-in function", "md_option": "0 1 2 3"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["An anonymous function"], "md_correct": ["1 2 3"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What does the map() function do?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 5"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Applies a function to every item in an iterable", "md_option": "12"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Creates a dictionary from two lists", "md_option": "‘age’"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "Filters out elements from a list", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 3, "message": "Question 4, Option 3 text differs", "js_option": "Sorts elements in a list", "md_option": "Nothing"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Applies a function to every item in an iterable"], "md_correct": ["Nothing"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 10, "message": "Question 10 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 11, "message": "Question 11 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/more-classes-quiz.js", "md_file": "./quiz_files/0x08__Python_-_More_Classes_and_Objects_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 6, <PERSON> has 14"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What decorator is used to define a class method in Python?", "md_text": "What is ?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "@classmethod", "md_option": "Instance method that removes the last character of an instance"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "@staticmethod", "md_option": "Instance method called when an instance is deleted"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "@method", "md_option": "Instance method that prints the memory address of an instance"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["@classmethod"], "md_correct": ["Instance method called when an instance is deleted"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "When should you use a static method?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "When the method needs to modify the class state", "md_option": "None"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "When the method needs instance attributes", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "When the method doesn", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": ",\n      ", "md_option": "98"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["When the method doesn"], "md_correct": ["89"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which special method is called when str() is used on an object?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "__repr__", "md_option": "None"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "__str__", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "__print__", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "__string__", "md_option": "98"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["__str__"], "md_correct": ["98"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is a class variable?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "A variable defined inside a method", "md_option": "None"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "A variable defined inside the constructor", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "A variable shared by all instances of a class", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": "A variable unique to each instance", "md_option": "98"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["A variable shared by all instances of a class"], "md_correct": ["89"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Which method is called when an object is being destroyed?", "md_text": "What is ?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "__init__", "md_option": "A class attribute"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "__del__", "md_option": "The instance method called when a new object is created"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "__exit__", "md_option": "A class method"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 3, "message": "Question 4, Option 3 text differs", "js_option": "__destroy__", "md_option": "The instance method called when a class is called for the first time"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["__del__"], "md_correct": ["The instance method called when a new object is created"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "What is the main purpose of using properties in Python?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "To make attributes public", "md_option": "None"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "To prevent attribute access", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 2, "message": "Question 5, Option 2 text differs", "js_option": "To control attribute access and add validation", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 3, "message": "Question 5, Option 3 text differs", "js_option": "To improve performance", "md_option": "98"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["To control attribute access and add validation"], "md_correct": ["98"]}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 10, "message": "Question 10 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 11, "message": "Question 11 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 12, "message": "Question 12 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 13, "message": "Question 13 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/input-output-quiz.js", "md_file": "./quiz_files/0x0B__Python_-_Input_Output_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 6, <PERSON> has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 5, "message": "Question 5 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/if-else-loops-quiz.js", "md_file": "./quiz_files/0x01__Python_-_if_else__loops__functions_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 9"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the correct way to check if a number is positive in Python?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "if (number > 0): print(", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": ")", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["if number > 0: print("], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "How do you write a program that prints the ASCII alphabet in lowercase using only one loop and one print function?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 7, MD has 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "print(", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": ")", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["for i in range(97, 123): print(chr(i), end="], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which of the following is the correct way to define a function that checks if a character is lowercase?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 7, MD has 4"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "def islower(c): return c >= ", "md_option": "2 3 4 5 6 7 8 9 10"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": " and c <= ", "md_option": "4 6 8 10 12 14 16 18"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "def islower(c): return ord(c) >= 97 and ord(c) <= 122", "md_option": "2 4 6 8"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["def islower(c): return ord(c) >= 97 and ord(c) <= 122"], "md_correct": ["2 4 6 8"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "How do you get the last digit of a number in Python?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 1, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "number % 10", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["number % 10"], "md_correct": ["School"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "In a FizzBuzz implementation, what should be printed for the number 15?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Fizz", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Buzz", "md_option": "School"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["FizzBuzz"], "md_correct": ["<PERSON><PERSON><PERSON>"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/network-1-quiz.js", "md_file": "./quiz_files/0x11__Python_-_Network__1_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/inheritance-quiz.js", "md_file": "./quiz_files/0x0A__Python_-_Inheritance_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 6, <PERSON> has 10"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is the syntax for a class to inherit from another class in Python?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "class Child extends Parent:", "md_option": "4"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "class Child inherits Parent:", "md_option": "5"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "class Child(Parent):", "md_option": "3"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "class Child: Parent", "md_option": "None"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["class Child(Parent):"], "md_correct": ["4"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "How does Python handle multiple inheritance?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "It", "md_option": "None"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": ",\n      ", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": ",\n      ", "md_option": "0"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["Through the method resolution order (MRO)"], "md_correct": ["1"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which function is used to check if an object is an instance of a class?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "isinstance()", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "issubclass()", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "hasattr()", "md_option": "90"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["isinstance()"], "md_correct": ["89"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "How do you call a parent class", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "parent.method()", "md_option": "None"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "super().method()", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "self.parent.method()", "md_option": "0"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": "this.super.method()", "md_option": "2"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["super().method()"], "md_correct": ["1"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What happens when a child class defines a method with the same name as in the parent class?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 5, MD has 3"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "A syntax error occurs", "md_option": "99"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Both methods are available", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "The child", "md_option": "100"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["The child"], "md_correct": ["100"]}, {"type": "question_text_mismatch", "question_number": 5, "message": "Question 5 text differs", "js_text": "Which class is the base class for all classes in Python?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 0, "message": "Question 5, Option 0 text differs", "js_option": "base", "md_option": "None"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 1, "message": "Question 5, Option 1 text differs", "js_option": "object", "md_option": "4"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 2, "message": "Question 5, Option 2 text differs", "js_option": "class", "md_option": "3"}, {"type": "option_text_mismatch", "question_number": 5, "option_number": 3, "message": "Question 5, Option 3 text differs", "js_option": "Parent", "md_option": "2"}, {"type": "correct_answers_mismatch", "question_number": 5, "message": "Question 5 correct answers differ", "js_correct": ["object"], "md_correct": ["3"]}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/exceptions-quiz.js", "md_file": "./quiz_files/0x05__Python_-_Exceptions_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: JS has 5, MD has 0"}, {"type": "missing_md_question", "question_number": 0, "message": "Question 0 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 1, "message": "Question 1 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 2, "message": "Question 2 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 3, "message": "Question 3 exists in JS but not in MD"}, {"type": "missing_md_question", "question_number": 4, "message": "Question 4 exists in JS but not in MD"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/import-modules-quiz.js", "md_file": "./quiz_files/0x02__Python_-_import___modules_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 6"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "How do you import an entire module in Python?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "import module", "md_option": "Counter: 12"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "include module", "md_option": "Counter: 101"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "using module", "md_option": "Counter: 89"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["import module"], "md_correct": ["Counter: 12"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "How do you import a specific function from a module?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "from module import function", "md_option": "“In my function”"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "import function from module", "md_option": "function my_function at …"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "using function from module", "md_option": "In my function"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "require function from module", "md_option": "Nothing"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["from module import function"], "md_correct": ["In my function"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "How do you create an alias for an imported module?", "md_text": "What do these lines print?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "import module as alias", "md_option": "Counter: counter"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "import module with alias", "md_option": "Counter: 12"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "module as alias", "md_option": "Counter: c"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["import module as alias"], "md_correct": ["Counter: 12"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "Which of these is a built-in Python module?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "math", "md_option": "“In my function”"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "calculator", "md_option": "function my_function at …"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "numbers", "md_option": "In my function"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": "compute", "md_option": "Nothing"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["math"], "md_correct": ["function my_function at …"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Which directory does Python look in first when importing modules?", "md_text": "What do these lines print?"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "Current directory", "md_option": "1"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Python installation directory", "md_option": "90"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "System directory", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 3, "message": "Question 4, Option 3 text differs", "js_option": "Home directory", "md_option": "891"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["Current directory"], "md_correct": ["90"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/test-driven-development-quiz.js", "md_file": "./quiz_files/0x07__Python_-_Test-driven_development_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 7"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is Test-Driven Development (TDD)?", "md_text": "Is this a standardized way to comment a function in Python?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "Writing tests before writing the code", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "Writing code before writing tests", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["Writing tests before writing the code"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is doctest in Python?", "md_text": "Is this a standardized way to comment a function in Python?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "A module that lets you test code examples in docstrings", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "A tool for formatting documentation", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["A module that lets you test code examples in docstrings"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is the base class for test cases in unittest?", "md_text": "Is this a standardized way to comment a function in Python?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "TestCase", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "UnitTest", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["TestCase"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "Which method naming convention is used for test methods in unittest?", "md_text": "Is this a standardized way to comment a function in Python?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "test_*", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "check_*", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["test_*"], "md_correct": ["No"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Which assertion is used to verify that two values are equal?", "md_text": "Is this module correctly commented?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "assertEqual()", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "assertSame()", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["assertEqual()"], "md_correct": ["No"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/network-0-quiz.js", "md_file": "./quiz_files/0x10__Python_-_Network__0_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 36"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What is cURL used for?", "md_text": "What is the name of the HTTP response header used to define the formatting of the body? (This header gives details to the client on how to interpret the data received.)"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "Making HTTP requests from the command line", "md_option": "Type"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "Writing Python code", "md_option": "Format"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "Managing databases", "md_option": "Content-Format"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "Creating web servers", "md_option": "Content-Type"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["Making HTTP requests from the command line"], "md_correct": ["Content-Type"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What HTTP method is typically used to retrieve data?", "md_text": "What will be the port number requested by this URL?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "GET", "md_option": "8080"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "POST", "md_option": "443"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "PUT", "md_option": "80"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "DELETE", "md_option": "22"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["GET"], "md_correct": ["80"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which header indicates the type of data being sent?", "md_text": "What is the name of the HTTP request header that defines the size (in bytes) of the message body?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "Content-Type", "md_option": "Content-Length"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "Content-Length", "md_option": "Content-Size"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "Authorization", "md_option": "Length"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "Accept", "md_option": "Size"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["Content-Type"], "md_correct": ["Content-Length"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What does a 200 status code indicate?", "md_text": "What is the name of the HTTP response header used to define the status code of the response?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "Success", "md_option": "Status"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "Client error", "md_option": "Code"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "Server error", "md_option": "Status-Code"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": "Redirect", "md_option": "Http-Status"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Success"], "md_correct": ["Status"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Which part of an HTTP request contains the query parameters?", "md_text": "In the following URL, what’s the name of the parameter in the query string?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "URL", "md_option": "batch"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "Body", "md_option": "89"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 2, "message": "Question 4, Option 2 text differs", "js_option": "Headers", "md_option": "apply"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["URL"], "md_correct": ["batch"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 10, "message": "Question 10 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 11, "message": "Question 11 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 12, "message": "Question 12 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 13, "message": "Question 13 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 14, "message": "Question 14 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 15, "message": "Question 15 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 16, "message": "Question 16 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 17, "message": "Question 17 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 18, "message": "Question 18 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 19, "message": "Question 19 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 20, "message": "Question 20 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 21, "message": "Question 21 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 22, "message": "Question 22 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 23, "message": "Question 23 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 24, "message": "Question 24 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 25, "message": "Question 25 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 26, "message": "Question 26 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 27, "message": "Question 27 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 28, "message": "Question 28 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 29, "message": "Question 29 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 30, "message": "Question 30 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 31, "message": "Question 31 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 32, "message": "Question 32 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 33, "message": "Question 33 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 34, "message": "Question 34 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 35, "message": "Question 35 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/python/hello-world-quiz.js", "md_file": "./quiz_files/0x00__Python_-_Hello__World_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 3, <PERSON> has 10"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What function is used to print output in Python 3?", "md_text": "What does this command line print?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "print()", "md_option": "“98 Battery street, San Francisco”"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "write()", "md_option": "98 Battery street, San Francisco"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "echo()", "md_option": "8 Battery street, San"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "display()", "md_option": "San Francisco Battery street, 98"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["print()"], "md_correct": ["98 Battery street, San Francisco"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "How do you use string formatting in Python 3?", "md_text": "Who created Python?"}, {"type": "options_count_mismatch", "question_number": 1, "message": "Question 1 options count differs: JS has 4, MD has 3"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "Using .format() method", "md_option": "<PERSON>"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "Using % operator", "md_option": "<PERSON>"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "Using f-strings", "md_option": "<PERSON><PERSON><PERSON>"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["All of the above"], "md_correct": ["<PERSON>"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is the correct file extension for Python files?", "md_text": "What does this command line print?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 5"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": ".py", "md_option": "si"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": ".python", "md_option": "on"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": ".pt", "md_option": "Python"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": ".p", "md_option": "is"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": [".py"], "md_correct": ["is"]}, {"type": "missing_js_question", "question_number": 3, "message": "Question 3 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 4, "message": "Question 4 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 9, "message": "Question 9 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/sql/more-queries-quiz.js", "md_file": "./quiz_files/0x0E__SQL_-_More_queries_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 7"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "Which command is used to grant privileges to a user?", "md_text": "Is it possible to give only read access to multiple databases and tables to a user?"}, {"type": "options_count_mismatch", "question_number": 0, "message": "Question 0 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "GRANT", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "GIVE", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["GRANT"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What type of JOIN returns all records when there is a match in either left or right table?", "md_text": "What DCL means?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "FULL OUTER JOIN", "md_option": "Document Control Language"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "INNER JOIN", "md_option": "Data Concept Language"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "LEFT JOIN", "md_option": "Data Control Language"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "RIGHT JOIN", "md_option": "Document Control Line"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["FULL OUTER JOIN"], "md_correct": ["Data Control Language"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "What is a FOREIGN KEY?", "md_text": "Is it possible to give only read access to a table to a user?"}, {"type": "options_count_mismatch", "question_number": 2, "message": "Question 2 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "A key that references a PRIMARY KEY in another table", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "The main key of a table", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["A key that references a PRIMARY KEY in another table"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What does UNION ALL do?", "md_text": "Is it possible to give only read access to a database to a user?"}, {"type": "options_count_mismatch", "question_number": 3, "message": "Question 3 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "Combines results including duplicates", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "Removes all duplicates", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["Combines results including duplicates"], "md_correct": ["Yes"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "What is a VIEW in SQL?", "md_text": "Is it possible to give only insert access to a table to a user?"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 2"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 0, "message": "Question 4, Option 0 text differs", "js_option": "A virtual table based on a SELECT query", "md_option": "Yes"}, {"type": "option_text_mismatch", "question_number": 4, "option_number": 1, "message": "Question 4, Option 1 text differs", "js_option": "A physical table in the database", "md_option": "No"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["A virtual table based on a SELECT query"], "md_correct": ["Yes"]}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}]}, {"js_file": "./src/data/project-quizzes/higher-level/sql/introduction-quiz.js", "md_file": "./quiz_files/0x0D__SQL_-_Introduction_quiz.md", "discrepancies": [{"type": "question_count_mismatch", "message": "Question count mismatch: J<PERSON> has 5, <PERSON> has 9"}, {"type": "question_text_mismatch", "question_number": 0, "message": "Question 0 text differs", "js_text": "What does SQL stand for?", "md_text": "What does DML stand for?"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 0, "message": "Question 0, Option 0 text differs", "js_option": "Structured Query Language", "md_option": "Database Manipulation Language"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 1, "message": "Question 0, Option 1 text differs", "js_option": "Simple Query Language", "md_option": "Data Manipulation Language"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 2, "message": "Question 0, Option 2 text differs", "js_option": "System Query Language", "md_option": "Document Manipulation Language"}, {"type": "option_text_mismatch", "question_number": 0, "option_number": 3, "message": "Question 0, Option 3 text differs", "js_option": "Sequential Query Language", "md_option": "Document Model Language"}, {"type": "correct_answers_mismatch", "question_number": 0, "message": "Question 0 correct answers differ", "js_correct": ["Structured Query Language"], "md_correct": ["Data Manipulation Language"]}, {"type": "question_text_mismatch", "question_number": 1, "message": "Question 1 text differs", "js_text": "What is a database?", "md_text": "What does SQL stand for?"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 0, "message": "Question 1, Option 0 text differs", "js_option": "An organized collection of data", "md_option": "Sequences of Query Logic"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 1, "message": "Question 1, Option 1 text differs", "js_option": "A programming language", "md_option": "Solid Query Language"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 2, "message": "Question 1, Option 2 text differs", "js_option": "A web server", "md_option": "Structured Query Language"}, {"type": "option_text_mismatch", "question_number": 1, "option_number": 3, "message": "Question 1, Option 3 text differs", "js_option": "A network protocol", "md_option": "Structured Question Language"}, {"type": "correct_answers_mismatch", "question_number": 1, "message": "Question 1 correct answers differ", "js_correct": ["An organized collection of data"], "md_correct": ["Structured Query Language"]}, {"type": "question_text_mismatch", "question_number": 2, "message": "Question 2 text differs", "js_text": "Which SQL command is used to create a new database?", "md_text": "How do you list all  records with age > 21 in this table?"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 0, "message": "Question 2, Option 0 text differs", "js_option": "CREATE DATABASE", "md_option": "SELECT * FROM users WHERE age < 21;"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 1, "message": "Question 2, Option 1 text differs", "js_option": "NEW DATABASE", "md_option": "SELECT * FROM users WHERE age > 21;"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 2, "message": "Question 2, Option 2 text differs", "js_option": "MAKE DATABASE", "md_option": "SELECT * FROM users WHERE age IS UP TO 21;"}, {"type": "option_text_mismatch", "question_number": 2, "option_number": 3, "message": "Question 2, Option 3 text differs", "js_option": "BUILD DATABASE", "md_option": "SELECT * FROM users WHERE age BETWEEN 21 AND 89;"}, {"type": "correct_answers_mismatch", "question_number": 2, "message": "Question 2 correct answers differ", "js_correct": ["CREATE DATABASE"], "md_correct": ["SELECT * FROM users WHERE age > 21;"]}, {"type": "question_text_mismatch", "question_number": 3, "message": "Question 3 text differs", "js_text": "What is a table in SQL?", "md_text": "What does DDL stand for?"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 0, "message": "Question 3, Option 0 text differs", "js_option": "A collection of related data entries organized in columns and rows", "md_option": "Data Definition Language"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 1, "message": "Question 3, Option 1 text differs", "js_option": "A file containing SQL commands", "md_option": "Data Document Language"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 2, "message": "Question 3, Option 2 text differs", "js_option": "A connection between databases", "md_option": "Database Definition Language"}, {"type": "option_text_mismatch", "question_number": 3, "option_number": 3, "message": "Question 3, Option 3 text differs", "js_option": "A type of query", "md_option": "Document Data Language"}, {"type": "correct_answers_mismatch", "question_number": 3, "message": "Question 3 correct answers differ", "js_correct": ["A collection of related data entries organized in columns and rows"], "md_correct": ["Data Definition Language"]}, {"type": "question_text_mismatch", "question_number": 4, "message": "Question 4 text differs", "js_text": "Which is NOT a common SQL data type?", "md_text": "What is a relational database? (please select all correct answers)"}, {"type": "options_count_mismatch", "question_number": 4, "message": "Question 4 options count differs: JS has 4, MD has 0"}, {"type": "correct_answers_mismatch", "question_number": 4, "message": "Question 4 correct answers differ", "js_correct": ["POINTER"], "md_correct": []}, {"type": "missing_js_question", "question_number": 5, "message": "Question 5 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 6, "message": "Question 6 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 7, "message": "Question 7 exists in MD but not in JS"}, {"type": "missing_js_question", "question_number": 8, "message": "Question 8 exists in MD but not in JS"}]}], "unmatched_js": ["./src/data/project-quizzes/onboarding/owning-learning.js", "./src/data/project-quizzes/system-engineering-devops/shell-loops-quiz.js", "./src/data/project-quizzes/system-engineering-devops/config-management-quiz.js", "./src/data/project-quizzes/system-engineering-devops/shell-variables-quiz.js", "./src/data/project-quizzes/system-engineering-devops/shell-processes-quiz.js", "./src/data/project-quizzes/system-engineering-devops/shell-regex-quiz.js", "./src/data/project-quizzes/low-level/more-linked-lists-quiz.js", "./src/data/project-quizzes/tools/vi-advanced.js", "./src/data/project-quizzes/tools/vagrant.js", "./src/data/project-quizzes/tools/navigation-basics.js", "./src/data/project-quizzes/tools/git-advanced.js", "./src/data/project-quizzes/tools/emacs-advanced.js", "./src/data/project-quizzes/higher-level/airbnb/rest-api-quiz.js", "./src/data/project-quizzes/higher-level/python/orm-quiz.js", "./src/data/project-quizzes/higher-level/python/almost-circle-quiz.js", "./src/data/project-quizzes/higher-level/python/classes-objects-quiz.js"], "unmatched_md": ["./quiz_files/0x04__Loops__conditions_and_parsing_quiz.md", "./quiz_files/Command_line_for_the_win_quiz.md", "./quiz_files/Owning_your_Learning_quiz.md", "./quiz_files/0x06__Python_-_Classes_and_Objects_quiz.md", "./quiz_files/0x00__Fix_my_code_quiz.md", "./quiz_files/_quiz.md", "./quiz_files/Your_network_is_your_net_worth_quiz.md", "./quiz_files/Build_your_portfolio_project__Week_3___Presentation_quiz.md", "./quiz_files/0x0A__Configuration_management_quiz.md", "./quiz_files/0x18__C_-_Dynamic_libraries_quiz.md", "./quiz_files/0x05__Processes_and_signals_quiz.md", "./quiz_files/0x02__C_-_Functions__nested_loops_quiz.md", "./quiz_files/0x06__Regular_expression_quiz.md", "./quiz_files/Build_your_portfolio_project__Week_1___Making_Progress_quiz.md", "./quiz_files/RSA_Factoring_Challenge_quiz.md", "./quiz_files/0x1E__C_-_Search_Algorithms_quiz.md", "./quiz_files/Setting_up_your_local_coding_environment_quiz.md", "./quiz_files/0x03__C_-_Debugging_quiz.md", "./quiz_files/Professional_Social_Presence_quiz.md", "./quiz_files/0x02__vi_quiz.md", "./quiz_files/0x1B__Web_stack_debugging__4_quiz.md", "./quiz_files/0x00__C_-_Hello__World_quiz.md", "./quiz_files/Attack_is_the_best_defense_quiz.md", "./quiz_files/Professional_Relationships_quiz.md", "./quiz_files/0x11__What_happens_when_you_type_google_com_in_your_browser_and_press_Enter_quiz.md", "./quiz_files/0x05__C_-_Pointers__arrays_and_strings_quiz.md", "./quiz_files/Cleanup_your_Portfolio_Project_quiz.md", "./quiz_files/Build_your_portfolio_project__Week_2___MVP_Complete_quiz.md", "./quiz_files/0x0C__Python_-_Almost_a_circle_quiz.md", "./quiz_files/0x13__C_-_More_singly_linked_lists_quiz.md", "./quiz_files/Research___Project_approval__Part_1__quiz.md", "./quiz_files/0x01__Emacs_quiz.md", "./quiz_files/Build_your_portfolio_project__Week_3___Project_Landing_Page_quiz.md", "./quiz_files/0x01__C_-_Variables__if__else__while_quiz.md", "./quiz_files/0x12__C_-_Singly_linked_lists_quiz.md", "./quiz_files/0x0F__Python_-_Object-relational_mapping_quiz.md", "./quiz_files/0x1C__C_-_Makefiles_quiz.md", "./quiz_files/0x05__AirBnB_clone_-_RESTful_API_quiz.md", "./quiz_files/Research___Project_approval__Part_2__quiz.md", "./quiz_files/0x01__Git_quiz.md", "./quiz_files/Portfolio_Project_Blog_post_quiz.md", "./quiz_files/0x02__AirBnB_clone_-_MySQL_quiz.md", "./quiz_files/0x15__API_quiz.md", "./quiz_files/First_Day_of_C_Programming_quiz.md", "./quiz_files/0x19__Postmortem_quiz.md", "./quiz_files/0x01__Fix_my_code_quiz.md", "./quiz_files/0x0E__C_-_Structures__typedef_quiz.md"]}