import datetime
import math


def parse_date(date_str):
    """
    Parse a date string in the form "Mar1125" by ignoring the provided year
    and forcing the year to 2025.
    """
    # Remove the last two characters (the year) and parse the month and day.
    month_day = date_str[:-2]  # e.g. "Mar11"
    dt = datetime.datetime.strptime(month_day, "%b%d")
    # Force the year to 2025.
    return dt.replace(year=2025).date()


def format_date(date_obj):
    """
    Format a date object as a string in the form "Feb2625" (i.e. month, day, and forced "25").
    """
    return date_obj.strftime("%b%d") + "25"


def adjust_for_weekend(date_obj):
    """
    If a date falls on a weekend, shift it to the next Monday.
    """
    while date_obj.weekday() >= 5:  # Saturday==5, Sunday==6
        date_obj += datetime.timedelta(days=1)
    return date_obj


def adjust_project_dates(tentative_start, duration):
    """
    Given a tentative start date and a project duration (in days), adjust the start date
    so that both the start and end (start + duration - 1) fall on a weekday.
    """
    new_start = adjust_for_weekend(tentative_start)
    new_end = new_start + datetime.timedelta(days=duration - 1)
    # If the end falls on a weekend, shift the entire project forward until it doesn't.
    while new_end.weekday() >= 5:
        new_start += datetime.timedelta(days=1)
        new_start = adjust_for_weekend(new_start)
        new_end = new_start + datetime.timedelta(days=duration - 1)
    return new_start, new_end


def generate_new_schedule(
    projects, global_start_str="Feb2625", global_last_start_str="Dec0325"
):
    """
    Generate a new schedule for the list of projects (each with keys:
       - 'id'
       - 'project'
       - 'orig_start' (e.g. "Mar1125")
       - 'orig_end'   (e.g. "Mar1225")

    The new schedule ensures that:
      • The first project's start is fixed to Feb26,2025.
      • The last project's start is fixed to Dec03,2025.
      • Projects in between are evenly spaced by their order.
      • Each project preserves its original duration.
      • Neither the new start nor the new end falls on a weekend.

    (Note: if there are exactly 21 projects, the spacing will be 14 days. Otherwise, the gap is computed so that
    all projects are scheduled between Feb26 and Dec03,2025.)
    """
    global_start = parse_date(global_start_str)
    global_last_start = parse_date(global_last_start_str)
    N = len(projects)

    # Total available days between the first project's start and the last project's start.
    total_interval = (global_last_start - global_start).days
    if N > 1:
        gap = total_interval / (N - 1)
    else:
        gap = 0

    new_schedule = []
    for i, proj in enumerate(projects):
        # Determine the project's original duration.
        orig_start = parse_date(proj["orig_start"])
        orig_end = parse_date(proj["orig_end"])
        duration = (orig_end - orig_start).days + 1

        # Compute the tentative start date by evenly spacing projects.
        tentative_start = global_start + datetime.timedelta(days=round(i * gap))
        # Adjust so that both start and end fall on weekdays.
        new_start, new_end = adjust_project_dates(tentative_start, duration)

        new_schedule.append(
            {
                "id": proj["id"],
                "project": proj["project"],
                "new_start": new_start,
                "new_end": new_end,
                "duration": duration,
            }
        )

    return new_schedule


# Example usage:
if __name__ == "__main__":
    # Sample list of projects; add your full project list as needed.
    projects = [
        {
            "id": "205",
            "project": "Shell, basics",
            "orig_start": "Mar1125",
            "orig_end": "Mar1225",
        },
        {
            "id": "207",
            "project": "Shell, permissions",
            "orig_start": "Mar1225",
            "orig_end": "Mar1325",
        },
        {
            "id": "208",
            "project": "Shell, I/O Redirections and filters",
            "orig_start": "Mar1625",
            "orig_end": "Mar1725",
        },
        {
            "id": "212",
            "project": "0x00. C - Hello, World",
            "orig_start": "Mar1925",
            "orig_end": "Mar2025",
        },
        {
            "id": "100073",
            "project": "First Day of C Programming",
            "orig_start": "Mar1925",
            "orig_end": "Mar2325",
        },
        {
            "id": "213",
            "project": "0x01. C - Variables, if, else, while",
            "orig_start": "Mar2025",
            "orig_end": "Mar2125",
        },
        {
            "id": "214",
            "project": "0x02. C - Functions, nested loops",
            "orig_start": "Mar2425",
            "orig_end": "Mar2525",
        },
        {
            "id": "539",
            "project": "0x03. C - Debugging",
            "orig_start": "Mar2425",
            "orig_end": "Mar2725",
        },
        {
            "id": "215",
            "project": "0x04. C - More functions, more nested loops",
            "orig_start": "Mar2625",
            "orig_end": "Mar2725",
        },
        {
            "id": "216",
            "project": "0x05. C - Pointers, arrays and strings",
            "orig_start": "Apr725",
            "orig_end": "Apr825",
        },
        {
            "id": "217",
            "project": "0x06. C - More pointers, arrays and strings",
            "orig_start": "Apr925",
            "orig_end": "Apr1025",
        },
        {
            "id": "218",
            "project": "0x07. C - Even more pointers, arrays and strings",
            "orig_start": "Apr1325",
            "orig_end": "Apr1425",
        },
        {
            "id": "219",
            "project": "0x08. C - Recursion",
            "orig_start": "Apr1525",
            "orig_end": "Apr1625",
        },
        {
            "id": "220",
            "project": "0x09. C - Static libraries",
            "orig_start": "Apr1825",
            "orig_end": "Apr2125",
        },
        {
            "id": "221",
            "project": "0x0A. C - argc, argv",
            "orig_start": "Apr1825",
            "orig_end": "Apr2125",
        },
        {
            "id": "222",
            "project": "0x0B. C - malloc, free",
            "orig_start": "Apr2025",
            "orig_end": "Apr2225",
        },
        {
            "id": "223",
            "project": "0x0C. C - More malloc, free",
            "orig_start": "Apr2325",
            "orig_end": "Apr2425",
        },
        {
            "id": "224",
            "project": "0x0D. C - Preprocessor",
            "orig_start": "Apr2725",
            "orig_end": "Apr2825",
        },
        {
            "id": "225",
            "project": "0x0E. C - Structures, typedef",
            "orig_start": "Apr2725",
            "orig_end": "Apr2825",
        },
        {
            "id": "226",
            "project": "0x0F. C - Function pointers",
            "orig_start": "Apr2925",
            "orig_end": "Apr3025",
        },
        {
            "id": "227",
            "project": "0x10. C - Variadic functions",
            "orig_start": "Apr3025",
            "orig_end": "May125",
        },
        {
            "id": "228",
            "project": "0x11. C - printf",
            "orig_start": "May125",
            "orig_end": "May625",
        },
        {
            "id": "229",
            "project": "0x12. C - Singly linked lists",
            "orig_start": "May725",
            "orig_end": "May825",
        },
        {
            "id": "230",
            "project": "0x13. C - More singly linked lists",
            "orig_start": "May1125",
            "orig_end": "May1325",
        },
        {
            "id": "232",
            "project": "0x14. C - Bit manipulation",
            "orig_start": "May1425",
            "orig_end": "May1525",
        },
        {
            "id": "234",
            "project": "0x15. C - File I/O",
            "orig_start": "May1825",
            "orig_end": "May1925",
        },
        {
            "id": "235",
            "project": "0x16. C - Simple Shell",
            "orig_start": "May2025",
            "orig_end": "Jun425",
        },
        {
            "id": "240",
            "project": "0x17. C - Doubly linked lists",
            "orig_start": "Jun2525",
            "orig_end": "Jun2625",
        },
        {
            "id": "242",
            "project": "0x18. C - Dynamic libraries",
            "orig_start": "Jun2925",
            "orig_end": "Jun3025",
        },
        {
            "id": "249",
            "project": "0x19. C - Stacks, Queues - LIFO, FIFO",
            "orig_start": "Jun3025",
            "orig_end": "Jul325",
        },
        {
            "id": "253",
            "project": "0x1A. C - Hash tables",
            "orig_start": "Jul1725",
            "orig_end": "Jul2125",
        },
        {
            "id": "248",
            "project": "0x1B. C - Sorting algorithms & Big O",
            "orig_start": "Jul2925",
            "orig_end": "Aug525",
        },
        {
            "id": "273",
            "project": "0x1C. C - Makefiles",
            "orig_start": "Sep225",
            "orig_end": "Sep325",
        },
        {
            "id": "270",
            "project": "0x1D. C - Binary trees",
            "orig_start": "Sep725",
            "orig_end": "Sep1125",
        },
        {
            "id": "295",
            "project": "0x1E. C - Search Algorithms",
            "orig_start": "Oct1925",
            "orig_end": "Oct2025",
        },
        {
            "id": "231",
            "project": "0x00. Python - Hello, World",
            "orig_start": "Jun1525",
            "orig_end": "Jun1625",
        },
        {
            "id": "233",
            "project": "0x01. Python - if/else, loops, functions",
            "orig_start": "Jun1625",
            "orig_end": "Jun1725",
        },
        {
            "id": "239",
            "project": "0x02. Python - import & modules",
            "orig_start": "Jun1925",
            "orig_end": "Jun2025",
        },
        {
            "id": "241",
            "project": "0x03. Python - Data Structures: Lists, Tuples",
            "orig_start": "Jun2025",
            "orig_end": "Jun2425",
        },
        {
            "id": "243",
            "project": "0x04. Python - More Data Structures: Set, Dictionary",
            "orig_start": "Jun2525",
            "orig_end": "Jun2625",
        },
        {
            "id": "245",
            "project": "0x05. Python - Exceptions",
            "orig_start": "Jul725",
            "orig_end": "Jul825",
        },
        {
            "id": "247",
            "project": "0x06. Python - Classes and Objects",
            "orig_start": "Jul725",
            "orig_end": "Jul825",
        },
        {
            "id": "246",
            "project": "0x07. Python - Test-driven development",
            "orig_start": "Jul925",
            "orig_end": "Jul1525",
        },
        {
            "id": "250",
            "project": "0x08. Python - More Classes and Objects",
            "orig_start": "Jul1325",
            "orig_end": "Jul1425",
        },
        {
            "id": "252",
            "project": "0x09. Python - Everything is object",
            "orig_start": "Jul1425",
            "orig_end": "Jul1525",
        },
        {
            "id": "254",
            "project": "0x0A. Python - Inheritance",
            "orig_start": "Jul2025",
            "orig_end": "Jul2125",
        },
        {
            "id": "260",
            "project": "0x0B. Python - Input/Output",
            "orig_start": "Jul2125",
            "orig_end": "Jul2225",
        },
        {
            "id": "331",
            "project": "0x0C. Python - Almost a circle",
            "orig_start": "Jul2325",
            "orig_end": "Jul2825",
        },
        {
            "id": "283",
            "project": "0x0F. Python - Object-relational mapping",
            "orig_start": "Sep2425",
            "orig_end": "Sep2825",
        },
        {
            "id": "299",
            "project": "0x10. Python - Network #0",
            "orig_start": "Oct825",
            "orig_end": "Oct925",
        },
        {
            "id": "300",
            "project": "0x11. Python - Network #1",
            "orig_start": "Oct925",
            "orig_end": "Oct1025",
        },
        {
            "id": "263",
            "project": "0x00. AirBnB clone - The console",
            "orig_start": "Aug1725",
            "orig_end": "Aug2525",
        },
        {
            "id": "268",
            "project": "0x01. AirBnB clone - Web static",
            "orig_start": "Aug2725",
            "orig_end": "Sep125",
        },
        {
            "id": "289",
            "project": "0x02. AirBnB clone - MySQL",
            "orig_start": "Sep2925",
            "orig_end": "Oct625",
        },
        {
            "id": "288",
            "project": "0x03. AirBnB clone - Deploy static",
            "orig_start": "Oct1425",
            "orig_end": "Oct1625",
        },
        {
            "id": "290",
            "project": "0x04. AirBnB clone - Web framework",
            "orig_start": "Oct2925",
            "orig_end": "Nov325",
        },
        {
            "id": "301",
            "project": "0x05. AirBnB clone - RESTful API",
            "orig_start": "Nov525",
            "orig_end": "Nov625",
        },
        {
            "id": "309",
            "project": "0x06. AirBnB clone - Web dynamic",
            "orig_start": "Nov1225",
            "orig_end": "Nov1725",
        },
        {
            "id": "209",
            "project": "0x03. Shell, init files, variables and expansions",
            "orig_start": "Mar1625",
            "orig_end": "Mar1825",
        },
        {
            "id": "251",
            "project": "0x04. Loops, conditions and parsing",
            "orig_start": "Aug625",
            "orig_end": "Aug725",
        },
        {
            "id": "255",
            "project": "0x05. Processes and signals",
            "orig_start": "Aug725",
            "orig_end": "Aug825",
        },
        {
            "id": "78",
            "project": "0x06. Regular expression",
            "orig_start": "Aug1125",
            "orig_end": "Aug1225",
        },
        {
            "id": "259",
            "project": "0x07. Networking basics #0",
            "orig_start": "Aug1225",
            "orig_end": "Aug1425",
        },
        {
            "id": "285",
            "project": "0x08. Networking basics #1",
            "orig_start": "Aug1225",
            "orig_end": "Aug1425",
        },
        {
            "id": "302",
            "project": "0x09. Web infrastructure design",
            "orig_start": "Sep325",
            "orig_end": "Sep725",
        },
        {
            "id": "292",
            "project": "0x0A. Configuration management",
            "orig_start": "Oct225",
            "orig_end": "Oct325",
        },
        {
            "id": "244",
            "project": "0x0B. SSH",
            "orig_start": "Oct225",
            "orig_end": "Oct525",
        },
        {
            "id": "265",
            "project": "0x0D. Web stack debugging #0",
            "orig_start": "Oct525",
            "orig_end": "Oct725",
        },
        {
            "id": "266",
            "project": "0x0C. Web server",
            "orig_start": "Oct525",
            "orig_end": "Oct725",
        },
        {
            "id": "275",
            "project": "0x0F. Load balancer",
            "orig_start": "Oct1225",
            "orig_end": "Oct1325",
        },
        {
            "id": "271",
            "project": "0x0E. Web stack debugging #1",
            "orig_start": "Oct1225",
            "orig_end": "Oct1725",
        },
        {
            "id": "276",
            "project": "0x10. HTTPS SSL",
            "orig_start": "Oct1625",
            "orig_end": "Oct1725",
        },
        {
            "id": "298",
            "project": "0x11. What happens when you type google.com in your browser and press Enter",
            "orig_start": "Oct1925",
            "orig_end": "Oct2725",
        },
        {
            "id": "284",
            "project": "0x13. Firewall",
            "orig_start": "Oct2625",
            "orig_end": "Oct2725",
        },
        {
            "id": "287",
            "project": "0x12. Web stack debugging #2",
            "orig_start": "Oct2625",
            "orig_end": "Oct2825",
        },
        {
            "id": "280",
            "project": "0x14. MySQL",
            "orig_start": "Oct2725",
            "orig_end": "Oct2825",
        },
        {
            "id": "269",
            "project": "0x15. API",
            "orig_start": "Nov225",
            "orig_end": "Nov325",
        },
        {
            "id": "294",
            "project": "0x19. Postmortem",
            "orig_start": "Nov1625",
            "orig_end": "Nov1925",
        },
        {
            "id": "314",
            "project": "0x16. API advanced",
            "orig_start": "Nov2025",
            "orig_end": "Nov2125",
        },
        {
            "id": "293",
            "project": "0x17. Web stack debugging #3",
            "orig_start": "Nov2025",
            "orig_end": "Nov2325",
        },
        {
            "id": "281",
            "project": "0x18. Webstack monitoring",
            "orig_start": "Nov2125",
            "orig_end": "Nov2425",
        },
        {
            "id": "311",
            "project": "0x1A. Application server",
            "orig_start": "Nov2325",
            "orig_end": "Nov2725",
        },
        {
            "id": "313",
            "project": "0x1B. Web stack debugging #4",
            "orig_start": "Nov2325",
            "orig_end": "Nov2725",
        },
        {
            "id": "262",
            "project": "RSA Factoring Challenge",
            "orig_start": "Jun2925",
            "orig_end": "Jul1325",
        },
        {
            "id": "324",
            "project": "Command line for the win",
            "orig_start": "Jul2025",
            "orig_end": "Aug425",
        },
        {
            "id": "372",
            "project": "0x00. Fix my code",
            "orig_start": "Aug2425",
            "orig_end": "Sep725",
        },
        {
            "id": "264",
            "project": "Attack is the best defense",
            "orig_start": "Sep2825",
            "orig_end": "Oct1325",
        },
        {
            "id": "375",
            "project": "0x01. Fix my code",
            "orig_start": "Oct1425",
            "orig_end": "Oct2825",
        },
        {
            "id": "361",
            "project": "Research & Project approval (Part 1)",
            "orig_start": "Nov625",
            "orig_end": "Nov725",
        },
        {
            "id": "571",
            "project": "Research & Project approval (Part 2)",
            "orig_start": "Nov725",
            "orig_end": "Nov1425",
        },
        {
            "id": "572",
            "project": "Research & Project approval (Part 3)",
            "orig_start": "Nov1425",
            "orig_end": "Nov2125",
        },
        {
            "id": "312",
            "project": "Build your portfolio project (Week 1): Making Progress",
            "orig_start": "Nov2125",
            "orig_end": "Dec1925",
        },
        {
            "id": "564",
            "project": "Build your portfolio project (Week 2): MVP Complete",
            "orig_start": "Dec425",
            "orig_end": "Dec3025",
        },
        {
            "id": "565",
            "project": "Build your portfolio project (Week 3): Project Landing Page",
            "orig_start": "Dec1125",
            "orig_end": "Dec1825",
        },
        {
            "id": "573",
            "project": "Build your portfolio project (Week 3): Presentation",
            "orig_start": "Dec1125",
            "orig_end": "Dec3125",
        },
        {
            "id": "567",
            "project": "Cleanup your Portfolio Project",
            "orig_start": "Dec1725",
            "orig_end": "Dec3025",
        },
        {
            "id": "566",
            "project": "Portfolio Project Blog post",
            "orig_start": "Dec1725",
            "orig_end": "Dec3025",
        },
    ]

    schedule = generate_new_schedule(projects)

    with open("schedule.md", "w") as file:
        for item in schedule:
            file.write(
                f'{item["id"]} {item["project"]} {format_date(item["new_start"])}...{format_date(item["new_end"])}\n'
            )

