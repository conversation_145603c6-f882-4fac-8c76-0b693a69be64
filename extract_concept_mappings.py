#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to extract concept mappings from project files.
This script analyzes JavaScript files in the src/data/projects directory
to find mappings between concept names and their numeric IDs.
"""

import os
import re
import json
from collections import defaultdict

def extract_concept_mappings(projects_dir):
    """Extract concept mappings from project files."""
    mappings = {}
    concept_id_to_names = defaultdict(list)

    # Regular expression to find concept links in markdown format
    # Example: [Pointers and arrays](/concepts/60)
    concept_pattern = r'\[([^\]]+)\]\(/concepts/(\d+)\)'

    # Walk through all files in the projects directory
    for root, _, files in os.walk(projects_dir):
        for file in files:
            if file.endswith('.js'):
                file_path = os.path.join(root, file)

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                        # Find all concept links
                        matches = re.findall(concept_pattern, content)

                        for concept_name, concept_id in matches:
                            # Store the raw concept name for this ID
                            concept_id_to_names[concept_id].append(concept_name)

                            # Convert concept name to filename format
                            # This is an approximation - may need adjustment
                            filename = concept_name.replace(' ', '-').replace('?', '').replace(':', '').replace('.', '').replace(',', '')
                            filename = re.sub(r'[^a-zA-Z0-9-_]', '', filename)
                            filename = filename + '.md'
                            numeric_id = f"_{concept_id}.md"

                            # Store the mapping
                            mappings[filename] = numeric_id
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")

    # Print all found concept IDs and their names for debugging
    print("Found concept IDs and their names:")
    for concept_id, names in concept_id_to_names.items():
        print(f"ID: {concept_id}, Names: {', '.join(set(names))}")

    return mappings

def main():
    # Path to projects directory
    projects_dir = 'src/data/projects'

    # Extract mappings
    mappings = extract_concept_mappings(projects_dir)

    # Generate JavaScript code for the mappings
    js_code = """// This file provides mapping between concept names and their numeric IDs
// Generated automatically from project files

// Map from concept name to numeric ID
export const conceptNameToId = {
"""

    # Add each mapping
    for concept_name, numeric_id in sorted(mappings.items()):
        js_code += f"  '{concept_name}': '{numeric_id}',\n"

    js_code += """};

// Map from numeric ID to concept name (reverse mapping)
export const conceptIdToName = Object.entries(conceptNameToId).reduce(
  (acc, [name, id]) => {
    acc[id] = name;
    return acc;
  },
  {}
);

// Extract numeric ID from URL
export const getNumericIdFromUrl = (url) => {
  // URL format: /concepts/60
  const match = url.match(/\\/concepts\\/(\\d+)$/);
  return match ? `_${match[1]}.md` : null;
};

// Get concept file path from numeric ID
export const getConceptFilePath = (numericId) => {
  return `concept_files/${numericId}`;
};
"""

    # Write to file
    with open('src/utils/conceptMapping.js', 'w', encoding='utf-8') as f:
        f.write(js_code)

    print(f"Generated mappings for {len(mappings)} concepts")
    print("Saved to src/utils/conceptMapping.js")

if __name__ == "__main__":
    main()
