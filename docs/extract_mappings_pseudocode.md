# Extract Mappings Pseudocode

```python
# Helper functions
def normalize_filename(concept_name):
    """Convert concept name to normalized filename"""
    # TEST: Should convert spaces and special chars to hyphens
    # Convert to lowercase
    name = concept_name.lower()
    
    # Replace special characters with hyphens
    # TEST: Should handle _/: and other special chars
    name = re.sub(r'[_/:,\s]+', '-', name)
    
    # Remove leading/trailing hyphens
    # TEST: Should clean up extra hyphens
    name = name.strip('-')
    
    return f"{name}.md"

def get_filename_variations(concept_name):
    """Generate possible filename variations"""
    # TEST: Should return list of possible filename formats
    variations = []
    
    # Original normalized version
    variations.append(normalize_filename(concept_name))
    
    # Spaces-only version
    spaces_version = concept_name.lower().replace(' ', '-').strip('-') + '.md'
    variations.append(spaces_version)
    
    # Underscore version
    underscore_version = concept_name.lower().replace(' ', '_').strip('_') + '.md'
    variations.append(underscore_version)
    
    return variations

def find_matching_file(concept_name, concept_files_dir):
    """Find matching file from possible variations"""
    # TEST: Should find existing file with matching name
    variations = get_filename_variations(concept_name)
    
    for filename in variations:
        full_path = os.path.join(concept_files_dir, filename)
        if os.path.exists(full_path):
            return filename
            
    return None

def extract_concept_mappings():
    """Main function to extract and map concepts"""
    concept_id_to_name = {}  # Maps concept IDs to names
    id_to_filename = {}      # Maps concept IDs to filenames
    fallback_ids = []        # List of IDs using fallback naming
    
    # Directory containing concept files
    concept_files_dir = 'public/concept_files/'
    
    # Scan project files to extract (concept_name, concept_id) pairs
    # (Keep existing logic for this part)
    
    # For each concept found
    for concept_id, concept_name in concept_id_to_name.items():
        # Try to find matching human-readable filename
        matching_file = find_matching_file(concept_name, concept_files_dir)
        
        if matching_file:
            # Use found human-readable filename
            id_to_filename[concept_id] = matching_file
            print(f"Using human-readable filename '{matching_file}' for concept: {concept_name}")
        else:
            # Fall back to _<concept_id>.md pattern
            fallback_name = f"_{concept_id}.md"
            id_to_filename[concept_id] = fallback_name
            fallback_ids.append(concept_id)
            print(f"Falling back to '{fallback_name}' for concept: {concept_name}")
    
    # Generate conceptMapping.js with accurate mappings
    generate_mapping_file(concept_id_to_name, id_to_filename)
    
    # Report concepts using fallback naming
    if fallback_ids:
        print("\nConcepts using fallback _<id>.md naming:")
        for concept_id in fallback_ids:
            print(f"- {concept_id_to_name[concept_id]} (ID: {concept_id})")
    
    return concept_id_to_name, id_to_filename

def generate_mapping_file(concept_id_to_name, id_to_filename):
    """Generate the conceptMapping.js file"""
    # TEST: Should generate valid JavaScript mapping file
    # (Keep existing logic for this part)
```

## Key Features

1. **Filename Normalization**
   - Converts to lowercase
   - Replaces spaces and special chars with hyphens
   - Removes leading/trailing hyphens
   - Maintains consistent format

2. **Multiple Filename Variations**
   - Tries normalized version
   - Tries spaces-to-hyphens only version
   - Tries underscore version
   - Easily extensible for more variations

3. **Smart File Matching**
   - Checks multiple variations against existing files
   - Uses first matching file found
   - Falls back to _<concept_id>.md if no match

4. **Detailed Logging**
   - Reports successful human-readable filename matches
   - Lists concepts using fallback naming
   - Helps identify opportunities for filename improvements

5. **Maintainable Structure**
   - Modular helper functions
   - Clear separation of concerns
   - TDD-ready with test anchors
   - Easily extensible

## Test Cases

1. **Filename Normalization**
   ```python
   assert normalize_filename("Web Stack Portfolio Project") == "web-stack-portfolio-project.md"
   assert normalize_filename("C - Hello, World") == "c-hello-world.md"
   assert normalize_filename("Variables_if_else_while") == "variables-if-else-while.md"
   ```

2. **Variation Generation**
   ```python
   variations = get_filename_variations("Web Stack Portfolio")
   assert "web-stack-portfolio.md" in variations
   assert "web_stack_portfolio.md" in variations
   ```

3. **File Matching**
   ```python
   # Should find existing file from variations
   assert find_matching_file("Web Stack", "concept_files/") == "web-stack.md"
   
   # Should return None if no match
   assert find_matching_file("NonexistentConcept", "concept_files/") is None