# Codebase Cleanup Specification

## 1. Criteria for "Useless" Files/Directories

### 1.1 Empty or Near-Empty
- Empty directories
- Files with no content or only whitespace
- Files with only comments/no functional code
- Files with `.bak` extension like `src/components/ProjectDetails.css.bak`
- Generated files that can be recreated
- Test files without actual tests (e.g., `src/components/test`, `src/components/test.c`)

### 1.2 Redundant/Duplicate Content
- Duplicate JavaScript files (e.g., `src/data/project-quizzes/low-level/more_functions.js` vs `more-functions.js`)
- Redundant quiz files if covered elsewhere
- Backup files with extensions like .bak, ~, etc.
- Multiple versions of similar components (e.g., Month0.js through Month10.js could be consolidated)
- Identical content across different directory structures

### 1.3 Obsolete/Unused
- Old Python scripts in root directory (`extract_mappings.py`, `organize_concept_files.py`, `schedule.py`)
- Unused log files (`link_replacement_log.txt`, `s3_link_replacement_log.txt`)
- Commented-out code blocks
- Deprecated scripts and utilities
- Unused image assets in `/public/images/`
- Orphaned concept files not referenced in the application

### 1.4 Build/Development Artifacts
- Compiled output files
- Debug logs
- Cache directories
- Local environment specific files (but preserve core `.env`)
- Package manager directories
- IDE-specific files not in .gitignore

## 2. Report Format

The analysis results will be output as a Markdown file with the following structure:

```markdown
# Codebase Cleanup Analysis Report
Generated: [TIMESTAMP]

## Summary
- Total files analyzed: [COUNT]
- Potentially removable files: [COUNT]
- Estimated space savings: [SIZE]

## Categories

### Empty/Near-Empty
- [FILE_PATH]
  - Size: [SIZE]
  - Last modified: [DATE]
  - Reason: [EMPTY/WHITESPACE/COMMENTS_ONLY]

### Redundant/Duplicate
- Group 1: [DESCRIPTION]
  - [FILE_PATH_1] (Primary)
  - [FILE_PATH_2] (Duplicate)
  - Evidence: [SIMILARITY_DETAILS]

### Obsolete/Unused
- [FILE_PATH]
  - Last accessed: [DATE]
  - Evidence: [REASON_FOR_OBSOLESCENCE]
  - Risk level: [LOW/MEDIUM/HIGH]

### Build/Development Artifacts
- [FILE_PATH]
  - Type: [ARTIFACT_TYPE]
  - Recommendation: [ACTION]

## Recommendations

### High Priority
- [ACTION_ITEM]
  - Files affected: [LIST]
  - Expected impact: [DESCRIPTION]
  - Precautions: [SAFETY_MEASURES]

### Medium Priority
[Similar structure to High Priority]

### Low Priority
[Similar structure to High Priority]

## Backup Recommendations
- Create backup branch before deletion
- Archive files locally before removal
- Document removed files for future reference
```

## 3. Analysis Process

```pseudocode
FUNCTION analyze_codebase():
    // Phase 1: Initial Scan
    // TEST: Should collect metadata for all files
    scan_result = {
        total_files: 0,
        total_size: 0,
        files_by_type: {},
        empty_files: [],
        potential_duplicates: []
    }
    
    FOR each_file IN traverse_directory("/"):
        scan_result.total_files++
        collect_file_metadata(each_file)
        check_file_emptiness(each_file)
        add_to_duplicate_detection(each_file)
    
    // Phase 2: Dependency Analysis
    // TEST: Should identify file relationships and usage
    dependency_map = {}
    
    FOR each_file IN get_source_files():
        analyze_imports(each_file, dependency_map)
        analyze_references(each_file, dependency_map)
        check_component_usage(each_file)
    
    // Phase 3: Usage Pattern Analysis
    // TEST: Should detect obsolete and unused files
    usage_patterns = {
        active_files: Set(),
        obsolete_files: [],
        unused_files: []
    }
    
    analyze_git_history()
    analyze_build_artifacts()
    cross_reference_dependencies(dependency_map)
    
    // Phase 4: Content Analysis
    // TEST: Should identify duplicate and low-value content
    FOR each_file IN scan_result.files:
        IF is_source_code(file):
            analyze_code_quality(file)
            check_commented_code(file)
        ELSE IF is_asset(file):
            check_asset_usage(file)
        
    // Phase 5: Report Generation
    generate_cleanup_report(
        scan_result,
        dependency_map,
        usage_patterns
    )
```

## 4. Constraints and Focus Areas

### 4.1 Areas to Focus On
- `/public/images/` - Look for unused or duplicate images
- `/src/components/` - Check for unused or redundant components
- `/src/data/project-quizzes/` - Identify duplicate or obsolete quiz files
- Root directory Python scripts and log files
- `.bak` files and other temporary files
- Empty or test-only files

### 4.2 Areas to Preserve
- Core application source code in `/src`
- Active documentation in `/docs`
- Essential configuration files:
  - package.json
  - .env
  - .gitignore
- Memory bank files containing project context
- Currently referenced concept files
- Critical image assets used in the application

### 4.3 Analysis Constraints
- Never delete without version control backup
- Preserve all files needed for build process
- Maintain backward compatibility
- Consider dependencies before removal
- Document all cleanup actions
- Test application functionality after each removal
- Consider SEO impact of removing assets
- Respect file permissions and ownership
- Consider client-side caching requirements

### 4.4 Safety Measures
- Create a cleanup branch for all changes
- Maintain a restoration plan
- Document removal decisions
- Test application after each phase
- Keep archived copies of removed files
- Update documentation to reflect changes
- Monitor application performance
- Plan rollback procedures