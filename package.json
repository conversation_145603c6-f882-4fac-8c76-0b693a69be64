{"name": "alx-course-tracker", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cra-template": "1.2.0", "dotenv": "^16.5.0", "glob": "^11.0.1", "gray-matter": "^4.0.3", "lucide-react": "^0.475.0", "puppeteer": "^24.8.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-markdown": "^8.0.7", "react-router-dom": "^6.26.1", "react-scripts": "^5.0.1", "react-syntax-highlighter": "^15.6.1", "rehype-autolink-headings": "^6.1.1", "rehype-slug": "^5.1.0", "remark-gfm": "^3.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^9.3.4", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^27.5.2", "@types/node": "^16.18.108", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "autoprefixer": "^10.4.20", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "^4.9.5"}}