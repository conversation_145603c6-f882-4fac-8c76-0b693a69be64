# ALX Course Tracker

A comprehensive React-based learning management system designed specifically for ALX Software Engineering students. This application provides a centralized, well-organized platform to access curriculum content, track progress, and engage with interactive learning materials through a structured month-based progression system.

## 🎯 Purpose

The ALX Course Tracker addresses the need for a unified educational platform that:
- Streamlines access to ALX Software Engineering curriculum content
- Provides clear progress tracking through the entire program
- Offers interactive learning through integrated assessments
- Organizes complex technical content in an intuitive, navigable structure

## ✨ Key Features

### 📚 Comprehensive Content Organization
- **Month-based progression**: Structured learning path from Month 0-10
- **Category-based projects**: Organized by programming domains and skill levels
- **80+ project modules** covering the complete ALX curriculum
- **Extensive concept library** with detailed explanations and visual assets
- **Comprehensive markdown content** for each course and project

### 🎯 Interactive Learning System
- **Integrated quiz system** with domain-specific assessments
- **Progress tracking** and status management
- **Interactive project details** with task requirements and code examples
- **Knowledge assessment tools** for self-evaluation
- **Resource integration** with external learning materials

### 🏗️ Project Categories
- **Low-level programming**: C programming, data structures, algorithms, system programming
- **Higher-level programming**: Python, JavaScript, SQL, web development
- **System Engineering & DevOps**: Shell scripting, networking, web infrastructure, monitoring
- **Development tools**: Git, Emacs, Vi, professional technologies
- **Portfolio projects**: Research, development, presentation, and deployment phases
- **Onboarding**: Foundational skills, mindset development, community building

### 🎨 Enhanced User Experience
- **Responsive design** optimized for all devices
- **Dark/light theme** support with system preference detection
- **Intuitive navigation** with clear content hierarchy
- **Search and filtering** capabilities for concepts and projects
- **Consistent styling patterns** following modern UI/UX principles
- **Accessibility features** for inclusive learning

## 📁 Project Architecture

```
alx-course-tracker/
├── public/                     # Static assets and build output
│   ├── concept-images/        # Educational concept visuals
│   ├── concept_files/         # Public concept content
│   └── images/               # Project and course images
├── src/                       # Main application source
│   ├── components/           # React components
│   │   ├── Month0-10.js     # Month-based learning modules
│   │   ├── concepts/        # Concept browsing and details
│   │   ├── quiz/            # Interactive quiz system
│   │   └── ui/              # Reusable UI components
│   ├── data/                # Structured content and data
│   │   ├── projects/        # Project definitions by category
│   │   │   ├── low-level/       # C programming, algorithms
│   │   │   ├── higher-level/    # Python, JavaScript, SQL
│   │   │   ├── system-engineering-devops/  # Infrastructure
│   │   │   ├── tools/           # Development tools
│   │   │   ├── onboarding/      # Foundational content
│   │   │   └── portfolio/       # Capstone projects
│   │   ├── project-quizzes/ # Assessment content by domain
│   │   ├── evaluation-quizzes/  # Comprehensive evaluations
│   │   ├── concepts/        # Concept categorization
│   │   └── months/          # Month-specific content
│   ├── contexts/            # React context providers
│   ├── utils/               # Utility functions and helpers
│   └── lib/                 # Shared library code
├── concept_files/           # Educational concept explanations
├── markdown_files/          # Course content in markdown
├── quiz_files/             # Quiz content and assessments
├── memory-bank/            # Project documentation and planning
├── scripts/                # Automation and content management
└── comparison_scripts/     # Content validation and analysis

## 🛠️ Technology Stack

### Frontend Framework
- **React 19** with modern hooks and functional components
- **TypeScript** for enhanced type safety and developer experience
- **React Router DOM** for client-side routing and navigation

### Styling & UI
- **Tailwind CSS 4.0** for utility-first styling
- **Radix UI** for accessible component primitives
- **Lucide React** for consistent iconography
- **Custom theming** with CSS variables and dark/light mode support

### Content Management
- **Markdown processing** with React Markdown and plugins
- **Gray Matter** for frontmatter parsing
- **Syntax highlighting** with React Syntax Highlighter
- **JSON-based** data structure for projects and quizzes

### Development Tools
- **Create React App** for build tooling and development server
- **PostCSS** with Autoprefixer for CSS processing
- **Testing Library** for component testing
- **ESLint** for code quality and consistency

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm, yarn, or bun package manager
- Git for version control

### Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/RonnyOtieno20/alx-course-tracker.git
   cd alx-course-tracker
   ```

2. **Install dependencies**:
   ```bash
   # Using npm
   npm install
   
   # Using yarn
   yarn install
   
   # Using bun
   bun install
   ```

3. **Start the development server**:
   ```bash
   # Using npm
   npm start
   
   # Using yarn
   yarn start
   
   # Using bun
   bun start
   ```

4. **Open your browser** and navigate to `http://localhost:3000`

### Available Scripts
- `npm start` - Runs the app in development mode
- `npm build` - Builds the app for production
- `npm test` - Launches the test runner
- `npm eject` - Ejects from Create React App (one-way operation)

## 📊 Content Overview

### Educational Content Scale
- **80+ comprehensive projects** across all programming domains
- **500+ quiz questions** for knowledge assessment
- **100+ educational concepts** with detailed explanations
- **Month-based progression** covering 11 months of curriculum
- **Multi-domain coverage** from low-level to high-level programming

### Learning Pathways
1. **Foundation Track** (Month 0-2): Shell basics, Git, development tools
2. **Low-Level Programming** (Month 3-5): C programming, data structures, algorithms
3. **Higher-Level Development** (Month 6-8): Python, JavaScript, web development
4. **System Engineering** (Month 9-10): DevOps, infrastructure, deployment
5. **Portfolio Development**: Capstone projects and professional presentation

### Assessment System
- **Domain-specific quizzes** for each programming area
- **Progressive difficulty** matching curriculum advancement
- **Comprehensive evaluations** for milestone assessment
- **Self-paced learning** with immediate feedback

## 🤝 Contributing

We welcome contributions to improve the ALX Course Tracker! Here's how you can help:

### Ways to Contribute
- **Content Updates**: Add new projects, improve existing content, or fix errors
- **Feature Development**: Implement new features or enhance existing functionality
- **Bug Fixes**: Report and fix issues you encounter
- **Documentation**: Improve documentation and code comments
- **Testing**: Add tests and improve test coverage

### Contribution Process
1. **Fork the repository** and create your feature branch
2. **Make your changes** following the existing code style and patterns
3. **Test your changes** thoroughly
4. **Submit a pull request** with a clear description of your changes

### Development Guidelines
- Follow React best practices and hooks patterns
- Maintain consistent code formatting with ESLint
- Ensure responsive design compatibility
- Test across different browsers and devices
- Document any new features or significant changes

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👨‍💻 Author & Acknowledgments

**Primary Developer**
- GitHub: [@RonnyOtieno20](https://github.com/RonnyOtieno20)
- Project: ALX Software Engineering Course Tracker

**Special Thanks**
- ALX Software Engineering Program for curriculum content
- The open-source community for the amazing tools and libraries
- All contributors who help improve this educational platform

## 🔗 Related Resources

- [ALX Software Engineering Program](https://www.alxafrica.com/)
- [React Documentation](https://react.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [Markdown Guide](https://www.markdownguide.org/)

---

**Built with ❤️ for the ALX Software Engineering Community**
