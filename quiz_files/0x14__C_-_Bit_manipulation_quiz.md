# 0x14. C - Bit manipulation

## Quiz questions

Question #0

?

```c
0x01 | 0x00 =
```

[ ] 0x00
[ ] 0x02
[x] 0x01

Question #1

?

```c
0x01 << 1 =
```

[ ] 0x03
[ ] 0x00
[x] 0x02
[ ] 0x10
[ ] 0x01

Question #2

?

```c
~ 0x12 =
```

[x] 0xED
[ ] 0xEE
[ ] 0xFD
[ ] 0x21

Question #3

?

```c
~ 0x98 =
```

[ ] 0x66
[ ] 0x68
[x] 0x67

Question #4

?

```c
0x01 & 0x01 =
```

[ ] 0x00
[ ] 0x02
[x] 0x01

Question #5

What is  in base16?

```c
98
```

[x] 0x62
[ ] 0x96
[ ] 0x98

Question #6

?

```c
0x02 >> 1 =
```

[ ] 0x02
[ ] 0x00
[x] 0x01

Question #7

?

```c
0x01 & 0x00 =
```

[x] 0x00
[ ] 0x02
[ ] 0x01

Question #8

?

```c
0x89 >> 3 =
```

[x] 0x11
[ ] 0x89
[ ] 0x22
[ ] 0x08
[ ] 0x44

Question #9

What is  in base10?

```c
0x89
```

[ ] 89
[x] 137
[ ] 135
[ ] 139

Question #10

?

```c
0x13 << 1 =
```

[ ] 0x13
[ ] 0x4C
[x] 0x26
[ ] 0x98

Question #11

?

```c
0x01 | 0x01 =
```

[ ] 0x00
[ ] 0x02
[x] 0x01

Question #12

What is  in base2?

```c
0x89
```

[ ] 0b10101001
[ ] 0b01101001
[x] 0b10001001
[ ] 0b10001000

Question #13

?

```c
0x88 & 0x01 =
```

[x] 0x00
[ ] 0x88
[ ] 0x01
[ ] 0x89

Question #14

What is  in base16?

```c
0b01101101
```

[ ] 0x7D
[ ] 0x36
[ ] 0x6E
[ ] 0xD6
[x] 0x6D

Question #15

?

```c
0x44 | 0x22 =
```

[ ] 0x22
[x] 0x66
[ ] 0x44

Question #16

What is  in base2?

```c
98
```

[ ] 0b01010010
[ ] 0b10011000
[x] 0b01100010

Question #17

What is  in base10?

```c
0b001010010
```

[ ] 81
[ ] 83
[x] 82
[ ] 84

Question #18

?

```c
0x66 & 0x22 =
```

[x] 0x22
[ ] 0x66
[ ] 0x44

Question #19

?

```c
0x89 & 0x01 =
```

[ ] 0x00
[ ] 0x88
[x] 0x01
[ ] 0x89