# 0x07. Python - Test-driven development

## Quiz questions

Question #0

Is this a standardized way to comment a function in Python?

```c
def add(a, b):
    """ Addition function """
    return a + b
```

[x] Yes
[ ] No

Question #1

Is this a standardized way to comment a function in Python?

```c
/* Addition function */
def add(a, b):
    return a + b
```

[ ] Yes
[x] No

Question #2

Is this a standardized way to comment a function in Python?

```c
##########
# Addition function
##########
def add(a, b):
    return a + b
```

[ ] Yes
[x] No

Question #3

Is this a standardized way to comment a function in Python?

```c
"""" Addition function """
def add(a, b):
    return a + b
```

[ ] Yes
[x] No

Question #4

Is this module correctly commented?

Docstring must be before import statements

```c
#!/usr/bin/python3
import sys

""" 
    My calculation module
"""
...
```

[ ] Yes
[x] No

Question #5

Is this module correctly commented?

```c
#!/usr/bin/python3
""" 
    My calculation module
"""
import sys
...
```

[x] Yes
[ ] No

Question #6

Based on this code, what should all the test cases be? (select multiple)

```c
def uniq(list):
    """ Returns unique values of a list """
    u_list = []
    for item in list:
        if item not in u_list:
            u_list.append(item)
    return u_list
```