# 0x08. Python - More Classes and Objects

## Quiz questions

Question #0

What is ?

```c
__del__
```

[ ] Instance method that removes the last character of an instance
[x] Instance method called when an instance is deleted
[ ] Instance method that prints the memory address of an instance

Question #1

What do these lines print?

```c
class User:
    id = 1

User.id = 98
u = User()
u.id = 89
print(u.id)
```

[ ] None
[x] 89
[ ] 1
[ ] 98

Question #2

What do these lines print?

```c
class User:
    id = 1

u = User()
u.id = 89
User.id = 98
print(User.id)
```

[ ] None
[ ] 89
[ ] 1
[x] 98

Question #3

What do these lines print?

```c
class User:
    id = 1

u = User()
u.id = 89
User.id = 98
print(u.id)
```

[ ] None
[x] 89
[ ] 1
[ ] 98

Question #4

What is ?

```c
__init__
```

[ ] A class attribute
[x] The instance method called when a new object is created
[ ] A class method
[ ] The instance method called when a class is called for the first time

Question #5

What do these lines print?

```c
class User:
    id = 1

User.id = 98
u = User()
print(u.id)
```

[ ] None
[ ] 89
[ ] 1
[x] 98

Question #6

What is ?

```c
__repr__
```

[ ] Instance method that prints an “official” string representation of an instance
[ ] Instance method that returns the dictionary representation of an instance
[x] Instance method that returns an “official” string representation of an instance

Question #7

What do these lines print?

```c
class User:
    id = 1

u = User()
User.id = 98
print(u.id)
```

[ ] None
[ ] 89
[ ] 1
[x] 98

Question #8

What do these lines print?

```c
class User:
    id = 1

User.id = 98
u = User()
u.id = 89
print(User.id)
```

[ ] None
[ ] 89
[ ] 1
[x] 98

Question #9

What is ?

```c
__str__
```

[x] Instance method that returns an “informal” and nicely printable string representation of an instance
[ ] Instance method that prints an “informal” and nicely printable string representation of an instance
[ ] Instance method that returns the dictionary representation of an instance

Question #10

What do these lines print?

```c
class User:
    id = 1

u = User()
u.id = 89
print(u.id)
```

[ ] None
[x] 89
[ ] 1
[ ] 98

Question #11

What do these lines print?

```c
class User:
    id = 1

print(User.id)
```

[ ] None
[ ] 89
[x] 1
[ ] 98

Question #12

What is ?

```c
__doc__
```

[x] The string documentation of an object (based on docstring)
[ ] Creates man file
[ ] Prints the documentation of an object

Question #13

What do these lines print?

```c
class User:
    id = 1

u = User()
print(u.id)
```

[ ] None
[ ] 89
[x] 1
[ ] 98