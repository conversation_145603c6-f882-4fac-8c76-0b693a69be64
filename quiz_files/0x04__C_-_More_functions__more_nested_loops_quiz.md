# 0x04. C - More functions, more nested loops

## Quiz questions

Question #0

What is the output of the following piece of code?

```c
int i;

i = 0;
while (i < 10)
{
    i++;
    printf("%d", i / 2);
}
```

[ ] 0011223344
[x] 0112233445
[ ] 0123456789

Question #1

What is the output of the following piece of code?

```c
int i;

for (i = 48; i < 58; i++)
{
    printf("%c", i);
}
```

[ ] 48495051525354555657
[ ] School
[x] 0123456789

Question #2

What is the return value of the following function?

```c
int some_function(void)
{
    int i;

    for (i = 0; i < 10; i++)
    {
        printf("%d", i);
    }
    return(i);
}
```

[ ] 0
[x] 10
[ ] 9
[ ] 0123456789

Question #3

What is the output of the following piece of code?

```c
int i;

for (i = 0; i < 10; i++)
{
    printf("%d", i * 2);
}
```

[ ] 0123456789
[x] 024681012141618
[ ] 2468101214161820

Question #4

What is the output of the following piece of code?

```c
int i;

i = 0;
while (i < 10)
{
    printf("%d", i % 2);
    i++;
}
```

[x] 0101010101
[ ] 1010101010
[ ] 0123456789

Question #5

What is the return value of the following function?

```c
int some_function(void)
{
    printf("%d", 12);
    return (98);
}
```

[ ] 12
[ ] 402
[x] 98

Question #6

What is the output of the following piece of code?

```c
int i;

i = 9;
while (i--)
{
    printf("%d", i);
}
```

[ ] 87654321
[ ] 9876543210
[x] 876543210
[ ] 987654321

Question #7

What is the output of the following piece of code?

```c
int i;

i = -9;
while (i < 0)
{
    printf("%d", -i);
    i++;
}
```

[ ] 9876543210
[ ] -9-8-7-6-5-4-3-2-10
[x] 987654321
[ ] -9-8-7-6-5-4-3-2-1

Question #8

What is the output of the following piece of code?

```c
int i;

i = 9;
while (--i)
{
    printf("%d", i);
}
```

[x] 87654321
[ ] 876543210
[ ] 9876543210
[ ] 987654321