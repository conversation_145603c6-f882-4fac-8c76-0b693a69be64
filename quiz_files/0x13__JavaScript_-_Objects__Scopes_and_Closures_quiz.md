# 0x13. JavaScript - Objects, Scopes and Closures

## Quiz questions

Question #0

What is the output of this code?

```c
const a = 12;

function myFunction(a) {
    console.log(a);
}

myFunction(89);
```

[ ] 1
[ ] 2
[ ] 12
[x] 89

Question #1

What is the output of this code?

```c
function myFunction(a) {
    console.log(a);
}

myFunction(12);
```

[ ] 1
[ ] 2
[x] 12

Question #2

What is the output of this code?

```c
function myFunction(a) {
    console.log(a);
}

const number = 12;
myFunction(number);
```

[ ] 1
[ ] 2
[x] 12

Question #3

What is the output of this code?

```c
const number = 12;
function myFunction(a) {
    console.log(a);
}

myFunction(number);
```

[ ] 1
[ ] 2
[x] 12

Question #4

What is the output of this code?

```c
let b = 1;

function myFunction(a) {
    console.log(a + b);
    b = a;
}

myFunction(3);
myFunction(4);
```

[ ] 3, 4
[ ] 3, 7
[x] 4, 7
[ ] 4, 3

Question #5

What is the output of this code?

```c
const b = 79;
function myFunction(a) {
    console.log(a + b);
}

myFunction(10);
```

[x] 89
[ ] 79
[ ] 10

Question #6

What is the output of this code?

```c
function myFunction(a) {
    console.log(a);
}

const a = 12;
myFunction(89);
```

[ ] 1
[ ] 2
[ ] 12
[x] 89

Question #7

What is the output of this code?

```c
function myFunction(a) {
    console.log(a + b);
}

const b = 79;
myFunction(10);
```

[ ] 10
[x] 89
[ ] 79