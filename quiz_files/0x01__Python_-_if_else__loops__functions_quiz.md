# 0x01. Python - if/else, loops, functions

## Quiz questions

Question #0

What do these lines print?

```c
if 12 == 48/3 or 12 is 12:
    print("Holberton")
else:
    print("School")
```

[x] Holberton
[ ] School

Question #1

What do these lines print?

```c
if True:
    print("Holberton")
else:
    print("School")
```

[x] Holberton
[ ] School

Question #2

What do these lines print?

```c
for i in range(2, 10, 2):
    print(i, end=" ")
```

[ ] 2 3 4 5 6 7 8 9 10
[ ] 4 6 8 10 12 14 16 18
[ ] 2 3 4 5 6 7 8 9
[x] 2 4 6 8

Question #3

What do these lines print?

```c
if 12 == 48/4 and False:
    print("Holberton")
else:
    print("School")
```

[ ] Holberton
[x] School

Question #4

What do these lines print?

```c
if 12 == 48/4:
    print("Holberton")
else:
    print("School")
```

[x] Holberton
[ ] School

Question #5

What do these lines print?

```c
for i in range(2, 4):
    print(i, end=" ")
```

[ ] 2 4
[ ] 2 3 4
[x] 2 3
[ ] 3 4

Question #6

What do these lines print?

```c
a = 12
if a < 2:
    print("Holberton")
elif a % 2 == 0:
    print("C is fun")
else:
    print("School")
```

[ ] Holberton
[ ] School
[x] C is fun

Question #7

What do these lines print?

```c
a = 12
if a > 2:
    if a % 2 == 0:
        print("Holberton")
    else:
        print("C is fun")
else:
    print("School")
```

[x] Holberton
[ ] School
[ ] C is fun

Question #8

What do these lines print?

```c
for i in range(4):
    print(i, end=" ")
```

[ ] 1 2 3 4
[x] 0 1 2 3
[ ] 1 2 3
[ ] 0 1 2 3 4