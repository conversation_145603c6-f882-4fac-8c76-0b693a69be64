# 0x03. AirBnB clone - Deploy static

## Quiz questions

Question #0

What is the Fabric command to execute a shell command locally?

[ ] put
[ ] run
[ ] get
[ ] local

Question #1

What is the default name of a Fabric file?

[ ] Dockerfile
[ ] Fabric.py
[ ] fabfile.py
[ ] Fabricfile

Question #2

What is the Fabric command to upload a file (from local to remote)?

[ ] get
[ ] run
[ ] put
[ ] local

Question #3

What is the Fabric command to download a file (from remote to local)?

[ ] get
[ ] run
[ ] put
[ ] local

Question #4

What is the Fabric command for asking information to the user?

[ ] put
[ ] ask
[ ] prompt
[ ] local

Question #5

What is the Fabric command to execute a shell command remotely?

[ ] put
[ ] run
[ ] get
[ ] local