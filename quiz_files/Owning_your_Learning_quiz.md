# Owning your Learning

## Quiz questions

Question #0

"which of the following is part of the recommended learning framework within the ALX program. (Select all that apply)

[x] Read the project prerequisites, man pages, documentation, books, and articles. Try all the examples in the docs
[x] Write or draw out your problem on a whiteboard or paper. It's easier to visualize your problem when it isn't pure code.
[x] Pause, take a breath, and think of the solution. Make sure you understand all of the required concepts and algorithms before you proceed to solve tasks.
[x] Whether or not your code runs when you input it, make sure you know why it doesn't (or does) work without looking at the error message.

Question #1

How do you ask good technical questions?

[x] Share what you've already tried
[x] Sum up your question in a title
[x] Provide code sample
[ ] Provide screenshots
[x] Format, Lint, and Document Your Code
[x] Provide context

Question #2

Which of the following is true about the <PERSON><PERSON>man learning technique (Select all that apply)

[x] Because learning doesn't happen from skimming through a book or remembering enough to pass a test
[x] Ultimately, the point of learning is to understand the world"
[x] Information is learned when you can explain it and use it in a wide variety of situations.

Question #3

Which is more important: focused vs. diffuse thinking?

[ ] Focused thinking, your brain processes very specific information deeply
[x] Both are required in order to master a topic or make progress on a difficult project. When you are learning something new, you need to understand both the context for the information (diffuse) and the specifics of the subject (focused).
[ ] Diffuse thinking, the brain analyses much more information at once but in less depth

Question #4

According to Richard Faynmen, what does it mean to know: (Select all that apply)

[x] The person who says he knows what he thinks but cannot express it usually does not know what he thinks
[x] When you know something, the labels are unimportant, because it's not necessary to keep it in the box it came in."
[x] When you only know what something is called, you have no real sense of what it is.

Question #5

Why you should ask and answer technical questions?

[x] You learn how to ask questions
[x] You learn by answering questions
[x] You will gain a better understanding of the problem

Question #6

Which of the following is not a step in the Faynmen learning technique

[ ] Pretend to teach a concept you want to learn about to a student in the sixth grade.
[ ] Organize and simplify.
[ ] Identify gaps in your explanation. Go back to the source material to better understand it.
[x] make things bigger, more complex

Question #7

Which of the following completely describes the difference between focussed thinking and diffused thinking

[x] focussed thinking is like seeing a brick wall and saying it's just too difficult to run through or climb through while
diffused thinking is like your mind saying "Hey why don't we tie balloons and just float over it
[ ] focussed thinking is like your mind saying "Hey why don't we tie balloons and just float over it" while diffused 
thinking is like seeing a brick wall and saying it's too difficult to run through or climb through."

Question #8

When Asking a peer for help, it is important to remember… (Select all that apply)

[x] it's your job to ask and it's your job to help
[x] Explain what the problem is and what you've tried"
[x] Respect other people's time

Question #9

If you still can't find the solution or understand why your code doesn't work, google it. Which of the following facts should be considered when googling (Select all that apply)

[ ] Trust everything on the Intranet
[x] Never copy and paste something from the Internet. You need to understand everything before you implement 
something you read. Typing yourself - instead of cp/paste - ensures that you better understand and not miss any details.
[x] Only google something specific

Question #10

When giving help to a peer, it is important to… (Select all that apply)

[x] Respect every question; there is nothing more demotivating than having someone 
laughing at your question or being asked "what? you still don't know that?"
[x] There are no stupid questions
[x] Do NOT give the final answer. You are here to guide, help them find the solution by themselves.