# 0x03. Shell, init files, variables and expansions

## Quiz questions

Question #0

Which command should I use to define a new command  for pushing in Github?

```c
push
```

Example:

```c
$ push 
Everything up-to-date
$
```

[x] alias push="git push"
[ ] alias push=git push
[ ] export push="git push"
[ ] export push=git push

Question #1

Which command should I use to display a variable?

[ ] ls $MYVAR
[ ] export $MYVAR
[ ] cd $MYVAR
[x] echo $MYVAR

Question #2

Which command should I use to display the exit code of the previous command?

[ ] echo ?
[x] echo $?
[ ] echo $EXITCODE
[ ] echo $CODE

Question #3

What is the variable name who contains the previous working directory path?

[x] OLDPWD
[ ] OLDDIR
[ ] PREVPWD
[ ] PREVDIR