# Setting up your local coding environment

## Quiz questions

Question #0

Which is the correct way of accessing a Linux environment for coding? Select all correct answers

Question #1

What is a virtual machine?

[ ] A set of servers for software development
[x] An emulation of a computer system
[ ] A system for developing virtual reality

Question #2

What is the difference between a Docker image and a Docker container?

Question #3

What is the correct definition of WSL? Select all correct answers

Question #4

[Fun question] Are you aware that all ALX products (the Intranet, the Checker, Sandboxes, Kimba,,,) are built and developed under several docker containers?

Question #5

Ubuntu is a ____ distribution.

[ ] Windows
[ ] MacOS
[x] Linux

Question #6

Where can you get help with WSL? Select all correct answers

Question #7

How do you launch a WSL distribution?

[ ] wsl --distribution <distribution name>
[ ] start wsl <distribution name>
[ ] wsl run <distribution name>

Question #8

Select all correct definitions of Docker

Question #9

What is the difference between a WSL distribution and a Linux distribution? Select all correct answers?

[ ] A WSL distribution is a special type of Linux distribution that is designed to run on Windows.
[ ] A WSL distribution is a regular Linux distribution that is packaged to run on Windows using the Windows Subsystem for Linux (WSL).
[ ] A WSL distribution is a lightweight version of a Linux distribution that is designed to be used on resource-constrained devices.