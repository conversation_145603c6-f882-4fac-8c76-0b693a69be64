# 0x05. C - Pointers, arrays and strings

## Quiz questions

Question #0

What is the identifier to print an address with ?

```c
printf
```

[ ] %a
[x] %p
[ ] %d
[ ] %x

Question #1

What is the value of  after the following code is executed?

```c
n
```

```c
int n = 98;
int *p = &n;

*p++;
```

[ ] 0
[ ] 99
[x] 98
[ ] 402

Question #2

We declare the following variable

What is the equivalent of typing ?

```c
arr[2]
```

```c
int arr[5];
```

[ ] arr + 2
[x] *(arr + 2)
[ ] *arr + 2

Question #3

What is the size of a pointer to a  (on a 64-bit architecture)

```c
char
```

[ ] 1 byte
[ ] 4 bytes
[ ] 2 bytes
[x] 8 bytes

Question #4

What is the value of  after the following code is executed?

```c
n
```

```c
int n = 98;
int *p = &n;

p = 402;
```

[ ] 0
[ ] 99
[x] 98
[ ] 402

Question #5

If we have a iable called var of type int, how can we get its address in memory?

```c
var
```

[ ] *var
[x] &var
[ ] *(var)

Question #6

What is the value of  after the following code is executed?

```c
n
```

```c
int n = 98;
int *p = &n;

*p = 402;
```

[ ] 0
[ ] 99
[ ] 98
[x] 402

Question #7

We declare the following variable

What is the size in memory of the variable ?

```c
arr
```

```c
int arr[5];
```

[ ] 32 bytes
[ ] 4 bytes
[ ] 8 bytes
[ ] 10 bytes
[x] 20 bytes
[ ] 5 bytes

Question #8

What is the size of a poer to an int (on a 64-bit architecture)

```c
int
```

[ ] 1 byte
[ ] 4 bytes
[ ] 2 bytes
[x] 8 bytes

Question #9

What is the value of  after the following code is executed?

```c
n
```

```c
int n = 98;
int *p = &n;
```

[ ] 0
[ ] 99
[x] 98
[ ] 402

Question #10

Is it possible to declare a pointer to a pointer?

[x] Yes
[ ] It depends on the type the pointer is pointing to
[ ] No

Question #11

The process of getting the value that is stored in the memory location pointed to by a pointer is called:

[ ] Pointing
[x] Dereferencing
[ ] Accessing
[ ] Casting

Question #12

What happens when one tries to access an illegal memory location?

[ ] The operation is ignored
[ ] The computer shuts down
[x] Segmentation fault
[ ] There’s a chance for the computer to catch fire, and sometimes even explode