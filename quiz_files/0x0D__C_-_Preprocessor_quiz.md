# 0x0D. C - Preprocessor

## Quiz questions

Question #0

The preprocessor links our code with libraries.

[ ] True
[x] False

Question #1

What does the macro  expand to?

```c
TABLESIZE
```

```c
#define B<PERSON>FS<PERSON>ZE 1020
#define T<PERSON><PERSON>SIZ<PERSON> BUFSIZE
#undef BUFSIZE
#define B<PERSON>FSIZE 37
```

[ ] 1020
[ ] nothing
[x] 37

Question #2

Why should we use include guards in our header files?

[ ] Because we said so, and we should never ask why.
[x] To avoid the problem of double inclusion when dealing with the include directive.

Question #3

The macro  expands to the name of the current input file, in the form of a C string constant.

```c
__FILE__
```

[x] True
[ ] False

Question #4

What are the steps of compilation?

[ ] compiler 2. preprocessor 3. assembler 4. linker
[ ] preprocessor 2.compiler 3. linker 4. assembler
[x] preprocessor 2.compiler 3. assembler 4. linker

Question #5

What will be the last 5 lines of the output of the command  on this code?

```c
gcc -E
```

```c
#include <stdlib.h>

int main(void)
{
    NULL;
    return (EXIT_SUCCESS);
}
```

```c
int main(void)
{
 0;
 return (0);
}
```

```c
int main(void)
{
 ((void *)0);
 return (0);
}
```

```c
int main()
{
 0;
 return (0);
}
```

```c
int main(void)
{
 '\0';
 return (0);
}
```

[ ] int main(void)
{
 0;
 return (0);
}
[x] int main(void)
{
 ((void *)0);
 return (0);
}
[ ] int main()
{
 0;
 return (0);
}
[ ] int main(void)
{
 '\0';
 return (0);
}

Question #6

The preprocessor generates object code

[ ] True
[x] False

Question #7

This code will try to allocate 1024 bytes in the heap:

```c
#define BUFFER_SIZE 1024
malloc(BUFFER_SIZE)
```

[x] True
[ ] False

Question #8

What will be the output of this program? (on a standard 64 bits, Linux machine)

```c
#include <stdio.h>
#include <stdlib.h>

#define int char

int main(void)
{
    int i;

    i = 5;
    printf ("sizeof(i) = %lu", sizeof(i));
    return (EXIT_SUCCESS);
}
```

[ ] sizeof(i) = 4
[ ] Segmentation Fault
[ ] sizeof(i) = 8
[ ] sizeof(i) = 5
[x] sizeof(i) = 1
[ ] It does not compile

Question #9

This is the correct way to define the macro :

```c
SUB
```

```c
#define SUB(a, b) a - b
```

```c
#define SUB(a, b) (a) - (b)
```

```c
#define SUB(a, b) (a - b)
```

```c
#define SUB(a, b) ((a) - (b))
```

[ ] Yes
[ ] No, it should be written this way:
[ ] No, it should be written this way:
[x] No, it should be written this way:

Question #10

The preprocessor generates assembly code

[ ] True
[x] False

Question #11

is a macro

```c
NULL
```

[x] True
[ ] False

Question #12

The preprocessor removes all comments

[x] True
[ ] False

Question #13

This portion of code is actually using the library stdlib.

```c
#include <stdlib.h>
```

[ ] True
[x] False

Question #14

What is the  option that runs only the preprocessor?

```c
gcc
```

[x] -E
[ ] -pedantic
[ ] -a
[ ] -cisfun
[ ] -p
[ ] -preprocessor
[ ] -P