# 0x0F. C - Function pointers

## Quiz questions

Question #0

This  is:

```c
void (*anjula[])(int, float)
```

[x] An array of pointers to functions that take an int and a float as parameters and returns nothing
[ ] A pointer to a function that takes an int and a float as parameters and returns nothing
[ ] A pointer to a function that takes an int and a float as parameters and returns an empty array
[ ] A pointer to an array of functions that take an int and a float as parameters and returns nothing
[ ] A pointer to a function that takes an array of int and float as a parameter and returns nothing

Question #1

Which one is a pointer to a function?

[ ] int func(int a, float b);
[x] int (*func)(int a, float b);
[ ] int *func(int a, float b);
[ ] (int *)func(int a, float b);

Question #2

I f is a pointer to a function that takes no parameter and returns an int, you can call the function pointed by f this way (check all correct answers if there is more than one):

```c
f
```

Question #3

To store the address of this function:

to the variable  of type pointer to a function that does not take any argument and does not return anything, you would do (check all correct answers if there is more than one):

```c
f
```

```c
void neyo(void);
```

Question #4

What does a pointer to a function point to (check all correct answers if there is more than one)?