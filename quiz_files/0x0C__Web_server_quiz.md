# 0x0C. Web server

## Quiz questions

Question #0

What was one of the most important reason for which DNS was created

[x] because humans are not good at remembering long sequences of numbers (IP address)
[ ] to index the web
[ ] to connect the Internet

Question #1

The main role of a web server is to

[ ] serve dynamic content
[ ] host files
[x] serve static content

Question #2

What is TTL within the context of DNS

[x] a time period when DNS query results are cached
[ ] a time period for DNS maintenance
[ ] a time period when DNS is not answering requests

Question #3

The main role of DNS is to

[x] translate domain name into IP address
[ ] name websites
[ ] translate domain name into port

Question #4

Why web servers usually use child processes?

[ ] That’s just a subjective technical choice from the developers who created the software
[ ] To prevent memory leak
[x] So that the web server can dynamically change the number of child process to accommodate the volume of requests to be processed

Question #5

A web server is

[ ] a physical machine
[x] a software

Question #6

A DNS CNAME record translates to

[ ] an IP
[x] a domain

Question #7

A HTTP POST request is to

[ ] request data
[ ] delete data
[x] submit data

Question #8

A HTTP GET request is to

[x] request data
[ ] delete data
[ ] submit data

Question #9

A DNS A record translates to

[x] an IP
[ ] a domain