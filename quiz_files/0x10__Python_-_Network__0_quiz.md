# 0x10. Python - Network #0

## Quiz questions

Question #0

What is the name of the HTTP response header used to define the formatting of the body? (This header gives details to the client on how to interpret the data received.)

[ ] Type
[ ] Format
[ ] Content-Format
[x] Content-Type

Question #1

What will be the port number requested by this URL?

```c
http://www.google.com/apply
```

[ ] 8080
[ ] 443
[x] 80
[ ] 22

Question #2

What is the name of the HTTP request header that defines the size (in bytes) of the message body?

[x] Content-Length
[ ] Content-Size
[ ] Length
[ ] Size

Question #3

What is the name of the HTTP response header used to define the status code of the response?

[x] Status
[ ] Code
[ ] Status-Code
[ ] Http-Status

Question #4

In the following URL, what’s the name of the parameter in the query string?

```c
https://www.google.com/apply?batch=89
```

[x] batch
[ ] 89
[ ] apply

Question #5

In the following URL, how many parameters are in the query string?

```c
https://www.google.com/apply?batch=89&location=SF&name=John%20do%20is%20the%20best%20%3D%20c%20is%20fun
```

[ ] 4
[x] 3
[ ] 1
[ ] 5
[ ] 2

Question #6

When you are typing  in your browser, which HTTP verb is used?

```c
https://intranet.hbtn.io
```

[ ] POST
[x] GET
[ ] DELETE
[ ] PUT

Question #7

In the following URL, what’s the hostname?

```c
http://www.google.com
```

[ ] google
[x] www.google.com
[ ] google.com
[ ] www.google

Question #8

What is the name of the HTTP response header used to send cookies to the client from the server?

[ ] Send-Cookies
[ ] Cookie-Setter
[x] Set-Cookie

Question #9

What is the first digit of status codes that indicate a server error?

[ ] 4xx
[ ] 1xx
[ ] 3xx
[x] 5xx
[ ] 2xx

Question #10

What is the  option to set a cookie with a key-value pair?

```c
curl
```

[ ] -d
[ ] -a
[x] -b
[ ] -c

Question #11

In the following URL, what’s the resource path?

```c
https://www.google.com/assets/scripts/main.js
```

[x] assets/scripts/main.js
[ ] assets/scripts
[ ] main.js

Question #12

What will be the port number requested by this URL?

```c
https://www.google.com:8080/apply
```

[ ] 80
[ ] 8888
[x] 8080

Question #13

In the following URL, what’s the resource path?

```c
https://api.google.com/v1/auth/new
```

[ ] v1
[x] v1/auth/new
[ ] v1/auth
[ ] v1/auth/new/index.html

Question #14

In this following HTML code, which HTTP verb will be used when you will submit this form?

```c
<FORM action="/login.php" method="post">
    <INPUT type="email" name="email" placeholder="Email" required/>
    <INPUT type="password" name="password" placeholder="Password" required/>
    <INPUT type="submit" name="submit" value="Login" />
<FORM>
```

[ ] ENTER
[x] POST
[ ] SUBMIT
[ ] GET
[ ] FORM

Question #15

What is the name of the HTTP request header used to send cookies from the client?

[ ] Set-Cookie
[x] Cookie
[ ] Send-Cookie
[ ] Cookies

Question #16

What is the name of the HTTP response header that defines a list of available HTTP methods for this URL?

[ ] Verbs
[ ] Verbs-Allowed
[x] Allow

Question #17

What is the  option to disable the progression display?

```c
curl
```

[x] -s
[ ] -b
[ ] -c
[ ] -p

Question #18

In the following URL, how many parameters are in the query string?

```c
https://www.google.com/apply?batch=89&location=SF
```

[x] 2
[ ] 1
[ ] 3

Question #19

What is the  option to save the body of the resulting response to a file?

```c
curl
```

[ ] -d
[ ] -r
[ ] -b
[x] -o

Question #20

Which HTTP request header indicates the browser used by the client sending the request?

[ ] Origin
[ ] I-Am
[x] User-Agent
[ ] Browser-Name

Question #21

In the following URL, what’s the protocol?

```c
http://www.google.com
```

[x] http
[ ] ftp
[ ] https

Question #22

What is the  option that defines the HTTP method used?

```c
curl
```

[ ] -d
[ ] -s
[x] -X

Question #23

In this following HTML code, which HTTP verb will be used when you will submit this form?

```c
<FORM action="/12/update.php" method="put">
    <INPUT type="text" name="first_name" value="Bob"/>
    <INPUT type="text" name="last_name" value="Dylan"/>
    <INPUT type="submit" name="update" value="Update" />
<FORM>
```

[ ] GET
[ ] POST
[ ] UPDATE
[x] PUT

Question #24

In the following URL, what’s the resource path?

```c
https://www.google.com/index.html
```

[ ] /
[ ] www.google.com/index.html
[x] index.html

Question #25

What’s the status code number for a permanent redirection (moved permanently)?

[ ] 302
[ ] 201
[x] 301
[ ] 304
[ ] 300

Question #26

What is the  option to follow all redirects?

```c
curl
```

[ ] -s
[x] -L
[ ] -X

Question #27

What is the name of the HTTP response header used to define the size, in bytes, of the body of the response?

[ ] Body-Size
[ ] Length
[ ] Content-Size
[x] Content-Length

Question #28

In the following URL, what’s the sub domain?

```c
https://api.google.com/v1/auth
```

[ ] .com
[x] api
[ ] api.google

Question #29

Which  option is used to set an HTTP header to a specific value?

```c
curl
```

[x] -H
[ ] -s
[ ] -X

Question #30

What’s the status code number for an invalid HTTP request (server can’t understand it)?

[ ] 500
[x] 400
[ ] 404

Question #31

When an HTTP response indicates a redirection, which header defines the URL the client should be redirected to?

[ ] Redirect-Location
[ ] Redirect-URI
[ ] Next-Location
[ ] Redirect
[x] Location

Question #32

What’s the status code number for a web page that can’t be found?

[x] 404
[ ] 500
[ ] 405

Question #33

What is the  option to set a body key-value parameter?

```c
curl
```

[ ] -b
[x] -d
[ ] -X

Question #34

What will be the port number requested by this URL?

```c
afp://www.google.com/access_in_port_543
```

[ ] 543
[x] 548
[ ] 80

Question #35

In the following URL, what’s the sub domain?

```c
https://api-dev.google.com/v1/auth/new
```

[x] api-dev
[ ] /v1
[ ] /v1/auth/new