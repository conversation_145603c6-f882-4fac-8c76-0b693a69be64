# 0x09. Web infrastructure design

## Quiz questions

Question #0

What is a database?

[ ] Is a collection of text files that are stored so that it can be easily accessed, updated and managed by the local application.
[x] Is a collection of information that is stored and organized so that it can be easily accessed, updated and managed.
[ ] Is a collection of information that is stored on a physical server and organized so that it can be easily accessed, updated and managed.

Question #1

What is a server?

[x] A server is a device, a virtual device or computer program or providing functionality for other programs or devices, called “clients”.
[ ] A server is returning information to other computers when asked.
[ ] A server is a software that serves web pages.

Question #2

What is a codebase?

[ ] A list of software libraries.
[ ] Is the most important files of a software system.
[x] Is the collection of source code that is used to build a software system.

Question #3

What is a web server?

[ ] A web server is a software or physical device serving web pages over HTTP.
[ ] A web server is a software that serves web pages to clients upon their request.
[x] A web server is a software that serves web pages to clients upon their request, it does this over the protocol HTTP.

Question #4

What is HTTPS?

[ ] A faster version of HTTP.
[x] A version of HTTP that secure the traffic between your browser and the website by encrypting it.
[ ] A version of HTTP that protect your personal information.

Question #5

What is a DNS?

[ ] A list of domain names.
[ ] A system that contain all Internet IPs.
[x] A system to translate domain names into IP addresses.

Question #6

What is TCP/IP?

[x] Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on the Internet or any private network.
[ ] Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on private network.
[ ] Transmission Control Protocol/Internet Protocol, is a suite of communications protocols used to interconnect network devices on the Internet.