# 0x06. Python - Classes and Objects

## Quiz questions

Question #0

In this following code, what is ?

```c
is_new
```

```c
class User:
    id = 89
    name = "no name"
    __password = None

    def __init__(self, new_name=None):
        self.is_new = True
        if new_name is not None:
            self.name = new_name
```

[ ] A private class attribute
[ ] A public class attribute
[ ] A protected class attribute
[ ] A protected instance attribute
[ ] A private instance attribute
[x] A public instance attribute

Question #1

In this following code, what is ?

```c
User
```

```c
class User:
    id = 89
    name = "no name"
    __password = None

    def __init__(self, new_name=None):
        self.is_new = True
        if new_name is not None:
            self.name = new_name
```

[ ] A method
[x] A class
[ ] An attribute
[ ] An instance
[ ] A string

Question #2

In this following code, what is ?

```c
__password
```

```c
class User:
    id = 89
    name = "no name"
    __password = None

    def __init__(self, new_name=None):
        self.is_new = True
        if new_name is not None:
            self.name = new_name
```

[x] A private class attribute
[ ] A public class attribute
[ ] A protected class attribute
[ ] A protected instance attribute
[ ] A private instance attribute
[ ] A public instance attribute

Question #3

In this following code, what is ?

```c
id
```

```c
class User:
    id = 89
    name = "no name"
    __password = None

    def __init__(self, new_name=None):
        self.is_new = True
        if new_name is not None:
            self.name = new_name
```

[ ] A private class attribute
[ ] A public instance attribute
[ ] A public class method
[ ] A public instance method
[ ] A protected class attribute
[x] A public class attribute

Question #4

What do these lines print?

```c
>>> class User:
>>>     id = 89
>>>     name = "no name"
>>>     __password = None
>>>     
>>>     def __init__(self, new_name=None):
>>>         self.is_new = True
>>>         if new_name is not None:
>>>             self.name = new_name
>>> 
>>> u = User()
>>> u.name
```

[ ] name
[ ] John
[ ] None
[x] no name

Question #5

What do these lines print?

```c
>>> class User:
>>>     id = 89
>>>     name = "no name"
>>>     __password = None
>>>     
>>>     def __init__(self, new_name=None):
>>>         self.is_new = True
>>>         if new_name is not None:
>>>             self.name = new_name
>>> 
>>> u = User()
>>> u.id
```

[x] 89
[ ] User.id
[ ] id
[ ] Nothing

Question #6

What do these lines print?

```c
>>> class User:
>>>     id = 89
>>>     name = "no name"
>>>     __password = None
>>>     
>>>     def __init__(self, new_name=None):
>>>         self.is_new = True
>>>         if new_name is not None:
>>>             self.name = new_name
>>> 
>>> u = User()
>>> u.is_new
```

[ ] is_new
[ ] False
[ ] Nothing
[x] True

Question #7

What do these lines print?

```c
>>> class User:
>>>     id = 89
>>>     name = "no name"
>>>     __password = None
>>>     
>>>     def __init__(self, new_name=None):
>>>         self.is_new = True
>>>         if new_name is not None:
>>>             self.name = new_name
>>> 
>>> u = User("John")
>>> u.name
```

[ ] name
[x] John
[ ] None
[ ] no name