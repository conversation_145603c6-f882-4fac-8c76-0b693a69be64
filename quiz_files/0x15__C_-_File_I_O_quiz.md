# 0x15. C - File I/O

## Quiz questions

Question #0

What is the  used to open a file with the mode read only?

```c
oflag
```

[ ] O_WRONLY
[ ] O_RDWR
[x] O_RDONLY

Question #1

Most of the time, on a classic, modern Linux system, what will be the value of the first file descriptor you will get after ing a new file with open (if open succeeds of course):

```c
open
```

[ ] 4
[x] 3
[ ] 0
[ ] 5
[ ] 2
[ ] 6
[ ] 1

Question #2

Which of these answers are the equivalent of  on Ubuntu 14.04 LTS? (select all correct answers):

```c
O_RDWR
```

Use  or read the headers to see the definitions/values of these macros.

```c
printf
```

Question #3

What happens if you try to write “Best” to the standard input on Ubuntu 14.04 LTS?

Just try it! :)

[ ] Nothing
[ ] The text will be printed on the terminal but I can’t pipe it
[ ] Segmentation fault
[x] The text will be printed on the terminal on the standard output

Question #4

What is the  symbolic constant for the standard input?

```c
unistd
```

[x] STDIN_FILENO
[ ] STDERR_FILENO
[ ] STDOUT_FILENO

Question #5

Without context, on Ubuntu 14.04 LTS,  is a … (please select all correct answers):

```c
write
```

Question #6

What is the  symbolic constant for the Standard error?

```c
unistd
```

[ ] STDIN_FILENO
[x] STDERR_FILENO
[ ] STDOUT_FILENO

Question #7

What is the return value of the system call  if it fails?

```c
open
```

[ ] 0
[ ] 98
[x] -1

Question #8

When I am using  -> the | are bitwise operators.

```c
O_WRONLY | O_CREAT | O_APPEND
```

[x] True
[ ] False

Question #9

why? #AlwaysAskWhy

[x] Because most of the time, I will already have stdin (value 0), stdout (value 1) and stderr (value 2) opened when my program starts executing.
[ ] Because this will be the first opened file descriptor and in CS we start counting starting from 0
[ ] Because this will be the second opened file descriptor for my process
[ ] Because this will be the third opened file descriptor for my process
[ ] I don’t care I never ask why, just let me access the tasks!
[ ] Because this will be the first opened file descriptor and we start counting starting from 1

Question #10

What is the  symbolic constant for the standard output?

```c
unistd
```

[ ] STDIN_FILENO
[ ] STDERR_FILENO
[x] STDOUT_FILENO

Question #11

What system call would you use to write to a file descriptor? (select all correct answers)

[ ] printf
[x] write
[ ] fprintf

Question #12

What is the correct combination of s used to open a file with the mode write only, create it if it doesn’t exist and append new content at the end if it already exists?

```c
oflag
```

[ ] O_WRONLY
[x] O_WRONLY | O_CREAT | O_APPEND
[ ] O_WRONLY | O_CREAT | O_EXCL
[ ] O_RDWR | O_CREAT | O_APPEND

Question #13

What is the  used to open a file in mode read + write?

```c
oflag
```

[ ] O_WRONLY
[x] O_RDWR
[ ] O_RDONLY

Question #14

is  a function or a system call? (select all valid answers)

```c
open
```