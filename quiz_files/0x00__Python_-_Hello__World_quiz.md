# 0x00. Python - Hello, World

## Quiz questions

Question #0

What does this command line print?

```c
>>> print(f"{98} Battery street, {'San Francisco'}")
```

[ ] “98 Battery street, San Francisco”
[x] 98 Battery street, San Francisco
[ ] 8 Battery street, San
[ ] San Francisco Battery street, 98

Question #1

Who created Python?

[ ] <PERSON>
[x] <PERSON>
[ ] <PERSON><PERSON><PERSON>

Question #2

What does this command line print?

```c
>>> a = "Python is cool"
>>> print(a[7:-5])
```

[ ] si
[ ] on
[ ] Python
[x] is
[ ] nohtyP

Question #3

What does this command line print?

```c
>>> print(f"{98} Battery street")
```

[x] 98 Battery street
[ ] 9 Battery street
[ ] f"98 Battery street"
[ ] 8 Battery street

Question #4

What does this command line print?

```c
>>> a = "Python is cool"
>>> print(a[-2])
```

[ ] ol
[x] o
[ ] l
[ ] Nothing

Question #5

What does this command line print?

```c
>>> print("Holberton school")
```

[ ] Holberton
[x] Holberton school
[ ] “<PERSON><PERSON>on school”
[ ] ‘Holberton school’

Question #6

What does this command line print?

```c
>>> a = "Python is cool"
>>> print(a[7:])
```

[ ] Python i
[ ] cool
[ ] Python is
[x] is cool

Question #7

What does this command line print?

```c
>>> a = "Python is cool"
>>> print(a[0:6])
```

[x] Python
[ ] Python is
[ ] Pytho
[ ] Python is cool

Question #8

What does this command line print?

```c
>>> a = "Python is cool"
>>> print(a[4])
```

[ ] P
[x] o
[ ] n
[ ] h

Question #9

What does this command line print?

```c
>>> a = "Python is cool"
>>> print(a[:6])
```

[ ] Pytho
[ ] Python is
[x] Python
[ ] is cool