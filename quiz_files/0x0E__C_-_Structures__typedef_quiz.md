# 0x0E. C - Structures, typedef

## Quiz questions

Question #0

Given this code:

To set the member y of my variable my_point to 98, I can do (select all valid answers):

```c
struct point {
   int x;
   int y;
};
struct point my_point = { 3, 7 };
struct point *p = &my_point;
```

Question #1

You should write documentation for all the structures you create

Question #2

Those two codes do the same thing:

```c
typedef struct point point;
struct point {
   int    x;
   int    y;
};
point p = {1, 2};
```

```c
typedef struct point point;
struct point {
   int    x;
   int    y;
};
point p = { .y = 2, .x = 1 };
```

[x] True
[ ] False: the members of the structures will not have the same values
[ ] False: the second does not compile

Question #3

The general syntax for a struct declaration in C is:

```c
struct tag_name {
   type member1;
   type member2;
   /* declare as many members as desired, but the entire structure size must be known to the compiler. */
};
```

[x] True
[ ] Maybe
[ ] False