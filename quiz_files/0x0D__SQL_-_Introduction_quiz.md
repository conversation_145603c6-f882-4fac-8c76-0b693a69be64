# 0x0D. SQL - Introduction

## Quiz questions

Question #0

What does DML stand for?

[ ] Database Manipulation Language
[x] Data Manipulation Language
[ ] Document Manipulation Language
[ ] Document Model Language

Question #1

What does SQL stand for?

[ ] Sequences of Query Logic
[ ] Solid Query Language
[x] Structured Query Language
[ ] Structured Question Language

Question #2

How do you list all  records with age > 21 in this table?

```c
users
```

```c
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| Table | Create Table                                                                                                                  |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| users | CREATE TABLE `users` (
  `id` int(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `age` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
```

[ ] SELECT * FROM users WHERE age < 21;
[x] SELECT * FROM users WHERE age > 21;
[ ] SELECT * FROM users WHERE age IS UP TO 21;
[ ] SELECT * FROM users WHERE age BETWEEN 21 AND 89;

Question #3

What does DDL stand for?

[x] Data Definition Language
[ ] Data Document Language
[ ] Database Definition Language
[ ] Document Data Language

Question #4

What is a relational database? (please select all correct answers)

Question #5

How do you change the name of the  record with id = 89 to Holberton?

```c
users
```

```c
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| Table | Create Table                                                                                                                  |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| users | CREATE TABLE `users` (
  `id` int(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `age` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
```

[x] UPDATE users SET name = “Holberton” WHERE id = 89;
[ ] UPDATE users SET name = “Holberton”;
[ ] CHANGE users SET name = “Holberton” WHERE id = 89;

Question #6

How to you add a new record in the table ?

```c
users
```

```c
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| Table | Create Table                                                                                                                  |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| users | CREATE TABLE `users` (
  `id` int(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `age` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
```

[ ] INSERT users (id, name, age) VALUES (2, “Betty”, 30);
[x] INSERT INTO users (id, name, age) VALUES (2, “Betty”, 30);
[ ] INSERT INTO users (id, name) VALUES (2, “Betty”, 30);
[ ] INSERT INTO users (id, name, age) VALUES (2, “Betty”);

Question #7

How do you list all  in this table?

```c
users
```

```c
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| Table | Create Table                                                                                                                  |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| users | CREATE TABLE `users` (
  `id` int(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `age` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
```

[ ] DELETE * FROM users;
[ ] SELECT ALL users;
[x] SELECT * FROM users;

Question #8

How do you delete the  record with id = 89 in this table?

```c
users
```

```c
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| Table | Create Table                                                                                                                  |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
| users | CREATE TABLE `users` (
  `id` int(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `age` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 |
+-------+-------------------------------------------------------------------------------------------------------------------------------+
```

[ ] DELETE users WHERE id = 89;
[ ] DELETE FROM users;
[x] DELETE FROM users WHERE id = 89;
[ ] DELETE FROM users WHERE id IS EQUAL TO 89;