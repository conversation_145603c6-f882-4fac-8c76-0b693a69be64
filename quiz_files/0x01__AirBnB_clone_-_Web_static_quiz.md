# 0x01. AirBnB clone - Web static

## Quiz questions

Question #0

Is the following HTML markup valid?

(elements are correctly tagged, we don’t care about  here)

```c
!Doctype
```

is an empty element

```c
<img />
```

```c
<html>
    <head>
    </head>
    <body>
        <img src="logo.png" />
    </body>
</html>
```

[x] Yes
[ ] No

Question #1

In the following code, is the text  red?

```c
Best School
```

css:

html:

```c
h3 span.text,
h1,
div.title {
    color: red;
}
```

```c
<h1>Best School</h1>
```

[x] Yes
[ ] No

Question #2

Is following CSS syntax valid?

CSS vs SCSS

```c
body {
    color: #FF0000;
}

* {
    font-size: 14px;
    text-align: center;

    h1 {
        margin: 30px;
    }
}
```

[ ] Yes
[x] No

Question #3

Is following CSS syntax valid?

```c
body {
    color: #FF0000;
}

h1.title {
    font-size: 16px;
}
```

[x] Yes
[ ] No

Question #4

Is following CSS syntax valid?

```c
body {
    color: #FF0000;
}

div.filters h2 {
    font-size: 16px;
}
```

[x] Yes
[ ] No

Question #5

Is the following HTML markup valid?

(elements are correctly tagged, we don’t care about  here)

```c
!Doctype
```

```c
<html></html>
```

[x] Yes
[ ] No

Question #6

In the following code, is the text  red?

```c
Best School
```

css:

html:

CSS selector math

```c
h1 .my_title {
    color: green;
}

.my_title {
    color: red;
}
```

```c
<h1>
    <span class="my_title">Best School</span>
</h1>
```

[ ] Yes
[x] No

Question #7

In the following code, is the text  red?

```c
Best School
```

css:

html:

```c
h1.title {
    color: red;
}
```

```c
<h1>Best School</h1>
```

[ ] Yes
[x] No

Question #8

Is following CSS syntax valid?

Universal selectors

```c
body {
    color: #FF0000;
}

* {
    font-size: 14px;
}
```

[x] Yes
[ ] No

Question #9

Is the following HTML markup valid?

(elements are correctly tagged, we don’t care about  here)

```c
!Doctype
```

Each HTML tag must be closed

```c
<html>
    <head>
    </head>
    <body>
    <body>
</html>
```

[ ] Yes
[x] No

Question #10

Is following CSS syntax valid?

Betty for CSS!

```c
body {
    color: #FF0000;
}

* {
    font-size: 14px;
    font-weight: 400
    text-align: center;
}
```

[ ] Yes
[x] No

Question #11

Is following CSS syntax valid?

```c
body {
    color: #FF0000;
}

div.filters p.title h2 span.text.big {
    font-size: 20px;
}
```

[x] Yes
[ ] No

Question #12

In the following code, is the text  red?

```c
Best School
```

css:

html:

CSS selector math

```c
h1 {
    color: green;
}

span.my_title {
    color: red;
}
```

```c
<h1>
    <span class="my_title">Best School</span>
</h1>
```

[x] Yes
[ ] No

Question #13

Is following CSS syntax valid?

and padding support 4 different syntaxes: margin

```c
margin
```

```c
body {
    color: #FF0000;
}

* {
    font-size: 14px;
    text-align: center;
    margin: 30px 12px 4px;
}
```

[x] Yes
[ ] No

Question #14

In the following code, is the text  red?

```c
Best School
```

css:

html:

```c
h1 div.title {
    color: red;
}
```

```c
<h1>Best School</h1>
```

[ ] Yes
[x] No

Question #15

Is the following HTML markup valid?

(elements are correctly tagged, we don’t care about  here)

```c
!Doctype
```

```c
<html>
    <head>
    </head>
    <body>
    </body>
</html>
```

[x] Yes
[ ] No

Question #16

Is following CSS syntax valid?

```c
body {
    color: #FF0000;
}

h3,
div.full_text,
div.small_text h4,
div.filters p.title {
    font-size: 20px;
}
```

[x] Yes
[ ] No

Question #17

Is following CSS syntax valid?

```c
body {
    color: #FF0000;
}
```

[x] Yes
[ ] No

Question #18

In the following code, is the text  red?

```c
Best School
```

css:

html:

```c
h1 {
    color: red;
}
```

```c
<h1>Best School</h1>
```

[x] Yes
[ ] No

Question #19

Is the following HTML markup valid?

(elements are correctly tagged, we don’t care about  here)

```c
!Doctype
```

Number of quotes is important!

```c
<html>
    <head>
    </head>
    <body>
        <h1>
            <a href="www.google.com'>Google</a>
        </h1>
    </body>
</html>
```

[ ] Yes
[x] No

Question #20

In the following code, is the text  red?

```c
Best School
```

css:

html:

```c
h2 {
    color: red;
}
```

```c
<h1>Best School</h1>
```

[ ] Yes
[x] No

Question #21

Is following CSS syntax valid?

separates multiple selector, without it’s specific selector

```c
,
```

```c
body {
    color: #FF0000;
}

h3,
div.full_text
div.small_text h4
div.filters p.title {
    font-size: 20px;
}
```

[x] Yes
[ ] No

Question #22

Is the following HTML markup valid?

(elements are correctly tagged, we don’t care about  here)

```c
!Doctype
```

“Always close something before opening a new thing”

```c
<html>
    <head>
    </head>
    <body>
        <h1>Best <b>School</h1></b>
    </body>
</html>
```

[ ] Yes
[x] No

Question #23

Is the following HTML markup valid?

(elements are correctly tagged, we don’t care about  here)

```c
!Doctype
```

```c
<html>
    <head>
    </head>
    <body>
        <h1>
            <a href="www.google.com">Go to <b>Google</b>
        </h1>
    </body>
</html>
```

[ ] Yes
[x] No