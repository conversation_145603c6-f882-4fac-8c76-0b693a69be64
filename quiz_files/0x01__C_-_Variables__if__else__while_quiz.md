# 0x01. C - Variables, if, else, while

## Quiz questions

Question #0

Which of the following are valid  statements in ANSI C and Betty-compliant? (Considering a and b two variables of type int)

```c
if
```

Please select all correct answers

```c
if ((((((a > b))))))
{
  return (a);
}
```

```c
if a > b
{
  return (a);
}
```

```c
if {a > b}
(
  return {a};
)
```

```c
if (a > b)
  return (a);
```

```c
if (a > b)
{
  return (a);
}
```

Question #1

What is the size of the  data type?

```c
unsigned int
```

[ ] 1 byte
[x] 4 bytes
[ ] 2 bytes
[ ] 8 bytes

Question #2

What is the size of the  data type?

```c
float
```

[ ] 1 byte
[x] 4 bytes
[ ] 2 bytes
[ ] 8 bytes

Question #3

What is the size of the  data type?

```c
char
```

[x] 1 byte
[ ] 4 bytes
[ ] 2 bytes
[ ] 8 bytes

Question #4

Which of the following are valid  or do/while statements in ANSI C and Betty-compliant? (Considering a and b two variables of type int)

```c
while
```

Please select all correct answers

```c
a = 0;
while (a < b)
(
    printf("%d\n", a);
    a++;
)
```

```c
while (a = 0; a < b; a++)
{
    printf("%d\n", a);
}
```

```c
a = 0;
do {
    printf("%d\n", a);
    a++;
} while (a < b);
```

```c
a = 0;
while (a < b)
    printf("%d\n", a++);
```

```c
a = 0;
do while (a < b)
{
    printf("%d\n", a);
    a++;
}
```

```c
a = 0;
while (a < b)
{
    printf("%d\n", a);
    a++;
}
```

Question #5

Which of the following are valid  statements in ANSI C and Betty-compliant? (Considering a and b two variables of type int)

```c
for
```

Please select all correct answers

```c
a = 0;
for (a < b;;)
{
    printf("%d\n", a++);
}
```

```c
for (a = 0; a < b; a++)
{
    printf("%d\n", a);
}
```

```c
for (int a = 0; a < b; a++)
{
    printf("%d\n", a);
}
```

```c
a = 0;
for (; a < b;)
{
    printf("%d\n", a++);
}
```

```c
for (a = 0; a < b; a++)
    printf("%d\n", a);
```