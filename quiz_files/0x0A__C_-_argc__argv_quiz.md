# 0x0A. C - argc, argv

## Quiz questions

Question #0

What is ?

```c
argv[argc]
```

[ ] The program name
[x] NULL
[ ] The first command line argument
[ ] The last command line argument
[ ] It does not always exist

Question #1

What is ?

```c
argc
```

Question #2

What is

```c
argv[0]
```

[ ] NULL
[ ] The first command line argument
[ ] It does not always exist
[x] The program name

Question #3

What is ?

```c
argv
```

Question #4

In the following command, what is ?

```c
argv[2]
```

```c
$ ./argv "My School" "is fun"
```

[ ] is
[ ] School
[ ] My School
[ ] NULL
[ ] fun
[ ] My
[ ] My School is fun
[ ] ./argv
[x] is fun

Question #5

In the following command, what is ?

```c
argv[2]
```

```c
$ ./argv My School is fun
```

[ ] is
[x] School
[ ] My School
[ ] NULL
[ ] fun
[ ] My
[ ] My School is fun
[ ] ./argv
[ ] is fun

Question #6

In the following command, what is ?

```c
argv[2]
```

```c
$ ./argv "My School is fun"
```

[ ] is
[ ] School
[ ] My School
[x] NULL
[ ] fun
[ ] My
[ ] My School is fun
[ ] ./argv
[ ] is fun