# 0x02. Shell, I/O Redirections and filters

## Quiz questions

Question #0

Which symbol should I use to start a comment?

[ ] &
[ ] //
[ ] !
[x] #

Question #1

Which symbol should I use to redirect the standard output to a file (overwrite the file)?

[ ] >>
[x] >
[ ] 2>
[ ] &

Question #2

Which symbol should I use to redirect the error output to the standard output?

[x] 2>&1
[ ] 1>&2
[ ] 2>

Question #3

Which symbol should I use to redirect the standard output to a file (appending to the file)?

[x] >>
[ ] >
[ ] 2>
[ ] &

Question #4

Which command should I use to display the last 11 lines of a file?

[ ] head -n 11 my_file
[x] tail -n 11 my_file
[ ] head 11 my_file
[ ] tail 11 my_file

Question #5

Which command should I use to display the entire file content?

[ ] grep
[ ] head
[x] cat
[ ] tail

Question #6

Which symbol should I use to escape a special character?

[x] \
[ ] $
[ ] !
[ ] #