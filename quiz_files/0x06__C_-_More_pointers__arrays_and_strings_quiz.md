# 0x06. C - More pointers, arrays and strings

## Quiz questions

Question #0

What is wrong with the following code?

```c
int n = 5;
int array[5];
int i = 3;

array[n] = i;
```

[ ] Nothing is wrong
[ ] The array array is not entirely initialized
[ ] It is impossible to declare the variable array this way
[x] It is not possible to access array[n]

Question #1

What is the type of ?

```c
var
```

```c
var = "Best";
```

[ ] string
[ ] int *
[x] char *

Question #2

What is wrong with the following code?

```c
int n = 5;
int array[n];
int i = 3;

array[n] = i;
```

[ ] Nothing is wrong
[ ] The array array is not entirely initialized
[x] It is impossible to declare the variable array this way
[ ] It is not possible to access array[n]

Question #3

What is wrong with the following code?

```c
int n = 5;
int array[10];
int i = 3;

array[n] = i;
```

[x] Nothing is wrong
[ ] The array array is not entirely initialized
[ ] It is impossible to declare the variable array this way
[ ] It is not possible to access array[n]

Question #4

What is/are the difference(s) between the two following variables? (Except their names)

```c
char *s1 = "";
char *s2 = NULL;
```

Question #5

Why is it important to reserve enough space for an extra character when declaring/allocating a string?

[ ] In case we need one
[x] For the null byte (end of string)
[ ] For memory alignment
[ ] For fun

Question #6

What happens when one tries to dereference a pointer to NULL?

[ ] Nothing
[ ] Kernel panic
[x] Segmentation fault
[ ] World War Z