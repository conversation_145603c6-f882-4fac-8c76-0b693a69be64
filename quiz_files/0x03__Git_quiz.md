# 0x03. Git

## Quiz questions

Question #0

You have the following files in your project directory:

You’ve edited  and you want to add it to your GitHub repo. What is the correct command to add only 0-test?

```c
0-test
```

You should learn what each of these commands would actually do if you were to execute them!

```c
julien@ubuntu:/tmp/git_project$ ls
0-test  0-test~ #0-test# file1  file2
```

[ ] git add .
[x] git add 0-test
[ ] git add -N 0-test

Question #1

What command can you use to see what changes have been staged, which haven’t, and which files aren’t being tracked by Git?

[ ] git init
[ ] git checkout
[x] git status