# 0x07. C - Even more pointers, arrays and strings

## Quiz questions

Question #0

In this following code, what is the value of ?

```c
a[3][0]
```

```c
int a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};
```

[x] 7
[ ] {7, 8}
[ ] 8
[ ] 5

Question #1

What is the size of  in this code?

```c
p
```

```c
int *p;
```

[ ] 4 bytes
[ ] 16 bytes
[x] 8 bytes

Question #2

What is stored inside a pointer to a pointer to an int?

[ ] An address where an int is stored
[x] An address where an address is stored
[ ] An int

Question #3

In this following code, what is the value of ?

```c
a[0][0]
```

```c
int a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};
```

[x] 1
[ ] 3
[ ] 2
[ ] 4

Question #4

What is the size of  in this code?

```c
p
```

```c
int **p;
```

[ ] 4 bytes
[ ] 16 bytes
[x] 8 bytes

Question #5

What is the size of  in this code?

```c
*p
```

```c
int *p;
```

[x] 4 bytes
[ ] 16 bytes
[ ] 8 bytes

Question #6

In this following code, what is the value of ?

```c
a[1][1]
```

```c
int a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};
```

[ ] 1
[ ] 3
[ ] 2
[x] 4

Question #7

In this following code, what is the value of ?

```c
a[3][1]
```

```c
int a[5][2] = {{1, 2}, {3, 4}, {5, 6}, {7, 8}, {9, 10}};
```

[ ] 7
[ ] {7, 8}
[ ] 9
[x] 8

Question #8

What is the size of  in this code?

```c
*p
```

```c
int **p;
```

[ ] 4 bytes
[ ] 16 bytes
[x] 8 bytes