# 0x02. C - Functions, nested loops

## Quiz questions

Question #0

What is the ASCII value of ?

```c
J
```

[ ] 70
[x] 74
[ ] 72
[ ] 76

Question #1

What is the result of ?

```c
89 % 7
```

[ ] 0
[ ] 3
[ ] 2
[x] 5

Question #2

Which of these loop statements don’t exist?

Question #3

What is the result of ?

```c
12 % 3
```

[x] 0
[ ] 2
[ ] 1
[ ] 3

Question #4

Wht is the ASCII value of a?

```c
a
```

[x] 97
[ ] 12
[ ] 65
[ ] 1

Question #5

What is the result of ?

```c
12 % 10
```

[ ] 0
[x] 2
[ ] 1
[ ] 3

Question #6

What is the SCII value of A?

```c
A
```

[ ] 97
[ ] 12
[x] 65
[ ] 1

Question #7

What is the result of ?

```c
12 % 2
```

[x] 0
[ ] 2
[ ] 1

Question #8

What is the ASCII value of ?

```c
-
```

[x] 45
[ ] 47
[ ] 3

Question #9

What is the ASCII value of ?

```c
0
```

[ ] 79
[x] 48
[ ] 0

Question #10

What is the ASCII value of ?

```c
5
```

[ ] 50
[ ] 5
[x] 53