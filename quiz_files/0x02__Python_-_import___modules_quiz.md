# 0x02. Python - import & modules

## Quiz questions

Question #0

What do these lines print?

```c
>>> def my_function(counter=89):
>>>     print("Counter: {}".format(counter))
>>> 
>>> my_function(12)
```

[x] Counter: 12
[ ] Counter: 101
[ ] Counter: 89

Question #1

What do these lines print?

```c
>>> def my_function():
>>>     print("In my function")
>>> 
>>> my_function()
```

[ ] “In my function”
[ ] function my_function at …
[x] In my function
[ ] Nothing

Question #2

What do these lines print?

```c
>>> def my_function(counter):
>>>     print("Counter: {}".format(counter))
>>> 
>>> my_function(12)
```

[ ] Counter: counter
[x] Counter: 12
[ ] Counter: c

Question #3

What do these lines print?

```c
>>> def my_function():
>>>     print("In my function")
>>> 
>>> my_function
```

[ ] “In my function”
[x] function my_function at …
[ ] In my function
[ ] Nothing

Question #4

What do these lines print?

```c
>>> def my_function(counter=89):
>>>     return counter + 1
>>> 
>>> print(my_function())
```

[ ] 1
[x] 90
[ ] 89
[ ] 891

Question #5

What do these lines print?

```c
>>> def my_function(counter=89):
>>>     print("Counter: {}".format(counter))
>>> 
>>> my_function()
```

[ ] Counter: 12
[ ] Counter: 101
[x] Counter: 89