# 0x12. C - Singly linked lists

## Quiz questions

Question #0

In a singly linked list, what are possible directions to traverse it? (select all possible answers)

[x] Forward
[ ] Backward

Question #1

What’s a node? (select all possible answers)

Question #2

What’s the “tail” of a linked list?

[x] It’s the node with the pointer to the next equals to NULL
[ ] It’s the node with the highest value
[ ] It’s the first node
[ ] It’s the node with the lowest value

Question #3

What’s the “head” of a linked list?

[ ] It’s the node with the lowest value
[ ] It’s the last node
[x] It’s the first node
[ ] It’s the node with the pointer to the next equals to NULL
[ ] It’s the node with the highest value

Question #4

Arrays Vs Linked Lists: select all true statements