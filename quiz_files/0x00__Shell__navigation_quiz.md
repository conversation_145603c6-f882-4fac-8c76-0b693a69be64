# 0x00. Shell, navigation

## Quiz questions

Question #0

Which command should you use to display the content of a file?

[ ] ls
[ ] pwd
[ ] cd
[ ] touch
[x] less

Question #1

Which command should you use to display the current path of your current directory?

[ ] touch
[x] pwd
[ ] ls
[ ] less
[ ] cd

Question #2

Which command should you use to create a directory?

[ ] cp
[ ] rmdir
[ ] mv
[x] mkdir
[ ] rm

Question #3

Which command should you use to change directory?

[ ] less
[ ] touch
[ ] pwd
[x] cd
[ ] ls

Question #4

Which command should you use to delete a directory?

[ ] mkdir
[ ] cp
[ ] rm
[x] rmdir
[ ] mv

Question #5

Which command should you use to list all files of your current directory?

[ ] less
[ ] pwd
[ ] cd
[ ] touch
[x] ls

Question #6

Which command should you use to delete a file (or directory if additional argument)?

[ ] mkdir
[ ] rmdir
[x] rm
[ ] cp
[ ] mv

Question #7

Which command should you use to copy a file (or directory if additional argument)?

[x] cp
[ ] mkdir
[ ] mv
[ ] rmdir
[ ] rm

Question #8

Which command should you use to create an empty file?

[ ] less
[ ] pwd
[ ] cd
[ ] ls
[x] touch

Question #9

Which command should you use to rename or move a file (or directory)?

[ ] mkdir
[ ] cp
[ ] rm
[ ] rmdir
[x] mv