# 0x0B. C - malloc, free

## Quiz questions

Question #0

How many bytes will this statement allocate?

```c
malloc(sizeof(unsigned int) * 2)
```

[ ] 2
[x] 8
[ ] 4

Question #1

What is Valgrind?

[ ] A container service
[ ] It’s a new step when I compile with gcc
[x] It’s a program to validate memory allocation
[ ] It’s a program to test a C program in a specific environment

Question #2

How many bytes will this statement allocate?

```c
malloc(sizeof(int) * 10)
```

[x] 40
[ ] 10
[ ] 32

Question #3

How many bytes will this statement allocate?

```c
malloc(sizeof(int) * 4)
```

[ ] 4
[x] 16
[ ] 8
[ ] 32

Question #4

How many bytes will this statement allocate?

```c
malloc(10)
```

[ ] 2
[ ] 4
[x] 10
[ ] 40

Question #5

How many bytes will this statement allocate?

```c
malloc(sizeof(char) * 10)
```

[x] 10
[ ] 40
[ ] 20

Question #6

How many bytes will this statement allocate?

```c
malloc((sizeof(char) * 10) + 1)
```

[ ] 20
[ ] 1
[ ] 21
[x] 11
[ ] 10