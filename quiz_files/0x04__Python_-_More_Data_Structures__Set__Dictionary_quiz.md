# 0x04. Python - More Data Structures: Set, Dictionary

## Quiz questions

Question #0

What do these lines print?

```c
>>> a = { 'id': 89, 'name': "<PERSON>" }
>>> a.get('age', 0)
```

[ ] ‘age’
[x] 0
[ ] Nothing
[ ] 89

Question #1

What do these lines print?

```c
>>> for i in [1, 3, 4, 2]:
>>>     print(i, end=" ")
```

[ ] 0 1 2 3
[x] 1 3 4 2
[ ] 1 2 3 4
[ ] 1 3 4 2 0

Question #2

What do these lines print?

```c
>>> a = { 'id': 89, 'name': "<PERSON>" }
>>> a['id']
```

[x] 89
[ ] id
[ ] a[‘id’]
[ ] John
[ ] ‘id’

Question #3

What do these lines print?

```c
>>> for i in range(1, 4):
>>>     print(i, end=" ")
```

[x] 1 2 3
[ ] 1 2 3 4
[ ] 0 1 2 3

Question #4

What do these lines print?

```c
>>> a = { 'id': 89, 'name': "<PERSON>" }
>>> a.get('age')
```

[ ] 12
[ ] ‘age’
[ ] 89
[x] Nothing
[ ] Not found

Question #5

What do these lines print?

```c
>>> for i in [1, 2, 3, 4]:
>>>     print(i, end=" ")
```

[ ] 0 1 2 3
[ ] 1 2 3
[ ] 0 1 2 3 5
[x] 1 2 3 4

Question #6

What do these lines print?

```c
>>> for i in ["Hello", "Holberton", "School", 98]:
>>>     print(i, end=" ")
```

[ ] 0 1 2 3
[x] Hello Holberton School 98
[ ] 1 2 3 4

Question #7

What do these lines print?

```c
>>> a = { 'id': 89, 'name': "John" }
>>> a.get('id')
```

[x] 89
[ ] id
[ ] a[‘id’]
[ ] John
[ ] ‘id’

Question #8

What do these lines print?

```c
>>> for i in range(0, 3):
>>>     print(i, end=" ")
```

[ ] 1 2 3
[x] 0 1 2
[ ] 0 1 2 3

Question #9

What do these lines print?

```c
>>> a = { 'id': 89, 'name': "John", 'projects': [1, 2, 3, 4] }
>>> a.get('projects')[3]
```

[ ] 3
[x] 4
[ ] [1, 2, 3, 4]
[ ] [3]
[ ] [4]

Question #10

What do these lines print?

```c
>>> a = { 'id': 89, 'name': "John", 'projects': [1, 2, 3, 4] }
>>> a.get('projects')
```

[ ] list
[ ] ‘projects’
[ ] [1]
[ ] Nothing
[x] [1, 2, 3, 4]

Question #11

What do these lines print?

```c
>>> a = { 'id': 89, 'name': "John", 'projects': [1, 2, 3, 4], 'friends': [ { 'id': 82, 'name': "Bob" }, { 'id': 83, 'name': "Amy" } ] }
>>> a.get('friends')[-1].get("name")
```

[ ] Bob
[ ] 89
[x] Amy
[ ] Nothing
[ ] [ { ‘id’: 82, ‘name’: “Bob” }, { ‘id’: 83, ‘name’: “Amy” } ]