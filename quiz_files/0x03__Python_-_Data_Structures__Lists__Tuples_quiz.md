# 0x03. Python - Data Structures: Lists, Tuples

## Quiz questions

Question #0

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> len(a)
```

[ ] 2
[ ] 6
[x] 4
[ ] 8

Question #1

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> a[0]
```

[ ] [1, 2]
[x] 1
[ ] [1]
[ ] [1, 2, 3, 4]
[ ] 2

Question #2

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> b = a
>>> a[2] = 10
>>> a
```

[ ] a
[ ] [1]
[ ] [1, 2, 3, 4]
[ ] b
[x] [1, 2, 10, 4]

Question #3

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> a[-3]
```

[ ] -3
[x] 2
[ ] [4, 3]

Question #4

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> b = a
>>> a[2] = 10
>>> b
```

[ ] a
[ ] [1]
[ ] [1, 2, 3, 4]
[ ] b
[x] [1, 2, 10, 4]

Question #5

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> a[-1]
```

[ ] -1
[x] 4
[ ] 2
[ ] [4, 3, 2, 1]

Question #6

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> b = a
>>> b
```

[x] [1, 2, 3, 4]
[ ] 1
[ ] [1]
[ ] a

Question #7

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> a[1:3]
```

[ ] [1, 2, 3]
[x] [2, 3]
[ ] [1, 2]

Question #8

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> a.append(5)
>>> len(a)
```

[ ] 2
[ ] 6
[x] 5

Question #9

What do these lines print?

```c
>>> a = [1, 2, 3, 4]
>>> a[2] = 10
>>> a
```

[ ] [1, 2, 3, 4]
[x] [1, 2, 10, 4]
[ ] [1, 10, 3, 4]
[ ] [1, 2, 10, 10]