# 0x1B. C - Sorting algorithms & Big O

## Quiz questions

Question #0

What is the time complexity accessing the nth element in an unsorted Python 3 list?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #1

What is the time complexity of this function / algorithm?

```c
void f(unsigned int n)
{
    int i;

    for (i = 1; i < n; i = i * 2)
    {
        printf("[%d]\n", i);
    }
}
```

[x] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #2

What is the time complexity of accessing the nth element of a doubly linked list?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #3

Assuming you have a pointer to the node to set the value of, what is the time complexity of setting the value of the nth element in a doubly linked list?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #4

What is the best case time complexity of insertion in a hash table with the implementation you used during the previous Hash Table C project (chaining)?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #5

What is the time complexity of this function / algorithm?

```c
void f(int n)
{
     int i;
     int j;

     for (i = 0; i < n; i++)
     {
          for (j = i + 1; j < n; j++)
          {
               printf("[%d] [%d]\n", i, j);
          }
     }
}
```

[ ] O(log(n))
[x] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #6

What is the time complexity of inserting at index n on an unsorted array?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #7

What is the time complexity of removing at index n in an unsorted array?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #8

What is the time complexity of this function / algorithm?

```c
void f(int n)
{
    printf("n = %d\n", n);
}
```

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #9

Assuming you have a pointer to the node to remove, what is the time complexity of removing the nth element of a doubly linked list?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #10

What is the time complexity of searching for an element in a singly linked list of size n?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #11

What is the time complexity of searching for an element in a stack of size n?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #12

What is the time complexity of this function / algorithm?

```c
int Fibonacci(int number)
{
    if (number <= 1) return number;

    return Fibonacci(number - 2) + Fibonacci(number - 1);
}
```

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[x] O(2^n)
[ ] O(n)

Question #13

What is the time complexity of searching for an element in a queue of size n if you are given a pointer to both the head and the tail of the queue?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #14

What is the time complexity of this function / algorithm?

```c
void f(int n)
{
    int i;

    for (i = 0; i < n; i += 98)
    {
        printf("[%d]\n", i);
    }
}
```

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #15

What is the time complexity of this function / algorithm?

```c
def func(n):
    a=5
    b=6
    c=10
    for i in range(n):
        for j in range(n):
            x = i * i
            y = j * j
            z = i * j
    for k in range(n):
        w = a*k + 45
        v = b*b
    d = 33
```

[ ] O(log(n))
[x] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #16

What is the time complexity of searching for an element in an unsorted array of size n?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #17

Assuming you have a pointer to the node to insert, what is the time complexity of inserting after the nth element of a doubly linked list?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #18

What is the time complexity of removing at index n from an unsorted Python 3 list?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #19

What is the time complexity of “pushing” an element into a queue if you are given a pointer to both the head and the tail of the queue?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #20

What is the time complexity of accessing the nth element on an unsorted array?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #21

What is the time complexity of “popping” an element in a queue if you are given a pointer to both the head and the tail of the queue?

[ ] O(nlog(n))
[x] O(1)
[ ] O(n!)
[ ] O(log(n))
[ ] O(2^n)
[ ] O(n)

Question #22

What is the time complexity of setting the value of the nth element in a singly linked list? (Assuming you have a pointer to the node to set the value of)

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #23

What is the time complexity of searching for an element - worst case - in a hash table with the implementation you used during the previous Hash Table C project (chaining)?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #24

What is the time complexity of this function / algorithm?

```c
void f(int n)
{
    int i;

    for (i = 0; i < n; i++)
    {
        printf("[%d]\n", i);
    }
}
```

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #25

What is the time complexity of searching for an element in an unsorted Python 3 list of size n?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #26

What is the time complexity of setting a value at index n in an unsorted array?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #27

What is the time complexity of this function / algorithm?

```c
void f(int n)
{
    int i;
    int j;

    for (i = 0; i < n; i++)
    {
        if (i % 2 == 0)
        {
            for (j = 1; j < n; j = j * 2)
            {
                printf("[%d] [%d]\n", i, j);
            }
        }
        else
        {
            for (j = 0; j < n; j = j + 2)
            {
                printf("[%d] [%d]\n", i, j);
            }
        }
    }
}
```

[ ] O(log(n))
[x] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #28

What is the time complexity of worst case deletion from a hash table with the implementation you used during the previous Hash Table C project (chaining)?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #29

What is the time complexity of inserting after the nth element of a singly linked list? (Assuming you have a pointer to the node to insert)

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #30

What is the time complexity of setting value at index n in an unsorted Python 3 list?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #31

What is the time complexity of searching for an element in a doubly linked list of size n?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #32

What is the time complexity of this function / algorithm?

```c
foreach($numbers as $number)
{
    echo $number;
}
```

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #33

What is the worst case time complexity of insertion in a hash table with the implementation you used during the previous Hash Table C project (chaining)?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #34

What is the time complexity of best case deletion from a hash table with the implementation you used during the previous Hash Table C project (chaining)?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #35

What is the time complexity of the “push” operation onto a stack?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #36

What is the time complexity of removing the nth element of a singly linked list? (Assuming you have a pointer to the node to remove)

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #37

What is the best case time complexity searching for an element in a hash table with the implementation you used during the previous Hash Table C project (chaining)?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #38

What is the time complexity of inserting into an unsorted Python 3 list at index n?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #39

What is the time complexity of accessing the nth element of a singly linked list?

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #40

What is the time complexity of the “pop” operation onto a stack?

[ ] O(log(n))
[ ] O(n^2)
[x] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)

Question #41

What is the time complexity of this function / algorithm?

```c
var factorial = function(n) {
    if(n == 0) {
        return 1
    } else {
        return n * factorial(n - 1);
    }
}
```

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[ ] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[x] O(n)

Question #42

What is the time complexity of this function / algorithm?

```c
void f(unsigned int n)
{
    int i;
    int j;

    for (i = 0; i < n; i++)
    {
        for (j = 1; j < n; j = j * 2)
        {
            printf("[%d] [%d]\n", i, j);
        }
    }
}
```

[ ] O(log(n))
[ ] O(n^2)
[ ] O(1)
[x] O(nlog(n))
[ ] O(n!)
[ ] O(2^n)
[ ] O(n)