# Deep dive into the Intranet

## Quiz questions

Question #0

What are the steps of the Feynman Learning Technique? (Select all that apply)

[x] Step 3: Reflect, Refine, and Simplify
Only when you can explain the subject in simple terms do you understand it.

[x] Step 1: Choose a concept you want to learn about.
Once you identify a topic, take out a blank sheet of paper. Write out everything you know about the subject you want to understand as if you were teaching it to a child.

[ ] Step 2: Close your eyes and think
Now that you think you understand a topic reasonably well, close your eyes and think about your topic.

[x] Step 2: Explain it to a 12-year-old
Now that you think you understand a topic reasonably well, explain it to a 12-year-old.

[x] Step 4: Organize and Review
To test your understanding in the real world, run it by someone else. How effective was your explanation? What questions did they ask? What parts did they get confused about?

[ ] Step 1: Open a random page and start reading/watching
Once you find a topic, sit back and relax while you consume the newly found knowledge. It is enough to passively take the new information in.

Question #1

How can I interact with the sandbox? (Select all that apply)

[ ] SMTP
[x] Webterminal
[x] SFTP
[ ] IMAP
[x] SSH

Question #2

When should I use the checker

[ ] I am dependent on the checker - this is the only way to check my code
[x] I will develop and test my code myself - the checker is there to confirm the checks I have done myself.

Question #3

When will the checker details be available to me? (Select all that apply)

[x] Only in the first few projects
[ ] Never
[ ] Always
[x] After the deadline

Question #4

Where can I find a summary and details of my scores?

[x] Dashboard, click on the eye icon
[ ] Concepts page
[ ] My Profile
[ ] Slack

Question #5

What is the preferred development environment?

[ ] Mac Os Big Sur
[ ] Windows 10
[ ] Red Hat Enterprise 8.4
[ ] Chrome OS
[x] Ubuntu 20.04

Question #6

What is the purpose of the Captains Log? (Select all that apply)

[ ] To increase your network
[x] To let the team know how you are doing
[ ] To apply for a job
[x] To reflect on your learning journey
[x] To flag issues if any

Question #7

What happens when I close the web terminal window/tab

[ ] I will be notified that I still have running processes
[ ] The process will stop running
[x] The process will remain running in the background
[ ] Nothing

Question #8

In case of a project with 4 tasks, 3 of which are mandatory and each 10 points. and one is optional (advanced) for 10 points.
Finished the first two tasks before the first deadline, 
Finished third task before second deadline,
Finished The last optional task after the second deadline
What will my score be?

[ ] 90,25%
[x] 132,5%
[ ] 136,66%
[ ] 120%

Question #9

What is the purpose of the checker? (Select all that apply)

[ ] It is AI that will help you debug your code
[x] To check your code
[x] To score your project
[ ] To extend the deadline

Question #10

What is the duration of the foundations?

[ ] 6 months
[ ] 3 months
[x] 9 months
[ ] 12 months

Question #11

In the intro video we import the time module and use sleep, i.e. . In a general sense, why is it important to get enough sleep and maintain good sleep hygiene? (Select all that apply)

[x] When you sleep, new connections are made in your brain that were not there during the day and it makes you understand better
[x] Poor sleep has been shown to impair brain function.
[x] Good sleep can maximize problem-solving skills and enhance memory.
[x] Poor sleep is linked to depression, not being able to cope with all that is coming towards us.

Question #12

What should you do when you have time left in the PLD (Select all that apply)

[x] Code challenges
[x] Internet projects
[ ] Leave the session
[x] Look into previous concepts

Question #13

What are the elements of a PLD? (Select all that apply)

[x] Clarify Action Items
[x] Pair/Group Program
[x] Whiteboarding
[x] Share group progress

Question #14

What is the aim of a PLD session? (Select all that apply)

[x] To ensure each and every one of our peers understands content
[ ] To provide your peers with answers and share solutions
[x] To share your thought processes
[x] To ensure everyone is collectively growing in technical, soft and professional skills
[x] To empower peers with the ability to solve problems

Question #15

I can learn best by… (Select all that apply)

[x] Doing
[x] Explaining it to another person
[ ] Skipping the PLD
[ ] Copy-pasting another person's code
[x] Having another person explain it to me