# 0x00. C - Hello, World

## Quiz questions

Question #0

Which command can be used to compile a C source file?

[ ] c-compiler
[ ] bash
[x] gcc

Question #1

In which category belongs the C programming language?

[ ] Interpreted language
[x] Compiled language

Question #2

What is the common extension for a C header file?

[ ] .header
[x] .h
[ ] .hpp
[ ] .ch

Question #3

What is the common extension for a C source file?

[ ] .txt
[ ] .cpp
[x] .c
[ ] .py

Question #4

What are the different steps to form an executable file from C source code

[ ] Compilation and linking
[ ] Interpretation, compilation and assembly
[ ] Interpretation, assembly and compilation
[ ] Preprocessing and compilation
[x] Preprocessing, compilation, assembly, and linking

Question #5

Which of the following are both valid comment syntaxes in ANSI C, and Betty-compliant?

```c
# Comment
```

```c
/* Comment */
```

```c
/*
Comment
*/
```

```c
/*
 * Comment
 */
```

```c
/* Comment /* nested */ */
```

```c
// Comment
```