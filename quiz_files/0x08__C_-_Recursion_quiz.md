# 0x08. C - Recursion

## Quiz questions

Question #0

What does this code print?

```c
void print(int nb)
{
    if (nb < 0) 
    {
        return;
    }
    printf("%d", nb);
    nb --;
    print(nb);
}

int main(void)
{
    print(4);
    return (0);
}
```

[ ] 01234
[ ] 1234
[x] 43210
[ ] 4321

Question #1

What does this code print?

```c
void print(int nb)
{
    printf("%d", nb);
    nb --;
    if (nb > 0) 
    {
        print(nb);
    }
}

int main(void)
{
    print(2);
    return (0);
}
```

[ ] 012
[x] 21
[ ] 12
[ ] 210

Question #2

What does this code print?

```c
void print(int nb)
{
    printf("%d", nb);
    nb ++;
    if (nb < 10) 
    {
        print(nb);
    }
}

int main(void)
{
    print(4);
    return (0);
}
```

[ ] 987654
[ ] 345678910
[ ] 109876543
[x] 456789

Question #3

What does this code print?

```c
void print(int nb)
{
    printf("%d", nb);
    -- nb;
    if (nb > 0) 
    {
        print(nb);
    }
}

int main(void)
{
    print(4);
    return (0);
}
```

[x] 4321
[ ] 321
[ ] 43210
[ ] 3210

Question #4

What does this code print?

```c
int print(int nb)
{
    if (nb < 0) 
    {
        return (0);
    }
    printf("%d", nb + print(nb - 1));
    nb --;
    return (nb);
}

int main(void)
{
    print(4);
    return (0);
}
```

[ ] 64200
[ ] 01234568
[x] 00246