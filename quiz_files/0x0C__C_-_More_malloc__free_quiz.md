# 0x0C. C - More malloc, free

## Quiz questions

Question #0

malloc returns an address

[x] True
[ ] False

Question #1

To allocate enough space for an array of 10 integers (on a 64bit, Linux machine), I can use:

[ ] malloc(64 * 10)
[x] malloc(10 * sizeof(int))
[ ] malloc(10 * int)

Question #2

The memory space reserved when calling  is on:

```c
malloc
```

[ ] The stack
[x] The heap

Question #3

malloc returns a pointer

[x] True
[ ] False

Question #4

What will you see on the terminal?

```c
int main(void)
{
    int *ptr;

    *ptr = 98;
    printf("%d\n", *ptr);
    return (0);
}
```

[ ] 0
[ ] It doesn’t compile
[ ] 98
[x] Segmentation Fault

Question #5

If I want to copy the string “Best School” into a new space in memory, I can use this statement to reserve enough space for it (select all valid answers):

Question #6

You can do this:

```c
char *s;

s = strdup("Best School");
if (s != NULL)
{
    free(s);
}
```

[x] Yes
[ ] No

Question #7

You can do this:

```c
free("Best School");
```

[ ] Yes
[x] No

Question #8

What is wrong with this code:

```c
int cp(void)
{
    char *s;

    s = malloc(12);
    strcpy(s, "Best School");
    return (0);
}
```

Question #9

You can do this:

```c
char str[] = "Best School";

free (str);
```

[ ] Yes
[x] No