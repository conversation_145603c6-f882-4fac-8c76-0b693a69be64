# 0x01. Shell, permissions

## Quiz questions

Question #0

What is the permission value for a file read only for the group owner?

[x] 040
[ ] 060
[ ] 050
[ ] 070

Question #1

Which command should I use for changing a file permission?

[ ] su
[ ] chown
[x] chmod
[ ] chgrp

Question #2

What is the permission value for a file without any restriction?

[ ] 600
[x] 777
[ ] 644

Question #3

Which command should I use for changing a file owner?

[ ] su
[x] chown
[ ] chmod
[ ] chgrp

Question #4

What is the numerical value for the  permission?

```c
r-xr--r--
```

[ ] 522
[ ] 644
[x] 544
[ ] 411

Question #5

What is the numerical value for the  permission?

```c
rwx------
```

[ ] 600
[ ] 704
[ ] 621
[x] 700

Question #6

What is the numerical value for the  permission?

```c
----w---x
```

[ ] 123
[ ] 221
[ ] 121
[x] 021