# Web Stack Portfolio Project Criteria

## Web Stack Portfolio Project Criteria

#### Overview

This paper describes the requirements for the web stack portfolio project.

#### Project Scope:

Projects must fall into one of the following categories:

- Front-end only

- Back-end only

- Full-stack applications

- Mobile applications

- AI projects

Static sites are not permitted; projects must demonstrate dynamic functionality.

#### Timeline:

- Projects built before the designated time are not acceptable. Learners must adhere to the specified timeframe the curriculum provides for project development. Learners will have one month to develop, and present their project.

#### GitHub Commit History:

Learners must maintain a consistent track record of commits on GitHub. Each commit should be accompanied by a standard commit message, providing clarity on the changes made.

#### Group Contribution:

- All project group members are expected to contribute to the codebase actively.

- Contributions should be proportional, ensuring fair distribution of workload among team members.

#### Originality:

Instances of plagiarism will result in consequences, including disqualification from graduation, disengagement from the program, or a flag from Kimba.

#### Framework Flexibility:

- Learners have the freedom to choose any framework they prefer for their project.

#### Front-end Only Projects:

- For projects focusing solely on the front end, learners may use mock API tools to simulate backend functionality.

#### Backend Only Projects:

- Projects focusing exclusively on the backend must include comprehensive documentation of API endpoints.

#### Documentation:

- README.md should include setup instructions, usage guidelines, and an overview of the project architecture.

#### Demonstration:

- The demonstration should provide insights into the project’s development process and highlight key achievements.