# Approaching a Project

It can be difficult to know how to approach a project. You’ve read resources and tried some examples, and now it’s time to apply what you know. Where do you start? The following framework can help you focus how you approach a project.

Read the steps below and think about how you would follow them for a task.

- Read the task or challenge.

- Rephrase it in your own words.

- Verify expected behavior by getting example inputs and outputs.

- Whiteboard your solution with words.

- Walk through your solution with an example as if you were a machine. Do only what is written down. Correct your solution if needed.

- Whiteboard your solution with pseudocode.

- Walk through your solution with an example as if you were a machine. Do only what is written down. Correct your solution if needed.

- Whiteboard your solution with code.

- Walk through your solution with an example as if you were a machine. Do only what is written down. Correct your solution if needed.

- Write your code on your computer.  [Note: This is this first time you are using your computer! Only when you know exactly what to code.]

- Test your code on your computer.

- Debug if necessary.

- You complete the task or challenge!

Be sure to break your task or project into smaller and smaller pieces, until you are comfortable solving each piece with the above steps.

When you are in step 10, “Write your code on your computer”, you should code each piece one at a time and debug right after each small step to make sure everything works properly. This will also prevent you from having to debug multiple pieces at the same time and thus, will make your life much easier.