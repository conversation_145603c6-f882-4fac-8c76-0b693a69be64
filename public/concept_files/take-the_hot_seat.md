# Guidelines: Take the Hot Seat - Live Interview and Leetcode Problem DEMOs #ALXLevelUp

# Welcome!

## Read these Guidelines to participate in the event Take the Hot Seat - Live Interview and Leetcode Problem DEMOs  🔥

You can use these guidelines to repeat the DEMOs when and where you want with who you wish to! ❤️

### 🧑🏾‍💼 Live Interview DEMO

### 📣 Rules of participation

#### 🧑🏾‍💻 Participants:

- 1 learner to volunteer as the Interviewee

- 1 learner to volunteer as the interviewer

Both volunteers will be randomly selected during the event.

- The rest of the audience will listen and take notes to give feedbacks at the end of the interview

#### ✍🏾 Rules of the DEMO:

The interview will be based on this job description hereunder:

- Each interview will last 20 minutes

- The volunteering recruiter will ask 3 non-technical questions and 2 technical questions - the questions will be sourced in these two lists (technical and non-technical questions)

- At the end of the interview - 10 minutes for feedback from the audience

#### 📖 Get ready for the interview

*Do you feel ready to participate in the Live Interview DEMO as interviewer or Interviewee? 
*
Make sure to arrive prepared!

Check out the videos in this playlist and read how to get ready for a mock interview here .

From meh to wow: How to answer “Tell me about yourself” in an interview

If you want to volunteer for the role of Interviewee, be ready to use https://codeshare.io/

#### 📖 The Tech Interview Handbook

If you are preparing for a Tech Interview, you should check this out! Start reading here

### 🧑🏾‍💼 Live Leetcode DEMO

### 📣 Rules of participation

#### 🧑🏾‍💻 Participants:

Group of 5 volunteers: preference will be given to people with little or zero experience in solving leetcodes
You must:

- Have little or zero experience with leetcode;

- Have knowledge of Python;

- Good use of whiteboard

#### ✍🏾 Structure of the DEMO

Duration: 30 minutes max

With the guidance of the facilitators, the group will:

- Read and understand the problem

- Brainstorm a possible solution on a whiteboard: https://excalidraw.com/

- Pseudocode: Transform the solution in logical steps

- Recommended when working in groups: Go to https://codeshare.io/ and start writing it as a code (python)

- Run the code on Leetcode

- Debugging as needed

#### 📖 Get ready for solving Leetcodes!

Do you feel ready to participate in the Live Leetcode DEMO? Make sure to arrive prepared!
Be ready to use https://codeshare.io/ and https://excalidraw.com/

Do you know that some ALX SE Projects help you get ready for your next Technical Interview?

They are released during Specialization, one per week!

- Prime Game

- Island Perimeter

- Rotate 2D Matrix

- Minimum Operations

- Lockboxes

- Pascal’s Triangle

- Log Parsing

- UTF-8 Validation

- N Queens

- Star Wars API

- Making Change

What is Leetcode?

It is an online platform where you can challenge yourself by solving Coding challenge and it is often used in Coding interviews

Do you want to start practicing for your next Technical Interview?
Here you can find some of the many existing online Platforms:

- Leetcode

- Systemexpert

- Codeinterview.io

- Geeksforgeeks

- Geektastic

- Codewars

- Aceinterview

### 🧑🏾‍💼Job Description - Full-Stack Developer Trainee

#### About the job

We are looking for a Full-Stack Developer to join our team as a trainee. Successful candidates will join our engineering team, a group of people focused on designing, implementing, and delivering amazing products directly to thousands of customers.
You will learn how to build Web based applications such as content-management platforms and Web API’s supporting mobile apps and cloud-based solutions.

#### Requirements

We know that you will be a good fit for our team, if…

- You have familiarity with Object-Oriented languages and university-level programming skills

- You simply enjoy coding, regardless of the programming language

- You have familiarity with any Database Management System( MySQL, PostgreSQL, SQL Server) and SQL

- You have a special attention to detail and want to understand how technologies work and not just how they are used.

- You have a problem solver mentality and understand that coding is a skill that requires practice to acquire

- You have already experimented with backend frameworks like .NET 5/6, Java Spring Boot, NodeJS and/or Javascript frameworks like Angular.

- Undergraduate studies in Computer Science, Engineering, Mathematics, Physics or relevant field

### 🗣️ LIST OF TECHNICAL QUESTIONS (If you are volunteering as the interviewer, please pick 2)

#### General Programming and Object-Oriented Concepts

- Can you explain the basic principles of Object-Oriented Programming (OOP)? Provide an example of how you have used OOP in a project.

- What are the basic data types in the python programming language?

- If possible provide all operations that can be performed on those types; like retrieving, adding, deleting etcetera.

- Describe a recent project you worked on that involved coding. What challenges did you face, and how did you overcome them?

- How do you stay updated with new programming languages and technologies?

#### Database Management and SQL

- What is a relational database? How does it differ from a non-relational database?

- Can you write a SQL query to find all records in a table where a specific column value meets a condition?

- What is an ORM?

- Describe one query optimization technique in relational databases like MySQL.

- Describe a project where you used a Database Management System (e.g., MySQL, PostgreSQL, SQL Server). What was your approach to database design?

#### Backend Development

- What is your experience with backend frameworks like NodeJS express, Flask & Django in python? Can you describe a project where you used one of these frameworks?

- What are the differences between a production server and a development server?

- In application servers like express and flask, explain the uses of a middleware.

- Describe what an MVC architecture is.

- How would you handle error management and logging in a backend application?

- Explain the concept of RESTful APIs. Have you ever created one? If so, describe the process and tools you used.

#### Frontend Development

- What JavaScript frameworks have you used? Can you describe a project where you implemented a frontend feature using one of these frameworks?

- In JavaScript, explain the concept of hoisting.

- In JavaScript, explain the differences and similarities between null and undefined.

- How do you ensure the responsiveness and cross-browser compatibility of a web application?

- Describe your experience with HTML and CSS. How do you approach styling and layout in a web application?

### 🗣️ LIST OF NON-TECHNICAL QUESTIONS (Source: Julien Barbier’s Linkedin)  (If you are volunteering as the interviewer, please pick 3)

- Tell me about your background?

- How did you get into programming? (can be changed to match the current program they are in)

- What is ALX?

- What did you do before ALX? and Why?

- What is the project you liked the most at ALX and why?

- Tell me about you?

- How would your friends and family describe you, your personality?

- What would be your perfect role in a company? What would be your different responsibilities?

- How do you usually communicate with the team?

- How would you describe yourself – your personality?

- What do you know about working in a start-up?

- What do you expect from a supervisor?

- What would be your perfect type of Company to work in?

- Tell me about the last time a co-worker or a peer at school got mad at you and how did you react?

- How do you usually deal with a complicated project?

- What would be the perfect day or work for you?

- What would get on your nerves – professionally speaking?

- What would be your perfect time window for working?

- Where do you see yourself, 5 years from now

- Tell me about a time you knew you were right but still had to follow directions or guidelines

- In 3 words: why should I hire you?

- What are your weaknesses?

- Tell me about yourself?

- Can you list your strengths?

- What weaknesses do you have?

- Why should I consider hiring you?

- Why do you want to work here?

- What is your salary expectation?

- What motivates you?

- What makes a good team player?

- Is there anything that you would like to ask me?

- What is your greatest strength?

- Why do you want this job?

- Describe a difficult work situation / project and how you overcame it

- What is your greatest weakness?

- What can you do better for us than the other applicants?

- How do you handle stress and pressure?

- What are your goals for the future?

- If I asked someone who doesn’t like you why they don’t like you, what would they say?

- Tell me about a time when you had to act very quickly to make a decision about a problem. What was the situation and what did you do?

- What can you bring to ?

- Name a time you brought someone joy at your last job

- What are three words you would use to describe your personal brand

- Tell me about a time when you had to learn something on your own?

- Why do you want to leave your current position?

- What makes you want to work for ?

- Name a time you solved a problem creatively

- Tell me about a time where you had to bend the rules to help someone

- If a former coworker were to describe you, what are some strengths and weaknesses they’d say you have?

- What was your last position, and what are some reasons for leaving it?

- Name a time you helped a coworker out

- What makes someone a good team player?