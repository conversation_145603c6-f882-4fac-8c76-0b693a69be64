# Technical Writing

## Technical Writing Overview

Technical writing is an invaluable skill and an excellent way to articulate and share your knowledge. It’s not enough to just be able to code; being able to explain the concepts behind the code proves deeper understanding as well as the ability to communicate with others.

## Technical Writing Basics

You will write several blogs during your time as a student. Technical writing pieces are included in both project assignments and as extracurricular assignments from staff.

The general requirements for all blogs are as follow:

- Have at least one picture at the top of the blog post

- Publish your blog post on Medium or LinkedIn

- Share your blog post at least on LinkedIn

- Write professionally and intelligibly

- Explain every step/concept you know, and give examples – a total beginner should understand what you have written

- Please remember that these blogs must be written in English to further your technical ability in a variety of settings

- Remember that future employers will see your articles; take your writing seriously and produce something that will be an asset to your future

Blogs are manually reviewed by a staff member, a TA, or a peer and are evaluated on how well they meet the above requirements as well as specific requirements based on topic, which is listed in the associated project.

This should go without saying, but plagiarism is unacceptable.

## Technical Writing FAQ

#### “Why are the blog instructions so vague? How do I know what to write about?”

We do not outline all the requirements because part of these challenges is to teach/learn/experience how to find what needs to be said on a topic that’s new to you. You may not know what needs to be explained to a reader, but your research on the topic should help guide you.

#### “If the blog should be understood by beginners, why is there an expectation of having in-depth explanations, even to the point of using jargon beginners may not understand?”

Even if your audience is beginners, we must trust that they could understand a highly technical topic if it is explained well. Be sure to not only use appropriate jargon but to define the jargon you use. After reading your blog, the hope is that your audience will know the technical topic at the deepest level possible.

#### “My reviewer scored me more strictly than others. Can’t we make this more standardized?”

Each student is entrusted to score fairly and to the best of their ability. We understand that there may be a variance between how reviewers score, but it also reflects the interpretation and understanding of a general reader audience.

#### “Can I write other technical blogs outside of school projects?”

Of course! You’re encouraged to share them with staff and peers as well. If you’re interested in writing more but don’t know what to write about, here are some ideas:

- Keeping a devblog for big projects is a great way to document processes, successes, failures, and to-dos.

- If you find a framework, SDK, API, etc. has limited or confusing documentation, write a tutorial to help others with the problems that you’ve encountered.