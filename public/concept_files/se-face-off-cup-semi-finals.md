# SE Face-Off Cup: Semi Finals 💻🏆





Semi Finals Challenge

## ⏬Read below before filling the form⏬

NB. Team Leaders (who submitted the qualification form) are responsible for submitting this one! Only ONE submission per team is possible

## Challenge Name

Conway’s Game of Life: A Tkinter Desktop App

## Description

In this challenge, you’ll be creating a desktop application simulating <PERSON>’s Game of Life, a cellular automaton that evolves based on simple rules.

You’ll specifically use the Tkinter library to build a visually appealing and interactive interface.

## Requirements:



### Core Functionality:

- Game of Life Rules: Implement the core rules of the Game of Life as described in previous responses.

- Grid Representation: Create a grid-based representation of the game world using <PERSON><PERSON><PERSON>’s canvas widget.

- Initialization: Allow users to choose a starting pattern or initialize the grid randomly.

### Additional Features (Optional):

- Boundary Conditions: Explore different boundary conditions (e.g., toroidal, finite).

- Persistence:Implement saving and loading of game states.

- Performance Optimization:Optimize the application for larger grids and faster simulations.

- Custom Patterns:Allow users to create and save custom patterns.

## Evaluation Criteria:

In addition to the criteria mentioned above, consider the following:

### Tkinter Usage:

- Effective use of Tkinter widgets and methods.

- Proper event handling and response to user input.

- Clear and organized code structure.

### Visual Design:

This is just additional stuff you can think of after implementing the core functionalities of the game.

- Aesthetically pleasing and intuitive interface.

- Consistent use of colors, fonts, and spacing.

- Clear labeling and tooltips.

### Submission Requirements:

▶️ Python Code:Submit your Python code repository.

▶️ Executable:If possible, provide an executable version of your application for easy testing.

Note: This challenge specifically requires the use of the Tkinter library for building the desktop application. Focus on leveraging Tkinter’s features to create a visually appealing and interactive interface.

## 👏Congratulations for reading till here!

## ▶️ FILL THE FORM!◀️

