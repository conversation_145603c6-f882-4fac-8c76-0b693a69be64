# BrandU Challenge - 2nd challenge:  Revamp Your CV & Cover Letter







## Challenge #2 of the BrandU Challenge

Let’s dive into the second challenge of this end-of-the-year challenge that will help you standing out through your CV and Cover Letter, ready for your next application!🎉

## Read about the BrandU Challenge here

### What is the challenge about?

Write a standout CV and cover letter for an existing job on Remote Otter

📅 Start Date: Monday, December 9

📅 End Date: Sunday, December 15

### Why This Challenge Matters?

🔑CV Insights

- Recruiters spend 6–7 seconds reviewing your CV.

- 75% use Applicant Tracking Systems (ATS).

- Tech roles can attract 1,000+ applicants within a week!

Cover Letter Stats:

- 77% of recruiters favor candidates who submit a cover letter.

- 53% prefer candidates who submit both a CV and cover letter.

### Before you begin:

👉 Understand what employers look at in CV

<iframe src="https://www.youtube.com/embed/bmFLzUGKhC4?si=4qfPFgLHz0AQbJEc" width="560" height="315" frameborder="0" allowfullscreen></iframe>





### Ready to Begin?

#### Here’s how to create an ATS-friendly CV:

- 1️⃣ Avoid Headers/Footers: Place contact details in the main body of the document.

- 2️⃣ Use Keywords: Match skills from the job description (e.g., “Python,” “SEO”).

- 3️⃣ Skip Graphics: ATS can’t read them—save visuals for your portfolio.

- 4️⃣ Stick to Simple Bullets: Solid or open circles work best.

- 5️⃣ Highlight Achievements: Use action verbs (e.g., executed, strategized) and quantify results (e.g., boosted revenue by 20%).

#### Mistakes to Avoid and Tips for a Better CV and Cover Letter

##### 🙅🏾‍♂️Instead of this:

Uninspired work experience with a vague list of responsibilities.

> “Responsible for managing software updates.”

##### 👌🏾Try this:

Highlight data-backed achievements: Specify measurable outcomes (e.g., increased efficiency by 30%).

Use action verbs:Use words like achieved, executed, strategized, led, explained, and influenced. Avoid diminishing verbs like helped, tried, or attempted.

> “Led the integration of a CI/CD pipeline, reducing deployment times by 30%”

Highlight impact over duties: Emphasize the results of your work. How much revenue did you generate? What metrics improved because of your contribution?

> “Managed software updates, reducing downtime by 15% through efficient planning and execution.”

Showcase how your contributions made a difference.

Use Challenge-Action-Result (CAR) statements: Frame achievements as:

- Challenge: What was the problem?

- Action: What steps did you take?



##### 🙅🏾‍♂️Instead of this:

A disorganized design with inconsistent formatting, margins, or fonts, making it hard to read.

##### 👌🏾Try this:

Use clean and professional fonts: Stick with easily readable fonts like Arial or Times New Roman, size 11–12.

Keep formatting consistent: Ensure margins, headers, and bullet points align throughout the document.

Avoid graphics or photos: These can distract and sometimes cause issues with ATS (Applicant Tracking Systems).

Separate sections with clear headers: Use bold or slightly larger text for section titles like “Experience” and “Skills.

Save and send your CV as a PDF to ensure the formatting remains intact

Limit the lenght to 1 page focusing on the most recent experience

##### 🙅🏾‍♂️Instead of this:

An unimpressive resume summary that doesn’t reflect your skills or career aspirations.

##### 👌🏾Try this:

Write a compelling, tailored resume summary at the top. Include:

- Exact years of experience.

- Your top skills relevant to the job.

- A standout achievement.

> “Software engineer with 5+ years of experience in full-stack development. Successfully led a team to deliver an award-winning application, increasing client retention by 40%.”

##### 👇🏾More tips for crafting your CV:

💡Emphasize experience: Use 2–3 bullet points per role to describe significant contributions and results.

💡Include optional sections: Volunteer work, projects, or certifications can add value if relevant.

#### Steps for Writing a Cover Letter

##### Start with research

Study the company values, goals, and recent news.

Review the job description to identify key skills and responsibilities.

##### Structure your cover letter

📄Heading: Include your name, contact details, and the date. Optionally, add a link to your LinkedIn or portfolio.

🗣️Address the recipient: Use the hiring manager’s name if available; otherwise, “Dear Hiring Manager” works.

📄Opening paragraph: Mention the role you’re applying for and where you found the job listing. Show enthusiasm and briefly highlight why you’re interested.

##### 👌🏾Try this:

> “I’m thrilled to apply for the Software Engineer position at [Company Name]. With a passion for [specific skill] and a track record of [specific achievement], I’m eager to contribute to your team.”

Body paragraphs



🤓Showcase 1–2 examples of your qualifications.Use metrics or outcomes to quantify your success.

🤓Align your skills and experiences with the job’s requirements.

🤓Replace buzzwords with specific examples of your skills in action.

Skills highlight: 
Mention relevant technical skills (e.g., programming languages, frameworks) and soft skills (e.g., collaboration, problem-solving).

Emphasize Soft Skills with Examples

##### 🙅🏾‍♂️Instead of this:

> “Good at teamwork and leadership.”

> I’m a motivated, results-driven team player who thrives in dynamic environments

##### 👌🏾Try this:

> “Collaborated with cross-functional teams to launch a feature used by 10,000+ users, demonstrating effective communication and leadership skills.”

📄Closing paragraph: Reiterate your enthusiasm for the role. Thank the hiring manager for their time and suggest a follow-up, such as a call or interview.

##### 👌🏾Try this:

> “Thank you for considering my application. I look forward to discussing how my expertise can contribute to your team’s success.”



##### 👇🏾More tips for crafting your Cover Letter:

💡Tailor each cover letter to the role and company.

Personalize your message by mentioning the company’s achievements or values and connecting them to your skills.

##### 🙅🏾‍♂️Instead of this:

> “I am excited to apply for this role and believe I have the skills required.”

##### 👌🏾Try this:

> “I am drawn to [Company Name] because of your recent project on [specific example]. My experience in [related skill or achievement] makes me confident in contributing to your team’s success.”

💡Rehashing your CV: Use the cover letter to add personality and depth. For example, give them a specific reason that you’re interested in working for them at the start.

💡Check for spelling and Grammar Errors

Use tools like Grammarly and read your CV aloud to catch mistakes. Asking a trusted peer to review it can also help.

### ✍🏾You are ready to start writing!

Jump on Remote Otter and find a job you want to apply to!

Resume Templates:

Template #1

Template #2

Template #3

Template #4

Template #5

More tips!

The Tech Resume: Common Mistakes

The Tech Resume: Common Mistakes



CV and cover letter prepping playlist

Cover Templates:

Template #1

Template #2

Template #3

Template #4

Template #5

Feel free to use any other template you prefer

More tips!

Indeed article

Free code camp article



CV and cover letter prepping playlist

### 📢 Make it Loud!

Share your progress on social media using these hashtags #LifeAtALX #BrandUChallenge #ALX_SE . You can get inspired by the post samples hereunder 👇🏾

Twitter🎯 “Every great career starts with a standout CV and cover letter!”
This week I am drafting my CV with measurable achievements and action verbs to apply to this job[insert link]

🚀 Next step: a tailored cover letter showcasing my unique fit for my dream role. 🌟

How’s your #BrandUChallenge going? Share your progress! 💪

LinkedIn: 🚀✨ Progress update on my #BrandUChallenge journey!

Crafting my CV and cover letter has been a game-changer. I am focusing on:[insert what you are focusing on]

For my cover letter, I’m personalizing it with [insert what you are focusing on]

Thanks to the #LifeAtALX and #ALX_SE community for inspiring me to level up! Let’s keep growing together. 🚀

How are you progressing? Let’s see those updates! 😊 #BrandUChallenge

🚀 Let’s build your brand together! 🚀