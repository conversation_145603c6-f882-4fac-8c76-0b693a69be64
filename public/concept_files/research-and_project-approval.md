# Research & Project Approval Overview

Before starting any coding, you will need to research and define your portfolio project over the course of 3 weeks. Each part will be released on a Friday, and be due the following week on Friday.

A second deadline will be available for the first two parts but not the third. If a project is not approved by the Part 3 deadline, then a student will default to working on the Maze project

#### Part 1

This first part is focused on creating a Project Proposal and requires that a Google Document is submitted for Manual Review by a technical staff member. Please take time to brainstorm, research, and explore what currently exists during this first phase.

#### Part 2

Create a specification for the first version of your software, the Minimum Viable Product (MVP).

#### Part 3

Create a Trello board where the features defined in the MVP are broken down into tasks with specific deadlines and team member assignments.

#### A Note about Staff Approval

Members of technical staff will be reviewing your project throughout these 3 weeks to ensure a plan is in place, and progress is being made. Please do not rely on the technical staff to provide detailed technical feedback. If there is bandwidth, they may be able to offer some notes to guide you in the right direction. It is your responsibility to seek out the support you need to reach the challenge you’ve defined for yourself. The technical work is yours to research, prove, and navigate through this final series of projects.

## Examples of the past

Explore some student projects from previous cohorts!

- Eggify (Github) – by Athena Deng & Samie <PERSON>zad

- IdeaDog (Github) – by <PERSON>

- <PERSON> (Github) – by <PERSON> & <PERSON>e

- Synth (Github) – by Jack Gindi & <PERSON> Dixon

- DeTweet (Github) – by Robert G<PERSON>el

- GIPSY (Github) – by Afa Mazda