# Pair Programming - How To

![Image](assets/image_2_6aeed3f518d8655dc457.jpeg)



> Regardless of if you think you understand something, pair programming is an invaluable industry skill that you need to practice

Regardless of if you think you understand something, pair programming is an invaluable industry skill that you need to practice

Perhaps, it seems obvious what you need to do, but ensure you know what you’re doing by learning through the examples below.

Let’s say we have two students, <PERSON> and <PERSON>. <PERSON> is trying to understand a few things that <PERSON> wants to help with. Here’s how it’d break down:

<PERSON> is not allowed to touch the computer; instead, they need to make sure that <PERSON> goes through The ALX Framework.

<PERSON> should try to prevent <PERSON> from searching Google if the answer can be found in the man or documentations provided on the Intranet.

If <PERSON> struggles too much solving one task, <PERSON> is allowed to use the computer and code the solution in front of <PERSON>, and explain the thought process.

<PERSON> should make sure that <PERSON> understands everything, though. When done, <PERSON> deletes all the files containing the solution, and <PERSON> tries again.

If you’re trying to help someone:

- When explaining a concept, make sure you give examples to illustrate. Try to use whiteboards as much as you can.

When explaining a concept, make sure you give examples to illustrate. Try to use whiteboards as much as you can.

- Be patient and understand that people think differently about the same problem. Sometimes, something will seem obvious to you, but to someone else, it may not be.

Be patient and understand that people think differently about the same problem. Sometimes, something will seem obvious to you, but to someone else, it may not be.

- Most of the time, if you explain the same concept twice and your peer doesn’t get it, it’s because you didn’t explain it well enough :) It’s your fault, not theirs.

Most of the time, if you explain the same concept twice and your peer doesn’t get it, it’s because you didn’t explain it well enough :) It’s your fault, not theirs.

- Try to rephrase, break it into smaller pieces, find new examples

Try to rephrase, break it into smaller pieces, find new examples

- To ensure someone understands a concept, it is not enough to ask “do you understand”. You should start with this question, yes, but you should ask them to solve a similar small task using the same concept. A good way to test is to ask a knowledgeable peer to explain it again to you.

To ensure someone understands a concept, it is not enough to ask “do you understand”. You should start with this question, yes, but you should ask them to solve a similar small task using the same concept. A good way to test is to ask a knowledgeable peer to explain it again to you.

For Instance, if [following our prior example] Kristen explains permissions / modes, the questions should be:

- Do you understand?

Do you understand?

- Yes

Yes

- Great! Explain it back to me now please :)

Great! Explain it back to me now please :)

- …

…

- Great, explain what does rwxr-xr– mean

Great, explain what does rwxr-xr– mean

- …

…

- Great, so what would be the octal notation of rwxr—-x?

Great, so what would be the octal notation of rwxr—-x?

- …

…

- Great! what would be the permissions of a directory with a mode of 755?

Great! what would be the permissions of a directory with a mode of 755?

- …

…

### Pair programming Continued …

When a peer receiving support completes all tasks demonstrated by the peer helping, they should work together on the remaining tasks (including the advanced tasks). Same rules apply:
* Read the documentation

- Don’t code before you have a potential solution

Don’t code before you have a potential solution

- Explain out loud your thought process

Explain out loud your thought process

- Etc..

Etc..

For each task, switch the students who is typing. The student needing support should start. When switching, the new student needs to code the same task again. No copy-paste.

Example: There are 3 tasks remaining: T0, T1, T2, and Kristen and Stuart are still our examples of collaborating students.

- Stuart and Kristen solve all tasks together, but:

Stuart and Kristen solve all tasks together, but:

- Stuart starts typing for T0 and when done pushes on his github.

Stuart starts typing for T0 and when done pushes on his github.

- Then Kristen types again the solution of T0 and pushes to her github.

Then Kristen types again the solution of T0 and pushes to her github.

- Then Kristen types for T1, pushes on her github.

Then Kristen types for T1, pushes on her github.

- Then Stuart types again the solution of T1, pushes, and starts T2.

Then Stuart types again the solution of T1, pushes, and starts T2.

- When Stuart and Kristen found the solution for T2, Stuart pushes to his github.

When Stuart and Kristen found the solution for T2, Stuart pushes to his github.

- Then Kristen types the solution again and pushes on her github.

Then Kristen types the solution again and pushes on her github.