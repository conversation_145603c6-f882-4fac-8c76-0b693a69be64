#!/usr/bin/env python3
"""
Script to remove duplicate headings from concept files
"""

import re
from pathlib import Path

def remove_duplicate_headings():
    """Remove duplicate headings from concept files"""
    
    public_concept_dir = Path("public/concept_files")
    
    processed_count = 0
    
    for concept_file in public_concept_dir.glob("*.md"):
        try:
            # Read the file
            with open(concept_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Check if first two non-empty lines are the same heading
            first_heading = None
            first_heading_line = -1
            second_heading_line = -1
            
            for i, line in enumerate(lines):
                stripped = line.strip()
                if stripped.startswith('#') and first_heading is None:
                    first_heading = stripped
                    first_heading_line = i
                elif stripped.startswith('#') and stripped == first_heading:
                    second_heading_line = i
                    break
                elif stripped and not stripped.startswith('#'):
                    # Found non-heading content, stop looking
                    break
            
            # If we found duplicate headings, remove the second one
            if second_heading_line != -1:
                # Remove the duplicate heading line and any empty line after it
                lines_to_remove = [second_heading_line]
                if second_heading_line + 1 < len(lines) and lines[second_heading_line + 1].strip() == '':
                    lines_to_remove.append(second_heading_line + 1)
                
                # Remove lines in reverse order to maintain indices
                for line_idx in reversed(lines_to_remove):
                    lines.pop(line_idx)
                
                # Write back to the file in public/concept_files
                new_content = '\n'.join(lines)
                with open(concept_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print(f"✅ Fixed duplicate heading in {concept_file.name}")
                processed_count += 1
            else:
                print(f"📝 No duplicate heading found in {concept_file.name}")
                
        except Exception as e:
            print(f"❌ Error processing {concept_file.name}: {e}")
    
    print(f"\n📊 Summary: {processed_count} files processed")

if __name__ == "__main__":
    print("🧹 Removing duplicate headings from concept files...")
    print()
    
    remove_duplicate_headings()
    
    print("\n✅ Duplicate heading removal completed!")
