import os
import shutil
import json

def organize_files():
    workspace_root = os.getcwd()
    source_dir_name = "concept_files"
    dest_dir_name = "public/concept_files"
    mapping_file_name = "src/data/concepts/concept_mapping.json"

    source_dir = os.path.join(workspace_root, source_dir_name)
    dest_dir = os.path.join(workspace_root, dest_dir_name)
    mapping_file_path = os.path.join(workspace_root, mapping_file_name)

    print(f"Source directory: {source_dir}")
    print(f"Destination directory: {dest_dir}")
    print(f"Mapping file: {mapping_file_path}")

    # Step 1: Ensure destination directory is clean or created
    if os.path.exists(dest_dir):
        print(f"Clearing destination directory: {dest_dir}")
        for item in os.listdir(dest_dir):
            item_path = os.path.join(dest_dir, item)
            try:
                if os.path.isfile(item_path) or os.path.islink(item_path):
                    os.unlink(item_path)
                elif os.path.isdir(item_path):
                    shutil.rmtree(item_path)
            except Exception as e:
                print(f"Error removing {item_path}: {e}")
    else:
        print(f"Creating destination directory: {dest_dir}")
        os.makedirs(dest_dir)

    # Step 2: Copy files and directories from source to destination
    if not os.path.exists(source_dir):
        print(f"Error: Source directory {source_dir} not found.")
        return

    print(f"Copying items from {source_dir} to {dest_dir}...")
    for item in os.listdir(source_dir):
        source_item_path = os.path.join(source_dir, item)
        dest_item_path = os.path.join(dest_dir, item)
        try:
            if os.path.isdir(source_item_path):
                shutil.copytree(source_item_path, dest_item_path)
                print(f"Copied directory {source_item_path} to {dest_item_path}")
            else:
                shutil.copy2(source_item_path, dest_item_path)
                print(f"Copied file {source_item_path} to {dest_item_path}")
        except Exception as e:
            print(f"Error copying {source_item_path} to {dest_item_path}: {e}")

    # Step 3: Rename files in destination based on mapping
    if not os.path.exists(mapping_file_path):
        print(f"Error: Mapping file {mapping_file_path} not found. Skipping renaming.")
        return

    print(f"Renaming files in {dest_dir} based on mapping...")
    try:
        with open(mapping_file_path, 'r') as f:
            mapping_data = json.load(f)
    except Exception as e:
        print(f"Error reading or parsing mapping file {mapping_file_path}: {e}")
        return

    renamed_count = 0
    not_found_count = 0
    for original_name, id_suffix in mapping_data.items():
        if id_suffix:  # Ensure id_suffix is not null or empty
            # id_suffix is like "_135.md". We need to form "concept_135.md"
            file_id = id_suffix.replace('_', '').replace('.md', '')
            current_filename_in_dest = f"concept_{file_id}.md"

            old_path = os.path.join(dest_dir, current_filename_in_dest)
            new_path = os.path.join(dest_dir, original_name)

            if os.path.exists(old_path):
                try:
                    os.rename(old_path, new_path)
                    print(f"Renamed: '{old_path}' to '{new_path}'")
                    renamed_count += 1
                except Exception as e:
                    print(f"Error renaming {old_path} to {new_path}: {e}")
            else:
                # This can happen if the concept_XXX.md file wasn't in the source_dir
                # or if the id_suffix in JSON doesn't correctly point to a copied file.
                # print(f"Warning: File to rename '{old_path}' not found in destination directory.")
                not_found_count +=1
        else:
            # Handles cases like "README.md": null
            # These files, if not directly copied with the final name from source_dir,
            # and not generated from a concept_XXX.md, will effectively be absent
            # from the dest_dir unless they were part of unmapped files in source_dir.
            pass
    
    print(f"Renaming complete. {renamed_count} files renamed.")
    if not_found_count > 0:
        print(f"Warning: {not_found_count} source files for renaming were not found (this might be expected for null mappings or missing concept files).")

if __name__ == "__main__":
    organize_files()