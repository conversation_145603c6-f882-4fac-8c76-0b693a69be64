# Project and Concept Name to ID Mappings

This file maps project and concept names (derived from `identified_s3_source_names.md`) to their corresponding Intranet IDs.
Project IDs are sourced from `markdown_files/projects.md`.
Concept IDs are sourced from `corrected_mapping.md`.

## Project Mappings

| Project Name      | Intranet ID |
|-------------------|-------------|
| `hello-world`     | 212         |
| `intranet`        | 100009      |
| `malloc`          | 222         |
| `recursion`       | 219         |
| `shell-variables` | 209         |
| `structures`      | 225         |
| `variables`       | 213         |
| `web-static`      | 268         |

## Concept Mappings

| Concept Name                              | Intranet ID |
|-------------------------------------------|-------------|
| `3-ways-to-make-a-portfolio-great`        | 135         |
| `ALX_SE-discord-migration`                | 100033      |
| `C-programming`                           | 26          |
| `CICD`                                    | 43          |
| `Pointers-and_arrays`                     | 60          |
| `airbnb_clone`                            | 74          |
| `all-about-team-projects-FAQ-pairings`    | 100037      |
| `automatic_and-dynamic-allocation`        | 62          |
| `chrome`                                  | 224         |
| `code-of-conduct`                         | 100011      |
| `code-of-conduct_enforcement`             | 100012      |
| `essential_cheat-sheet`                   | 105416      |
| `flowcharts`                              | 130         |
| `git-github_cheatsheet`                   | 57          |
| `group-projects`                          | 111         |
| `load-balancer`                           | 46          |
| `maze-project`                            | 133         |
| `mentor_yellow-pages`                     | 100031      |
| `on-call`                                 | 39          |
| `pair-programming`                        | 121         |
| `peer-learning-day`                       | 58          |
| `slack`                                   | 221         |
| `source-code-mgt`                         | 22          |
| `trinity_of-frontend-quality`             | 4           |
| `webstack-debugging`                      | 68          |